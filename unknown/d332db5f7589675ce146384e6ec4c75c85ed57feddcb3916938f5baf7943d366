package com.github.manasmods.tensura.config;

import java.util.Arrays;
import java.util.List;
import net.minecraftforge.common.ForgeConfigSpec.Builder;
import net.minecraftforge.common.ForgeConfigSpec.ConfigValue;
import net.minecraftforge.common.ForgeConfigSpec.DoubleValue;

public class NamingConfig {
   public final DoubleValue lowHPToName;
   public final DoubleValue maximumEPToName;
   public final DoubleValue maxEPGain;
   public final DoubleValue maxCost;
   public final ConfigValue<List<? extends String>> names;

   public NamingConfig(Builder builder) {
      builder.push("namingRequirement");
      this.lowHPToName = builder.comment("The percentage of max HP for the target to submit to the namer").defineInRange("hpToName", 25.0D, 0.0D, 100.0D);
      this.maximumEPToName = builder.comment("The percentage of EP of the namer for the target to submit to the namer").defineInRange("epToName", 1.0D, 0.0D, 100.0D);
      builder.pop();
      builder.push("namingCost");
      this.maxEPGain = builder.comment("The maximum amount of EP that an entity can gain from being named").defineInRange("maxEPFromNaming", 1000000.0D, 0.0D, 1.0E9D);
      this.maxCost = builder.comment("The maximum amount of Magicule that the namer can lose from naming a target").defineInRange("maxMagiculeCost", 3000000.0D, 0.0D, 1.0E9D);
      builder.pop();
      builder.push("names");
      this.names = builder.comment("Random names for the Naming menu").defineList("names", Arrays.asList("Viciel", "MinhEragon", "Borniuus", "Nieadni", "R.E", "Arthur", "ProfessorAkaChris", "Subordinate", "Memoires", "Kenji", "Mac", "AxelDude", "Geld", "Benimaru", "Shion", "Shuna", "Rimuru", "Veldora", "Hakurou", "Milim", "Gabiru", "Souei", "Ranga", "Gobta", "Diablo", "Rigurd", "Beretta", "Clayman", "Leon", "Ifrit", "Mjurran", "Carrion"), (check) -> {
         return true;
      });
      builder.pop();
   }
}
