package com.github.manasmods.tensura.effect.template;

import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import net.minecraft.ChatFormatting;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.Style;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import org.jetbrains.annotations.Nullable;

public interface Transformation {
   default boolean failedToActivate(LivingEntity entity, @Nullable MobEffect effect) {
      MobEffectInstance interference = entity.m_21124_((MobEffect)TensuraMobEffects.MAGIC_INTERFERENCE.get());
      if (interference != null && interference.m_19564_() >= 1) {
         if (entity instanceof Player) {
            Player player = (Player)entity;
            player.m_5661_(Component.m_237115_("tensura.skill.magic_interference").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), true);
         }

         if (effect != null && entity.m_21023_(effect)) {
            entity.m_21195_(effect);
         }

         return true;
      } else {
         return false;
      }
   }

   default void applyDebuff(LivingEntity entity) {
      entity.m_7292_(new MobEffectInstance(MobEffects.f_19613_, 12000, 1, false, false));
      entity.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.FRAGILITY.get(), 12000, 1, false, false));
      entity.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.PARALYSIS.get(), 12000, 1, false, false));
   }
}
