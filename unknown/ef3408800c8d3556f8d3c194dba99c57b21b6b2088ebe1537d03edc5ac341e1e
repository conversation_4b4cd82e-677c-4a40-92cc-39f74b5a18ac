package com.github.manasmods.tensura.item.armor;

import com.github.manasmods.tensura.item.TensuraArmourMaterials;
import com.github.manasmods.tensura.item.TensuraCreativeTab;
import net.minecraft.world.entity.EquipmentSlot;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Item.Properties;
import software.bernie.geckolib3.core.IAnimatable;
import software.bernie.geckolib3.core.manager.AnimationData;
import software.bernie.geckolib3.core.manager.AnimationFactory;
import software.bernie.geckolib3.item.GeoArmorItem;
import software.bernie.geckolib3.util.GeckoLibUtil;

public class OrichalcumArmorItem extends GeoArmorItem implements IAnimatable {
   private final AnimationFactory factory = GeckoLibUtil.createFactory(this);

   public OrichalcumArmorItem(EquipmentSlot equipmentSlot) {
      super(TensuraArmourMaterials.ORICHALCUM, equipmentSlot, (new Properties()).m_41491_(TensuraCreativeTab.ARMOUR).m_41486_());
   }

   public void registerControllers(AnimationData data) {
   }

   public AnimationFactory getFactory() {
      return this.factory;
   }

   public boolean makesPiglinsNeutral(ItemStack stack, LivingEntity wearer) {
      return true;
   }
}
