{"parent": "block/block", "ambientocclusion": false, "render_type": "cutout", "elements": [{"name": "Glass shell", "from": [0, 0, 0], "to": [16, 16, 16], "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#casing"}, "east": {"uv": [0, 0, 16, 16], "texture": "#casing"}, "south": {"uv": [0, 0, 16, 16], "texture": "#casing"}, "west": {"uv": [0, 0, 16, 16], "texture": "#casing"}, "up": {"uv": [0, 0, 16, 16], "texture": "#casing"}, "down": {"uv": [0, 0, 16, 16], "texture": "#casing"}}}, {"name": "Inner beacon texture", "from": [3, 3, 3], "to": [13, 13, 13], "faces": {"north": {"uv": [3, 3, 13, 13], "texture": "#lampcore"}, "east": {"uv": [3, 3, 13, 13], "texture": "#lampcore"}, "south": {"uv": [3, 3, 13, 13], "texture": "#lampcore"}, "west": {"uv": [3, 3, 13, 13], "texture": "#lampcore"}, "up": {"uv": [3, 3, 13, 13], "texture": "#lampcore"}, "down": {"uv": [3, 3, 13, 13], "texture": "#lampcore"}}}, {"from": [13, 1, 1], "to": [15, 3, 15], "rotation": {"angle": 0, "axis": "y", "origin": [21, 10, 10]}, "faces": {"north": {"uv": [1, 13, 3, 15], "texture": "#metalplate"}, "east": {"uv": [1, 13, 15, 15], "texture": "#metalplate"}, "south": {"uv": [13, 13, 15, 15], "texture": "#metalplate"}, "west": {"uv": [1, 12, 15, 14], "texture": "#metalplate"}, "up": {"uv": [12, 1, 14, 15], "texture": "#metalplate"}, "down": {"uv": [13, 1, 15, 15], "texture": "#metalplate"}}}, {"from": [1, 1, 1], "to": [3, 3, 15], "rotation": {"angle": 0, "axis": "y", "origin": [-5, 10, 6]}, "faces": {"north": {"uv": [13, 13, 15, 15], "texture": "#metalplate"}, "east": {"uv": [1, 12, 15, 14], "texture": "#metalplate"}, "south": {"uv": [1, 13, 3, 15], "texture": "#metalplate"}, "west": {"uv": [1, 13, 15, 15], "texture": "#metalplate"}, "up": {"uv": [12, 1, 14, 15], "rotation": 180, "texture": "#metalplate"}, "down": {"uv": [13, 1, 15, 15], "rotation": 180, "texture": "#metalplate"}}}, {"from": [1, 13, 1], "to": [3, 15, 15], "rotation": {"angle": 0, "axis": "x", "origin": [-5, 6, 10]}, "faces": {"north": {"uv": [1, 13, 3, 15], "rotation": 180, "texture": "#metalplate"}, "east": {"uv": [1, 12, 15, 14], "rotation": 180, "texture": "#metalplate"}, "south": {"uv": [13, 13, 15, 15], "rotation": 180, "texture": "#metalplate"}, "west": {"uv": [1, 13, 15, 15], "rotation": 180, "texture": "#metalplate"}, "up": {"uv": [13, 1, 15, 15], "rotation": 180, "texture": "#metalplate"}, "down": {"uv": [12, 1, 14, 15], "rotation": 180, "texture": "#metalplate"}}}, {"from": [13, 13, 1], "to": [15, 15, 15], "rotation": {"angle": 0, "axis": "y", "origin": [21, 6, 6]}, "faces": {"north": {"uv": [13, 13, 15, 15], "rotation": 180, "texture": "#metalplate"}, "east": {"uv": [1, 13, 15, 15], "rotation": 180, "texture": "#metalplate"}, "south": {"uv": [1, 13, 3, 15], "rotation": 180, "texture": "#metalplate"}, "west": {"uv": [1, 12, 15, 14], "rotation": 180, "texture": "#metalplate"}, "up": {"uv": [13, 1, 15, 15], "texture": "#metalplate"}, "down": {"uv": [12, 1, 14, 15], "texture": "#metalplate"}}}, {"from": [3, 13, 1], "to": [13, 15, 3], "rotation": {"angle": 0, "axis": "y", "origin": [6, 6, -5]}, "faces": {"north": {"uv": [3, 13, 13, 15], "rotation": 180, "texture": "#metalplate"}, "east": {"uv": [3, 11, 5, 13], "rotation": 180, "texture": "#metalplate"}, "south": {"uv": [3, 12, 13, 14], "rotation": 180, "texture": "#metalplate"}, "west": {"uv": [11, 11, 13, 13], "rotation": 180, "texture": "#metalplate"}, "up": {"uv": [13, 3, 15, 13], "rotation": 270, "texture": "#metalplate"}, "down": {"uv": [12, 3, 14, 13], "rotation": 90, "texture": "#metalplate"}}}, {"from": [3, 13, 13], "to": [13, 15, 15], "rotation": {"angle": 0, "axis": "x", "origin": [6, 21, 6]}, "faces": {"north": {"uv": [12, 3, 14, 13], "rotation": 270, "texture": "#metalplate"}, "east": {"uv": [3, 11, 5, 13], "rotation": 90, "texture": "#metalplate"}, "south": {"uv": [13, 3, 15, 13], "rotation": 270, "texture": "#metalplate"}, "west": {"uv": [11, 11, 13, 13], "rotation": 270, "texture": "#metalplate"}, "up": {"uv": [3, 13, 13, 15], "texture": "#metalplate"}, "down": {"uv": [3, 12, 13, 14], "rotation": 180, "texture": "#metalplate"}}}, {"from": [3, 1, 13], "to": [13, 3, 15], "rotation": {"angle": 0, "axis": "x", "origin": [6, 10, 21]}, "faces": {"north": {"uv": [3, 12, 13, 14], "texture": "#metalplate"}, "east": {"uv": [3, 11, 5, 13], "texture": "#metalplate"}, "south": {"uv": [3, 13, 13, 15], "texture": "#metalplate"}, "west": {"uv": [11, 11, 13, 13], "texture": "#metalplate"}, "up": {"uv": [12, 3, 14, 13], "rotation": 90, "texture": "#metalplate"}, "down": {"uv": [13, 3, 15, 13], "rotation": 270, "texture": "#metalplate"}}}, {"from": [3, 1, 1], "to": [13, 3, 3], "rotation": {"angle": 0, "axis": "x", "origin": [6, -5, 10]}, "faces": {"north": {"uv": [13, 3, 15, 13], "rotation": 90, "texture": "#metalplate"}, "east": {"uv": [3, 11, 5, 13], "rotation": 270, "texture": "#metalplate"}, "south": {"uv": [12, 3, 14, 13], "rotation": 90, "texture": "#metalplate"}, "west": {"uv": [11, 11, 13, 13], "rotation": 90, "texture": "#metalplate"}, "up": {"uv": [3, 12, 13, 14], "rotation": 180, "texture": "#metalplate"}, "down": {"uv": [3, 13, 13, 15], "texture": "#metalplate"}}}, {"from": [13, 3, 1], "to": [15, 13, 3], "rotation": {"angle": 0, "axis": "z", "origin": [21, 6, 10]}, "faces": {"north": {"uv": [13, 3, 15, 13], "rotation": 180, "texture": "#metalplate"}, "east": {"uv": [3, 13, 13, 15], "rotation": 270, "texture": "#metalplate"}, "south": {"uv": [12, 3, 14, 13], "texture": "#metalplate"}, "west": {"uv": [3, 12, 13, 14], "rotation": 90, "texture": "#metalplate"}, "up": {"uv": [3, 11, 5, 13], "rotation": 180, "texture": "#metalplate"}, "down": {"uv": [11, 11, 13, 13], "texture": "#metalplate"}}}, {"from": [1, 3, 1], "to": [3, 13, 3], "rotation": {"angle": 0, "axis": "y", "origin": [10, 6, -5]}, "faces": {"north": {"uv": [3, 13, 13, 15], "rotation": 270, "texture": "#metalplate"}, "east": {"uv": [12, 3, 14, 13], "texture": "#metalplate"}, "south": {"uv": [3, 12, 13, 14], "rotation": 90, "texture": "#metalplate"}, "west": {"uv": [13, 3, 15, 13], "rotation": 180, "texture": "#metalplate"}, "up": {"uv": [3, 11, 5, 13], "rotation": 90, "texture": "#metalplate"}, "down": {"uv": [11, 11, 13, 13], "rotation": 90, "texture": "#metalplate"}}}, {"from": [1, 3, 13], "to": [3, 13, 15], "rotation": {"angle": 0, "axis": "y", "origin": [-5, 6, 6]}, "faces": {"north": {"uv": [12, 3, 14, 13], "texture": "#metalplate"}, "east": {"uv": [3, 12, 13, 14], "rotation": 90, "texture": "#metalplate"}, "south": {"uv": [13, 3, 15, 13], "rotation": 180, "texture": "#metalplate"}, "west": {"uv": [3, 13, 13, 15], "rotation": 270, "texture": "#metalplate"}, "up": {"uv": [3, 11, 5, 13], "texture": "#metalplate"}, "down": {"uv": [11, 11, 13, 13], "rotation": 180, "texture": "#metalplate"}}}, {"from": [13, 3, 13], "to": [15, 13, 15], "rotation": {"angle": 0, "axis": "y", "origin": [6, 6, 21]}, "faces": {"north": {"uv": [3, 12, 13, 14], "rotation": 90, "texture": "#metalplate"}, "east": {"uv": [13, 3, 15, 13], "rotation": 180, "texture": "#metalplate"}, "south": {"uv": [3, 13, 13, 15], "rotation": 270, "texture": "#metalplate"}, "west": {"uv": [12, 3, 14, 13], "texture": "#metalplate"}, "up": {"uv": [3, 11, 5, 13], "rotation": 270, "texture": "#metalplate"}, "down": {"uv": [11, 11, 13, 13], "rotation": 270, "texture": "#metalplate"}}}], "groups": [{"name": "beacon", "origin": [8, 8, 8], "children": [0, 1]}, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13]}