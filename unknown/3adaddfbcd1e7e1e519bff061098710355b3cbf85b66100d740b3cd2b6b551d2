package com.github.manasmods.tensura.item.client;

import com.github.manasmods.tensura.item.custom.OrcDisasterHeadItem;
import net.minecraft.resources.ResourceLocation;
import software.bernie.geckolib3.model.AnimatedGeoModel;

public class OrcDisasterHeadItemModel extends AnimatedGeoModel<OrcDisasterHeadItem> {
   public ResourceLocation getModelResource(OrcDisasterHeadItem object) {
      return new ResourceLocation("tensura", "geo/blocks/orc_disaster_head.geo.json");
   }

   public ResourceLocation getTextureResource(OrcDisasterHeadItem object) {
      return new ResourceLocation("tensura", "textures/entity/orc/orc_disaster.png");
   }

   public ResourceLocation getAnimationResource(OrcDisasterHeadItem animatable) {
      return null;
   }
}
