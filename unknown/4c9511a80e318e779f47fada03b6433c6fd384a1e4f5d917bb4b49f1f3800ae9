package com.github.manasmods.tensura.effect.skill.debuff;

import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.capability.effects.TensuraEffectsCapability;
import com.github.manasmods.tensura.effect.template.TensuraMobEffect;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import java.util.Collections;
import java.util.List;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectCategory;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.attributes.AttributeModifier.Operation;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.ItemStack;

public class OppressionEffect extends TensuraMobEffect {
   protected static final String OPPRESSION = "58ee6ef8-547b-11ee-8c99-0242ac120002";

   public OppressionEffect(MobEffectCategory pCategory, int pColor) {
      super(pCategory, pColor);
      this.m_19472_(Attributes.f_22279_, "58ee6ef8-547b-11ee-8c99-0242ac120002", -0.949999988079071D, Operation.MULTIPLY_TOTAL);
   }

   public void m_6742_(LivingEntity pLivingEntity, int pAmplifier) {
      int level = pAmplifier;
      MobEffectInstance instance = pLivingEntity.m_21124_((MobEffect)TensuraMobEffects.INSANITY.get());
      if (instance != null) {
         level = instance.m_19564_() + 1;
      }

      Player source = TensuraEffectsCapability.getEffectSource(pLivingEntity, this);
      if (source == null) {
         pLivingEntity.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.INSANITY.get(), 220, level, false, false, false));
      } else {
         SkillHelper.addEffectWithSource(pLivingEntity, source, (MobEffect)TensuraMobEffects.INSANITY.get(), 220, level, true);
      }

   }

   public boolean m_6584_(int pDuration, int pAmplifier) {
      return pDuration % 200 == 0;
   }

   public List<ItemStack> getCurativeItems() {
      return Collections.emptyList();
   }
}
