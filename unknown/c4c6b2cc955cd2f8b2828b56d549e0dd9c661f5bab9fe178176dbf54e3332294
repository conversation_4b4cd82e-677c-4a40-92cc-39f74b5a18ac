package com.github.manasmods.tensura.effect.skill;

import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.effect.template.SkillMobEffect;
import com.github.manasmods.tensura.effect.template.Transformation;
import com.github.manasmods.tensura.registry.attribute.TensuraAttributeRegistry;
import com.github.manasmods.tensura.registry.particle.TensuraParticles;
import java.util.UUID;
import net.minecraft.core.particles.ParticleOptions;
import net.minecraft.world.effect.MobEffectCategory;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.ai.attributes.AttributeMap;
import net.minecraft.world.entity.ai.attributes.AttributeModifier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.attributes.AttributeModifier.Operation;
import net.minecraft.world.entity.player.Player;

public class BeastTransformationEffect extends SkillMobEffect implements Transformation {
   protected static final String BEAST = "5e6fe67e-4a06-11ee-be56-0242ac120002";
   protected static final String BEAST_ENERGY = "0a92e00e-79ec-11ee-b962-0242ac120002";

   public BeastTransformationEffect(MobEffectCategory pCategory, int pColor) {
      super(pCategory, pColor);
      this.m_19472_(Attributes.f_22281_, "5e6fe67e-4a06-11ee-be56-0242ac120002", 15.0D, Operation.ADDITION);
      this.m_19472_(Attributes.f_22284_, "5e6fe67e-4a06-11ee-be56-0242ac120002", 10.0D, Operation.ADDITION);
      this.m_19472_(Attributes.f_22279_, "5e6fe67e-4a06-11ee-be56-0242ac120002", 0.03999999910593033D, Operation.ADDITION);
      this.m_19472_((Attribute)TensuraAttributeRegistry.MAX_MAGICULE.get(), "0a92e00e-79ec-11ee-b962-0242ac120002", 1.0D, Operation.MULTIPLY_TOTAL);
      this.m_19472_((Attribute)TensuraAttributeRegistry.MAX_AURA.get(), "0a92e00e-79ec-11ee-b962-0242ac120002", 1.0D, Operation.MULTIPLY_TOTAL);
   }

   public void m_6385_(LivingEntity pLivingEntity, AttributeMap pAttributeMap, int pAmplifier) {
      super.m_6385_(pLivingEntity, pAttributeMap, pAmplifier);
      if (pLivingEntity instanceof Player) {
         Player player = (Player)pLivingEntity;
         TensuraPlayerCapability.getFrom(player).ifPresent((cap) -> {
            double maxMagicule = player.m_21133_((Attribute)TensuraAttributeRegistry.MAX_MAGICULE.get());
            cap.setMagicule(maxMagicule);
            double maxAura = player.m_21133_((Attribute)TensuraAttributeRegistry.MAX_AURA.get());
            cap.setAura(maxAura);
         });
         TensuraPlayerCapability.sync(player);
         TensuraEPCapability.updateEP(player);
         if (pAmplifier >= 1) {
            if (player.m_7500_() || player.m_5833_()) {
               return;
            }

            if (player.m_150110_().f_35936_) {
               return;
            }

            player.m_150110_().f_35936_ = true;
            player.m_150110_().f_35935_ = true;
            player.m_6885_();
         }
      }

   }

   public void m_6742_(LivingEntity entity, int pAmplifier) {
      if (!this.failedToActivate(entity, this)) {
         ParticleOptions particle = pAmplifier >= 1 ? (ParticleOptions)TensuraParticles.YELLOW_LIGHTNING_SPARK.get() : (ParticleOptions)TensuraParticles.LIGHTNING_SPARK.get();
         TensuraParticleHelper.addParticlesAroundSelf(entity, particle);
      }
   }

   public void m_6386_(LivingEntity entity, AttributeMap pAttributeMap, int pAmplifier) {
      Player player;
      if (entity instanceof Player) {
         player = (Player)entity;
         TensuraPlayerCapability.getFrom(player).ifPresent((cap) -> {
            cap.setMagicule(cap.getMagicule() / 2.0D);
            cap.setAura(cap.getAura() / 2.0D);
            TensuraPlayerCapability.sync(player);
         });
      }

      super.m_6386_(entity, pAttributeMap, pAmplifier);
      TensuraEPCapability.updateEP(entity);
      this.applyDebuff(entity);
      if (pAmplifier >= 1 && entity instanceof Player) {
         player = (Player)entity;
         if (player.m_7500_() || player.m_5833_()) {
            return;
         }

         if (!player.m_150110_().f_35936_) {
            return;
         }

         player.m_150110_().f_35936_ = false;
         player.m_150110_().f_35935_ = false;
         player.m_6885_();
      }

   }

   public boolean m_6584_(int pDuration, int amplifier) {
      return pDuration % 10 == 0;
   }

   public double m_7048_(int pAmplifier, AttributeModifier pModifier) {
      return pModifier.m_22209_().equals(UUID.fromString("0a92e00e-79ec-11ee-b962-0242ac120002")) ? pModifier.m_22218_() : pModifier.m_22218_() * (double)(pAmplifier + 1);
   }
}
