package com.github.manasmods.tensura.entity.magic.misc;

import com.github.manasmods.tensura.capability.effects.TensuraEffectsCapability;
import com.github.manasmods.tensura.effect.template.MobEffectHelper;
import com.github.manasmods.tensura.event.ForcedTeleportationEvent;
import com.github.manasmods.tensura.network.TensuraNetwork;
import com.github.manasmods.tensura.network.play2client.RequestFxSpawningPacket;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import java.util.Iterator;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import javax.annotation.Nullable;
import net.minecraft.ChatFormatting;
import net.minecraft.core.BlockPos;
import net.minecraft.core.Direction;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.Style;
import net.minecraft.network.protocol.Packet;
import net.minecraft.network.protocol.game.ClientGamePacketListener;
import net.minecraft.network.syncher.EntityDataAccessor;
import net.minecraft.network.syncher.EntityDataSerializers;
import net.minecraft.network.syncher.SynchedEntityData;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.players.OldUsersConverter;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.Entity.RemovalReason;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.entity.vehicle.Minecart;
import net.minecraft.world.level.Level;
import net.minecraft.world.phys.AABB;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.network.NetworkHooks;
import net.minecraftforge.network.PacketDistributor;
import net.minecraftforge.network.PlayMessages.SpawnEntity;

public class WarpPortalEntity extends Entity {
   protected static final EntityDataAccessor<Integer> LIFE;
   protected static final EntityDataAccessor<Boolean> INSTANT;
   protected static final EntityDataAccessor<Direction> DIRECTION;
   private static final EntityDataAccessor<Optional<BlockPos>> DESTINATION;
   private static final EntityDataAccessor<Optional<UUID>> DESTINATION_PORTAL;
   protected static final EntityDataAccessor<Optional<UUID>> DATA_OWNER_UUID_ID;

   public WarpPortalEntity(EntityType<?> entityTypeIn, Level worldIn) {
      super(entityTypeIn, worldIn);
      this.setLife(400);
   }

   public WarpPortalEntity(SpawnEntity spawnEntity, Level world) {
      this((EntityType)TensuraEntityTypes.WARP_PORTAL.get(), world);
   }

   public WarpPortalEntity(Level world) {
      this((EntityType)TensuraEntityTypes.WARP_PORTAL.get(), world);
   }

   public Packet<ClientGamePacketListener> m_5654_() {
      return NetworkHooks.getEntitySpawningPacket(this);
   }

   protected void m_8097_() {
      this.f_19804_.m_135372_(LIFE, 400);
      this.f_19804_.m_135372_(INSTANT, false);
      this.f_19804_.m_135372_(DIRECTION, Direction.DOWN);
      this.f_19804_.m_135372_(DESTINATION, Optional.empty());
      this.f_19804_.m_135372_(DESTINATION_PORTAL, Optional.empty());
      this.f_19804_.m_135372_(DATA_OWNER_UUID_ID, Optional.empty());
   }

   protected void m_7378_(CompoundTag compound) {
      this.setLife(compound.m_128451_("Life"));
      this.setInstant(compound.m_128471_("Instant"));
      this.f_19804_.m_135381_(DIRECTION, Direction.m_122376_(compound.m_128445_("Direction")));
      if (compound.m_128441_("DestinationX")) {
         int i = compound.m_128451_("DestinationX");
         int j = compound.m_128451_("DestinationY");
         int k = compound.m_128451_("DestinationZ");
         this.f_19804_.m_135381_(DESTINATION, Optional.of(new BlockPos(i, j, k)));
      } else {
         this.f_19804_.m_135381_(DESTINATION, Optional.empty());
      }

      if (compound.m_128403_("DestinationPortal")) {
         this.setDestinationPortalId(compound.m_128342_("DestinationPortal"));
      }

      if (this.getOwnerUUID() != null) {
         compound.m_128362_("Owner", this.getOwnerUUID());
      }

   }

   protected void m_7380_(CompoundTag compound) {
      compound.m_128405_("Life", (Integer)this.f_19804_.m_135370_(LIFE));
      compound.m_128379_("Instant", (Boolean)this.f_19804_.m_135370_(INSTANT));
      compound.m_128344_("Direction", (byte)((Direction)this.f_19804_.m_135370_(DIRECTION)).m_122411_());
      if (this.getDestinationPortalId() != null) {
         compound.m_128362_("DestinationPortal", this.getDestinationPortalId());
      }

      BlockPos blockpos = this.getDestination();
      if (blockpos != null) {
         compound.m_128405_("DestinationX", blockpos.m_123341_());
         compound.m_128405_("DestinationY", blockpos.m_123342_());
         compound.m_128405_("DestinationZ", blockpos.m_123343_());
      }

      UUID uuid = null;
      if (compound.m_128403_("Owner")) {
         uuid = compound.m_128342_("Owner");
      } else if (this.m_20194_() != null) {
         uuid = OldUsersConverter.m_11083_(this.m_20194_(), compound.m_128461_("Owner"));
      }

      if (uuid != null) {
         this.setOwnerUUID(uuid);
      }

   }

   public int getLife() {
      return (Integer)this.f_19804_.m_135370_(LIFE);
   }

   public void setLife(int i) {
      this.f_19804_.m_135381_(LIFE, i);
   }

   public boolean isInstant() {
      return (Boolean)this.f_19804_.m_135370_(INSTANT);
   }

   public void setInstant(boolean instant) {
      this.f_19804_.m_135381_(INSTANT, instant);
   }

   public Direction getFacingDirection() {
      return (Direction)this.f_19804_.m_135370_(DIRECTION);
   }

   public void setFacingDirection(Direction facing) {
      this.f_19804_.m_135381_(DIRECTION, facing);
   }

   public BlockPos getDestination() {
      return (BlockPos)((Optional)this.f_19804_.m_135370_(DESTINATION)).orElse((Object)null);
   }

   public void setDestination(BlockPos destination) {
      this.f_19804_.m_135381_(DESTINATION, Optional.ofNullable(destination));
      if (this.getDestinationPortalId() == null) {
         this.createDestinationPortal(this.f_19853_);
      }

   }

   public WarpPortalEntity getDestinationPortal() {
      UUID id = this.getDestinationPortalId();
      if (id != null) {
         Level var3 = this.f_19853_;
         if (var3 instanceof ServerLevel) {
            ServerLevel serverLevel = (ServerLevel)var3;
            return (WarpPortalEntity)serverLevel.m_8791_(id);
         }
      }

      return null;
   }

   @Nullable
   public UUID getDestinationPortalId() {
      return (UUID)((Optional)this.f_19804_.m_135370_(DESTINATION_PORTAL)).orElse((Object)null);
   }

   public void setDestinationPortalId(@Nullable UUID uniqueId) {
      this.f_19804_.m_135381_(DESTINATION_PORTAL, Optional.ofNullable(uniqueId));
   }

   @Nullable
   public UUID getOwnerUUID() {
      return (UUID)((Optional)this.f_19804_.m_135370_(DATA_OWNER_UUID_ID)).orElse((Object)null);
   }

   public void setOwnerUUID(@Nullable UUID pUuid) {
      this.f_19804_.m_135381_(DATA_OWNER_UUID_ID, Optional.ofNullable(pUuid));
   }

   @Nullable
   public LivingEntity getOwner() {
      try {
         UUID uuid = this.getOwnerUUID();
         return uuid == null ? null : this.f_19853_.m_46003_(uuid);
      } catch (IllegalArgumentException var2) {
         return null;
      }
   }

   public void createDestinationPortal(Level world) {
      WarpPortalEntity portal = new WarpPortalEntity(this.f_19853_);
      portal.setOwnerUUID(this.getOwnerUUID());
      portal.setFacingDirection(this.getFacingDirection().m_122424_());
      BlockPos safeDestination = this.getDestination();
      portal.m_20324_((double)((float)safeDestination.m_123341_() + 0.5F), (double)((float)safeDestination.m_123342_() + 0.5F), (double)((float)safeDestination.m_123343_() + 0.5F));
      portal.linkPortals(this);
      world.m_7967_(portal);
   }

   public void linkPortals(WarpPortalEntity portal) {
      this.setDestinationPortalId(portal.m_20148_());
      portal.setDestinationPortalId(this.m_20148_());
      this.setLife(portal.getLife());
      this.setInstant(portal.isInstant());
      this.setDestination(portal.m_20183_());
      portal.setDestination(this.m_20183_());
   }

   public void m_8119_() {
      super.m_8119_();
      this.directionHandler();
      if (this.f_19796_.m_188503_(100) == 0) {
         this.f_19853_.m_7785_(this.m_20185_() + 0.5D, this.m_20186_() + 0.5D, this.m_20189_() + 0.5D, SoundEvents.f_12286_, SoundSource.BLOCKS, 0.5F, this.f_19796_.m_188501_() * 0.4F + 0.8F, false);
      }

      this.setLife(this.getLife() - 1);
      if (!this.f_19853_.f_46443_ && this.getDestination() != null && this.f_19797_ > 20) {
         List<Entity> list = this.m_9236_().m_6443_(Entity.class, this.m_20191_().m_82400_(0.20000000298023224D), (entityx) -> {
            return !entityx.m_7306_(this) && entityx.m_6084_();
         });
         Iterator var2 = list.iterator();

         label57:
         while(true) {
            Entity entity;
            do {
               do {
                  if (!var2.hasNext()) {
                     break label57;
                  }

                  entity = (Entity)var2.next();
               } while(entity.m_6144_());
            } while(entity.m_20092_());

            BlockPos warpPos;
            if (this.getFacingDirection() != Direction.UP && this.getFacingDirection() != Direction.DOWN) {
               warpPos = this.getDestination().m_5484_(entity.m_6374_(), 2 + (int)entity.m_20205_());
            } else {
               int distance = 2 + (int)entity.m_20206_();
               if (entity instanceof Player && entity.m_146909_() <= -20.0F) {
                  distance *= -1;
               }

               warpPos = this.getDestination().m_6625_(distance);
            }

            if (entity instanceof LivingEntity) {
               LivingEntity living = (LivingEntity)entity;
               if (MobEffectHelper.noTeleportation(living)) {
                  if (entity instanceof Player) {
                     Player player = (Player)entity;
                     player.m_5661_(Component.m_237115_("tensura.skill.spatial_blockade").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), true);
                  }
               } else {
                  this.warpPassengers(living);
                  this.warpEntity(living, (double)((float)warpPos.m_123341_() + 0.5F), (double)((float)warpPos.m_123342_() + 0.5F), (double)((float)warpPos.m_123343_() + 0.5F));
               }
            } else {
               this.coolDownWarp(entity);
               this.warpPassengers(entity);
               entity.m_19877_();
               entity.m_20324_((double)((float)warpPos.m_123341_() + 0.5F), (double)((float)warpPos.m_123342_() + 0.5F), (double)((float)warpPos.m_123343_() + 0.5F));
            }
         }
      }

      if (this.getLife() <= 0) {
         this.m_142687_(RemovalReason.DISCARDED);
      }

   }

   private void warpEntity(LivingEntity living, double x, double y, double z) {
      ForcedTeleportationEvent event = new ForcedTeleportationEvent(living, this.getOwner(), x, y, z);
      if (!MinecraftForge.EVENT_BUS.post(event)) {
         if (this.isInstant()) {
            this.coolDownWarp(living);
            living.m_19877_();
            living.m_20324_(x, y, z);
         } else {
            TensuraEffectsCapability.getFrom(living).ifPresent((cap) -> {
               if (!living.m_20092_() && cap.getWarpPortalTime() <= 0) {
                  cap.setWarpPortalTime(100);
               } else {
                  cap.setWarpPortalTime(cap.getWarpPortalTime() - 1);
                  if (cap.getWarpPortalTime() == 0) {
                     this.coolDownWarp(living);
                     living.m_19877_();
                     living.m_20324_(x, y, z);
                  }
               }

            });
            TensuraEffectsCapability.sync(living);
         }

      }
   }

   private void warpPassengers(Entity entity) {
      if (entity.m_20160_() && !(entity instanceof Minecart)) {
         Iterator var2 = entity.m_20197_().iterator();

         while(var2.hasNext()) {
            Entity passenger = (Entity)var2.next();
            passenger.m_19877_();
            if (passenger instanceof LivingEntity) {
               LivingEntity livingPassenger = (LivingEntity)passenger;
               BlockPos warpPos = this.getDestination().m_5484_(this.getFacingDirection(), 2 + (int)passenger.m_20205_());
               this.warpEntity(livingPassenger, (double)((float)warpPos.m_123341_() + 0.5F), (double)((float)warpPos.m_123342_() + 0.5F), (double)((float)warpPos.m_123343_() + 0.5F));
            }
         }

      }
   }

   public void coolDownWarp(Entity entity) {
      entity.f_19839_ = 10;
      if (entity instanceof LivingEntity) {
         LivingEntity living = (LivingEntity)entity;
         TensuraEffectsCapability.getFrom(living).ifPresent((cap) -> {
            cap.setWarpPortalTime(-1);
         });
         TensuraEffectsCapability.sync(living);
      }

   }

   private void directionHandler() {
      Direction opposite = this.getFacingDirection().m_122424_();
      float minX = -0.15F;
      float minY = -0.15F;
      float minZ = -0.15F;
      float maxX = 0.15F;
      float maxY = 0.15F;
      float maxZ = 0.15F;
      switch(opposite) {
      case NORTH:
      case SOUTH:
         minX = -1.5F;
         maxX = 1.5F;
         minY = -1.5F;
         maxY = 1.5F;
         if (!this.f_19853_.m_5776_() && this.f_19797_ == 1) {
            TensuraNetwork.INSTANCE.send(PacketDistributor.TRACKING_ENTITY_AND_SELF.with(() -> {
               return this;
            }), new RequestFxSpawningPacket(new ResourceLocation("tensura:warp_portal_z"), this.m_19879_(), 0.0D, 0.0D, 0.0D, false));
         }
         break;
      case EAST:
      case WEST:
         minZ = -1.5F;
         maxZ = 1.5F;
         minY = -1.5F;
         maxY = 1.5F;
         if (!this.f_19853_.m_5776_() && this.f_19797_ == 1) {
            TensuraNetwork.INSTANCE.send(PacketDistributor.TRACKING_ENTITY_AND_SELF.with(() -> {
               return this;
            }), new RequestFxSpawningPacket(new ResourceLocation("tensura:warp_portal_x"), this.m_19879_(), 0.0D, 0.0D, 0.0D, false));
         }
         break;
      case UP:
      case DOWN:
         minX = -1.5F;
         maxX = 1.5F;
         minZ = -1.5F;
         maxZ = 1.5F;
         if (!this.f_19853_.m_5776_() && this.f_19797_ == 1) {
            TensuraNetwork.INSTANCE.send(PacketDistributor.TRACKING_ENTITY_AND_SELF.with(() -> {
               return this;
            }), new RequestFxSpawningPacket(new ResourceLocation("tensura:warp_portal_y"), this.m_19879_(), 0.0D, 0.0D, 0.0D, false));
         }
      }

      this.m_20011_(new AABB(this.m_20185_() + (double)minX, this.m_20186_() + (double)minY, this.m_20189_() + (double)minZ, this.m_20185_() + (double)maxX, this.m_20186_() + (double)maxY, this.m_20189_() + (double)maxZ));
   }

   static {
      LIFE = SynchedEntityData.m_135353_(WarpPortalEntity.class, EntityDataSerializers.f_135028_);
      INSTANT = SynchedEntityData.m_135353_(WarpPortalEntity.class, EntityDataSerializers.f_135035_);
      DIRECTION = SynchedEntityData.m_135353_(WarpPortalEntity.class, EntityDataSerializers.f_135040_);
      DESTINATION = SynchedEntityData.m_135353_(WarpPortalEntity.class, EntityDataSerializers.f_135039_);
      DESTINATION_PORTAL = SynchedEntityData.m_135353_(WarpPortalEntity.class, EntityDataSerializers.f_135041_);
      DATA_OWNER_UUID_ID = SynchedEntityData.m_135353_(WarpPortalEntity.class, EntityDataSerializers.f_135041_);
   }
}
