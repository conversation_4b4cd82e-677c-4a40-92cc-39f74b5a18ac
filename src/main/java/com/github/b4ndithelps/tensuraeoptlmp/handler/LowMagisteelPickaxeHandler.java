package com.github.b4ndithelps.tensuraeoptlmp.handler;

import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraftforge.event.entity.player.PlayerEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;
import net.minecraftforge.registries.ForgeRegistries;

/**
 * 处理LOW_MAGISTEEL镐子挖掘magic_ore的事件处理器
 * 通过监听HarvestCheck事件让LOW_MAGISTEEL镐子能够挖掘magic_ore
 */
@Mod.EventBusSubscriber(modid = "tensuraenigmatic", bus = Mod.EventBusSubscriber.Bus.FORGE)
public class LowMagisteelPickaxeHandler {

    /**
     * 监听挖掘检查事件，特殊处理LOW_MAGISTEEL镐子挖掘magic_ore的情况
     */
    @SubscribeEvent
    public static void onHarvestCheck(PlayerEvent.HarvestCheck event) {
        Player player = event.getEntity();
        if (player == null) {
            return;
        }

        // 如果已经可以挖掘，不需要处理
        if (event.canHarvest()) {
            return;
        }

        ItemStack heldItem = player.getMainHandItem();
        if (heldItem.isEmpty()) {
            return;
        }

        // 检查是否是LOW_MAGISTEEL镐子
        String itemName = ForgeRegistries.ITEMS.getKey(heldItem.getItem()).toString();
        if (!"tensura:low_magisteel_pickaxe".equals(itemName)) {
            return;
        }

        // 检查目标方块是否是magic_ore
        BlockState targetBlock = event.getTargetBlock();
        String blockName = ForgeRegistries.BLOCKS.getKey(targetBlock.getBlock()).toString();

        if ("tensura:magic_ore".equals(blockName) || "tensura:deepslate_magic_ore".equals(blockName)) {
            // 允许LOW_MAGISTEEL镐子挖掘magic_ore
            event.setCanHarvest(true);
        }
    }
}
