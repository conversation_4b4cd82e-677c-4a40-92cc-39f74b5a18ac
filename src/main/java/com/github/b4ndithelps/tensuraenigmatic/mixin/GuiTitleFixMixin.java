package com.github.b4ndithelps.tensuraenigmatic.mixin;

import com.mojang.blaze3d.vertex.PoseStack;
import net.minecraft.client.Minecraft;
import net.minecraft.client.gui.Gui;
import net.minecraft.network.chat.Component;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.Shadow;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;

/**
 * GUI Title Fix Mixin - 直接访问和清除GUI中的标题字段
 * GUI Title Fix Mixin - Directly access and clear title fields in GUI
 */
@Mixin(Gui.class)
public class GuiTitleFixMixin {

    @Shadow
    private Component title;

    @Shadow
    private Component subtitle;

    @Shadow
    private int titleFadeInTime;

    @Shadow
    private int titleStayTime;

    @Shadow
    private int titleFadeOutTime;

    @Shadow
    private int titleTime;

    // 标记是否应该清除标题
    private boolean shouldClearTitle = false;

    /**
     * 清除所有标题相关字段
     * Clear all title-related fields
     */
    public void clearAllTitles() {
        this.title = null;
        this.subtitle = null;
        this.titleFadeInTime = 0;
        this.titleStayTime = 0;
        this.titleFadeOutTime = 0;
        this.titleTime = 0;
        this.shouldClearTitle = true;
    }


}
