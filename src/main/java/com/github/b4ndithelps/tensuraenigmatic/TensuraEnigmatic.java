package com.github.b4ndithelps.tensuraenigmatic;

import com.github.b4ndithelps.tensuraenigmatic.handler.LowMagisteelPickaxeHandler;
import com.mojang.logging.LogUtils;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.eventbus.api.IEventBus;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;
import net.minecraftforge.fml.event.lifecycle.FMLCommonSetupEvent;
import net.minecraftforge.event.server.ServerStartingEvent;
import net.minecraftforge.fml.javafmlmod.FMLJavaModLoadingContext;
import org.slf4j.Logger;

// 这里的值应该与META-INF/mods.toml文件中的条目匹配
@Mod(TensuraEnigmatic.MODID)
public class TensuraEnigmatic {

    // 定义模组ID，供所有地方引用
    public static final String MODID = "tensuraenigmatic";
    // 直接引用slf4j日志记录器
    public static final Logger LOGGER = LogUtils.getLogger();

    public TensuraEnigmatic() {
        IEventBus modEventBus = FMLJavaModLoadingContext.get().getModEventBus();
        modEventBus.addListener(this::commonSetup);

        // 注册事件处理器 - 只保留镐子挖掘修复
        MinecraftForge.EVENT_BUS.register(LowMagisteelPickaxeHandler.class);
        MinecraftForge.EVENT_BUS.register(this);
        LOGGER.info("Tensura镐子挖掘修复模组加载成功！");
    }

    private void commonSetup(final FMLCommonSetupEvent event) {

    }

    // 你可以使用SubscribeEvent让事件总线发现要调用的方法
    @SubscribeEvent
    public void onServerStarting(ServerStartingEvent event) {

    }

    // 客户端设置已移除，因为只需要服务端的镐子挖掘修复功能
}
