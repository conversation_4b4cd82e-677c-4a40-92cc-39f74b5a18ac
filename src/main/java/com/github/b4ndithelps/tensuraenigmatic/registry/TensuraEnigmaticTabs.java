package com.github.b4ndithelps.tensuraenigmatic.registry;

import net.minecraft.world.item.CreativeModeTab;
import net.minecraft.world.item.ItemStack;

/**
 * 转生史莱姆神秘遗产模组的创造栏注册
 */
public class TensuraEnigmaticTabs {

    // 主创造栏
    public static final CreativeModeTab TENSURA_ENIGMATIC_TAB = new CreativeModeTab("tensuraenigmatic.main") {
        @Override
        public ItemStack makeIcon() {
            return new ItemStack(TensuraEnigmaticItems.RING_OF_ARCANE_SOURCE.get());
        }

        @Override
        public void fillItemList(net.minecraft.core.NonNullList<ItemStack> items) {
            // 添加魔源之戒
            items.add(new ItemStack(TensuraEnigmaticItems.RING_OF_ARCANE_SOURCE.get()));

            // 添加精灵之戒
            items.add(new ItemStack(TensuraEnigmaticItems.RING_OF_THE_ELVES.get()));

            // 添加魔晶之戒
            items.add(new ItemStack(TensuraEnigmaticItems.MANA_RING.get()));
        }
    };
}
