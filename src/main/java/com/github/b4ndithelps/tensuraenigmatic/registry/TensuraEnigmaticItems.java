package com.github.b4ndithelps.tensuraenigmatic.registry;

import com.github.b4ndithelps.tensuraenigmatic.items.ManaRing;
import com.github.b4ndithelps.tensuraenigmatic.items.RingOfArcaneSource;
import com.github.b4ndithelps.tensuraenigmatic.items.RingOfTheElves;
import net.minecraft.world.item.Item;
import net.minecraftforge.eventbus.api.IEventBus;
import net.minecraftforge.registries.DeferredRegister;
import net.minecraftforge.registries.ForgeRegistries;
import net.minecraftforge.registries.RegistryObject;

/**
 * 转生史莱姆神秘遗产模组的物品注册
 */
public class TensuraEnigmaticItems {
    
    public static final DeferredRegister<Item> ITEMS = 
        DeferredRegister.create(ForgeRegistries.ITEMS, "tensuraenigmatic");
    
    // 魔源之戒
    public static final RegistryObject<Item> RING_OF_ARCANE_SOURCE =
        ITEMS.register("ring_of_arcane_source", RingOfArcaneSource::new);

    // 精灵之戒
    public static final RegistryObject<Item> RING_OF_THE_ELVES =
        ITEMS.register("ring_of_the_elves", RingOfTheElves::new);

    // 魔晶之戒
    public static final RegistryObject<Item> MANA_RING =
        ITEMS.register("mana_ring", ManaRing::new);
    
    /**
     * 注册所有物品
     */
    public static void register(IEventBus eventBus) {
        ITEMS.register(eventBus);
    }
}
