package com.github.b4ndithelps.tensuraenigmatic.handler;

import net.minecraft.core.BlockPos;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraftforge.event.level.BlockEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;
import net.minecraftforge.registries.ForgeRegistries;

/**
 * 处理LOW_MAGISTEEL镐子挖掘magic_ore的事件处理器
 * 通过事件监听的方式让LOW_MAGISTEEL镐子能够挖掘magic_ore
 */
@Mod.EventBusSubscriber(modid = "tensuraenigmatic", bus = Mod.EventBusSubscriber.Bus.FORGE)
public class LowMagisteelPickaxeHandler {
    
    /**
     * 监听方块破坏事件，特殊处理LOW_MAGISTEEL镐子挖掘magic_ore的情况
     */
    @SubscribeEvent
    public static void onBlockBreak(BlockEvent.BreakEvent event) {
        Player player = event.getPlayer();
        if (player == null) {
            return;
        }
        
        ItemStack heldItem = player.getMainHandItem();
        if (heldItem.isEmpty()) {
            return;
        }
        
        // 检查是否是LOW_MAGISTEEL镐子
        String itemName = ForgeRegistries.ITEMS.getKey(heldItem.getItem()).toString();
        if (!"tensura:low_magisteel_pickaxe".equals(itemName)) {
            return;
        }
        
        // 检查破坏的方块是否是magic_ore
        BlockState blockState = event.getState();
        String blockName = ForgeRegistries.BLOCKS.getKey(blockState.getBlock()).toString();
        
        if ("tensura:magic_ore".equals(blockName) || "tensura:deepslate_magic_ore".equals(blockName)) {
            // 如果事件被取消了（说明工具等级不够），我们强制允许破坏
            if (event.isCanceled()) {
                event.setCanceled(false);
                
                // 手动处理工具耐久度损耗
                Level level = (Level) event.getLevel();
                BlockPos pos = event.getPos();
                
                // 损耗工具耐久度
                heldItem.hurtAndBreak(1, player, (p) -> {
                    p.broadcastBreakEvent(net.minecraft.world.entity.EquipmentSlot.MAINHAND);
                });
            }
        }
    }
}
