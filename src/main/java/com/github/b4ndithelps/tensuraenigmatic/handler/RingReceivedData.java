package com.github.b4ndithelps.tensuraenigmatic.handler;

import net.minecraft.nbt.CompoundTag;
import net.minecraft.nbt.ListTag;
import net.minecraft.nbt.StringTag;
import net.minecraft.world.level.saveddata.SavedData;

import java.util.HashSet;
import java.util.Set;

/**
 * 保存已经获得精灵之戒的玩家UUID列表
 * 防止玩家重复获得精灵之戒
 */
public class RingReceivedData extends SavedData {
    
    private static final String PLAYERS_KEY = "players_received_ring";
    private final Set<String> playersReceivedRing = new HashSet<>();
    
    public RingReceivedData() {
        super();
    }
    
    public RingReceivedData(CompoundTag tag) {
        super();
        load(tag);
    }
    
    /**
     * 从NBT加载数据
     */
    private void load(CompoundTag tag) {
        playersReceivedRing.clear();
        
        if (tag.contains(PLAYERS_KEY)) {
            ListTag playersList = tag.getList(PLAYERS_KEY, 8); // 8 = StringTag
            for (int i = 0; i < playersList.size(); i++) {
                playersReceivedRing.add(playersList.getString(i));
            }
        }
    }
    
    /**
     * 保存数据到NBT
     */
    @Override
    public CompoundTag save(CompoundTag tag) {
        ListTag playersList = new ListTag();
        for (String playerUUID : playersReceivedRing) {
            playersList.add(StringTag.valueOf(playerUUID));
        }
        tag.put(PLAYERS_KEY, playersList);
        return tag;
    }
    
    /**
     * 添加已获得戒指的玩家
     */
    public void addPlayer(String playerUUID) {
        if (playersReceivedRing.add(playerUUID)) {
            setDirty(); // 标记数据已修改
        }
    }
    
    /**
     * 检查玩家是否已经获得过戒指
     */
    public boolean hasPlayerReceived(String playerUUID) {
        return playersReceivedRing.contains(playerUUID);
    }
    
    /**
     * 移除玩家记录（用于调试或重置）
     */
    public void removePlayer(String playerUUID) {
        if (playersReceivedRing.remove(playerUUID)) {
            setDirty(); // 标记数据已修改
        }
    }
    
    /**
     * 获取所有已获得戒指的玩家数量
     */
    public int getPlayerCount() {
        return playersReceivedRing.size();
    }
}
