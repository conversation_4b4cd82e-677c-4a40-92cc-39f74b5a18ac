# Enigmatic Legacy License, II Edition

**Issue Date:** 08.11.2022

**Issuer:** [<PERSON><PERSON><PERSON><PERSON>](https://aizistral.com)

Everyone is permitted to copy and distribute verbatim copies of this license document, but changing it is not allowed.


## I. Terminology

**1. "Works"** - any intellectual property that is distributed under the terms of this license;

**2. "Creator"** - the copyright holder and owner of abovementioned property;

**3. "Fork"** - a derivative work that is based on any version of Enigmatic Legacy, covered by current license or not, and contains substantial part of its code, assets or binaries.


## II. Extension

**1.** This license covers any code, assets and binaries that are a part of Enigmatic Legacy, officially published by the Creator at the time and after this license was made effective, including but not limited to: contents of repositories that may contain such code, assets and binaries in publicly available form. Every part of Enigmatic Legacy that was published, available and obtained before Enigmatic Legacy was covered by this exact license remains covered by whichever license was effective at the moment;

**2.** The Creator reserves all rights in regards to their works that are not explicitly mentioned and defined otherwise by this license.


## III. Warranties

Works of the Creator are provided "as is", in hope they will be useful, but without ANY warranties. The Creator takes no responsibility for any damage that may be suffered from any attempts, consequences of attempts or anything connected to attempts to use their works, in the intended ways or not, regardless of the intent, purpose or circumstances of such attempts.


## IV. Terms for Featuring, Redistribution, Copying and Modification

**1.** "Featuring" refers to using the Creator's works in an environment where you do not redistribute binaries, assets, or source code (an example would be a YouTube Let's Play). You are permitted to feature Creator's works in an instance if all of the following criteria are met:

- You must not claim ownership for any part of the Creator's works.

The one abovementioned criteria is mandatory, unless otherwise is explicitly indicated.


**2.** You are permitted to redistribute unmodified works obtained from official, publicly available sources provided by the Creator, in an instance if all of the following criteria are met:

- The integrity of the Creator's works stays preserved. In case of binaries - they must stay completely unmodified. In case of code and assets - they must be obtained from official repositories and come in their entirety, with no files being altered or removed. If this criteria is not met - your use case falls under the terms for copying, modification and incorporation outlined below;
- You explicitly and clearly inform users that you do not own the Creator's works, and reference Creator themselves as the owner;
- You provide at least one direct link to the official distribution source provided by the Creator themselves;
- Your redistribution bears non-commercial purpose; you do not charge for downloading or otherwise obtaining Creator's works, and do not monetize the process in any way, shape or form, including, but not limited to: monetizable links, such as URL shorteners, or advertisements in your service that somehow interfere with downloading process, or any form of paywall.

The four abovementioned criteria are mandatory, unless otherwise is explicitly indicated.


**3.** You are permitted to copy, modify and incorporate any parts of the Creator's works into your projects, in an instance if all of the following criteria are met:

- You explicitly and clearly inform users that you do not own the Creator's works, and reference Creator themselves as the owner;
- If you do any modifications to parts of Creator's works that you copy or incorporate, you should provide clear and explicit indication that this is done;
- Any parts of your project that copy, modify, incorporate, depend on or extend any parts of the Creator's works must be open-source (have its source visible and allow for redistribution and modification), and include a criteria similar to this one in license that applies to them.

The three abovementioned criteria are mandatory, unless otherwise is explicitly indicated.


## VII. General Clauses and Exceptions

### 1. Waive Clause:
All restrictions applied within Terms for Featuring, Redistribution, Copying and Modification of this license do not apply to you if you have personal permission of the Creator. The Creator reserves the right to provide personal permission to any person or party, or revoke it from any person or party it was previosly granted to. Be informed, however, that the Creator is under no obligation to provide the permission to anyone, regardless of the reasons and circumstances of it being requested.


### 2. Postmortal Clause:
Confirmed physical death of the Creator permanently removes any of the restrictions applied within Terms for Featuring, Redistribution, Copying and Modification of this license. They are also invalidated in case one century (100 years) have passed since this license was issued, regardless of the Creator's physical state.


### 3. API Clause:
All restrictions applied within Terms for Featuring, Redistribution, Copying and Modification of this license do not apply to API packages within the source code of the Enigmatic Legacy. API package is any Java package (and all contents of such package) that is specifically named `api` and that does contain `package-info.java` file, which in order explicitly indicates such package as API. However, if you redistribute and incorporate API packages of the Enigmatic Legacy, they must be incliuded verbatim as they were obtained; otherwise this clause does not apply and you use falls under Terms for Featuring, Redistribution, Copying and Modification of this license as they are.
