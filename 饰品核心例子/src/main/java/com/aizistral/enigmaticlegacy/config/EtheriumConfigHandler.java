package com.aizistral.enigmaticlegacy.config;

import javax.annotation.Nullable;

import com.aizistral.enigmaticlegacy.EnigmaticLegacy;
import com.aizistral.enigmaticlegacy.api.generic.SubscribeConfig;
import com.aizistral.enigmaticlegacy.api.materials.EnigmaticArmorMaterials;
import com.aizistral.enigmaticlegacy.api.materials.EnigmaticMaterials;
import com.aizistral.enigmaticlegacy.handlers.SuperpositionHandler;
import com.aizistral.enigmaticlegacy.items.CosmicScroll;
import com.aizistral.enigmaticlegacy.objects.Perhaps;
import com.aizistral.enigmaticlegacy.packets.clients.PacketPlayerMotion;
import com.aizistral.enigmaticlegacy.registries.EnigmaticItems;
import com.aizistral.enigmaticlegacy.registries.EnigmaticSounds;
import com.aizistral.etherium.core.IEtheriumConfig;
import com.aizistral.omniconfig.wrappers.Omniconfig;
import com.aizistral.omniconfig.wrappers.OmniconfigWrapper;
import com.google.common.base.Preconditions;

import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.ArmorMaterial;
import net.minecraft.world.item.CreativeModeTab;
import net.minecraft.world.item.Tier;
import net.minecraft.world.item.crafting.Ingredient;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.network.PacketDistributor;

public class EtheriumConfigHandler implements IEtheriumConfig {
	private static EtheriumConfigHandler instance;
	private static Omniconfig.PerhapsParameter shieldThreshold;
	private static Omniconfig.PerhapsParameter shieldReduction;
	private static Omniconfig.IntParameter axeMiningVolume;
	private static Omniconfig.IntParameter pickaxeMiningRadius;
	private static Omniconfig.IntParameter pickaxeMiningDepth;
	private static Omniconfig.IntParameter scytheMiningVolume;
	private static Omniconfig.IntParameter shovelMiningRadius;
	private static Omniconfig.IntParameter shovelMiningDepth;
	private static Omniconfig.IntParameter swordCooldown;

	public EtheriumConfigHandler() {
		Preconditions.checkArgument(instance == null, "Etherium config handler already created!");
		instance = this;
	}

	public static EtheriumConfigHandler instance() {
		return instance;
	}

	@SubscribeConfig
	public static void onConfig(OmniconfigWrapper builder) {
		builder.pushPrefix("EtheriumArmor");

		shieldThreshold = builder
				.comment("The value of health to which player wearing full Etherium Armor set should be brough to activate the shield ability. Defined as percentage.")
				.max(100)
				.getPerhaps("ShieldThreshold", 40);

		shieldReduction = builder
				.comment("Damage reduction of shield generated by Etherium Armor. Defined as percentage.")
				.max(100)
				.getPerhaps("ShieldReduction", 50);

		builder.popPrefix();

		builder.pushPrefix("EtheriumAxe");

		axeMiningVolume = builder
				.comment("The volume Etherium Waraxe AOE mining. Set to -1 to disable the feature.")
				.min(-1)
				.max(128-1)
				.getInt("MiningVolume", 3);

		builder.popPrefix();

		builder.pushPrefix("EtheriumPickaxe");

		pickaxeMiningRadius = builder
				.comment("The radius of Etherium Pickaxe AOE mining. Set to -1 to disable the feature.")
				.min(-1)
				.max(128-1)
				.getInt("MiningRadius", 3);

		pickaxeMiningDepth = builder
				.comment("The depth of Etherium Pickaxe AOE mining.")
				.max(128-1)
				.getInt("MiningDepth", 1);

		builder.popPrefix();

		builder.pushPrefix("EtheriumScythe");

		scytheMiningVolume = builder
				.comment("The volume Etherium Scythe AOE mining. Set to -1 to disable the feature.")
				.min(-1)
				.max(128-1)
				.getInt("MiningVolume", 3);

		builder.popPrefix();

		builder.pushPrefix("EtheriumShovel");

		shovelMiningRadius = builder
				.comment("The radius of Etherium Shovel AOE mining. Set to -1 to disable the feature.")
				.min(-1)
				.max(128-1)
				.getInt("MiningRadius", 3);

		shovelMiningDepth = builder
				.comment("The depth of Etherium Shovel AOE mining.")
				.max(128-1)
				.getInt("MiningDepth", 1);

		builder.popPrefix();

		builder.pushPrefix("EtheriumSword");

		swordCooldown = builder
				.comment("Cooldown of Etherium Broadsword ability. Measured in ticks.")
				.getInt("Cooldown", 40);

		builder.popPrefix();
	}

	@Override
	public Ingredient getRepairMaterial() {
		return Ingredient.of(EnigmaticItems.ETHERIUM_INGOT);
	}

	@Override
	public void knockBack(LivingEntity entityIn, float strength, double xRatio, double zRatio) {
		entityIn.hasImpulse = true;
		Vec3 vec3d = new Vec3(0D, 0D, 0D);
		Vec3 vec3d1 = (new Vec3(xRatio, 0.0D, zRatio)).normalize().scale(strength);

		if (entityIn instanceof ServerPlayer) {
			EnigmaticLegacy.packetInstance.send(PacketDistributor.PLAYER.with(() -> (ServerPlayer) entityIn), new PacketPlayerMotion(vec3d.x / 2.0D - vec3d1.x, entityIn.isOnGround() ? Math.min(0.4D, vec3d.y / 2.0D + strength) : vec3d.y, vec3d.z / 2.0D - vec3d1.z));
		}
		entityIn.setDeltaMovement(vec3d.x / 2.0D - vec3d1.x, entityIn.isOnGround() ? Math.min(0.4D, vec3d.y / 2.0D + strength) : vec3d.y, vec3d.z / 2.0D - vec3d1.z);
	}

	@Override
	public CreativeModeTab getCreativeTab() {
		return EnigmaticLegacy.mainTab;
	}

	@Override
	public String getOwnerMod() {
		return EnigmaticLegacy.MODID;
	}

	@Override
	public ArmorMaterial getArmorMaterial() {
		return EnigmaticArmorMaterials.ETHERIUM;
	}

	@Override
	public Perhaps getShieldThreshold(@Nullable Player player) {
		if (player != null) {
			try {
				if (SuperpositionHandler.hasArchitectsFavor(player))
					return CosmicScroll.etheriumShieldThreshold.getValue();
			} catch (Exception ex) {
				// NO-OP
			}
		}

		return shieldThreshold.getValue();
	}

	@Override
	public Perhaps getShieldReduction() {
		return shieldReduction.getValue();
	}

	@Override
	public boolean disableAOEShiftInhibition() {
		return OmniconfigHandler.disableAOEShiftSuppression.getValue();
	}

	@Override
	public SoundEvent getAOESoundOff() {
		return EnigmaticSounds.CHARGED_OFF;
	}

	@Override
	public SoundEvent getAOESoundOn() {
		return EnigmaticSounds.CHARGED_ON;
	}

	@Override
	public int getAxeMiningVolume() {
		return axeMiningVolume.getValue();
	}

	@Override
	public int getPickaxeMiningDepth() {
		return pickaxeMiningDepth.getValue();
	}

	@Override
	public int getPickaxeMiningRadius() {
		return pickaxeMiningRadius.getValue();
	}

	@Override
	public int getScytheMiningVolume() {
		return scytheMiningVolume.getValue();
	}

	@Override
	public int getShovelMiningDepth() {
		return shovelMiningDepth.getValue();
	}

	@Override
	public int getShovelMiningRadius() {
		return shovelMiningRadius.getValue();
	}

	@Override
	public int getSwordCooldown() {
		return swordCooldown.getValue();
	}

	@Override
	public Tier getToolMaterial() {
		return EnigmaticMaterials.ETHERIUM;
	}

	@Override
	public SoundEvent getShieldTriggerSound() {
		return EnigmaticSounds.SHIELD_TRIGGER;
	}

	@Override
	public boolean isStandalone() {
		return false;
	}

	@Override
	public int getAOEBoost(@Nullable Player player) {
		return player != null && SuperpositionHandler.hasArchitectsFavor(player) ? 2 : 0;
	}

}
