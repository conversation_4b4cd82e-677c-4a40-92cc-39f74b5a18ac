{"misc.hhon": {"sounds": [{"name": "enigmaticlegacy:misc/hhon", "stream": false}]}, "misc.hhoff": {"sounds": [{"name": "enigmaticlegacy:misc/hhoff", "stream": false}]}, "misc.deflect": {"sounds": [{"name": "enigmaticlegacy:misc/deflect", "stream": false}]}, "misc.learn": {"sounds": [{"name": "enigmaticlegacy:misc/learn", "stream": false}]}, "misc.sword_hit_reject": {"sounds": [{"name": "enigmaticlegacy:misc/sword_hit_reject", "stream": false}]}, "misc.shield_trigger": {"sounds": [{"name": "enigmaticlegacy:misc/shield_hit_0", "stream": false}, {"name": "enigmaticlegacy:misc/shield_hit_1", "stream": false}]}, "misc.uneat": {"sounds": [{"name": "enigmaticlegacy:misc/uneat1", "stream": false}, {"name": "enigmaticlegacy:misc/uneat2", "stream": false}, {"name": "enigmaticlegacy:misc/uneat3", "stream": false}]}, "misc.write": {"sounds": [{"name": "enigmaticlegacy:misc/write1", "stream": false}, {"name": "enigmaticlegacy:misc/write2", "stream": false}]}, "quote.no_peril": {"sounds": [{"name": "enigmaticlegacy:quote/no_peril", "stream": false}]}, "quote.i_wandered": {"sounds": [{"name": "enigmaticlegacy:quote/i_wandered", "stream": false}]}, "quote.another_demigod": {"sounds": [{"name": "enigmaticlegacy:quote/another_demigod", "stream": false}]}, "quote.another_eon": {"sounds": [{"name": "enigmaticlegacy:quote/another_eon", "stream": false}]}, "quote.death_may": {"sounds": [{"name": "enigmaticlegacy:quote/death_may", "stream": false}]}, "quote.demise_is": {"sounds": [{"name": "enigmaticlegacy:quote/demise_is", "stream": false}]}, "quote.end_doorstep": {"sounds": [{"name": "enigmaticlegacy:quote/end_doorstep", "stream": false}]}, "quote.eternity_to_keep": {"sounds": [{"name": "enigmaticlegacy:quote/eternity_to_keep", "stream": false}]}, "quote.oblivion_rejects": {"sounds": [{"name": "enigmaticlegacy:quote/oblivion_rejects", "stream": false}]}, "quote.only_because": {"sounds": [{"name": "enigmaticlegacy:quote/only_because", "stream": false}]}, "quote.perhaps_you": {"sounds": [{"name": "enigmaticlegacy:quote/perhaps_you", "stream": false}]}, "quote.setback": {"sounds": [{"name": "enigmaticlegacy:quote/setback", "stream": false}]}, "quote.sulfur_air": {"sounds": [{"name": "enigmaticlegacy:quote/sulfur_air", "stream": false}]}, "quote.tortured_rocks": {"sounds": [{"name": "enigmaticlegacy:quote/tortured_rocks", "stream": false}]}, "quote.violence_calls": {"sounds": [{"name": "enigmaticlegacy:quote/violence_calls", "stream": false}]}, "quote.we_fall": {"sounds": [{"name": "enigmaticlegacy:quote/we_fall", "stream": false}]}, "quote.you_will_endure": {"sounds": [{"name": "enigmaticlegacy:quote/you_will_endure", "stream": false}]}, "quote.immortal": {"sounds": [{"name": "enigmaticlegacy:quote/immortal", "stream": false}]}, "quote.appaling_presence": {"sounds": [{"name": "enigmaticlegacy:quote/appaling_presence", "stream": false}]}, "quote.toll_paid": {"sounds": [{"name": "enigmaticlegacy:quote/toll_paid", "stream": false}]}, "quote.its_destruction": {"sounds": [{"name": "enigmaticlegacy:quote/its_destruction", "stream": false}]}, "quote.breathes_relieved": {"sounds": [{"name": "enigmaticlegacy:quote/breathes_relieved", "stream": false}]}, "quote.whether_it_is": {"sounds": [{"name": "enigmaticlegacy:quote/whether_it_is", "stream": false}]}, "quote.poor_creature": {"sounds": [{"name": "enigmaticlegacy:quote/poor_creature", "stream": false}]}, "quote.horrible_existence": {"sounds": [{"name": "enigmaticlegacy:quote/horrible_existence", "stream": false}]}, "quote.countless_dead": {"sounds": [{"name": "enigmaticlegacy:quote/countless_dead", "stream": false}]}, "quote.with_dragons": {"sounds": [{"name": "enigmaticlegacy:quote/with_dragons", "stream": false}]}, "quote.terrifying_form": {"sounds": [{"name": "enigmaticlegacy:quote/terrifying_form", "stream": false}]}}