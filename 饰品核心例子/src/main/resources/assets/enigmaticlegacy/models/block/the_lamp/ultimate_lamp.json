{"credit": "Made with Blockbench", "ambientocclusion": false, "textures": {"0": "enigmaticlegacy:block/gradient_x64", "1": "enigmaticlegacy:block/chiseled_polished_blackstone", "particle": "enigmaticlegacy:block/chiseled_polished_blackstone"}, "elements": [{"from": [0, 0, 0], "to": [16, 16, 16], "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#1"}, "east": {"uv": [0, 0, 16, 16], "texture": "#1"}, "south": {"uv": [0, 0, 16, 16], "texture": "#1"}, "west": {"uv": [0, 0, 16, 16], "texture": "#1"}, "up": {"uv": [0, 0, 16, 16], "texture": "#1"}, "down": {"uv": [0, 0, 16, 16], "texture": "#1"}}}, {"from": [3, 13, 13], "to": [13, 16, 13], "rotation": {"angle": 0, "axis": "y", "origin": [2, 23, 6]}, "faces": {"north": {"uv": [2, 0, 13, 3], "texture": "#1"}, "east": {"uv": [0, 0, 0, 3], "texture": "#1"}, "south": {"uv": [3, 3, 13, 6], "texture": "#1"}, "west": {"uv": [0, 0, 0, 3], "texture": "#1"}, "up": {"uv": [0, 0, 10, 0], "rotation": 180, "texture": "#1"}, "down": {"uv": [0, 0, 10, 0], "rotation": 180, "texture": "#1"}}}, {"from": [13, 13, 3], "to": [13, 16, 13], "rotation": {"angle": 0, "axis": "y", "origin": [6, 23, 14]}, "faces": {"north": {"uv": [0, 0, 0, 3], "texture": "#1"}, "east": {"uv": [3, 3, 13, 6], "texture": "#1"}, "south": {"uv": [0, 0, 0, 3], "texture": "#1"}, "west": {"uv": [2, 13, 13, 16], "texture": "#1"}, "up": {"uv": [0, 0, 10, 0], "rotation": 90, "texture": "#1"}, "down": {"uv": [0, 0, 10, 0], "rotation": 270, "texture": "#1"}}}, {"from": [3, 13, 3], "to": [13, 16, 3], "rotation": {"angle": 0, "axis": "y", "origin": [14, 23, 10]}, "faces": {"north": {"uv": [3, 3, 13, 6], "texture": "#1"}, "east": {"uv": [0, 0, 0, 3], "texture": "#1"}, "south": {"uv": [3, 0, 13, 3], "rotation": 180, "texture": "#1"}, "west": {"uv": [0, 0, 0, 3], "texture": "#1"}, "up": {"uv": [0, 0, 10, 0], "texture": "#1"}, "down": {"uv": [0, 0, 10, 0], "texture": "#1"}}}, {"from": [3, 13, 3], "to": [3, 16, 13], "rotation": {"angle": 0, "axis": "y", "origin": [10, 23, 2]}, "faces": {"north": {"uv": [0, 0, 0, 3], "texture": "#1"}, "east": {"uv": [0, 3, 3, 13], "rotation": 270, "texture": "#1"}, "south": {"uv": [0, 0, 0, 3], "texture": "#1"}, "west": {"uv": [3, 3, 13, 6], "texture": "#1"}, "up": {"uv": [0, 0, 10, 0], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 10, 0], "rotation": 90, "texture": "#1"}}}, {"from": [3, 0, 3], "to": [13, 3, 3], "rotation": {"angle": 0, "axis": "y", "origin": [8, 1.5, 8]}, "faces": {"north": {"uv": [3, 3, 13, 6], "rotation": 180, "texture": "#1"}, "east": {"uv": [0, 0, 0, 3], "rotation": 180, "texture": "#1"}, "south": {"uv": [2, 0, 13, 3], "rotation": 180, "texture": "#1"}, "west": {"uv": [0, 0, 0, 3], "rotation": 180, "texture": "#1"}, "up": {"uv": [0, 0, 10, 0], "rotation": 180, "texture": "#1"}, "down": {"uv": [0, 0, 10, 0], "rotation": 180, "texture": "#1"}}}, {"from": [13, 0, 3], "to": [13, 3, 13], "rotation": {"angle": 0, "axis": "y", "origin": [8, 1.5, 8]}, "faces": {"north": {"uv": [0, 0, 0, 3], "rotation": 180, "texture": "#1"}, "east": {"uv": [3, 3, 13, 6], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 0, 0, 3], "rotation": 180, "texture": "#1"}, "west": {"uv": [2, 13, 13, 16], "rotation": 180, "texture": "#1"}, "up": {"uv": [0, 0, 10, 0], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 10, 0], "rotation": 90, "texture": "#1"}}}, {"from": [3, 0, 13], "to": [13, 3, 13], "rotation": {"angle": 0, "axis": "y", "origin": [8, 1.5, 8]}, "faces": {"north": {"uv": [3, 0, 13, 3], "texture": "#1"}, "east": {"uv": [0, 0, 0, 3], "rotation": 180, "texture": "#1"}, "south": {"uv": [3, 3, 13, 6], "rotation": 180, "texture": "#1"}, "west": {"uv": [0, 0, 0, 3], "rotation": 180, "texture": "#1"}, "up": {"uv": [0, 0, 10, 0], "texture": "#1"}, "down": {"uv": [0, 0, 10, 0], "texture": "#1"}}}, {"from": [3, 0, 3], "to": [3, 3, 13], "rotation": {"angle": 0, "axis": "y", "origin": [8, 1.5, 8]}, "faces": {"north": {"uv": [0, 0, 0, 3], "rotation": 180, "texture": "#1"}, "east": {"uv": [0, 3, 3, 13], "rotation": 90, "texture": "#1"}, "south": {"uv": [0, 0, 0, 3], "rotation": 180, "texture": "#1"}, "west": {"uv": [3, 3, 13, 6], "rotation": 180, "texture": "#1"}, "up": {"uv": [0, 0, 10, 0], "rotation": 90, "texture": "#1"}, "down": {"uv": [0, 0, 10, 0], "rotation": 270, "texture": "#1"}}}, {"from": [0, 3, 3], "to": [3, 3, 13], "rotation": {"angle": 0, "axis": "x", "origin": [1.6, 8, 8]}, "faces": {"north": {"uv": [0, 0, 0, 3], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 10, 0], "texture": "#1"}, "south": {"uv": [0, 0, 0, 3], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 10, 0], "rotation": 180, "texture": "#1"}, "up": {"uv": [2, 0, 13, 3], "rotation": 270, "texture": "#1"}, "down": {"uv": [3, 3, 13, 6], "rotation": 270, "texture": "#1"}}}, {"from": [0, 3, 13], "to": [3, 13, 13], "rotation": {"angle": 0, "axis": "x", "origin": [1.6, 8, 8]}, "faces": {"north": {"uv": [2, 13, 13, 16], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 10, 0], "rotation": 90, "texture": "#1"}, "south": {"uv": [3, 3, 13, 6], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 10, 0], "rotation": 90, "texture": "#1"}, "up": {"uv": [0, 0, 0, 3], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 0, 3], "rotation": 270, "texture": "#1"}}}, {"from": [0, 13, 3], "to": [3, 13, 13], "rotation": {"angle": 0, "axis": "x", "origin": [1.6, 8, 8]}, "faces": {"north": {"uv": [0, 0, 0, 3], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 10, 0], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 0, 0, 3], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 10, 0], "texture": "#1"}, "up": {"uv": [3, 3, 13, 6], "rotation": 270, "texture": "#1"}, "down": {"uv": [3, 0, 13, 3], "rotation": 90, "texture": "#1"}}}, {"from": [0, 3, 3], "to": [3, 13, 3], "rotation": {"angle": 0, "axis": "x", "origin": [1.6, 8, 8]}, "faces": {"north": {"uv": [3, 3, 13, 6], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 10, 0], "rotation": 270, "texture": "#1"}, "south": {"uv": [0, 3, 3, 13], "rotation": 180, "texture": "#1"}, "west": {"uv": [0, 0, 10, 0], "rotation": 270, "texture": "#1"}, "up": {"uv": [0, 0, 0, 3], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 0, 3], "rotation": 270, "texture": "#1"}}}, {"from": [3, 3, 0], "to": [13, 3, 3], "rotation": {"angle": 0, "axis": "y", "origin": [7.6, 8, 1]}, "faces": {"north": {"uv": [0, 0, 10, 0], "rotation": 180, "texture": "#1"}, "east": {"uv": [0, 0, 0, 3], "rotation": 90, "texture": "#1"}, "south": {"uv": [0, 0, 10, 0], "texture": "#1"}, "west": {"uv": [0, 0, 0, 3], "rotation": 270, "texture": "#1"}, "up": {"uv": [2, 0, 13, 3], "texture": "#1"}, "down": {"uv": [3, 3, 13, 6], "rotation": 180, "texture": "#1"}}}, {"from": [3, 3, 0], "to": [3, 13, 3], "rotation": {"angle": 0, "axis": "y", "origin": [7.6, 8, 1]}, "faces": {"north": {"uv": [0, 0, 10, 0], "rotation": 90, "texture": "#1"}, "east": {"uv": [2, 13, 13, 16], "rotation": 90, "texture": "#1"}, "south": {"uv": [0, 0, 10, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [3, 3, 13, 6], "rotation": 270, "texture": "#1"}, "up": {"uv": [0, 0, 0, 3], "texture": "#1"}, "down": {"uv": [0, 0, 0, 3], "rotation": 180, "texture": "#1"}}}, {"from": [3, 13, 0], "to": [13, 13, 3], "rotation": {"angle": 0, "axis": "y", "origin": [7.6, 8, 1]}, "faces": {"north": {"uv": [0, 0, 10, 0], "texture": "#1"}, "east": {"uv": [0, 0, 0, 3], "rotation": 90, "texture": "#1"}, "south": {"uv": [0, 0, 10, 0], "rotation": 180, "texture": "#1"}, "west": {"uv": [0, 0, 0, 3], "rotation": 270, "texture": "#1"}, "up": {"uv": [3, 3, 13, 6], "texture": "#1"}, "down": {"uv": [3, 0, 13, 3], "texture": "#1"}}}, {"from": [13, 3, 0], "to": [13, 13, 3], "rotation": {"angle": 0, "axis": "y", "origin": [7.6, 8, 1]}, "faces": {"north": {"uv": [0, 0, 10, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [3, 3, 13, 6], "rotation": 90, "texture": "#1"}, "south": {"uv": [0, 0, 10, 0], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 3, 3, 13], "rotation": 180, "texture": "#1"}, "up": {"uv": [0, 0, 0, 3], "texture": "#1"}, "down": {"uv": [0, 0, 0, 3], "rotation": 180, "texture": "#1"}}}, {"from": [13, 3, 3], "to": [16, 3, 13], "rotation": {"angle": 0, "axis": "y", "origin": [14.6, 8, 7]}, "faces": {"north": {"uv": [0, 0, 0, 3], "rotation": 270, "texture": "#1"}, "east": {"uv": [0, 0, 10, 0], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 0, 0, 3], "rotation": 90, "texture": "#1"}, "west": {"uv": [0, 0, 10, 0], "texture": "#1"}, "up": {"uv": [2, 0, 13, 3], "rotation": 90, "texture": "#1"}, "down": {"uv": [3, 3, 13, 6], "rotation": 90, "texture": "#1"}}}, {"from": [13, 3, 3], "to": [16, 13, 3], "rotation": {"angle": 0, "axis": "y", "origin": [14.6, 8, 7]}, "faces": {"north": {"uv": [3, 3, 13, 6], "rotation": 270, "texture": "#1"}, "east": {"uv": [0, 0, 10, 0], "rotation": 90, "texture": "#1"}, "south": {"uv": [2, 13, 13, 16], "rotation": 90, "texture": "#1"}, "west": {"uv": [0, 0, 10, 0], "rotation": 90, "texture": "#1"}, "up": {"uv": [0, 0, 0, 3], "rotation": 90, "texture": "#1"}, "down": {"uv": [0, 0, 0, 3], "rotation": 90, "texture": "#1"}}}, {"from": [13, 13, 3], "to": [16, 13, 13], "rotation": {"angle": 0, "axis": "y", "origin": [14.6, 8, 7]}, "faces": {"north": {"uv": [0, 0, 0, 3], "rotation": 270, "texture": "#1"}, "east": {"uv": [0, 0, 10, 0], "texture": "#1"}, "south": {"uv": [0, 0, 0, 3], "rotation": 90, "texture": "#1"}, "west": {"uv": [0, 0, 10, 0], "rotation": 180, "texture": "#1"}, "up": {"uv": [3, 3, 13, 6], "rotation": 90, "texture": "#1"}, "down": {"uv": [3, 0, 13, 3], "rotation": 270, "texture": "#1"}}}, {"from": [13, 3, 13], "to": [16, 13, 13], "rotation": {"angle": 0, "axis": "y", "origin": [14.6, 8, 7]}, "faces": {"north": {"uv": [0, 3, 3, 13], "rotation": 180, "texture": "#1"}, "east": {"uv": [0, 0, 10, 0], "rotation": 270, "texture": "#1"}, "south": {"uv": [3, 3, 13, 6], "rotation": 90, "texture": "#1"}, "west": {"uv": [0, 0, 10, 0], "rotation": 270, "texture": "#1"}, "up": {"uv": [0, 0, 0, 3], "rotation": 90, "texture": "#1"}, "down": {"uv": [0, 0, 0, 3], "rotation": 90, "texture": "#1"}}}, {"from": [3, 3, 13], "to": [13, 3, 16], "rotation": {"angle": 0, "axis": "y", "origin": [8.6, 8, 15]}, "faces": {"north": {"uv": [0, 0, 10, 0], "texture": "#1"}, "east": {"uv": [0, 0, 0, 3], "rotation": 270, "texture": "#1"}, "south": {"uv": [0, 0, 10, 0], "rotation": 180, "texture": "#1"}, "west": {"uv": [0, 0, 0, 3], "rotation": 90, "texture": "#1"}, "up": {"uv": [2, 0, 13, 3], "rotation": 180, "texture": "#1"}, "down": {"uv": [3, 3, 13, 6], "texture": "#1"}}}, {"from": [13, 3, 13], "to": [13, 13, 16], "rotation": {"angle": 0, "axis": "y", "origin": [8.6, 8, 15]}, "faces": {"north": {"uv": [0, 0, 10, 0], "rotation": 90, "texture": "#1"}, "east": {"uv": [3, 3, 13, 6], "rotation": 270, "texture": "#1"}, "south": {"uv": [0, 0, 10, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [2, 13, 13, 16], "rotation": 90, "texture": "#1"}, "up": {"uv": [0, 0, 0, 3], "rotation": 180, "texture": "#1"}, "down": {"uv": [0, 0, 0, 3], "texture": "#1"}}}, {"from": [3, 13, 13], "to": [13, 13, 16], "rotation": {"angle": 0, "axis": "y", "origin": [8.6, 8, 15]}, "faces": {"north": {"uv": [0, 0, 10, 0], "rotation": 180, "texture": "#1"}, "east": {"uv": [0, 0, 0, 3], "rotation": 270, "texture": "#1"}, "south": {"uv": [0, 0, 10, 0], "texture": "#1"}, "west": {"uv": [0, 0, 0, 3], "rotation": 90, "texture": "#1"}, "up": {"uv": [3, 3, 13, 6], "rotation": 180, "texture": "#1"}, "down": {"uv": [3, 0, 13, 3], "rotation": 180, "texture": "#1"}}}, {"from": [3, 3, 13], "to": [3, 13, 16], "rotation": {"angle": 0, "axis": "y", "origin": [8.6, 8, 15]}, "faces": {"north": {"uv": [0, 0, 10, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [0, 3, 3, 13], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 0, 10, 0], "rotation": 270, "texture": "#1"}, "west": {"uv": [3, 3, 13, 6], "rotation": 90, "texture": "#1"}, "up": {"uv": [0, 0, 0, 3], "rotation": 180, "texture": "#1"}, "down": {"uv": [0, 0, 0, 3], "texture": "#1"}}}, {"name": "core", "from": [3, 3, 3], "to": [13, 13, 13], "rotation": {"angle": 0, "axis": "y", "origin": [11, 11, 11]}, "faces": {"north": {"uv": [0, 0, 0, 0], "texture": "#0"}, "east": {"uv": [0, 0, 0, 0], "texture": "#0"}, "south": {"uv": [0, 0, 0, 0], "texture": "#0"}, "west": {"uv": [0, 0, 0, 0], "texture": "#0"}, "up": {"uv": [0, 0, 0, 0], "texture": "#0"}, "down": {"uv": [0, 0, 0, 0], "texture": "#0"}}}, {"from": [3.1, 3, 0], "to": [3.1, 13, 3], "rotation": {"angle": 0, "axis": "z", "origin": [8, 8, 1.5]}, "faces": {"east": {"uv": [0, 0, 16, 0.25], "texture": "#0"}}}, {"from": [3, 12.9, 0], "to": [13, 12.9, 3], "rotation": {"angle": 0, "axis": "y", "origin": [8, 8, 1.5]}, "faces": {"up": {"uv": [0, 0, 16, 0.25], "rotation": 270, "texture": "#0"}}}, {"from": [3, 3.1, 0], "to": [13, 3.1, 3], "rotation": {"angle": 0, "axis": "y", "origin": [8, 8, 1.5]}, "faces": {"up": {"uv": [0, 0, 16, 0.25], "rotation": 270, "texture": "#0"}}}, {"from": [12.9, 3, 0], "to": [12.9, 13, 3], "rotation": {"angle": 0, "axis": "z", "origin": [8, 8, 1.5]}, "faces": {"west": {"uv": [0, 0, 16, 0.25], "rotation": 180, "texture": "#0"}}}, {"from": [0, 3, 12.9], "to": [3, 13, 12.9], "rotation": {"angle": 0, "axis": "y", "origin": [2, 8, 7.5]}, "faces": {"north": {"uv": [0, 0, 16, 0.25], "texture": "#0"}}}, {"from": [0, 12.9, 3], "to": [3, 12.9, 13], "rotation": {"angle": 0, "axis": "y", "origin": [2, 8, 7.5]}, "faces": {"up": {"uv": [0, 0, 16, 0.25], "rotation": 180, "texture": "#0"}}}, {"from": [0, 3.1, 3], "to": [3, 3.1, 13], "rotation": {"angle": 0, "axis": "y", "origin": [2, 8, 7.5]}, "faces": {"up": {"uv": [0, 0, 16, 0.25], "rotation": 180, "texture": "#0"}}}, {"from": [0, 3, 3.1], "to": [3, 13, 3.1], "rotation": {"angle": 0, "axis": "y", "origin": [2, 8, 7.5]}, "faces": {"south": {"uv": [0, 0, 16, 0.25], "rotation": 180, "texture": "#0"}}}, {"from": [12.9, 3, 13], "to": [12.9, 13, 16], "rotation": {"angle": 0, "axis": "y", "origin": [8, 8, 14]}, "faces": {"west": {"uv": [0, 0, 16, 0.25], "texture": "#0"}}}, {"from": [3, 12.9, 13], "to": [13, 12.9, 16], "rotation": {"angle": 0, "axis": "y", "origin": [8, 8, 14]}, "faces": {"up": {"uv": [0, 0, 16, 0.25], "rotation": 90, "texture": "#0"}}}, {"from": [3, 3.1, 13], "to": [13, 3.1, 16], "rotation": {"angle": 0, "axis": "y", "origin": [8, 8, 14]}, "faces": {"up": {"uv": [0, 0, 16, 0.25], "rotation": 90, "texture": "#0"}}}, {"from": [3.1, 3, 13], "to": [3.1, 13, 16], "rotation": {"angle": 0, "axis": "y", "origin": [8, 8, 14]}, "faces": {"east": {"uv": [0, 0, 16, 0.25], "rotation": 180, "texture": "#0"}}}, {"from": [13, 3, 3.1], "to": [16, 13, 3.1], "rotation": {"angle": 0, "axis": "y", "origin": [14, 8, 8]}, "faces": {"south": {"uv": [0, 0, 16, 0.25], "texture": "#0"}}}, {"from": [13, 12.9, 3], "to": [16, 12.9, 13], "rotation": {"angle": 0, "axis": "y", "origin": [14, 8, 8]}, "faces": {"up": {"uv": [0, 0, 16, 0.25], "texture": "#0"}}}, {"from": [13, 3.1, 3], "to": [16, 3.1, 13], "rotation": {"angle": 0, "axis": "y", "origin": [14, 8, 8]}, "faces": {"up": {"uv": [0, 0, 16, 0.25], "texture": "#0"}}}, {"from": [13, 3, 12.9], "to": [16, 13, 12.9], "rotation": {"angle": 0, "axis": "y", "origin": [14, 8, 8]}, "faces": {"north": {"uv": [0, 0, 16, 0.25], "rotation": 180, "texture": "#0"}}}, {"from": [3, 13, 3.1], "to": [13, 16, 3.1], "rotation": {"angle": 0, "axis": "z", "origin": [8, 14, 8]}, "faces": {"south": {"uv": [0, 0, 16, 0.25], "rotation": 270, "texture": "#0"}}}, {"from": [3.1, 13, 3], "to": [3.1, 16, 13], "rotation": {"angle": 0, "axis": "z", "origin": [8, 14, 8]}, "faces": {"west": {"uv": [0, 0, 16, 0.25], "rotation": 270, "texture": "#0"}}}, {"from": [12.9, 13, 3], "to": [12.9, 16, 13], "rotation": {"angle": 0, "axis": "z", "origin": [8, 14, 8]}, "faces": {"west": {"uv": [0, 0, 16, 0.25], "rotation": 270, "texture": "#0"}}}, {"from": [3, 13, 12.9], "to": [13, 16, 12.9], "rotation": {"angle": 0, "axis": "z", "origin": [8, 14, 8]}, "faces": {"north": {"uv": [0, 0, 16, 0.25], "rotation": 270, "texture": "#0"}}}, {"from": [3, 0, 3.1], "to": [13, 3, 3.1], "rotation": {"angle": 0, "axis": "z", "origin": [8, 2, 8]}, "faces": {"south": {"uv": [0, 0, 16, 0.25], "rotation": 90, "texture": "#0"}}}, {"from": [12.9, 0, 3], "to": [12.9, 3, 13], "rotation": {"angle": 0, "axis": "z", "origin": [8, 2, 8]}, "faces": {"east": {"uv": [0, 0, 16, 0.25], "rotation": 90, "texture": "#0"}}}, {"from": [3.1, 0, 3], "to": [3.1, 3, 13], "rotation": {"angle": 0, "axis": "z", "origin": [8, 2, 8]}, "faces": {"east": {"uv": [0, 0, 16, 0.25], "rotation": 90, "texture": "#0"}}}, {"from": [3, 0, 12.9], "to": [13, 3, 12.9], "rotation": {"angle": 0, "axis": "z", "origin": [8, 2, 8]}, "faces": {"north": {"uv": [0, 0, 16, 0.25], "rotation": 90, "texture": "#0"}}}], "display": {}, "groups": [{"name": "The Group", "origin": [8, 8, 8], "children": [0, {"name": "group", "origin": [10, 23, 2], "children": [1, 2, 3, 4]}, {"name": "group", "origin": [10, 23, 2], "children": [5, 6, 7, 8]}, {"name": "group", "origin": [10, 23, 2], "children": [9, 10, 11, 12]}, {"name": "group", "origin": [10, 23, 2], "children": [13, 14, 15, 16]}, {"name": "group", "origin": [10, 23, 2], "children": [17, 18, 19, 20]}, {"name": "group", "origin": [10, 23, 2], "children": [21, 22, 23, 24]}]}, 25, {"name": "Unreal Light", "origin": [4.9, 11, 10], "children": [26, 27, 28, 29]}, {"name": "Unreal Light", "origin": [4.9, 11, 10], "children": [30, 31, 32, 33]}, {"name": "Unreal Light", "origin": [4.9, 11, 10], "children": [34, 35, 36, 37]}, {"name": "Unreal Light", "origin": [4.9, 11, 10], "children": [38, 39, 40, 41]}, {"name": "Unreal Light", "origin": [4.9, 11, 10], "children": [42, 43, 44, 45]}, {"name": "Unreal Light", "origin": [4.9, 11, 10], "children": [46, 47, 48, 49]}]}