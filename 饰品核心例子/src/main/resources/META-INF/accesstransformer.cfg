public net.minecraft.potion.EffectInstance field_76460_b #duration

public net.minecraft.entity.monster.EndermanEntity func_70816_c(Lnet/minecraft/entity/Entity;)Z #teleportToEntity
public net.minecraft.entity.monster.EndermanEntity func_70820_n()Z #teleportRandomly
public net.minecraft.entity.monster.EndermanEntity func_70825_j(DDD)Z #teleportTo

public net.minecraft.entity.monster.piglin.PiglinTasks func_234468_a_(Lnet/minecraft/entity/monster/piglin/PiglinEntity;Lnet/minecraft/entity/LivingEntity;)V #func_234468_a_
public net.minecraft.entity.monster.piglin.PiglinTasks func_234524_k_(Lnet/minecraft/entity/monster/piglin/PiglinEntity;)Ljava/util/List; #func_234524_k_
public net.minecraft.entity.monster.piglin.PiglinTasks func_234475_a_(Lnet/minecraft/entity/monster/piglin/PiglinEntity;Ljava/util/List;)V #func_234475_a_
public net.minecraft.entity.monster.piglin.PiglinTasks func_234498_c_(Lnet/minecraft/entity/monster/piglin/PiglinEntity;Lnet/minecraft/item/ItemStack;)V #func_234498_c_
public net.minecraft.entity.monster.piglin.PiglinTasks func_234480_a_(Lnet/minecraft/item/Item;)Z #func_234480_a_
public net.minecraft.entity.monster.piglin.PiglinEntity func_234438_m_(Lnet/minecraft/item/ItemStack;)V  #func_234438_m_
public net.minecraft.entity.monster.piglin.PiglinBruteBrain func_242353_a(Lnet/minecraft/entity/monster/piglin/PiglinBruteEntity;Lnet/minecraft/entity/LivingEntity;)V #func_242353_a

public net.minecraft.util.FoodStats field_75125_b #foodSaturation
public net.minecraft.tileentity.BeaconTileEntity field_174909_f #beamSegments
public net.minecraft.inventory.container.EnchantmentContainer field_75168_e #tableInventory
public net.minecraft.inventory.container.EnchantmentContainer field_217006_g #worldPosCallable
public net.minecraft.world.inventory.EnchantmentMenu f_39449_ #enchantSlots
public net.minecraft.world.inventory.EnchantmentMenu f_39450_ #access
public net.minecraft.world.inventory.EnchantmentMenu f_39452_ #enchantmentSeed
public net.minecraft.world.inventory.EnchantmentMenu m_39471_(Lnet/minecraft/world/item/ItemStack;II)Ljava/util/List; #getEnchantmentList
public net.minecraft.entity.player.PlayerEntity field_71076_b #sleepTimer

public net.minecraft.client.gui.screen.CreateWorldScreen field_146333_g #worldName
public net.minecraft.client.gui.screen.WorldOptionsScreen field_239033_g_ #worldSeed
public net.minecraft.client.gui.IngameGui field_73837_f #ticks

public net.minecraft.entity.Entity field_242273_aw #portalCooldown

public net.minecraft.entity.player.SpawnLocationHelper func_241092_a_(Lnet/minecraft/world/server/ServerWorld;IIZ)Lnet/minecraft/util/math/BlockPos; #func_241092_a_
public net.minecraft.entity.player.ServerPlayerEntity func_205734_a(Lnet/minecraft/world/server/ServerWorld;)V #func_205734_a
public net.minecraft.entity.player.ServerPlayerEntity func_205735_q(I)I #func_205735_q

public net.minecraft.entity.projectile.TridentEntity field_203054_h #thrownStack
public net.minecraft.entity.LivingEntity field_184617_aD #ticksSinceLastSwing

public net.minecraft.inventory.container.EnchantmentContainer field_178149_f #xpSeed
public net.minecraft.inventory.container.EnchantmentContainer func_178148_a(Lnet/minecraft/item/ItemStack;II)Ljava/util/List; #getEnchantmentList

public net.minecraft.block.VineBlock func_196539_a(Lnet/minecraft/world/IBlockReader;Lnet/minecraft/util/math/BlockPos;)Z #hasVineBelow

public net.minecraft.entity.player.PlayerInventory field_184440_g #allInventories
public net.minecraft.loot.LootTable field_186466_c #pools

public net.minecraft.server.level.ChunkMap f_140130_ #visibleChunkMap
public net.minecraft.world.level.block.entity.BeaconBlockEntity f_58650_ #levels

public net.minecraft.world.entity.monster.piglin.PiglinAi m_34996_(Lnet/minecraft/world/entity/monster/piglin/Piglin;)Ljava/util/List; #getBarterResponseItems
public net.minecraft.world.entity.monster.piglin.PiglinAi m_34860_(Lnet/minecraft/world/entity/monster/piglin/Piglin;Ljava/util/List;)V  #throwItems
public net.minecraft.world.entity.monster.piglin.PiglinAi m_34837_(Lnet/minecraft/world/entity/monster/piglin/Piglin;Lnet/minecraft/world/entity/LivingEntity;)V #wasHurtBy
public net.minecraft.world.entity.monster.piglin.PiglinBruteAi m_35096_(Lnet/minecraft/world/entity/monster/piglin/PiglinBrute;Lnet/minecraft/world/entity/LivingEntity;)V #wasHurtBy

public net.minecraft.world.entity.monster.EnderMan m_32500_(Lnet/minecraft/world/entity/Entity;)Z #teleportTowards
#public net.minecraft.world.entity.monster.EnderMan

public net.minecraft.world.food.FoodData f_38697_ #saturationLevel
public net.minecraft.world.entity.player.Player f_36110_ #sleepCounter
public net.minecraft.world.effect.MobEffectInstance f_19503_ #duration
public net.minecraft.world.entity.player.Inventory f_35979_ #compartments
public net.minecraft.world.entity.projectile.ThrownTrident f_37555_ #tridentItem
public net.minecraft.world.entity.LivingEntity f_20922_ #attackStrengthTicker
public net.minecraft.client.gui.screens.inventory.tooltip.ClientTextTooltip f_169936_ #attackStrengthTicker
public net.minecraft.world.entity.ai.goal.target.TargetGoal f_26135_ #mob
public net.minecraft.world.entity.ai.goal.target.TargetGoal f_26137_ #targetMob
public net.minecraft.server.MinecraftServer f_129762_ #levels
public net.minecraft.world.level.storage.LevelSummary

public net.minecraft.client.gui.screens.inventory.CreativeModeInventoryScreen f_98507_ #selectedTab

public net.minecraft.world.damagesource.DamageSources f_268645_ #damageSources
public net.minecraft.world.damagesource.DamageSources m_268998_(Lnet/minecraft/resources/ResourceKey;Lnet/minecraft/world/entity/Entity;Lnet/minecraft/world/entity/Entity;)Lnet/minecraft/world/damagesource/DamageSource; #source
public net.minecraft.world.damagesource.DamageSources m_269298_(Lnet/minecraft/resources/ResourceKey;Lnet/minecraft/world/entity/Entity;)Lnet/minecraft/world/damagesource/DamageSource; #source
public net.minecraft.world.damagesource.DamageSources m_269079_(Lnet/minecraft/resources/ResourceKey;)Lnet/minecraft/world/damagesource/DamageSource; #source

public net.minecraft.world.entity.item.ItemEntity f_31988_ #thrower