{"required": true, "minVersion": "0.8", "package": "com.aizistral.enigmaticlegacy.mixin", "compatibilityLevel": "JAVA_17", "refmap": "enigmaticlegacy.refmap.json", "verbose": true, "mixins": ["MixinPiglinTasks", "MixinVillagerEntity", "MixinForgeHooks", "MixinPhantomSpawner", "MixinEnchantmentContainer", "MixinHoglinTasks", "MixinEnchantmentHelper", "MixinCharmEffects", "MixinStarterGear", "MixinPlayer", "MixinLivingEntity", "MixinEnderDragon", "MixinInventoryChangeTrigger", "<PERSON><PERSON><PERSON><PERSON>", "AccessorAbstractArrowEntity", "AccessorAdvancementCommands", "MixinMobEffect", "MixinVillager", "MixinAutomaticRecipeUnlockModule", "MixinEnderEyeItem", "MixinSummonedEntityTrigger", "MixinEnterBlockTrigger", "MixinItemStack", "MixinPlayerList", "MixinCrossbowItem", "MixinBindingCurseEnchantment"], "client": ["AccessorDisconnectedScreen", "MixinForgeHooksClient", "MixinGuiBookLanding", "MixinMiniInventoryButton", "MixinEnti<PERSON><PERSON><PERSON><PERSON>", "MixinWorldListEntry", "MixinGuiBook"], "injectors": {"defaultRequire": 1}}