{"parent": "enigmaticlegacy:recipes/root", "rewards": {"recipes": ["enigmaticlegacy:forbidden_axe"]}, "criteria": {"has_items": {"trigger": "minecraft:inventory_changed", "conditions": {"items": [{"items": ["minecraft:netherite_ingot"]}]}}, "has_recipe_result": {"trigger": "minecraft:inventory_changed", "conditions": {"items": [{"items": ["enigmaticlegacy:forbidden_axe"]}]}}, "has_the_recipe": {"trigger": "minecraft:recipe_unlocked", "conditions": {"recipe": "enigmaticlegacy:forbidden_axe"}}}, "requirements": [["has_items", "has_recipe_result", "has_the_recipe"]]}