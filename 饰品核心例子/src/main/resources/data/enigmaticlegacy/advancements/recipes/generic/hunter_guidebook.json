{"parent": "enigmaticlegacy:recipes/root", "rewards": {"recipes": ["enigmaticlegacy:hunter_guidebook"]}, "criteria": {"has_items": {"trigger": "minecraft:inventory_changed", "conditions": {"items": [{"items": ["minecraft:ender_pearl"]}]}}, "has_recipe_result": {"trigger": "minecraft:inventory_changed", "conditions": {"items": [{"items": ["enigmaticlegacy:hunter_guidebook"]}]}}, "has_the_recipe": {"trigger": "minecraft:recipe_unlocked", "conditions": {"recipe": "enigmaticlegacy:hunter_guidebook"}}}, "requirements": [["has_items", "has_recipe_result", "has_the_recipe"]]}