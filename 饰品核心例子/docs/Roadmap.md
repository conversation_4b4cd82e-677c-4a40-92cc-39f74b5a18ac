# Don't-Forget-To-Do-Someday List
- [ ] Change update handler, so that update info is always pulled from master branch and can be fetched for different Minecraft versions individually;
- [x] Figure something out with blasted tooltips. The way we currently address damnable auto-wrapping from Forge, unfortunately, breaks a lot of other mods trying to add something to Enigmatic Legacy's tooltips. I am yet to attempt to post an issue on the matter to Forge repository, but my experience tells me that suggesting change on their side is often a futile effort, so we must be prepared to deal with this ourselves;
- [x] Migrate everything from `com.integral` to `com.aizistral`;
- [x] Make The Architect's Favor separate from the main mod and include it under different license terms;
- [x] Make the path to obtaining The Architect's Favor less illuminated;
- [ ] Make Soul Crystals pickup-able for everyone, regardless of who they were dropped by, with some way to obtain them in an item form. Also special cursed-only weapon to tear them out of other players and some way to get more of them without other players;
- [x] Reconsider playtime accounting mechanism for Ring of the Seven Curses;
- [x] Rename `architect_eye` to `enigmatic_eye`;
- [x] Re-integrate Enigmatic Lockbox into the main mod under new license;
- [x] Re-integrate Etherium into the main mod;
- [x] Add build action on Github;
- [ ] Make cosmic scroll more balanced by removing free scroll slot modifier.