license="${mod_license}"
modLoader="javafml"
loaderVersion="[31,)"
issueTrackerURL="${issue_tracker_url}"

[[mods]]
modId="${mod_id}"
version="${mod_version}"
displayName="${mod_name}"
displayURL="${mod_url}"
logoFile="${mod_icon}"
credits="${mod_credits}"
authors="${mod_author}"
description='''
${mod_description}
'''
[[dependencies.${mod_id}]]
    modId="forge"
    mandatory=true
    versionRange="${dep_forge}"
    ordering="NONE"
    side="BOTH"
[[dependencies.${mod_id}]]
    modId="minecraft"
    mandatory=true
    versionRange="${dep_minecraft}"
    ordering="NONE"
    side="BOTH"
[[dependencies.${mod_id}]]
    modId="curios"
    mandatory=true
    versionRange="${dep_curios}"
    ordering="NONE"
    side="BOTH"
[[dependencies.${mod_id}]]
    modId="patchouli"
    mandatory=true
    versionRange="${dep_patchouli}"
    ordering="NONE"
    side="BOTH"
[[dependencies.${mod_id}]]
    modId="caelus"
    mandatory=true
    versionRange="${dep_caelus}"
    ordering="NONE"
    side="BOTH"