<h3><b>The Changelog:</b></h3><br/>

<h5><b>[Release 2.28.0]:</b></h5>
<p>
&nbsp;- First and last 1.19.4 port;<br/>
&nbsp;- Implemented /getringtime and /setringtime commands, which allow to view and update player's accounted time spent bearing the Seven Curses.<br/>
<p/><br/><br/>


<h5><b>[Release 2.27.0]:</b></h5>
<p>
&nbsp;- First and last 1.19.3 port;<br/>
&nbsp;- Fixed some loose ends (<a href="https://github.com/Aizistral-Studios/Enigmatic-Legacy/issues/420">#420</a>);<br/>
&nbsp;- Fixed ultimate potion of swiftness not being registered correctly;<br/>
&nbsp;- Fixed forced Skyflap spawn not stopping when insomnia curse is disabled in config (<a href="https://github.com/Aizistral-Studios/Enigmatic-Legacy/issues/431">#431</a>);<br/>
&nbsp;- Fixed The Infinitum not displaying correct death prevention probability when changed in config (<a href="https://github.com/Aizistral-Studios/Enigmatic-Legacy/issues/414">#414</a>);<br/>
&nbsp;- Fixed unintended lava immunity when wearing Blazing Core and Bulwark of Blazing Pride at the same time (<a href="https://github.com/Aizistral-Studios/Enigmatic-Legacy/issues/399">#399</a>);<br/>
&nbsp;- Updated Russian localization (<a href="https://github.com/Aizistral-Studios/Enigmatic-Legacy/pull/429">thanks FETREX-DEV, #429</a>);<br/>
&nbsp;- Updated Chinese localization (<a href="https://github.com/Aizistral-Studios/Enigmatic-Legacy/pull/418">thanks Water-Moon, #418</a>).<br/>
<p/><br/><br/>


<h5><b>[Release 2.26.5]:</b></h5>
<p>
&nbsp;- Custom stats are now correctly registered under Enigmatic Legacy namespace (<a href="https://github.com/Aizistral-Studios/Enigmatic-Legacy/issues/404">#404</a>);<br/>
&nbsp;- Bee Queen from <a href="https://www.curseforge.com/minecraft/mc-mods/the-bumblezone-forge">The Bumblezone</a> is now a hardcoded exception to neutral anger mechanic of Ring of the Seven Curses (<a href="https://github.com/Aizistral-Studios/Enigmatic-Legacy/pull/412">thanks TelephaticGrunt, #412</a>).<br/>
<p/><br/><br/>


<h5><b>[Release 2.26.4]:</b></h5>
<p>
&nbsp;- Fixed custom hunger bar of The Forbidden Fruit not displaying after re-entering the world (<a href="https://github.com/Aizistral-Studios/Enigmatic-Legacy/issues/398">#398</a>);<br/>
&nbsp;- Ensured compatibility with older Forge versions, down to 43.1.0.<br/>
<p/><br/><br/>


<h5><b>[Release 2.26.3]:</b></h5>
<p>
&nbsp;- Fixed Bulwark of Blazing Pride not rendering correctly (<a href="https://github.com/Aizistral-Studios/Enigmatic-Legacy/issues/397">#397</a>).<br/>
<p/><br/><br/>


<h5><b>[Release 2.26.2]:</b></h5>
<p>
&nbsp;- Fixed crash when right-clicking with Etherium Broadsword (<a href="https://github.com/Aizistral-Studios/Enigmatic-Legacy/issues/395">#395</a>);<br/>
&nbsp;- Fixed crash when loading into the world with Roughly Enough Resources installed (<a href="https://github.com/Aizistral-Studios/Enigmatic-Legacy/issues/396">#396</a>).<br/>
<p/><br/><br/>


<h5><b>[Release 2.26.1]:</b></h5>
<p>
&nbsp;- Fixed "GolemHeartExcludedArmor" option not working with non-vanilla items (<a href="https://github.com/Aizistral-Studios/Enigmatic-Legacy/issues/394">#394</a>);<br/>
&nbsp;- Majestic Elytra is now included in "GolemHeartExcludedArmor" by default;<br/>
&nbsp;- Config file version raised to 2.2.<br/>
<p/><br/><br/>


<h5><b>[Release 2.26.0]:</b></h5>
<p>
&nbsp;- Initial 1.19.2 port;<br/>
&nbsp;- Changed registry names of some items;<br/>
&nbsp;- Renamed packages to correspond to domain I own (<a href="https://aizistral.com">aizistral.com</a>);<br/>
&nbsp;- Fixed enchanted item glint not working correctly with Wayfinder of the Damned;<br/>
&nbsp;- Made Bulwark of Blazing Pride fire-resistant;<br/>
&nbsp;- Reduced the chance of The Watcher's death quotes being played from 50% to 20%;<br/>
&nbsp;- Changed playtime accounting mechanism of Ring of the Seven Curses to hopefully address some issues with it;<br/>
&nbsp;- Changed implementation of crossbow enchantments to assist compatibility with other mods (<a href="https://github.com/Aizistral-Studios/Enigmatic-Legacy/issues/124">#124</a>, <a href="https://github.com/Aizistral-Studios/Enigmatic-Legacy/issues/115">#115</a>);<br/>
&nbsp;- Sharpshooter now provides plain +40% base damage per level of enchantment.<br/>
<p/><br/><br/>


<h5><b>[Release 2.25.0]:</b></h5>
<p>
&nbsp;- Mod license was changed;<br/>
&nbsp;- Re-integrated Enigmatic Lockbox into open-source part of the mod under the terms of updated license;<br/>
&nbsp;- Re-integrated <a href="https://www.curseforge.com/minecraft/mc-mods/etherium">Etherium</a> into main mod, will not be released as a separate mod any longer;<br/>
&nbsp;- Confirmation screen for links in The Acknowledgment will no longer have "Never open links from people you don't trust!" warning;<br/>
&nbsp;- Removed message about The Watcher feature being experimental;<br/>
&nbsp;- Removed custom tooltip rendering logic which was causing some issues with other mods (<a href="https://github.com/Aizistral-Studios/Enigmatic-Legacy/issues/370">#370</a>, <a href="https://github.com/Aizistral-Studios/Enigmatic-Legacy/issues/393">#393</a>);<br/>
&nbsp;- Removed some mod-specific hacks that were causing issues and are not needed any longer (<a href="https://github.com/Aizistral-Studios/Enigmatic-Legacy/issues/383">#383</a>);<br/>
&nbsp;- Fixed client crashes when opening world selection menu (<a href="https://github.com/Aizistral-Studios/Enigmatic-Legacy/issues/368">#368</a>);<br/>
&nbsp;- Fixed Inscrutable Eye not respecting its config toggle (<a href="https://github.com/Aizistral-Studios/Enigmatic-Legacy/issues/385">#385</a>);<br/>
&nbsp;- Fixed possible game freeze when deflecting Piercing arrows (<a href="https://github.com/Aizistral-Studios/Enigmatic-Legacy/issues/391">#391</a>);<br/>
&nbsp;- Fixed item rubberbanding near players with disabled magnet ring (<a href="https://github.com/Aizistral-Studios/Enigmatic-Legacy/issues/373">#373</a>);<br/>
&nbsp;- Added config option to disable curse of insomnia on Ring of the Seven Curses (<a href="https://github.com/Aizistral-Studios/Enigmatic-Legacy/issues/310">#310</a>);<br/>
&nbsp;- Updated Chinese localization (<a href="https://github.com/Aizistral-Studios/Enigmatic-Legacy/pull/365">thanks bucketBrian, #365</a>).<br/>
<p/><br/><br/>


<h5><b>[Release 2.24.1]:</b></h5>
<p>
&nbsp;- Fixed crashes related to The Acknowledgment (#359, #360, #361, #362);<br/>
&nbsp;- Fixed crash when typing "-!" in The Architect's Inkwell GUI (#358);<br/>
&nbsp;- Raised minimum required Patchouli version to 1.18.2-71.1;<br/>
&nbsp;- Updated Chinese localization (thanks whatisyours, #353).<br/>
<p/><br/><br/>


<h5><b>[Release 2.24.0]:</b></h5>
<p>
&nbsp;- Fixed crash upon world loading when using Patchouli 1.18.2-70 or later (#350);<br/>
&nbsp;- Survival worlds where player have equipped Ring of the Seven Curses will now be marked as "Cursed Mode" in world selection menu, or "Cursed Hardcore!" for hardcore worlds. Notice that the world has to be loaded with this or later version of Enigmatic Legacy at least once for this to work;<br/>
&nbsp;- Config now allows to make players able to lose all 10 Soul Crystals, which will lead to permanent death. Unlike death in hardcore, this will throw player out of the world, display special screen and leave them unable to enter the world once more;<br/>
&nbsp;- Made The Watcher slightly less loud;<br/>
&nbsp;- Implemented Dimensional Anchor.<br/>
<p/><br/><br/>

<h5><b>[Release 2.23.1]:</b></h5>
<p>
&nbsp;- Fixed possible startup crashes related to MixinItemStack.<br/>
<p/><br/><br/>


<h5><b>[Release 2.23.0]:</b></h5>
<p>
&nbsp;- Added config option that allows to make tooltip of Ring of the Seven Curses unreadable until player equips it;<br/>
&nbsp;- Items that require Ring of the Seven Curses to be used will now have unreadable tooltip until player has the ring equipped;<br/>
&nbsp;- Trying to sleep with Ring of the Seven Curses now displays message indicating player can't sleep;<br/>
&nbsp;- Experience absorption/extraction by Scroll of Ageless Wisdom now scales with the amount of experience player has;<br/>
&nbsp;- The Ender Slayer can now suppress player's ability to use Potion of Recall and Twisted Mirror;<br/>
&nbsp;- Fixed Astral Breaker being ineffective against amethyst blocks (#344);<br/>
&nbsp;- Improved Soul Crystal texture (courtesy of Soul);<br/>
&nbsp;- Minor corrections in English localization;<br/>
&nbsp;- Implemented Charming Insignia.<br/>
<p/><br/><br/>


<h5><b>[Release 2.22.2]:</b></h5>
<p>
&nbsp;- Fixed damage limit of Non-Euclidean Cube applying to players without Non-Euclidean Cube (#342);<br/>
&nbsp;- Updated Korean localization (thanks PixVoxel, #341).<br/>
<p/><br/><br/>


<h5><b>[Release 2.22.1]:</b></h5>
<p>
&nbsp;- Fixed hidden recipes not working properly.<br/>
<p/><br/><br/>


<h5><b>[Release 2.22.0]:</b></h5>
<p>
&nbsp;- Made Etherium Scythe effective against more materials;<br/>
&nbsp;- The Ender Slayer can now suppress active ability of Non-Euclidean Cube;<br/>
&nbsp;- Fixed decorative lamps being completely unbreakable with conventional tools;<br/>
&nbsp;- Fixed server freeze when using Non-Euclidean Cube immediately after equipping (#314);<br/>
&nbsp;- Fixed The Ender Slayer's teleportation suppression not applying with sweeping attack (#325);<br/>
&nbsp;- Fixed any arrows being pickupable after being deflected with Angel's Blessing (#330);<br/>
&nbsp;- Fixed Astral Breaker being ineffective against melons (#324);<br/>
&nbsp;- Fixed "Time without Seven Curses" stat increasing while player remains in death screen (#323);<br/>
&nbsp;- Fixed active ability of Non-Euclidean Cube being able to teleport player outside the world border (#339);<br/>
&nbsp;- Fixed dupe involving The Testament of Contempt (#340);<br/>
&nbsp;- Fixed Etherium Scythe not working as a hoe (#322);<br/>
&nbsp;- Fixed Extradimensional Vessel not spawning properly when dying in the void (#328);<br/>
&nbsp;- Fixed synchronization errors involving TransientPlayerData (#335);<br/>
&nbsp;- Fixed etherium tools and Astral Breaker being able to break unbreakable blocks from other mods (#334);<br/>
&nbsp;- Fixed potential crashes with The Architect's Favor and etherium shield (#332);<br/>
&nbsp;- Nefarious Essence and Nefarious Ingot are now explosion-resistant when dropped;<br/>
&nbsp;- The One Box now displays selected loot table above toolbar when cycling through tables;<br/>
&nbsp;- The Architect's Favor is now a secret item;<br/>
&nbsp;- Added advancement for obtaining Grace of the Creator;<br/>
&nbsp;- Implemented Majestic Elytra (#304);<br/>
&nbsp;- Implemented Bottle of Ichor;<br/>
&nbsp;- Implemented The Watcher, as well as related Dormant/Inscrutable Eye and Quote Player;<br/>
&nbsp;- Minor corrections in English localization;<br/>
&nbsp;- Updated Korean localization (thanks PixVoxel, #316 and #336);<br/>
&nbsp;- Updated Russian localization (thanks Alexsist, TyMVS and VektorZ1, #321).<br/>
<p/><br/><br/>


<h5><b>[Release 2.21.1]:</b></h5>
<p>
&nbsp;- Fixed server crashing after any player logs in;<br/>
&nbsp;- Fixed The Architect's Favor cooldown ticking down twice as fast as it should when equipped.<br/>
<p/><br/><br/>


<h5><b>[Release 2.21.0]:</b></h5>
<p>
&nbsp;- Implemented Enigmatic Lockbox, a somewhat discrete part of the mod which from now on contains The Architect's Favor;<br/>
&nbsp;- Mod license was changed. Current license can be found on Github/CurseForge or included with published .jar-files.<br/>
<p/><br/><br/>


<h5><b>[Release 2.20.2]:</b></h5>
<p>
&nbsp;- Fixed lore disabling config option for Ring of the Seven Curses not working.<br/>
<p/><br/><br/>


<h5><b>[Release 2.20.1]:</b></h5>
<p>
&nbsp;- Fixed crash on dedicated server startup (#308).<br/>
<p/><br/><br/>


<h5><b>[Release 2.20.0]:</b></h5>
<p>
&nbsp;- Tweaked priorities of some loot-altering effects;<br/>
&nbsp;- Active ability of Etherium Broadsword will no longer trigger if player holds shield in offhand;<br/>
&nbsp;- Enigmatic Amulet color is now pseudo-randomly determined by player's name in combination with world seed;<br/>
&nbsp;- Bees are now considered tamable animals by curse-bending effect of Guide to Animal Companionship;<br/>
&nbsp;- Lowered priority of Non-Euclidean cube in handling death event;<br/>
&nbsp;- Fixed Non-Euclidean cube not providing immunity from fire block damage (#307);<br/>
&nbsp;- Added config option to disable Night Vision ability of Charm of Treasure Hunter;<br/>
&nbsp;- Keystone of the Oblivion can now be right-clicked on/with within inventory to destroy dragged/hovered stacks;<br/>
&nbsp;- Tooltip of Exquisite Ring is now altered for bearers of Seven Curses, indicating that its Piglin-neutralizing effect will not work for them;<br/>
&nbsp;- Fixed client crash when viewing ctrl-tooltip of Keystone of The Oblivion (#306);<br/>
&nbsp;- Soul Crystal locations are now archived in soul_archive.json file at the root of world save folder (#78);<br/>
&nbsp;- Clarified some wording on Pearl of the Void/Non-Euclidean Cube;<br/>
&nbsp;- Fixed The Burden of Desolation not having cursed tooltip frame;<br/>
&nbsp;- Essence of Raging Life now works on chorus plants;<br/>
&nbsp;- Implemented Nefarious Ingot, as well as Etherium Block, Ingot and Scrap;<br/>
&nbsp;- Implemented Wayfinder of the Damned (#83);<br/>
&nbsp;- Implemented Tome of Devoured Malignancy;<br/>
&nbsp;- Implemented Amulet of Ascension;<br/>
&nbsp;- Implemented The Testament of Contempt;<br/>
&nbsp;- Implemented The Eternal Cake;<br/>
&nbsp;- Implemented Teleportato;<br/>
&nbsp;- Player may now obtain up to 4 Hearts of the Abyss per world (previously 3);<br/>
&nbsp;- Added Korean translation (thanks PixVoxel, #305);<br/>
&nbsp;- Ported fixes from 2.11.12.<br/>
<p/><br/><br/>


<h5><b>[Release 2.19.0]:</b></h5>
<p>
&nbsp;- Implemented configurable exlusion list for Heart of the Golem, which allows to make it not count certain items as armor even when equipped in armor slots. Elytra is listed by default;<br/>
&nbsp;- Added config option that makes Ring of the Seven Curses try to auto-equip itself when it enters player's inventory (#290);<br/>
&nbsp;- Pearl of the Void damage now respects Guide to Animal Companionship (#288);<br/>
&nbsp;- Implemented mechanism that prevents poison damage from ever killing the player (#299);<br/>
&nbsp;- Enchantment entries in The Acknowledgment are now automatically unlocked when opening Enchanting Table's GUI (#274);<br/>
&nbsp;- Fixed incorrect compatibility restrictions on Curse of Eternal Binding;<br/>
&nbsp;- Pearl of the Void no longer prevents Night Vision effects of Charm of the Treasure Hunter/Will of the Ocean from working;<br/>
&nbsp;- Fixed desynchronization issues when using any means of cross-dimensional teleportation in the mod;<br/>
&nbsp;- Etherium Shovel and Astral Breaker are now efficient against clay;<br/>
&nbsp;- If Quark is installed, its sort button in player's inventory will now be automatically moved out of the way of Enigmatic Legacy's buttons (#295);<br/>
&nbsp;- Additional integration with Quark which prevents it from ever changing anything within Enigmatic Legacy's tooltips (what it tries to add there is not compatible anyways);<br/>
&nbsp;- Made sure that Quark's features of automatically unlocking entire recipe book and and deleting all recipe-related advancements will not affect Enigmatic Legacy's recipes and advancements (they are integral to many things in The Acknowledgment, and Quark is too bold in its assumption that those can safely be messed with);<br/>
&nbsp;- Player may now obtain up to 3 Hearts of the Abyss per world (previously 2);<br/>
&nbsp;- Implemented The Burden of Desolation;<br/>
&nbsp;- Implemented Non-Euclidean Cube.<br/>
<p/><br/><br/>


<h5><b>[Release 2.18.1]:</b></h5>
<p>
&nbsp;- Removed some duplicate keys in English localization file;<br/>
&nbsp;- Fixed game crash when dealing damage to Ender Dragon (#298);<br/>
&nbsp;- Fixed possible desynchronization issues when applying dynamic attributes to a player (#286);<br/>
&nbsp;- Fixed dropped items rubberbanding when near another player who disabled magnetic effects for themselves (#283).<br/>
<p/><br/><br/>


<h5><b>[Release 2.18.0]:</b></h5>
<p>
&nbsp;- Initial 1.18.2 port;<br/>
&nbsp;- Enchanting with Enchanter's Pearl can no longer produce items that have both Curse of Eternal Binding and Curse of Vanishing.<br/>
<p/><br/><br/>


<h5><b>[Release 2.17.3]:</b></h5>
<p>
&nbsp;- Fixed invalid line in tooltip of Charm of Treasure Hunter.<br/>
<p/><br/><br/>


<h5><b>[Release 2.17.2]:</b></h5>
<p>
&nbsp;- Fixed client crash on startup when AppleSkin is present.<br/>
<p/><br/><br/>


<h5><b>[Release 2.17.1]:</b></h5>
<p>
&nbsp;- Improved compatibility of Sharpshooter enchantment with modded crossbows (thanks Flynatol, #282);<br/>
&nbsp;- Added Quark integration which prevents it from replacing item stats with icons in Enigmatic Legacy's item tooltips, as they do not display correctly there;<br/>
&nbsp;- Added AppleSkin integration which prevents it from adding icons for food hunger/saturation levels into Enigmatic Legacy's item tooltips, as they do not display correctly there;<br/>
&nbsp;- Extended duration of Night Vision effects granted by Charm of Treasure Hunter and Will of the Ocean;<br/>
&nbsp;- Charm of Treasure Hunter no longer requires low light level to work, and does not immediately remove Night Vision effect in inappropriate conditions, instead simply ceasing to refresh it;<br/>
&nbsp;- More spelling mistakes corrected in English localization.<br/>
<p/><br/><br/>


<h5><b>[Release 2.17.0]:</b></h5>
<p>
&nbsp;- Fixed stack overflow when trying to block Guardian attack with Bulwark of Blazing Pride (#276);<br/>
&nbsp;- Angel's Blessing can now accelerate thrown potions and similar projectiles;<br/>
&nbsp;- Added an option to client config which allows to disable activation of active ability of Angel's Blessing by pressing jump key in mid-air;<br/>
&nbsp;- Implemented The Ender Slayer.<br/>
<p/><br/><br/>


<h5><b>[Release 2.16.1]:</b></h5>
<p>
&nbsp;- Added "enchantment.desc" for new curses;<br/>
&nbsp;- Corrected one minor spelling mistakes in English localization;<br/>
&nbsp;- Curse of Eternal Binding is now incompatible with Enigmatic Amulet and Scroll of Postmortal Recall, as well as any modded enchantments that contain "soulbound" or "soulbinding" in their registry name;<br/>
&nbsp;- Enchanter's Pearl can no longer be equipped by players who do not bear the Seven Curses, and will not provide additional charm slot to them if it ends up equipped anyhow.<br/>
<p/><br/><br/>


<h5><b>[Release 2.16.0]:</b></h5>
<p>
&nbsp;- Fixed current looting bonus not displayed correctly in tooltip of Axe of Executioner (#271);<br/>
&nbsp;- Fixed a bug where enabling ultra hardcore mode for Ring of the Seven Curses in config caused two identical rings to be equipped onto player when spawning (#269);<br/>
&nbsp;- Fixed projectiles deflected by Angel's Blessing picking up potentially infinite acceleration and crashing the game as a result (#272);<br/>
&nbsp;- Active ability of Angel's Blessing can now be activated by pressing jump key in mid-air (#170);<br/>
&nbsp;- The Acknowledgment now has separate option for Patchouli's text overflow mode, with default set to "OVERFLOW" (#268);<br/>
&nbsp;- The Forbidden Fruit no longer counts as conventional food (#265);<br/>
&nbsp;- Unholy Grail is now 4 times more rare;<br/>
&nbsp;- Reduced intensity of particles produced by Astral Breaker;<br/>
&nbsp;- Somewhat improved the texture of Bulwark of Blazing Pride;<br/>
&nbsp;- Implemented "enigmaticlegacy:cursed_inventory_changed" advancement trigger, which is identical to vanilla's "inventory_changed" in every regard, except that it also checks whether or not the player is bearing Ring of the Seven Curses. Most ring-related advancements now use this trigger;<br/>
&nbsp;- Implemented some new advancements;<br/>
&nbsp;- Implemented "Time Played with Seven Curses" and "Time Played without Seven Curses" stats, visible in vanilla's stats menu;<br/>
&nbsp;- Opening The Twist now shows its name on the landing page, instead of it always being "The Acknowledgment". This will also apply to The Infinitum;<br/>
&nbsp;- The Twist can no longer be opened if the player does not bear the Seven Curses. This will also apply to The Infinitum;<br/>
&nbsp;- Upon entering any world for the first time, the default optimal GUI scale will be forced for The Acknowledgment;<br/>
&nbsp;- Enchanter's Pearl now works only when equipped as charm, but provides +1 charm slot while equipped;<br/>
&nbsp;- Pearl of the Void, The Twist and other items with related effects now have more precise check for applying effects that are supposed to be triggered only by player's melee attacks;<br/>
&nbsp;- Implemented configurable list of entities in config which are supposed to be counted as bosses by The Twist and the like;<br/>
&nbsp;- Expanded the description of Accessibility Config, to more precisely outline what effect does disabling items there have;<br/>
&nbsp;- A couple new special crafting ingredients - Heart of the Cosmos and Heart of the Abyss;<br/>
&nbsp;- Implemented Celestial Fruit;<br/>
&nbsp;- Implemented The Architect's Favor;<br/>
&nbsp;- Implemented The Infinitum (#267);<br/>
&nbsp;- Implemented Curse of Sorrow (#266);<br/>
&nbsp;- Implemented Curse of Eternal Binding (#266).<br/>
<p/><br/><br/>


<h5><b>[Release 2.15.1]:</b></h5>
<p>
&nbsp;- Fixed misplaced config options (#262).<br/>
<p/><br/><br/>


<h5><b>[Release 2.15.0]:</b></h5>
<p>
&nbsp;- Pearl of the Void now devours skyflaps regardless of light level, with the sole condition of them not being on fire (#253);<br/>
&nbsp;- Equipping Magnet Ring or Ring of Dislocation now adds a button to inventory with which their effects can be toggled (#245);<br/>
&nbsp;- Ring of the Seven Curses and associated items now have special tooltip frame and background colors (#258);<br/>
&nbsp;- The Twist will no longer prevent blocking ability of shields when in main hand;<br/>
&nbsp;- An amount of corrections to English localization;<br/>
&nbsp;- Added three more passive abilities for Eye of Nebula;<br/>
&nbsp;- Implemented /haveadv command, which serves no other purpose but checking if caller has certain advancement;<br/>
&nbsp;- Implemented configurable list of tamable entities for curse-altering effect of Guide to Animal Companionship;<br/>
&nbsp;- Guide to Animal Companionship can now be right-clicked with on entity in creative mode to see if it counts as tamable;<br/>
&nbsp;- Added additional requirements for unlocking Potion of Twisted Mercy entry in The Acknowledgment;<br/>
&nbsp;- Fixed a bug with Blazing Core making lava completely opaque instead of increasing transparency by default;<br/>
&nbsp;- Added lore to Ring of the Seven Curses, along with an option to disable displaying it in client config;<br/>
&nbsp;- Client config file version raised to 2.1 (just a reminder: respective file will be fully reset if generated by previous versions of the mod);<br/>
&nbsp;- Implemented Bulwark of Blazing Pride;<br/>
&nbsp;- Implemented Potion of Molten Heart.<br/>
<p/><br/><br/>


<h5><b>[Release 2.14.0]:</b></h5>
<p>
&nbsp;- Fixed server crash upon startup (#246).<br/>
&nbsp;- Fixed client crash upon hovering Keystone of The Oblivion and pressing Ctrl (#244);<br/>
&nbsp;- Fixed Angel's Blessing not actually increasing velocity of own projectiles (#248);<br/>
&nbsp;- Fixed hunger bar being rendered in spectator mode after consuming The Forbidden Fruit;<br/>
&nbsp;- Implemented config option that controls duration of debuffs applied upon consuming The Forbidden Fruit (#247);<br/>
&nbsp;- Player can now retaliate against animals if they somehow end up targeted, bypassing effects of Guide to Animal Companionship (#249);<br/>
&nbsp;- Implemented Potion of Twisted Mercy (#240);<br/>
&nbsp;- Implemented another secret interation;<br/>
&nbsp;- Implemented 7 new advancements.<br/>
<p/><br/><br/>


<h5><b>[Release 2.13.2]:</b></h5>
<p>
&nbsp;- Fixed armor and brewing page templates not rendering in The Acknowledgment (#242);<br/>
&nbsp;- Fixed permanent item entities always spawning at Y=0 if player dies below that point, even if build height goes deeper (#243).<br/>
<p/><br/><br/>


<h5><b>[Release 2.13.1]:</b></h5>
<p>
&nbsp;- Fixed all arthropods being cast to PathfinderMob in event handler, which led to crash with Alex's Mobs installed.<br/>
<p/><br/><br/>


<h5><b>[Release 2.13.0]:</b></h5>
<p>
&nbsp;- Initial 1.18 port.<br/>
<p/><br/><br/>


<h5><b>[Release 2.12.1]:</b></h5>
<p>
&nbsp;- Ported features and fixes from 2.11.10 and 2.11.11;<br/>
&nbsp;- Altered unlocking methods for spellstone and arcane scroll slots to make use of more modern utilities provided by Curios API. This unfortunately means that if these slots were unlocked when using 2.12.0, another pair will be added for the player;<br/>
&nbsp;- Enigmatic Amulet now has more specific lore if received upon entering the world (compared to last 1.16 releases where it now has more generic lore at all times);<br/>
&nbsp;- Fixed minor spelling mistake in English localization;<br/>
&nbsp;- Fixed incorrect display of bound coordinates in tooltip of Extradimensional Eye;<br/>
&nbsp;- Fixed hunger bar being rendered in creative mode after consuming The Forbidden Fruit;<br/>
&nbsp;- Corrected some of the compatibility mixins;<br/>
&nbsp;- Redone tooltip fixes from 2.11.7.<br/>
<p/><br/><br/>


<h5><b>[Release 2.12.0]:</b></h5>
<p>
&nbsp;- Initial 1.17 port. Due to overwhelming amount of changes in Forge and Minecraft itself, this is an experimental beta version released to let people test it, find and report bugs; don't expect unwavering stability yet.<br/>
<p/><br/><br/>


<h5><b>[Release 2.11.12]:</b></h5>
<p>
&nbsp;- Fixed player becoming immortal after applying /kill command, when Blue Skies and Project MMO present (#289);<br/>
&nbsp;- Fixed Astral Sorcery's Resplendent Prism whitelist being invalidated in presence of Enigmatic Legacy (#285).<br/>
<p/><br/><br/>


<h5><b>[Release 2.11.11]:</b></h5>
<p>
&nbsp;- Fixed startup error when Apotheosis is present (#238);<br/>
&nbsp;- Fixed datapack loading error when loading into the world (#239);<br/>
&nbsp;- Fixed incorrect effect positioning by custom wither skulls/thrown potions;<br/>
&nbsp;- Fixed minor spelling mistake in English localization;<br/>
&nbsp;- Altered lore of Enigmatic Amulet to be more generic;<br/>
&nbsp;- Implemented Unwitnessed Amulet (#186);<br/>
&nbsp;- Implemented entity blacklist for Second Curse of Ring of the Seven Curses (#180).<br/>
<p/><br/><br/>


<h5><b>[Release 2.11.10]:</b></h5>
<p>
&nbsp;- Fixed possibility of game freezing when using ancient tomes (#230);<br/>
&nbsp;- Fixed special recipes in the mod (Tome of Hungering Knowledge/Mending Mixture/Keystone of The Oblivion) not working properly on some Bukkit servers, including Mohist (#231);<br/>
&nbsp;- Fixed exploit with Scroll of a Thousand Curses, where holding another Ring of the Seven Curses in offhand would account for +7 curses (#235);<br/>
&nbsp;- Fixed incorrect positioning of Ender Chest button when opening recipe book (#237).<br/>
<p/><br/><br/>


<h5><b>[Release 2.11.9]:</b></h5>
<p>
&nbsp;- Fixed experience exploits with PlayerEx 1.1.8-1.16.5 and earlier (#223). This does sacrifice LevelHearts compatibility, but is neccessary to avoid many other potential experience exploits until we are ready to properly acknowledge alterable nature of XpChange and LevelChange events.<br/>
<p/><br/><br/>


<h5><b>[Release 2.11.8]:</b></h5>
<p>
&nbsp;- Fixed crash upon dedicated server startup (#226).<br/>
<p/><br/><br/>


<h5><b>[Release 2.11.7]:</b></h5>
<p>
&nbsp;- Fixed projectile deflection abilities not working properly against Piercing-enchanted arrows (#219);<br/>
&nbsp;- Fixed lines in item tooltips unpromptly wrapping at times (props to Forge for not addressing this issue for years);<br/>
&nbsp;- Implemented config option that controls how much experience is stored in Extradimensional Vessel upon player's death (#222);<br/>
&nbsp;- Implemented config options that allows to individually disable any enchantments and curses currently present in the mod from being obtainable (#221);<br/>
&nbsp;- Implemented config option that allows to disable all unique drops from vanilla mobs when bearing Ring of the Seven Curses, as well as separate option that allows to control the chance of emerald dropping from any mob when bearing Pact of Infinite Avarice (#210);<br/>
&nbsp;- Re-added Russian localization (#217).<br/>
<p/><br/><br/>


<h5><b>[Release 2.11.6]:</b></h5>
<p>
&nbsp;- Fixed severe increase in RAM usage incurred when repeatedly logging into and out of the world (#208);<br/>
&nbsp;- Fixed AOE interaction with Astral Sorcery's marble/wood, and spawners when Apothesis is installed (#211);<br/>
&nbsp;- Fixed soul loss mechanic not working properly with Corpse Complex installed (#209);<br/>
&nbsp;- Implemented highly efficient state-of-art defense system to prevent Custom Starter Gear from tampering with Enigmatic Legacy's starter gear.<br/>
<p/><br/><br/>


<h5><b>[Release 2.11.5]:</b></h5>
<p>
&nbsp;- Added client config option to disable particle effect of the Astral Breaker (thanks Pbone3, #200);<br/>
&nbsp;- Pearl of the Void now respects PvP rules (#198);<br/>
&nbsp;- Second Curse of Ring of the Seven Curses now respects invisibility effects affecting the bearer (#194);<br/>
&nbsp;- Fixed missing health pool percentage calculations for Emblem of Bloodstained Valor (#190);<br/>
&nbsp;- Will of the Ocean now actually drains player's experience when triggering it's active ability (#182);<br/>
&nbsp;- Hopefully fixed issues with flying when using Gift of the Heaven/Grace of the Creator (#178);<br/>
&nbsp;- Guide to Animal Companionship no longer counts modded neutral mobs as animals (#165);<br/>
&nbsp;- Fixed incorrect Ender Chest button positioning when backpack from Quark Oddities is equipped (#201);<br/>
&nbsp;- Fixed exploit allowing to get around Seventh Curse of Ring of the Seven Curses by using Sleep Charm from Dark Utilities;<br/>
&nbsp;- XpChange and LevelChange events are now dispatched when manipulating player's experience. Fixes interaction of ancient tomes with LevelHearts (#171);<br/>
&nbsp;- Status effect immunity provided by Heart of Creation/Pearl of the Void now prevents effects from being applied in addition to clearing them out every tick. Furthermore, they no longer protect against Chrono Exhaustion debuff from Mana and Artifice (#196).<br/>
<p/><br/><br/>


<h5><b>[Release 2.11.4]:</b></h5>
<p>
&nbsp;- Fixed crash upon dedicated server startup.<br/>
<p/><br/><br/>


<h5><b>[Release 2.11.3]:</b></h5>
<p>
&nbsp;- Updated simplified Chinese localization (thanks FierceManul, #183);<br/>
&nbsp;- Removed Russian localization, as it remains incomplete and does not follow updated tooltip conventions;<br/>
&nbsp;- Fixed bug where night vision mode of Charm of the Treasure Hunter could not be switched by right-clicking;<br/>
&nbsp;- Restored proper equip check that disallows duplicate accessories, now that problematic behavior is fixed on the of Curios.<br/>
<p/><br/><br/>


<h5><b>[Release 2.11.2]:</b></h5>
<p>
&nbsp;- Fixed bug with all equipped accessories being ejected into player inventory when re-entering the world (thanks wchen1990, #161).<br/>
<p/><br/><br/>


<h5><b>[Release 2.11.1]:</b></h5>
<p>
&nbsp;- Fixed mod crashing on dedicated server startup.<br/>
<p/><br/><br/>


<h5><b>[Release 2.11.0]:</b></h5>
<p>
&nbsp;- Initial 1.16.5 port;<br/>
&nbsp;- Attempt fix client-side crash when rendering permanent item pickup with OptiFine installed (#136);<br/>
&nbsp;- Removed handler for finding out unnamed LootPool injections, since an issue with these pools have been fixed on the side of Forge (related - #132);<br/>
&nbsp;- Fixed all loot tables ending up broken if any of the Ancient Tomes are disabled in Accessibility Config (#140);<br/>
&nbsp;- Fixed experience operations performed by variety of items that interact with player's experience (#133, #149);<br/>
&nbsp;- Fixed mod's splash and lingering potions having incorrect movement direction when thrown by a player (#154);<br/>
&nbsp;- Probably fixed config option for inverting magnet rings' shift behavior not working correctly (#153);<br/>
&nbsp;- Fixed Unholy Stone not working if keepInventory is disabled (#157);<br/>
&nbsp;- Fixed crashes with Blazing Core in certain cases when core's ability forces some potion effect to expire prematurely (#143);<br/>
&nbsp;- Implemented config option that allows to prevent Enigmatic Legacy from adding any custom loot to loot tables (#156);<br/>
&nbsp;- Added partial Russian localization (thanks kemika1girl, #120).<br/>
<p/><br/><br/>

<h5><b>[Release 2.10.3]:</b></h5>
<p>
&nbsp;- Implemented handler that purposefully crashes client whenever any mod attempts to inject unnamed LootPool into any loot table (which is an error state), printing out stacktraces that can be examined to see what mod have done this. Such cases have proven incredibly difficult to debug on the side of user, so should be a useful feature. Also added config option that allows to turn it off.
<p/><br/><br/>


<h5><b>[Release 2.10.2]:</b></h5>
<p>
&nbsp;- Fixed Encased Lanterns incorrectly rendering underwater (#121).<br/>
<p/><br/><br/>


<h5><b>[Release 2.10.1]:</b></h5>
<p>
&nbsp;- Fixed bug where Slayer enchantment applied Slowness debuff to monsters even if item with enchantment was held in offhand;<br/>
&nbsp;- Hopefully fixed bug with this mod's enchantments being applicable to items in more than one instance by Enchanting Table;<br/>
&nbsp;- Removed Fruit of Ascension from being registered among mod's items;<br/>
&nbsp;- Fixed typo in Etherium Pickaxe entry in The Acknowledgment (#107);<br/>
&nbsp;- Re-added simplified Chinese localization (thanks FierceManul, #111).<br/>
&nbsp;- The Acknowledgment edition raised to 7.<br/>
<p/><br/><br/>


<h5><b>[Release 2.10.0]:</b></h5>
<p>
&nbsp;- Corrected name of config option responsible for disabling Extradimensional Vessel;<br/>
&nbsp;- Implemented Darkest Scroll (as Bastion Treasure loot), Pact of Infinite Avarice and Essence of Raging Life;<br/>
&nbsp;- Changed recipe of Scroll of a Thousand Curses to involve Darkest Scroll;<br/>
&nbsp;- Removed options responsible for tweaking World Name Randomizer from client config;<br/>
&nbsp;- Added mixin to force dispatch LootTableLoadEvent to Enigmatic Legacy's handler when Forge refuses to do so;<br/>
&nbsp;- Fixed Emblem of Bloodstained Valor and Scroll of a Thousand Curses being equippable in more than one instance if player has more than one slot for either;<br/>
&nbsp;- The Acknowledgment edition raised to 6;<br/>
&nbsp;- Added Build Resource Updater to the project build, because apparently Forge stopped supporting similar feature which existed in the past somewhere after 1.7.10;<br/>
&nbsp;- Altered project building to make Eclipse runs properly work.<br/>
<p/><br/><br/>


<h5><b>[Release 2.9.2]:</b></h5>
<p>
&nbsp;- This and all subsequent releases will now be marked as 1.16.3-compatible when uploading;<br/>
&nbsp;- The Seventh Curse of Ring of the Seven Curses now causes Phantoms to spawn regardless of when player have last visited their bed;<br/>
&nbsp;- Excluded next major Minecraft release from being listed as compatible in mods.toml.<br/>
<p/><br/><br/>


<h5><b>[Release 2.9.1]:</b></h5>
<p>
&nbsp;- World Name Randomizer once again moved into separate mod: https://www.curseforge.com/minecraft/mc-mods/world-name-randomizer<br/>
&nbsp;- Glorified logger wrapper;<br/>
&nbsp;- Support of Enum types and filtered string in Omniconfig (yet unused);<br/>
&nbsp;- Designed IItemCurio interface to implement on items rather than doing so with ICurio;<br/>
&nbsp;- Implemented small compat with Apotheosis to make sure Enchanter's Pearl works with it (#103);<br/>
&nbsp;- Some more log output for PermanentItemEntities to indicate when player picks them up;<br/>
&nbsp;- Piglins are now affected by the Second Curse of Ring of the Seven Curses;<br/>
&nbsp;- Hopefully fixed Pearl of the Void crashes in certain instances when removing multiple potion effects from player;<br/>
&nbsp;- Added localization entries on every enchantment for Enchantment Descriptions (#102);<br/>
&nbsp;- A bit overhauled Unholy Grail texture;<br/>
&nbsp;- Ennobled project building and changelog file format.<br/>
<p/><br/><br/>


<h5><b>[Release 2.9.0]:</b></h5>
<p>
&nbsp;- Fixed bug where contents of the inventory of The Architect's Inkwell vanished upon closing it;<br/>
&nbsp;- Most accessories are now legitimately enchantable with Curse of Binding. This time for sure!;<br/>
&nbsp;- Torrent enchantment is now effective against Ender Dragon;<br/>
&nbsp;- Listed Soul Crystal in mod's creative tab, and added tooltip to it indicating it can be used to restore lost crystals by right-clicking with it. Using the crystal as item now also produces particle effect identical to absorption of it's entity form;<br/>
&nbsp;- Changed license reference in mods.toml to prompt new license there;<br/>
&nbsp;- This and all subsequent modfile releases now include full copy of The Copybruh License document;<br/>
&nbsp;- Implemented Enchanter's Pearl, Exquisite Ring and Unholy Stone;<br/>
&nbsp;- Changed the implementation of Guide to Animal Companionship to render Hoglins neutral to new one made through Mixin;<br/>
&nbsp;- Implemented config option that allows to make it so that the Ring of the Seven Curses will be automatically equipped into one of the player's ring slots when they enter the world for the first time, instead of just being added to their inventory;<br/>
&nbsp;- Rollback to previous version of AdvancedSpawnLocationHelper, because new one breaks badly when trying to teleport player to world's spawn point for some reason;<br/>
&nbsp;- The Acknowledgment edition raised to 5.<br/>
<p/><br/><br/>


<h5><b>[Release 2.8.0]:</b></h5>
<p>
&nbsp;- Pearl of the Void no longer clears Night Vision effects provided by Charm of the Treasure Hunter and Will of the Ocean (#100);<br/>
&nbsp;- Black Enigmatic Amulet nerfed to provide only 10% Lifesteal, instead of previous 15%;<br/>
&nbsp;- Will of the Ocean once again reduces damage from Drowned, because go screw yourself vanilla logic;<br/>
&nbsp;- Added elemental vulnerabilities to Angel's Blessing, Will of the Ocean, Blazing Core and Eye of Nebula (#71);<br/>
&nbsp;- At long last, finally properly set up the Mixin connection for further use;<br/>
&nbsp;- Implemented three new enchantments: Slayer, Torrent and Wrath;<br/>
&nbsp;- Removed wrong third page in The Acknowledgment entry of Ceaseless enchantment;<br/>
&nbsp;- The Acknowledgment edition raised to 4.<br/>
<p/><br/><br/>


<h5><b>[Release 2.7.3]:</b></h5>
<p>
&nbsp;- Defeated the invasion of Enigmatic Amulets (#95).<br/>
<p/><br/><br/>


<h5><b>[Release 2.7.2]:</b></h5>
<p>
&nbsp;- Fixed Ceaseless enchantment STILL requiring arrow in inventory to work, once and for all;<br/>
&nbsp;- Fixed arrows shot with Ceaseless enchantment being perfectly pickapable from the ground like nobody's business;<br/>
&nbsp;- Creative tab of Enigmatic Legacy now contains all color variations of Enigmatic Amulet;<br/>
&nbsp;- Renamed some of the config options for Enigmatic Amulet for the sake of case consistency;<br/>
&nbsp;- Added config option to allow simultaneously equipping multiple Enigmatic Amulets (if player somehow has more than one charm slot);<br/>
&nbsp;- Added config option to allow simultaneously equipping multiple spellstones if they are different items (if player somehow has more than one spellstone slot).<br/>
<p/><br/><br/>


<h5><b>[Release 2.7.1]:</b></h5>
<p>
&nbsp;- Fixed Forbidden Fruit checks crashing players on server if two or more are within render distance of one another (thanks Flynatol, #92);<br/>
&nbsp;- Hopefully fixed Ceaseless enchantment still requiring arrow in inventory to work.<br/>
<p/><br/><br/>


<h5><b>[Release 2.7.0]:</b></h5>
<p>
&nbsp;- Added option to client config that allows to turn off mod update notifications;<br/>
&nbsp;- Slightly overhauled texture of Extradimensional Vessel;<br/>
&nbsp;- Ring of the Seven Curses is now accounted for as item with 7 curses by Scroll of a Thousand Curses (#84);<br/>
&nbsp;- Scroll of a Thousand Curses and Emblem of Bloodstained Valor now have an indication of current total traits they provide when equipped;<br/>
&nbsp;- Scroll of a Thousand Curses now provides +7% mining speed boost by default for every curse;<br/>
&nbsp;- Config option for disabling Enigmatic Amulet now actually disables it from being gifted to players upon entering the world (#86);<br/>
&nbsp;- Most items in the mod can now be enchanted with Curse of Vanishing. Many couldn't be before;<br/>
&nbsp;- Most wearables can now be enchanted with Curse of Binding through Anvil;<br/>
&nbsp;- Chance of Netherite Scrap dropping from Piglin Brutes is nerfed from 50% to 20%, and from Wither Skeletons from 20% to 7% when player bears Ring of the Seven Curses;<br/>
&nbsp;- Fixed bug where players bearing Ring of the Seven Curses were able to deal full damage to monsters when using crossbow with Sharpshooter enchantment;<br/>
&nbsp;- Enigmatic Amulet that player receives when entring the world for the first time will now have randomly chosen one out of seven possible colors, with each color having it's own bonus trait. Amulets that existed before this update will have their color rerolled based on owner's username (#28);<br/>
&nbsp;- Ring of The Seven curses no longer angers neutral mobs if bearer is in Spectator mode;<br/>
&nbsp;- Ceaseless enchantment no longer demands an arrow in the inventory to shoot basic arrows. Also there is config option to make it required as it was;<br/>
&nbsp;- Bees are once again angered agaist bearers of Ring of the Seven Curses by default, in view of Guide to Animal Companionship with it's ability to counter that being introduced;<br/>
&nbsp;- Will of the Ocean now includes functionality of Aqua Affinity enchantment, does prevent air bar from rendering and negates gravity affecting player underwater;<br/>
&nbsp;- Scrapped most of reflection in favor of access transformations;<br/>
&nbsp;- Most numerical parameters affecting Ring of the Seven Curses and related items are now exposed as config variables;<br/>
&nbsp;- Implemented six new items: Guide to Animal Companionship, Guide to Feral Hunt, The Twist, The Forbidden Fruit, Potion of Redemption and Nefarious Essence;<br/>
&nbsp;- Added The Acknowledgment entry which provides a bit of lore about ancient tomes [Tattered/Withered/Corrupted], as well as a glimpse into their purpose;<br/>
&nbsp;- Removed Chineese and Russian localization. Old files are still stored in lang directory with "[OUTDATED]" suffix, in case they will be of any use to whoever might decide to help us updating those localizations, but so far they were not touched for a long time and the amount of changes brough to the mod in the meantime rendered them horribly broken;<br/>
&nbsp;- Fixed completely black "Heat" title above heat bar rendered by Blazing Core. Okay I'm not even gonna ask anymore why nobody reports this stuff;<br/>
&nbsp;- Permanent item entites, which include Extradimensional Vessels and Soul Crystals, can no longer be moved between dimensions. Also added log output to indicate when they are removed due to being attacked by absolute damage source or whatever other reason, to simplify investigation on potential future bug reports related to them;<br/>
&nbsp;- Common config version raised to 2.1.<br/>
<p/><br/><br/>


<h5><b>[Release 2.6.1]:</b></h5>
<p>
&nbsp;- Fixed variety of crashes that may have been caused by non-assigned config property in item class of Emblem of Monster Slayer (#85);<br/>
&nbsp;- Neutral mobs no longer anger against players with Ring of the Seven Curses equipped when they are in creative mode.<br/>
<p/><br/><br/>


<h5><b>[Release 2.6.0]:</b></h5>
<p>
&nbsp;- Brand new config implementation, ported right from 1.7.10 versions of Forge, with a lot of polishing and fancy wrapping on top of it. Notice that new config files have '.omniconf' extension; old '.toml' are not generated and not loaded anymore;<br/>
&nbsp;- Among particularly noticeable improvements, most options in common file are now automatically synchronized from server to client;<br/>
&nbsp;- Accessibility config options for most items are now generated and handled mostly automatically;<br/>
&nbsp;- Astral Breaker's AOE mining now has respective config options;<br/>
&nbsp;- Heart of Creation now provides immunity only to debuffs instead of all status effects (#17);<br/>
&nbsp;- Spiders now drop strings instead of slime balls with Ring of the Seven Curses equipped, as was initially intended;<br/>
&nbsp;- Emblem of Bloodstained Valor now increases movement speed regardless of whether or not the player is sprinting (#81);<br/>
&nbsp;- The Acknowledgment is now enchantable, but solely with Bane of Arthropods (#81);<br/>
&nbsp;- Neutral mobs no longer anger against players who bear Ring of the Seven Curses, unless there is a direct line of sight between them and the player, aka don't see players through walls anymore. Well, most of the time. They can still see the cursed players regardless of obstacles if the distance separating the player from them is below 4 blocks (#81);<br/>
&nbsp;- Endermen generally follow the abovementioned rule, but sometimes they might randomly disregard it and teleport behind unsuspecting player seemingly out of nowhere (#81);<br/>
&nbsp;- +1 Joke in Twisted Mirror entry in The Acknowledgment (#81);<br/>
&nbsp;- Significantly expanded The Acknowledgment entry on Ring of the Seven Curses, to provided explanation on how every curse works. Many players have found some of them unclear, like insomnia for instance, so that should be now addressed. Blessings did not receive as much attention, but it's now mentioned that they come along with the ring as permanent traits, similar to how curses do.<br/>
<p/><br/><br/>


<h5><b>[Release 2.5.3]:</b></h5>
<p>
&nbsp;- Removed unintended overrides of vanilla loot tables that someone forgot to delete (totally not me) (#80).<br/>
<p/><br/><br/>


<h5><b>[Release 2.5.2]:</b></h5>
<p>
&nbsp;- Fixed Heart of Creation crashing the server when attempting to use it's active ability (why nobody reported this?);<br/>
&nbsp;- Fixed active abilities of spellstones not having any cooldown at all (okay, but THIS?..);<br/>
&nbsp;- Fixed The Architect's Inkwell crashing the server when attempting to open it's GUI (#76).<br/>
<p/><br/><br/>


<h5><b>[Release 2.5.1]:</b></h5>
<p>
&nbsp;- Fixed crash on server startup (#75).<br/>
<p/><br/><br/>


<h5><b>[Release 2.5.0]:</b></h5>
<p>
&nbsp;- Fixed Astral Breaker recipe not being disabled by it's config option (#72);<br/>
&nbsp;- Implemented config option that allows to disable Ring of the Seven Curses from being granted to players upon first world visit (#67);<br/>
&nbsp;- Ancient tomes can now be used as furnace fuel (#68);<br/>
&nbsp;- Upon reading an ancient tome there's now a special toast indicating how much experience and knowledge points it granted;<br/>
&nbsp;- Scrapped custom implementation of Fortune/Looting bonuses in favor of one now provided by Curios API;<br/>
&nbsp;- Neutral mobs angered by Ring of the Seven Curses now exclude tamed animals, player-constructed Iron Golems and bees (#69);<br/>
&nbsp;- Decreased the range in which neutral mobs are rendered hostile towards bearers of Ring of the Seven Curses from 32 blocks radius to 24;<br/>
&nbsp;- Implemented two new enchantments for crossbows, Sharpshooter and Ceaseless (merged from Armorless & More);<br/>
&nbsp;- Implemented Scroll of a Thousand Curses, Emblem of Bloodstained Valor and Curse of Nemesis (#70);<br/>
&nbsp;- Corrected wording of a couple advancements to better correspond to possible means of aquiring them;<br/>
&nbsp;- Fixed The Acknowledgment content not loading properly when some of the mod's brewing recipes are disabled through config (#74);<br/>
&nbsp;- Addressed some obscure wording and inconsistent text coloring in item tooltips;<br/>
&nbsp;- Pearl of the Void now only applies Withering through direct attacks;<br/>
&nbsp;- Heart of the Golem's vulnerability to magic damage now includes damage from withering. Explosion protection trait is increased to 50% resistance by default, but is only provided with no armor equipped;<br/>
&nbsp;- Will of the Ocean's damage reduction against underwater creatures no longer includes drowned, to ensure consistency with vanilla logic;<br/>
&nbsp;- Blazing Core now provides only temporary immunity to lava;<br/>
&nbsp;- Pearl of the Void now provides complete immunity to any status effects. Removed it's trait that prevents hunger from affecting the bearer. Chance of death prevention is buffed from 15% to 35%;<br/>
&nbsp;- Resistance to magic damage of Eye of Nebula now includes withering damage;<br/>
&nbsp;- Etherium Armor set bonus no longer provides debuff immunity;<br/>
&nbsp;- Slightly overhauled texture of Charm of the Treasure Hunter;<br/>
&nbsp;- Implemented config options that allow to adjust dealt and received damage multipliers that bearers of the Ring of the Seven Curses are affected by;<br/>
&nbsp;- Disabled attribute tooltip on Will of the Ocean due to being redundant;<br/>
&nbsp;- Enigmatic Legacy now automatically triggers "minecraft:recipe_unlocked" trigger for every recipe player has unlocked when they join the world. Required due to some recipes in the mod requiring certain parent recipes to be unlocked, and new recipes added may never have this condition fulfilled. Also should be a good practice overall;<br/>
&nbsp;- Raised the Config Version to 1.2.<br/>
<p/><br/><br/>


<h5><b>[Release 2.4.0]:</b></h5>
<p>
&nbsp;- Initial 1.16.3 port. Should be backwards compatible with 1.16.2 but don't blame me if stuff breaks if you run it there.<br/>
<p/><br/><br/>


<h5><b>[Release 2.3.0]:</b></h5>
<p>
&nbsp;- Intermediate 1.16.2 port. No further support for this version in specific will be provided.<br/>
<p/><br/><br/>


<h5><b>[Release 2.2.1]:</b></h5>
<p>
&nbsp;- Changed Scroll of Postmortal Recall mechanic to teleport player's dropped items to their respawn point, instead of teleporting themselves. Fixes crash when recalling after being killed by neutral mob (#66).<br/>
<p/><br/><br/>


<h5><b>[Release 2.2.0]:</b></h5>
<p>
&nbsp;- Angel's Blessing now actually redirects projectiles through ProjectileImpactEvent handler instead of cloning them in LivingAttackEvent;<br/>
&nbsp;- Due to the previous, a bug is fixed where redirected tridents for some reason were unable to deal damage;<br/>
&nbsp;- The Architect's Inkwell now has it's own GUI for name/lore editing on Lore Fragments. Doing has no experience cost, unlike previously used Anvil operations;<br/>
&nbsp;- Expanded Inkwell's entry in The Acknowledgment to include overall guide on how to use operational and formatting codes;<br/>
&nbsp;- Made use of vanilla cooldown tracker to visually display cooldown on spellstone's active abilities and ability of Etherium Broadsword;<br/>
&nbsp;- Special ability of Etherium Broadsword can now be suppressed in a fashion similar to AOE mining abilities of other tools (#53);<br/>
&nbsp;- Fixed bug with Tile Entities bound to block being completely ignored when spawning block drops (#56);<br/>
&nbsp;- Fixed bug with NullPointerException being thrown whenever attempting to alter The Acknowledgment read/unread entry state while viewedEntries data is not generated yet by Patchouli;<br/>
&nbsp;- Fixed Etherium Axe/Astral Breaker being inefficient against warped/crimson stems and their derivative blocks (#59);<br/>
&nbsp;- Hopefully fixed possibility of gaining extreme velocities when using Angel's Blessing with Elytra (#65);<br/>
&nbsp;- Fixed Night Vision effect from items that provide it not being properly removed if player has Blazing Core equipped (#61);<br/>
&nbsp;- Nerfed attack damage of The Acknowledgment (#63);<br/>
&nbsp;- Implemented Ring of the Seven Curses, Twisted Mirror, Heart of the Earth and Twisted Heart;<br/>
&nbsp;- Ring slots are now unlocked by default, since Ring of the Seven Curses is guaranteed to be available to player right off the start;<br/>
&nbsp;- Charm of the Treasure Hunter now provides +1 Fortune Level instead of +1 Luck. Changed it's recipe to require Heart of the Earth;<br/>
&nbsp;- Spellstones are now generally more rare.<br/>
<p/><br/><br/>


<h5><b>[Release 2.1.3]:</b></h5>
<p>
&nbsp;- Closing Ender Chest inventory opened by Ring of Ender is now followed by appropriate sound effect;<br/>
&nbsp;- When having Ring of Ender equipped, special button is now added to inventory GUI that allows to open Ender Chest inventory. Also implemented a couple config options that allow to control existence and position of that button.<br/>
<p/><br/><br/>


<h5><b>[Release 2.1.2]:</b></h5>
<p>
&nbsp;- Fixed startup error caused by attempts to reset null config object when generating config file (#52).<br/>
<p/><br/><br/>


<h5><b>[Release 2.1.1]:</b></h5>
<p>
&nbsp;- Fixed critical error on startup caused by screwed up mods.toml file.<br/>
<p/><br/><br/>


<h5><b>[Release 2.1.0]:</b></h5>
<p>
&nbsp;- Magnetic Ring's effects produce particles no longer;<br/>
&nbsp;- Nerfed Angel's Blessing projectile redirection ability (#14). Now grants chance to deflect incoming projectiles instead of redirecting all of them away;<br/>
&nbsp;- Tools with area of effect abilities now allow to toggle these abilities on/off by shift+right-clicking. Also addded config option that allows to disable temporary suppression of such abilities by holding Shift;<br/>
&nbsp;- Added recipe linkages for Potion of Recall and all ultimate potion types in The Acknowledgment;<br/>
&nbsp;- Hopefully fixed mining speed corrections in the mod disrespecting other mods' attempts to alter it (#50);<br/>
&nbsp;- Fixed multiple bugs related to integrated World Name Randomizer functionality. Also implemented client-sided config option to allow disabling it (#51);  
&nbsp;- Fixed Etherium Scythe's AoE-tilling not working.<br/>
<p/><br/><br/>


<h5><b>[Release 2.0.0]:</b></h5>
<p>
&nbsp;- Implemented The Acknowledgment, vessel of knowledge about everything in the mod... and not only;<br/>
&nbsp;- Due to the former, Patchouli is now listed among mandatory dependencies*;<br/>
&nbsp;- Implemented three variations of ancient tomes, each added to the loot of respective dungeons;<br/>
&nbsp;- Updated loot table list of The One Box, to include nether bastion chests;<br/>
&nbsp;- Some of the new loot added by the mod now also generates in those chests;<br/>
&nbsp;- Altered some of the advancements that unlock recipes to player, to also require recipes of certain component items to be unlocked. Most of them now also instantly unlock the recipe when player obtains crafting result through other means;<br/>
&nbsp;- Optional Soul Loss mechanic can now be enabled through config.<br/><br/>
* I know it's "Dependecy-Free" and all, but I already ended up using multiple hooks outside of generic API, and that number will likely only increase as I attempt to tweak more custom functionality into our Enigmatinomicon. Besides, I never wanted it to be optional addition in the first place.<br/>
<p/><br/><br/>


<h5><b>[Release 1.8.7]:</b></h5>
<p>
&nbsp;- Guess here we patch again...<br/>
&nbsp;- Code of custom triggers actually makes sense when you look at it;<br/>
&nbsp;- Fixed crash when using keybind for spellstone active ability when spellstone slot is not unlocked yet (#47);<br/>
&nbsp;- Fixed Curios slots not being properly unlocked when they are supposed to be. Re-entering worlds when this issue occured should also fix it (#48).<br/>
<p/><br/><br/>


<h5><b>[Release 1.8.6]:</b></h5>
<p>
&nbsp;- Fixed crash with Optifine on Forge versions past 32.0.98 when rendering Extradimensional Vessel (#44);<br/>
&nbsp;- Implemented config options to adjust damage bonus stat provided by Enigmatic Amulet, as well as one to disable it's ability to summon Extradimensional Vessel. Also added option to prevent anyone besides original owner from picking up their Extradimensional Vessel (#45, #38);<br/>
&nbsp;- Before gods I swear, this is the last patch I am making until 2.0 release.<br/>
<p/><br/><br/>


<h5><b>[Release 1.8.5]:</b></h5>
<p>
&nbsp;- Scroll of Ageless Wisdom doesn't Agaless anymore;<br/>
&nbsp;- Fixed dedicated server crash when attempting to generate config files (#41);<br/>
&nbsp;- Potion of Recall and Scroll of Postmortal Recall are now capable of teleporting player across vanilla dimensions. Improved their algorithm to correctly seek respawn point in case player has no valid bed/anchor location. Their description also got altered to indicate the fact that they are capable of working with Respawn Anchors;<br/>
&nbsp;- Attempted to overhaul texture of The Architect's Inkwell. I really hope I didn't make it look worse than it did before.<br/>
<p/><br/><br/>


<h5><b>[Release 1.8.4]:</b></h5>
<p>
&nbsp;- Fixed item names not displaying correctly in list of consumable items of Keystone of The Oblivion (#40);<br/>
&nbsp;- Altered recipe of Emblem of Monster Slayer, to involve some new items from Nether Update (#37);<br/>
&nbsp;- Buffed armor toughness values of etherium armor from **** on each piece to ****, to surpass those of netherite armor;<br/>
&nbsp;- The One Box now looks a bit more like a box. Also doesn't prest anymore.<br/>
<p/><br/><br/>


<h5><b>[Release 1.8.3]:</b></h5>
<p>
&nbsp;- A little overhaul of mod's logo and updated description in mods.toml;<br/>
&nbsp;- Entirely removed sound and particle effects produced by Ring of Dislocation;<br/>
&nbsp;- Implemented new mechanic for Enigmatic Amulet, outlined in it's Shift-tooltip. Removed +2 armor stat bonus from it;<br/>
&nbsp;- Added lamps and encased lamps, with three variations being available;<br/>
&nbsp;- PermanentItemEntities created by some items upon player's death will now spawn at y=0 if player dies further below in the Void. Also, they are now visible from up to 64 blocks away;<br/>
&nbsp;- Technically completed soul loss mechanic. Will remain disabled, however, until implementation of in-game guidebook where details about it will be outlined for affected players;<br/>
&nbsp;- Axe of Executioner now takes two Netherite Ingots to be created, instead of iron ones previously used; 
&nbsp;- Following items are now immune to fire and lava, similar to netherite gear: etherium ore, ingots, armor, tools and weapons, Astral Breaker, Enigmatic Amulet, Heart of Creation, Axe of Executioner, Blazing Core, Keystone of The Oblivion, Unholy Grail, Void Pearl;<br/>
&nbsp;- Blazing Core now significantly improves visibility when submerged in lava, among it's other abilities.<br/>
<p/><br/><br/>


<h5><b>[Release 1.8.2]:</b></h5>
<p>
&nbsp;- Fixed dedicated server crashing when attempting to use active abilities of spellstones (#33).<br/>
<p/><br/><br/>


<h5><b>[Release 1.8.1]:</b></h5>
<p>
&nbsp;- Fallback to classical reflection via ObfuscationReflectionHelper;<br/>
&nbsp;- Void Pearl now once again nullifies saturation values;<br/>
&nbsp;- Implemented functionality of World Name Randomizer (as if someone asked for it);<br/>
&nbsp;- Gift of the Heaven now only works within the range of active beacon. Experience cost halved;<br/>
&nbsp;- Implemented Grace of the Creator, as direct upgrade from Gift of the Heaven;<br/>
&nbsp;- Mending Mixture now takes ghast tear to be created;<br/>
&nbsp;- Wither Skulls summoned by Heart of Creation now home in on observed entity. Also, new animation for indicating stronger skulls;<br/>
&nbsp;- Implemented null check in LootingLevelEvent event listener, to prevent potential issues (#31);<br/>
&nbsp;- Etherium Pickaxe and Astral Breaker are now effective against shulker boxes.<br/>
<p/><br/><br/>


<h5><b>[Release 1.8.0]:</b></h5>
<p>
&nbsp;- Initial 1.16.1 port;<br/>
&nbsp;- Lots of code rewriting and optimizations;<br/>
&nbsp;- Failed attempts to make use of Mixin.<br/>
<p/><br/><br/>


<h5><b>[Release 1.7.0]:</b></h5>
<p>
&nbsp;- Initial 1.15.2 port;<br/>
&nbsp;- Reduced amount of particles produced by Astral Breaker.<br/>
<p/><br/><br/>


<h5><b>[Release 1.6.4]:</b></h5>
<p>
&nbsp;- Fixed side violation on dedicated servers caused by Eye of the Nebula (#15);<br/>
&nbsp;- Unequipping Heart of Creation and Gift of the Heaven no longer causes flight to be disabled while in Creative Mode (#16);<br/>
&nbsp;- Removed excessive log output in BlockDropsHarvest event handler (#20);<br/>
&nbsp;- Astral Breaker and Dislocation Ring now respect client particle settings (#26).<br/>
<p/><br/><br/>


<h5><b>[Release 1.6.3]:</b></h5>
<p>
&nbsp;- Added simplified Chinese translation (thanks to LyricaPrismriver) #21.<br/>
<p/><br/><br/>


<h5><b>[Release 1.6.2]:</b></h5>
<p>
&nbsp;- Magnetic Ring and Dislocation Ring now respect Demagnetize.<br/>
<p/><br/><br/>


<h5><b>[Release 1.6.1]:</b></h5>
<p>
&nbsp;- Removed Universal Clock HUD. Now available as a separate mod: https://www.curseforge.com/minecraft/mc-mods/universal-clock-hud
<p/><br/><br/>


<h5><b>[Release 1.6.0]:</b></h5>
<p>
&nbsp;- Implemeted global changelog. Included as part of the mod file;<br/>
&nbsp;- Implemeted Tome of Hungering Knowledge.<br/>
<p/><br/><br/>


<h5><b>[Release 1.5.3]:</b></h5>
<p>
&nbsp;- Reclassified most sound events played by mod from neutral to players category;<br/>
&nbsp;- Lowered mining speed bonus provided by Charm of the Treasure Hunter to 30% (from previous 50%), to prevent players from gaining instamine on stone and similar blocks when using Diamond Pickaxe with Efficiency V;<br/>
&nbsp;- Increased light tolerance of Charm of the Treasure Hunters, so that it's not affected by weaker light sources, like lit Redstone Ore or Redstone Torch. Now works in light levels of 8 and below;<br/>
&nbsp;- Angel's Blessing now accelerates player's own projectiles (instead of slowing them down);<br/>
&nbsp;- Will of the Ocean is no longer capable of consuming experience from Scroll of Ageless Wisdom when one is equipped;<br/>
&nbsp;- Universal Clock HUD is now handled in RenderGameOverlayEvent.Post to avoid being overlapped by other elements, like vignette. Likely will soon be moved into a separate mod;<br/>
&nbsp;- Config versioning system is changed to also reset all values upon updating from versions of the mod that didn't have it implemented yet;<br/>
&nbsp;- Config version is changed to 1.1.<br/>
<p/><br/><br/>


<h5><b>[Release 1.5.2]:</b></h5>
<p>
&nbsp;- Implemented config option that allow to make Universal Clock HUD only displayed in fullscreen mode;<br/>
&nbsp;- Fixed overwriting any stack in targeted slot when adding Enigmatic Amulet to player's inventory.<br/>
<p/><br/><br/>


<h5><b>[Release 1.5.1]:</b></h5>
<p>
&nbsp;- Implemented Universal Clock HUD and a bunch of client-sided config options for it.<br/>
<p/><br/><br/>


<h5><b>[Release 1.5.0]:</b></h5>
<p>
&nbsp;- [DELETED].<br/>
<p/><br/><br/>


<h5><b>[Release 1.4.2]:</b></h5>
<p>
&nbsp;- Fixed player crashing on server upon equipping Pearl of the Void.<br/>
<p/><br/><br/>


<h5><b>[Release 1.4.1]:</b></h5>
<p>
&nbsp;- [DELETED].<br/>
<p/><br/><br/>


<h5><b>[Release 1.4.0]:</b></h5>
<p>
&nbsp;- Changed the way Charm of the Treasure Hunter and Will of the Ocean apply Night Vision effect to ultimately prevent rendering issues. Deprecated related config options;<br/>
&nbsp;- Changed the wording of Etherium Armor tooltip. Since obviously it remains unobvious for some people out there;<br/>
&nbsp;- Implemented Astral Breaker;<br/>
&nbsp;- Implemented Keystone of The Oblivion;<br/>
&nbsp;- Optimized capabilities registration for curios;<br/>
&nbsp;- Changed the way of collecting experience orbs by Scroll of Ageless Wisdom, now calls .onCollideWithPlayer() method similarly to Dislocation Ring;<br/>
&nbsp;- Implemented bonus recipes for more effective wool dyeing.<br/>
<p/><br/><br/>


<h5><b>[Release 1.3.4]:</b></h5>
<p>
&nbsp;- Fixed generating tridents within every single chest in underwater ruins;<br/>
&nbsp;- Fixed receiving Fearsome Vengeance advancement when beheading a Wither Skeleton ordinarily, without holding Axe of Executioner;<br/>
&nbsp;- Fixed dupe with Dislocation Ring (critical in multiplayer);<br/>
&nbsp;- Implemented config option for inverting Shift behaviour of Magnetic Ring and Dislocation Ring;<br/>
&nbsp;- Implemented config options for controlling the duration of Night Vision effect applied by Charm of the Treasure Hunter and Will of the Ocean.<br/>
<p/><br/><br/>


<h5><b>[Release 1.3.3]:</b></h5>
<p>
&nbsp;- Fixed crash with LivingExperienceDropEvent handler;<br/>
&nbsp;- Magnetic Ring and Dislocation Ring no longer work if player cannot pick up attracted/teleported items due to having their inventory full.<br/>
<p/><br/><br/>


<h5><b>[Release 1.3.2]:</b></h5>
<p>
&nbsp;- Angel's Blessing is no longer capable of redirecting Wither skulls;<br/>
&nbsp;- Wither Skulls shot by Heart of Creation now explode after existing for 20 seconds. They are also no longer removed upon world reloading, do not destroy blocks that are supposed to be indestructible and raytrace for target within 128 blocks distance, instead of previous 64;<br/>
&nbsp;- Etherium Scythe can now till dirt and grass blocks in 3x3x1 area;<br/>
&nbsp;- Added Ice, Packed Ice and Glass to the list of effective materials for Etherium Pickaxe;<br/>
&nbsp;- Added config option to disable generation of Etherium Ore as dungeon loot in End Cities;<br/>
&nbsp;- Replaced temporary texture of The Architect's Inkwell.<br/>
<p/><br/><br/>


<h5><b>[Release 1.3.1]:</b></h5>
<p>
&nbsp;- Fixed server crash upon any player opening Anvil (issue #6).<br/>
<p/><br/><br/>


<h5><b>[Release 1.3.0]:</b></h5>
<p>
&nbsp;- Increased attack cooldown of Axe of Executioner to that of a regular sword;<br/>
&nbsp;- Implemented Etherium, alongside with tools and armor made of it;<br/>
&nbsp;- Implemented The Architect's Inkwell and Lore Fragment;<br/>
&nbsp;- Implemented proper exception handling in onPlayerJoin event (related to issue #5);<br/>
&nbsp;- Expanded immunity list of Heart of Creation and added active ability, also changed it's tooltip to have more correct wording and be more similar to other spellstones;<br/>
&nbsp;- Heart of Creation and Gift of the Heaven now compensate mining speed penalty while in the air;<br/>
&nbsp;- All curios in the mod changed so that player cannot equip duplicate trinkets. Mainly affects rings;<br/>
&nbsp;- Significant optimizational changes into functional implementation of config;<br/>
&nbsp;- A bunch of new config options;<br/>
&nbsp;- Implemented config versioning;<br/>
&nbsp;- Added some new advancements;<br/>
&nbsp;- Added Russian localization.<br/>
<p/><br/><br/>


<h5><b>[Release 1.2.2]:</b></h5>
<p>
&nbsp;- Fixed broken code of PacketHandleItemPickup.<br/>
<p/><br/><br/>


<h5><b>[Release 1.2.1]:</b></h5>
<p>
&nbsp;- Updated to Forge 28.1.0.<br/>
<p/><br/><br/>


<h5><b>[Release 1.2.0]:</b></h5>
<p>
&nbsp;- Implemented advanced potions and related Astral Dust;<br/>
&nbsp;- Potions of Haste are merged into advanced potion system instead of being presented as separate items;<br/>
&nbsp;- The One Box now has the ability to simulate loot generation in 32768 instances, printing the results to log file;<br/>
&nbsp;- Changed Scroll of Postmortal Recall to create newly implemented PermanentItemEntity instead of regular dropped item;<br/>
&nbsp;- Fixed inevitable crash when on server startup with the mod, due to attempts to use client-only classes on server side (issue #2);<br/>
&nbsp;- Fixed crash caused by renamed method for getting persistent entity NBT (issue #3);<br/>
&nbsp;- Implemented update notifications;<br/>
&nbsp;- Additional logger output;<br/>
&nbsp;- Overall cleanup.<br/>
<p/><br/><br/>


<h5><b>[Release 1.1.0]:</b></h5>
<p>
&nbsp;- Fixed Mending Mixture recipe returning empty glass bottles. Now repair recipe it is used in does this;<br/>
&nbsp;- Pearl of the Void no longer harms other bearers of the pearl;<br/>
&nbsp;- Will of the Ocean will now consume experience from Scroll of Ageless Wisdom, if player has one equipped and if there is enough experience stored in it;<br/>
&nbsp;- Removed ability to open Ender Chest inventory by simply right-clicking with Ring of Ender, without equipping it and using keybind;<br/>
&nbsp;- Fixed fatal error appearing when player dies while /gamerule keepInventory is set to true;<br/>
&nbsp;- Some cleanup in lore functions;<br/>
&nbsp;- Implemented config file;<br/>
&nbsp;- Default cooldown of active ability of Eye of Nebula increased to 3 seconds;<br/>
&nbsp;- Default cooldown of active ability of Angel's Blessing increased to 2 seconds;<br/>
&nbsp;- Default mining speed boost from Charm of Treasure Hunter decreased to 50%.<br/>
<p/><br/><br/>


<h5><b>[Release 1.0.1]:</b></h5>
<p>
&nbsp;- Changed effects of Recall Potion and Scroll of Postmortal Recall so that they will return player to obsidian platform if they are in the End;<br/>
&nbsp;- Fixed bug with Extradimensional Eye not checking the dimension it is bound to;<br/>
&nbsp;- Fixed bug with players loosing unlocked Curio slots upon death;<br/>
&nbsp;- Attempt to fix new dungeon loot not generating in some cases.<br/>
<p/><br/><br/>


<h5><b>[Release 1.0.0]:</b></h5>
<p>
&nbsp;- Initial release.
<p/>