package com.github.manasmods.tensura.util;

import java.util.function.Function;
import java.util.function.Supplier;
import org.jetbrains.annotations.Nullable;

public class Cached<T, B> {
   private T lastValue;
   private final Supplier<T> updatedValueGetter;
   private final Function<Cached.CallbackInfo<B>, Cached.CallbackInfo<B>> updateCheck;
   private Cached.CallbackInfo<B> callbackInfo = new Cached.CallbackInfo();

   public Cached(Supplier<T> valueGetter, Function<Cached.CallbackInfo<B>, Cached.CallbackInfo<B>> updateCheck) {
      this.updatedValueGetter = valueGetter;
      this.updateCheck = updateCheck;
      this.lastValue = null;
   }

   public T getValue() {
      this.callbackInfo = (Cached.CallbackInfo)this.updateCheck.apply(this.callbackInfo);
      if (this.callbackInfo.needsUpdate) {
         this.lastValue = this.updatedValueGetter.get();
      }

      return this.lastValue;
   }

   public static class CallbackInfo<B> {
      public boolean needsUpdate = false;
      @Nullable
      public B lastCallbackReference = null;
   }
}
