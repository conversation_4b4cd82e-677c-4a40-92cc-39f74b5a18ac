package com.github.manasmods.tensura.util;

import com.mojang.blaze3d.systems.RenderSystem;
import com.mojang.blaze3d.vertex.PoseStack;
import java.util.Iterator;
import net.minecraft.client.gui.components.Widget;
import net.minecraft.client.gui.screens.Screen;
import net.minecraft.client.renderer.GameRenderer;
import net.minecraft.network.chat.Component;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.world.entity.player.Player;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.api.distmarker.OnlyIn;

@OnlyIn(Dist.CLIENT)
public abstract class SimpleScreen extends Screen {
   protected Player player;
   protected PoseStack poseStack;
   protected int guiLeft;
   protected int guiTop;
   protected int guiRight;
   protected int guiBottom;
   protected int guiCenterX;
   protected int guiCenterY;
   protected int mouseX;
   protected int mouseY;
   protected int imageWidth;
   protected int imageHeight;
   protected boolean shouldRenderWidgets;

   protected SimpleScreen(Component title, int width, int height) {
      this(title);
      this.imageWidth = width;
      this.imageHeight = height;
   }

   private SimpleScreen(Component title) {
      super(title);
   }

   public void m_7856_() {
      super.m_7856_();

      assert this.f_96541_ != null;

      this.player = this.f_96541_.f_91074_;

      assert this.player != null;

      this.guiLeft = (this.f_96543_ - this.imageWidth) / 2;
      this.guiTop = (this.f_96544_ - this.imageHeight) / 2;
      this.guiRight = this.imageWidth;
      this.guiBottom = this.imageHeight;
      this.guiCenterX = this.guiRight / 2;
      this.guiCenterY = this.guiBottom / 2;
      this.shouldRenderWidgets = true;
   }

   public void m_6305_(PoseStack pPoseStack, int pMouseX, int pMouseY, float pPartialTick) {
      this.renderBg(pPoseStack, pMouseX, pMouseY, pPartialTick);
      if (this.shouldRenderWidgets) {
         this.renderWidgets(pPoseStack, pMouseX, pMouseY, pPartialTick);
      }

      if (this.mouseX != pMouseX) {
         this.mouseX = pMouseX;
      }

      if (this.mouseY != pMouseY) {
         this.mouseY = pMouseY;
      }

      if (this.poseStack != pPoseStack) {
         this.poseStack = pPoseStack;
      }

   }

   protected void renderBg(PoseStack pPoseStack, int pMouseX, int pMouseY, float pPartialTick) {
      this.m_7333_(pPoseStack);
      RenderSystem.m_157427_(GameRenderer::m_172817_);
      RenderSystem.m_157429_(1.0F, 1.0F, 1.0F, 1.0F);
      this.renderTooltip(pPoseStack, pMouseX, pMouseY);
   }

   protected void renderWidgets(PoseStack pPoseStack, int pMouseX, int pMouseY, float pPartialTick) {
      Iterator var5 = this.f_169369_.iterator();

      while(var5.hasNext()) {
         Widget widget = (Widget)var5.next();
         widget.m_6305_(pPoseStack, pMouseX, pMouseY, pPartialTick);
      }

   }

   protected void renderTooltip(PoseStack poseStack, int pMouseX, int pMouseY) {
   }

   protected void renderBackground(PoseStack poseStack, ResourceLocation texture) {
      RenderSystem.m_157456_(0, texture);
      m_93133_(poseStack, this.guiLeft, this.guiTop, 0.0F, 0.0F, this.imageWidth, this.imageHeight, 256, 256);
   }

   public boolean m_7043_() {
      return false;
   }

   public int getGuiLeft() {
      return this.guiLeft;
   }

   public int getGuiTop() {
      return this.guiTop;
   }

   public int getGuiRight() {
      return this.guiRight;
   }

   public int getGuiBottom() {
      return this.guiBottom;
   }

   public int getGuiCenterX() {
      return this.guiCenterX;
   }

   public int getGuiCenterY() {
      return this.guiCenterY;
   }

   public int getMouseX() {
      return this.mouseX;
   }

   public int getMouseY() {
      return this.mouseY;
   }

   public int getImageWidth() {
      return this.imageWidth;
   }

   public int getImageHeight() {
      return this.imageHeight;
   }
}
