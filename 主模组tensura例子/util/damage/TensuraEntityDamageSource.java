package com.github.manasmods.tensura.util.damage;

import javax.annotation.Nullable;
import net.minecraft.network.chat.Component;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.damagesource.EntityDamageSource;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.phys.Vec3;
import org.jetbrains.annotations.NotNull;

public class TensuraEntityDamageSource extends TensuraDamageSource {
   protected Entity entity;
   private boolean isThorns;

   public TensuraEntityDamageSource(String pDamageTypeId, Entity pEntity) {
      super(pDamageTypeId);
      this.entity = pEntity;
   }

   public TensuraEntityDamageSource(EntityDamageSource entityDamageSource, @Nullable Entity owner) {
      super((DamageSource)entityDamageSource);
      this.entity = owner;
      this.isThorns = entityDamageSource.f_19392_;
   }

   public TensuraEntityDamageSource setThorns() {
      this.isThorns = true;
      return this;
   }

   public boolean isThorns() {
      return this.isThorns;
   }

   @NotNull
   public Component m_6157_(LivingEntity pLivingEntity) {
      ItemStack itemstack = this.entity instanceof LivingEntity ? ((LivingEntity)this.entity).m_21205_() : ItemStack.f_41583_;
      String s = "death.attack." + this.f_19326_;
      return !itemstack.m_41619_() && itemstack.m_41788_() ? Component.m_237110_(s + ".item", new Object[]{pLivingEntity.m_5446_(), this.entity.m_5446_(), itemstack.m_41611_()}) : Component.m_237110_(s, new Object[]{pLivingEntity.m_5446_(), this.entity.m_5446_()});
   }

   public boolean m_7986_() {
      return this.entity instanceof LivingEntity && !(this.entity instanceof Player);
   }

   @Nullable
   public Vec3 m_7270_() {
      return this.entity.m_20182_();
   }

   public String toString() {
      return "EntityDamageSource (" + this.entity + ")";
   }

   public Entity m_7639_() {
      return this.entity;
   }
}
