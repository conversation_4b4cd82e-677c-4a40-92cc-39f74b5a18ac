package com.github.manasmods.tensura.data.recipe;

import com.github.manasmods.tensura.item.food.HealingPotionItem;
import com.github.manasmods.tensura.registry.recipe.TensuraRecipeTypes;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.mojang.datafixers.util.Pair;
import com.mojang.serialization.JsonOps;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Consumer;
import java.util.function.Supplier;
import java.util.stream.Stream;
import net.minecraft.core.NonNullList;
import net.minecraft.data.recipes.FinishedRecipe;
import net.minecraft.network.FriendlyByteBuf;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.tags.TagKey;
import net.minecraft.world.Container;
import net.minecraft.world.item.ArmorItem;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Items;
import net.minecraft.world.item.PotionItem;
import net.minecraft.world.item.TieredItem;
import net.minecraft.world.item.alchemy.Potion;
import net.minecraft.world.item.alchemy.PotionUtils;
import net.minecraft.world.item.crafting.Ingredient;
import net.minecraft.world.item.crafting.Recipe;
import net.minecraft.world.item.crafting.RecipeSerializer;
import net.minecraft.world.item.crafting.RecipeType;
import net.minecraft.world.level.ItemLike;
import net.minecraft.world.level.Level;
import net.minecraftforge.registries.ForgeRegistries;
import net.minecraftforge.registries.IForgeRegistry;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.jetbrains.annotations.Nullable;

public class GreatSageRefiningRecipe implements Recipe<Container>, Comparator<GreatSageRefiningRecipe> {
   private final ResourceLocation id;
   private final ItemStack output;
   private final ItemStack input;
   private final Ingredient ingredient1;
   private final Ingredient ingredient2;
   private final Ingredient ingredient3;
   private final Ingredient ingredient4;
   private final Ingredient ingredient5;

   public GreatSageRefiningRecipe(ResourceLocation pRecipeId, ItemStack result, ItemStack input, List<Ingredient> ingredientList) {
      this.id = pRecipeId;
      this.output = result;
      this.input = input;
      int i = 0;
      if (ingredientList.size() > i) {
         this.ingredient1 = (Ingredient)ingredientList.get(i);
         ++i;
      } else {
         this.ingredient1 = Ingredient.f_43901_;
      }

      if (ingredientList.size() > i) {
         this.ingredient2 = (Ingredient)ingredientList.get(i);
         ++i;
      } else {
         this.ingredient2 = Ingredient.f_43901_;
      }

      if (ingredientList.size() > i) {
         this.ingredient3 = (Ingredient)ingredientList.get(i);
         ++i;
      } else {
         this.ingredient3 = Ingredient.f_43901_;
      }

      if (ingredientList.size() > i) {
         this.ingredient4 = (Ingredient)ingredientList.get(i);
         ++i;
      } else {
         this.ingredient4 = Ingredient.f_43901_;
      }

      if (ingredientList.size() > i) {
         this.ingredient5 = (Ingredient)ingredientList.get(i);
      } else {
         this.ingredient5 = Ingredient.f_43901_;
      }

   }

   public boolean m_5818_(Container container, Level pLevel) {
      if (!this.hasEnoughInput(container, this.input)) {
         return false;
      } else {
         boolean matched = true;
         List<Ingredient> ingredients = new ArrayList(List.of(this.ingredient1, this.ingredient2, this.ingredient3, this.ingredient4, this.ingredient5));

         for(int i = 3; i < container.m_6643_() && matched && !ingredients.isEmpty(); ++i) {
            ItemStack item = container.m_8020_(i);
            boolean contain = false;
            Iterator var8 = ingredients.iterator();

            while(var8.hasNext()) {
               Ingredient ingredient = (Ingredient)var8.next();
               if (ingredient.test(item)) {
                  ingredients.remove(ingredient);
                  contain = true;
                  break;
               }
            }

            matched = contain;
         }

         return matched;
      }
   }

   private boolean hasEnoughInput(Container container, ItemStack stack) {
      if (stack.m_41619_()) {
         return true;
      } else {
         for(int i = 0; i < 3; ++i) {
            ItemStack item = container.m_8020_(i);
            if (ItemStack.m_150942_(item, stack)) {
               return true;
            }
         }

         return false;
      }
   }

   public ItemStack m_5874_(Container pContainer) {
      return this.output.m_41777_();
   }

   public boolean m_8004_(int pWidth, int pHeight) {
      return true;
   }

   public ItemStack m_8043_() {
      return this.output.m_41777_();
   }

   public ItemStack getInputItem() {
      return this.input.m_41777_();
   }

   public RecipeSerializer<?> m_7707_() {
      return (RecipeSerializer)TensuraRecipeTypes.Serializer.REFINING.get();
   }

   public RecipeType<?> m_6671_() {
      return (RecipeType)TensuraRecipeTypes.REFINING.get();
   }

   public NonNullList<Ingredient> m_7527_() {
      return NonNullList.m_122783_(Ingredient.f_43901_, new Ingredient[]{this.ingredient1, this.ingredient2, this.ingredient3, this.ingredient4, this.ingredient5});
   }

   public FinishedRecipe finishedRecipe() {
      return new FinishedRecipe() {
         public void m_7917_(JsonObject root) {
            JsonArray ingredients = new JsonArray();
            this.addIngredient(ingredients, GreatSageRefiningRecipe.this.ingredient1);
            this.addIngredient(ingredients, GreatSageRefiningRecipe.this.ingredient2);
            this.addIngredient(ingredients, GreatSageRefiningRecipe.this.ingredient3);
            this.addIngredient(ingredients, GreatSageRefiningRecipe.this.ingredient4);
            this.addIngredient(ingredients, GreatSageRefiningRecipe.this.ingredient5);
            root.add("ingredients", ingredients);
            root.add("result", (JsonElement)ItemStack.f_41582_.encodeStart(JsonOps.INSTANCE, GreatSageRefiningRecipe.this.output).result().orElseThrow(() -> {
               return new IllegalArgumentException("Could not serialize ItemStack: " + GreatSageRefiningRecipe.this.output);
            }));
            root.add("input", (JsonElement)ItemStack.f_41582_.encodeStart(JsonOps.INSTANCE, GreatSageRefiningRecipe.this.input).result().orElseThrow(() -> {
               return new IllegalArgumentException("Could not serialize ItemStack: " + GreatSageRefiningRecipe.this.input);
            }));
         }

         private void addIngredient(JsonArray ingredients, Ingredient ingredient) {
            if (!ingredient.m_43947_()) {
               JsonObject root = new JsonObject();
               root.add("type", ingredient.m_43942_());
               ingredients.add(root);
            }
         }

         public ResourceLocation m_6445_() {
            return GreatSageRefiningRecipe.this.id;
         }

         public RecipeSerializer<?> m_6637_() {
            return GreatSageRefiningRecipe.this.m_7707_();
         }

         @Nullable
         public JsonObject m_5860_() {
            return null;
         }

         @Nullable
         public ResourceLocation m_6448_() {
            return null;
         }
      };
   }

   public int compare(GreatSageRefiningRecipe o1, GreatSageRefiningRecipe o2) {
      Item result1 = o1.m_8043_().m_41720_();
      Item result2 = o2.m_8043_().m_41720_();
      List<ResourceLocation> repairItems1 = this.getRepairItemsOf(result1);
      List<ResourceLocation> repairItems2 = this.getRepairItemsOf(result2);
      if (!repairItems1.isEmpty() && repairItems1.contains(ForgeRegistries.ITEMS.getKey(result2))) {
         return 1;
      } else {
         return !repairItems2.isEmpty() && repairItems2.contains(ForgeRegistries.ITEMS.getKey(result1)) ? -1 : ForgeRegistries.ITEMS.getKey(result1).m_135815_().compareTo(ForgeRegistries.ITEMS.getKey(result2).m_135815_());
      }
   }

   private List<ResourceLocation> getRepairItemsOf(Item item) {
      Stream var10000;
      IForgeRegistry var10001;
      if (item instanceof TieredItem) {
         TieredItem tieredItem = (TieredItem)item;
         var10000 = Arrays.stream(tieredItem.m_43314_().m_6282_().m_43908_()).map(ItemStack::m_41720_);
         var10001 = ForgeRegistries.ITEMS;
         Objects.requireNonNull(var10001);
         return var10000.map(var10001::getKey).filter(Objects::nonNull).toList();
      } else if (item instanceof ArmorItem) {
         ArmorItem armorItem = (ArmorItem)item;
         var10000 = Arrays.stream(armorItem.m_40401_().m_6230_().m_43908_()).map(ItemStack::m_41720_);
         var10001 = ForgeRegistries.ITEMS;
         Objects.requireNonNull(var10001);
         return var10000.map(var10001::getKey).filter(Objects::nonNull).toList();
      } else {
         return List.of();
      }
   }

   public boolean equals(Object o) {
      if (this == o) {
         return true;
      } else if (o != null && this.getClass() == o.getClass()) {
         GreatSageRefiningRecipe recipe = (GreatSageRefiningRecipe)o;
         return this.id.equals(recipe.id);
      } else {
         return false;
      }
   }

   public int hashCode() {
      return Objects.hash(new Object[]{this.id});
   }

   public void takeItemsFrom(Container container) {
      int j;
      for(j = 0; j < 5; ++j) {
         Ingredient ingredient = (Ingredient)this.m_7527_().get(j);
         if (!ingredient.m_43947_()) {
            for(int j = 3; j < container.m_6643_(); ++j) {
               ItemStack stack = container.m_8020_(j);
               if (!stack.m_41619_() && ingredient.test(stack)) {
                  stack.m_41774_(1);
                  break;
               }
            }
         }
      }

      for(j = 0; j < 3; ++j) {
         ItemStack stack = container.m_8020_(j);
         if (!stack.m_41619_() && ItemStack.m_150942_(this.input, stack)) {
            stack.m_41774_(1);
            break;
         }
      }

   }

   public GreatSageRefiningRecipe(ResourceLocation id, ItemStack output, ItemStack input, Ingredient ingredient1, Ingredient ingredient2, Ingredient ingredient3, Ingredient ingredient4, Ingredient ingredient5) {
      this.id = id;
      this.output = output;
      this.input = input;
      this.ingredient1 = ingredient1;
      this.ingredient2 = ingredient2;
      this.ingredient3 = ingredient3;
      this.ingredient4 = ingredient4;
      this.ingredient5 = ingredient5;
   }

   public ResourceLocation m_6423_() {
      return this.id;
   }

   public static class Builder {
      private final ItemStack result;
      private ItemStack input;
      private final NonNullList<Ingredient> ingredientMap;
      private int ingredientAmount;

      public static GreatSageRefiningRecipe.Builder of(Item result, int amount) {
         return of(new ItemStack(result, amount));
      }

      public static GreatSageRefiningRecipe.Builder of(Item result) {
         return of(result.m_7968_());
      }

      public static GreatSageRefiningRecipe.Builder of(Supplier<? extends Item> result) {
         return of((Item)result.get());
      }

      public static GreatSageRefiningRecipe.Builder of(Potion pPotion) {
         return of(pPotion, Items.f_42589_);
      }

      public static GreatSageRefiningRecipe.Builder of(Potion pPotion, Item bottle) {
         return of(PotionUtils.m_43549_(bottle.m_7968_(), pPotion));
      }

      public GreatSageRefiningRecipe.Builder addInput(ItemStack input) {
         this.input = input;
         return this;
      }

      public GreatSageRefiningRecipe.Builder addInput(Item result) {
         return this.addInput(result.m_7968_());
      }

      public GreatSageRefiningRecipe.Builder addInput(Potion pPotion) {
         return this.addInput(pPotion, Items.f_42589_);
      }

      public GreatSageRefiningRecipe.Builder addInput(Potion pPotion, Item bottle) {
         return this.addInput(PotionUtils.m_43549_(bottle.m_7968_(), pPotion));
      }

      public GreatSageRefiningRecipe.Builder addIngredient(Ingredient ingredient) {
         if (this.ingredientAmount + 1 > 5) {
            throw new IllegalStateException("RefiningRecipes only allow up to 5 ingredient");
         } else {
            this.ingredientMap.set(this.ingredientAmount++, ingredient);
            return this;
         }
      }

      public GreatSageRefiningRecipe.Builder addIngredient(ItemStack stack) {
         return this.addIngredient(Ingredient.m_43927_(new ItemStack[]{stack}));
      }

      public GreatSageRefiningRecipe.Builder addIngredient(TagKey<Item> itemTagKey) {
         return this.addIngredient(Ingredient.m_204132_(itemTagKey));
      }

      public GreatSageRefiningRecipe.Builder addIngredient(Item item) {
         return this.addIngredient(new ItemStack(item));
      }

      public GreatSageRefiningRecipe.Builder addIngredient(Item... items) {
         Stream.of(items).forEach((item) -> {
            if (this.ingredientAmount + 1 > 5) {
               throw new IllegalStateException("RefiningRecipes only allow up to 5 ingredient");
            } else {
               this.ingredientMap.set(this.ingredientAmount++, Ingredient.m_43929_(new ItemLike[]{item}));
            }
         });
         return this;
      }

      public void build(Consumer<FinishedRecipe> consumer, ResourceLocation id) {
         String path = "refining/" + id.m_135815_();
         Item var5 = this.result.m_41720_();
         if (var5 instanceof PotionItem) {
            PotionItem potionItem = (PotionItem)var5;
            if (!(potionItem instanceof HealingPotionItem)) {
               path = path + "/" + ((ResourceLocation)Objects.requireNonNull(ForgeRegistries.POTIONS.getKey(PotionUtils.m_43579_(this.result)))).m_135815_();
            }
         }

         if (!this.input.m_41619_()) {
            ResourceLocation location = ForgeRegistries.ITEMS.getKey(this.input.m_41720_());
            if (location != null) {
               path = path + "_from_" + location.m_135815_();
            }

            if (this.input.m_41720_() instanceof PotionItem) {
               path = path + "_" + ((ResourceLocation)Objects.requireNonNull(ForgeRegistries.POTIONS.getKey(PotionUtils.m_43579_(this.input)))).m_135815_();
            }
         }

         Ingredient ingredient = (Ingredient)this.ingredientMap.get(0);
         Optional<ItemStack> item = Arrays.stream(ingredient.m_43908_()).findFirst();
         if (item.isPresent()) {
            path = path + "_using_" + ((ResourceLocation)Objects.requireNonNull(ForgeRegistries.ITEMS.getKey(((ItemStack)item.get()).m_41720_()))).m_135815_();
         }

         consumer.accept((new GreatSageRefiningRecipe(new ResourceLocation(id.m_135827_(), path), this.result, this.input, ingredient, (Ingredient)this.ingredientMap.get(1), (Ingredient)this.ingredientMap.get(2), (Ingredient)this.ingredientMap.get(3), (Ingredient)this.ingredientMap.get(4))).finishedRecipe());
      }

      public void build(Consumer<FinishedRecipe> consumer) {
         this.build(consumer, (ResourceLocation)Objects.requireNonNull(ForgeRegistries.ITEMS.getKey(this.result.m_41720_())));
      }

      private Builder(ItemStack result) {
         this.ingredientMap = NonNullList.m_122780_(5, Ingredient.f_43901_);
         this.ingredientAmount = 0;
         this.result = result;
      }

      public static GreatSageRefiningRecipe.Builder of(ItemStack result) {
         return new GreatSageRefiningRecipe.Builder(result);
      }
   }

   public static class Type implements RecipeType<GreatSageRefiningRecipe> {
   }

   public static class Serializer implements RecipeSerializer<GreatSageRefiningRecipe> {
      private static final Logger log = LogManager.getLogger(GreatSageRefiningRecipe.Serializer.class);

      public GreatSageRefiningRecipe fromJson(ResourceLocation pRecipeId, JsonObject pSerializedRecipe) {
         JsonArray ingredients = pSerializedRecipe.getAsJsonArray("ingredients");
         ItemStack result = (ItemStack)((Pair)ItemStack.f_41582_.decode(JsonOps.INSTANCE, pSerializedRecipe.get("result")).result().orElseThrow(() -> {
            return new IllegalArgumentException("Could not load result ItemStack from: " + pRecipeId);
         })).getFirst();
         ItemStack input = (ItemStack)((Pair)ItemStack.f_41582_.decode(JsonOps.INSTANCE, pSerializedRecipe.get("input")).result().orElseThrow(() -> {
            return new IllegalArgumentException("Could not load result ItemStack from: " + pRecipeId);
         })).getFirst();
         if (ingredients.size() > 5) {
            log.fatal("Failed to load recipe {}. Too many Ingredients", pRecipeId);
            return null;
         } else {
            List<Ingredient> ingredientList = new ArrayList(5);
            ingredients.forEach((jsonElement) -> {
               JsonObject root = jsonElement.getAsJsonObject();
               ingredientList.add(Ingredient.m_43917_(root.get("type")));
            });
            return new GreatSageRefiningRecipe(pRecipeId, result, input, ingredientList);
         }
      }

      @Nullable
      public GreatSageRefiningRecipe fromNetwork(ResourceLocation pRecipeId, FriendlyByteBuf pBuffer) {
         return new GreatSageRefiningRecipe(pRecipeId, pBuffer.m_130267_(), pBuffer.m_130267_(), Ingredient.m_43940_(pBuffer), Ingredient.m_43940_(pBuffer), Ingredient.m_43940_(pBuffer), Ingredient.m_43940_(pBuffer), Ingredient.m_43940_(pBuffer));
      }

      public void toNetwork(FriendlyByteBuf pBuffer, GreatSageRefiningRecipe pRecipe) {
         pBuffer.writeItemStack(pRecipe.m_8043_(), false);
         pBuffer.writeItemStack(pRecipe.getInputItem(), false);
         pRecipe.ingredient1.m_43923_(pBuffer);
         pRecipe.ingredient2.m_43923_(pBuffer);
         pRecipe.ingredient3.m_43923_(pBuffer);
         pRecipe.ingredient4.m_43923_(pBuffer);
         pRecipe.ingredient5.m_43923_(pBuffer);
      }
   }
}
