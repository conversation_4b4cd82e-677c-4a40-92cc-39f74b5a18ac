package com.github.manasmods.tensura.data.recipe;

import com.github.manasmods.tensura.block.entity.KilnBlockEntity;
import com.github.manasmods.tensura.data.pack.KilnMoltenMaterial;
import com.github.manasmods.tensura.data.pack.TensuraData;
import com.github.manasmods.tensura.registry.recipe.TensuraRecipeTypes;
import com.google.gson.JsonObject;
import java.util.Iterator;
import java.util.Optional;
import java.util.function.Consumer;
import net.minecraft.data.recipes.FinishedRecipe;
import net.minecraft.network.FriendlyByteBuf;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.util.GsonHelper;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.crafting.Ingredient;
import net.minecraft.world.item.crafting.RecipeSerializer;
import net.minecraft.world.item.crafting.RecipeType;
import net.minecraft.world.level.Level;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.jetbrains.annotations.Nullable;

public class KilnMeltingRecipe extends KilnRecipe {
   private static final Logger log = LogManager.getLogger(KilnMeltingRecipe.class);
   private final ResourceLocation id;
   private final Ingredient input;
   private final ResourceLocation moltenType;
   private final int moltenAmount;
   private final ResourceLocation secondaryType;
   private final int secondaryAmount;

   public boolean matches(KilnBlockEntity pContainer, Level pLevel) {
      ItemStack inputStack = pContainer.m_8020_(1).m_41777_();
      if (!this.input.test(inputStack)) {
         return false;
      } else {
         boolean primaryPlaceable = this.sameOrEmpty(pContainer, this.moltenType, this.moltenAmount);
         if (this.secondaryType.equals(KilnMixingRecipe.EMPTY)) {
            return primaryPlaceable;
         } else {
            return !primaryPlaceable ? false : this.sameOrEmpty(pContainer, this.secondaryType, this.secondaryAmount);
         }
      }
   }

   private boolean sameOrEmpty(KilnBlockEntity container, ResourceLocation type, int amount) {
      Iterator var4 = TensuraData.getKilnMoltenMaterials().iterator();

      KilnMoltenMaterial moltenMaterial;
      do {
         if (!var4.hasNext()) {
            return false;
         }

         moltenMaterial = (KilnMoltenMaterial)var4.next();
      } while(!moltenMaterial.getMoltenType().equals(type));

      Optional<ResourceLocation> containerMaterial = moltenMaterial.isRightBar() ? container.getRightBarId() : container.getLeftBarId();
      if (containerMaterial.isEmpty()) {
         return true;
      } else if (((ResourceLocation)containerMaterial.get()).equals(KilnMixingRecipe.EMPTY)) {
         return true;
      } else if (!((ResourceLocation)containerMaterial.get()).equals(type)) {
         return false;
      } else {
         int existingAmount = moltenMaterial.isRightBar() ? container.getMagicMaterialAmount() : container.getMoltenAmount();
         return existingAmount + amount <= 144;
      }
   }

   public ItemStack assemble(KilnBlockEntity pContainer) {
      this.melt(pContainer, this.moltenType, this.moltenAmount);
      this.melt(pContainer, this.secondaryType, this.secondaryAmount);
      pContainer.m_7407_(1, 1);
      return ItemStack.f_41583_.m_41777_();
   }

   private void melt(KilnBlockEntity container, ResourceLocation type, int amount) {
      if (!type.equals(KilnMixingRecipe.EMPTY)) {
         TensuraData.getKilnMoltenMaterials().parallelStream().filter((moltenMaterial) -> {
            return moltenMaterial.getMoltenType().equals(type);
         }).findFirst().ifPresentOrElse((moltenMaterial) -> {
            if (moltenMaterial.isRightBar()) {
               container.setRightBarId(Optional.of(moltenMaterial.getMoltenType()));
               container.addMagicMaterialAmount(amount);
            } else {
               container.setLeftBarId(Optional.of(moltenMaterial.getMoltenType()));
               container.addMoltenMaterialAmount(amount);
            }

         }, () -> {
            log.error("Could not assemble MeltingRecipe: {}", this);
         });
      }
   }

   public RecipeSerializer<?> m_7707_() {
      return (RecipeSerializer)TensuraRecipeTypes.Serializer.KILN_MELTING.get();
   }

   public RecipeType<?> m_6671_() {
      return (RecipeType)TensuraRecipeTypes.KILN_MELTING.get();
   }

   public FinishedRecipe finishRecipe() {
      return new FinishedRecipe() {
         public void m_7917_(JsonObject root) {
            root.add("input", KilnMeltingRecipe.this.input.m_43942_());
            root.addProperty("moltenType", KilnMeltingRecipe.this.moltenType.toString());
            root.addProperty("moltenAmount", KilnMeltingRecipe.this.moltenAmount);
            if (!KilnMeltingRecipe.this.secondaryType.equals(KilnMixingRecipe.EMPTY)) {
               root.addProperty("secondaryType", KilnMeltingRecipe.this.secondaryType.toString());
               root.addProperty("secondaryAmount", KilnMeltingRecipe.this.secondaryAmount);
            }

         }

         public ResourceLocation m_6445_() {
            return KilnMeltingRecipe.this.id;
         }

         public RecipeSerializer<?> m_6637_() {
            return KilnMeltingRecipe.this.m_7707_();
         }

         @Nullable
         public JsonObject m_5860_() {
            return null;
         }

         @Nullable
         public ResourceLocation m_6448_() {
            return null;
         }
      };
   }

   public KilnMeltingRecipe(ResourceLocation id, Ingredient input, ResourceLocation moltenType, int moltenAmount, ResourceLocation secondaryType, int secondaryAmount) {
      this.id = id;
      this.input = input;
      this.moltenType = moltenType;
      this.moltenAmount = moltenAmount;
      this.secondaryType = secondaryType;
      this.secondaryAmount = secondaryAmount;
   }

   public String toString() {
      ResourceLocation var10000 = this.m_6423_();
      return "KilnMeltingRecipe(id=" + var10000 + ", input=" + this.getInput() + ", moltenType=" + this.getMoltenType() + ", moltenAmount=" + this.getMoltenAmount() + ", secondaryType=" + this.getSecondaryType() + ", secondaryAmount=" + this.getSecondaryAmount() + ")";
   }

   public ResourceLocation m_6423_() {
      return this.id;
   }

   public Ingredient getInput() {
      return this.input;
   }

   public ResourceLocation getMoltenType() {
      return this.moltenType;
   }

   public int getMoltenAmount() {
      return this.moltenAmount;
   }

   public ResourceLocation getSecondaryType() {
      return this.secondaryType;
   }

   public int getSecondaryAmount() {
      return this.secondaryAmount;
   }

   public static class Builder {
      private Ingredient input;
      private final ResourceLocation moltenType;
      private final int moltenAmount;
      private ResourceLocation secondaryType;
      private int secondaryAmount;

      public KilnMeltingRecipe.Builder requires(Ingredient ingredient) {
         this.input = ingredient;
         return this;
      }

      public KilnMeltingRecipe.Builder inputSecondary(ResourceLocation moltenType, int amount) {
         this.secondaryType = moltenType;
         this.secondaryAmount = amount;
         return this;
      }

      public void build(Consumer<FinishedRecipe> consumer, ResourceLocation id) {
         consumer.accept((new KilnMeltingRecipe(id, this.input == null ? Ingredient.f_43901_ : this.input, this.moltenType, this.moltenAmount, this.secondaryType, this.secondaryAmount)).finishRecipe());
      }

      public void build(Consumer<FinishedRecipe> consumer, String fileName) {
         this.build(consumer, new ResourceLocation(this.moltenType.m_135827_(), "melting/" + fileName));
      }

      private Builder(ResourceLocation moltenType, int moltenAmount) {
         this.secondaryType = KilnMixingRecipe.EMPTY;
         this.secondaryAmount = 0;
         this.moltenType = moltenType;
         this.moltenAmount = moltenAmount;
      }

      public static KilnMeltingRecipe.Builder of(ResourceLocation moltenType, int moltenAmount) {
         return new KilnMeltingRecipe.Builder(moltenType, moltenAmount);
      }
   }

   public static class Type implements RecipeType<KilnMeltingRecipe> {
   }

   public static class Serializer implements RecipeSerializer<KilnMeltingRecipe> {
      public KilnMeltingRecipe fromJson(ResourceLocation pRecipeId, JsonObject pSerializedRecipe) {
         Ingredient input = Ingredient.m_43917_(GsonHelper.m_13930_(pSerializedRecipe, "input"));
         ResourceLocation moltenType = ResourceLocation.m_135820_(GsonHelper.m_13906_(pSerializedRecipe, "moltenType"));
         int moltenAmount = GsonHelper.m_13927_(pSerializedRecipe, "moltenAmount");
         ResourceLocation secondaryType = KilnMixingRecipe.EMPTY;
         int secondaryAmount = 0;
         if (pSerializedRecipe.has("secondaryType")) {
            secondaryType = ResourceLocation.m_135820_(GsonHelper.m_13906_(pSerializedRecipe, "secondaryType"));
            secondaryAmount = GsonHelper.m_13927_(pSerializedRecipe, "secondaryAmount");
         }

         return new KilnMeltingRecipe(pRecipeId, input, moltenType, moltenAmount, secondaryType, secondaryAmount);
      }

      @Nullable
      public KilnMeltingRecipe fromNetwork(ResourceLocation pRecipeId, FriendlyByteBuf pBuffer) {
         return new KilnMeltingRecipe(pRecipeId, Ingredient.m_43940_(pBuffer), pBuffer.m_130281_(), pBuffer.readInt(), pBuffer.m_130281_(), pBuffer.readInt());
      }

      public void toNetwork(FriendlyByteBuf pBuffer, KilnMeltingRecipe pRecipe) {
         pRecipe.input.m_43923_(pBuffer);
         pBuffer.m_130085_(pRecipe.moltenType);
         pBuffer.writeInt(pRecipe.moltenAmount);
         pBuffer.m_130085_(pRecipe.secondaryType);
         pBuffer.writeInt(pRecipe.secondaryAmount);
      }
   }
}
