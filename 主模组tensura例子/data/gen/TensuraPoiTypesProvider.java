package com.github.manasmods.tensura.data.gen;

import net.minecraft.data.DataGenerator;
import net.minecraft.data.tags.PoiTypeTagsProvider;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.tags.PoiTypeTags;
import net.minecraftforge.common.data.ExistingFileHelper;
import net.minecraftforge.data.event.GatherDataEvent;
import org.jetbrains.annotations.Nullable;

public class TensuraPoiTypesProvider extends PoiTypeTagsProvider {
   public TensuraPoiTypesProvider(GatherDataEvent event) {
      this(event.getGenerator(), "tensura", event.getExistingFileHelper());
   }

   public TensuraPoiTypesProvider(DataGenerator generator, String modId, @Nullable ExistingFileHelper existingFileHelper) {
      super(generator, modId, existingFileHelper);
   }

   protected void m_6577_() {
      this.m_206424_(PoiTypeTags.f_215875_).m_176839_(new ResourceLocation("tensura", "gearsmith_poi"));
   }
}
