package com.github.manasmods.tensura.data.gen;

import com.github.manasmods.manascore.api.data.gen.CustomDataProvider;
import com.github.manasmods.tensura.data.pack.KilnMoltenMaterial;
import com.google.gson.JsonElement;
import java.awt.Color;
import java.util.function.BiConsumer;
import java.util.function.Supplier;
import net.minecraft.resources.ResourceLocation;
import net.minecraftforge.data.event.GatherDataEvent;

public class TensuraMoltenMaterialProvider extends CustomDataProvider {
   public static final ResourceLocation MOLTEN_GOLD = new ResourceLocation("tensura", "gold");
   public static final ResourceLocation MOLTEN_IRON = new ResourceLocation("tensura", "iron");
   public static final ResourceLocation MOLTEN_SILVER = new ResourceLocation("tensura", "silver");
   public static final ResourceLocation MOLTEN_MAGISTEEL = new ResourceLocation("tensura", "magisteel");
   public static final ResourceLocation MOLTEN_NETHERITE = new ResourceLocation("tensura", "netherite");

   public TensuraMoltenMaterialProvider(GatherDataEvent gatherDataEvent) {
      super("kiln/materials", gatherDataEvent.getGenerator());
   }

   protected void run(BiConsumer<ResourceLocation, Supplier<JsonElement>> biConsumer) {
      KilnMoltenMaterial.of(MOLTEN_GOLD, false, new Color(227, 167, 37, 255)).buildJson(biConsumer);
      KilnMoltenMaterial.of(MOLTEN_IRON, false, new Color(139, 128, 106, 255)).buildJson(biConsumer);
      KilnMoltenMaterial.of(MOLTEN_SILVER, false, new Color(206, 217, 217, 255)).buildJson(biConsumer);
      KilnMoltenMaterial.of(MOLTEN_MAGISTEEL, true, new Color(0, 233, 255, 255)).buildJson(biConsumer);
      KilnMoltenMaterial.of(MOLTEN_NETHERITE, true, new Color(57, 43, 43, 255)).buildJson(biConsumer);
   }

   public String m_6055_() {
      return "Tensura Molten Materials";
   }
}
