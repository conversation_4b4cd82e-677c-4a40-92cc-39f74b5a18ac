package com.github.manasmods.tensura.data.gen;

import com.github.manasmods.tensura.data.TensuraBiomeTags;
import com.github.manasmods.tensura.registry.biome.TensuraBiomes;
import net.minecraft.data.DataGenerator;
import net.minecraft.data.tags.BiomeTagsProvider;
import net.minecraft.resources.ResourceKey;
import net.minecraft.tags.BiomeTags;
import net.minecraft.world.level.biome.Biomes;
import net.minecraftforge.common.data.ExistingFileHelper;
import net.minecraftforge.data.event.GatherDataEvent;
import org.jetbrains.annotations.Nullable;

public class TensuraBiomeTagProvider extends BiomeTagsProvider {
   public TensuraBiomeTagProvider(DataGenerator generator, String modId, @Nullable ExistingFileHelper existingFileHelper) {
      super(generator, modId, existingFileHelper);
   }

   public TensuraBiomeTagProvider(GatherDataEvent gatherDataEvent) {
      this(gatherDataEvent.getGenerator(), "tensura", gatherDataEvent.getExistingFileHelper());
   }

   protected void m_6577_() {
      this.m_206424_(BiomeTags.f_207611_).m_211101_(new ResourceKey[]{TensuraBiomes.SAKURA_FOREST.getKey()});
      this.m_206424_(TensuraBiomeTags.IS_FLAT_LAND).m_211101_(new ResourceKey[]{Biomes.f_48202_, Biomes.f_186761_, Biomes.f_48176_, Biomes.f_186754_, Biomes.f_48203_, Biomes.f_48159_});
      this.m_206424_(TensuraBiomeTags.HAS_PALM).m_211101_(new ResourceKey[]{Biomes.f_48217_, Biomes.f_48208_});
      this.m_206424_(TensuraBiomeTags.HAS_HIPOKUTE).m_206428_(BiomeTags.f_215817_);
      this.m_206424_(TensuraBiomeTags.IS_HELL).m_211101_(new ResourceKey[]{TensuraBiomes.UNDERWORLD_BARRENS.getKey(), TensuraBiomes.UNDERWORLD_SPIKES.getKey(), TensuraBiomes.UNDERWORLD_RED_SANDS.getKey(), TensuraBiomes.UNDERWORLD_SANDS.getKey()});
      this.m_206424_(TensuraBiomeTags.IS_HELL_STONE).m_211101_(new ResourceKey[]{TensuraBiomes.UNDERWORLD_BARRENS.getKey(), TensuraBiomes.UNDERWORLD_SPIKES.getKey()});
      this.m_206424_(TensuraBiomeTags.IS_HELL_SAND).m_211101_(new ResourceKey[]{TensuraBiomes.UNDERWORLD_RED_SANDS.getKey(), TensuraBiomes.UNDERWORLD_SANDS.getKey()});
      this.m_206424_(TensuraBiomeTags.ANT_NEST).m_211101_(new ResourceKey[]{Biomes.f_48157_, Biomes.f_48205_, Biomes.f_48149_});
      this.m_206424_(TensuraBiomeTags.CHARYBDIS_CAVE).m_206428_(TensuraBiomeTags.IS_FLAT_LAND).m_206428_(BiomeTags.f_207611_).m_206428_(BiomeTags.f_207610_).m_206428_(BiomeTags.f_207609_).m_206428_(BiomeTags.f_207607_).m_206428_(BiomeTags.f_215816_);
      this.m_206424_(TensuraBiomeTags.GOBLIN_VILLAGE).m_211101_(new ResourceKey[]{TensuraBiomes.SAKURA_FOREST.getKey(), Biomes.f_48206_, Biomes.f_48205_, Biomes.f_48149_});
      this.m_206424_(TensuraBiomeTags.HELL_GATE).m_206428_(TensuraBiomeTags.IS_FLAT_LAND).m_206428_(TensuraBiomeTags.IS_HELL).m_206428_(BiomeTags.f_207607_).m_206428_(BiomeTags.f_215816_);
      this.m_206424_(TensuraBiomeTags.HELL_SAND_RUIN).m_211101_(new ResourceKey[]{TensuraBiomes.UNDERWORLD_SANDS.getKey()});
      this.m_206424_(TensuraBiomeTags.HELL_RED_SAND_RUIN).m_211101_(new ResourceKey[]{TensuraBiomes.UNDERWORLD_RED_SANDS.getKey()});
      this.m_206424_(TensuraBiomeTags.LABYRINTH_TREE).m_211101_(new ResourceKey[]{Biomes.f_48202_, Biomes.f_48157_});
      this.m_206424_(TensuraBiomeTags.LIZARDMAN_VILLAGE).m_211101_(new ResourceKey[]{Biomes.f_48207_, Biomes.f_220595_});
      this.m_206424_(TensuraBiomeTags.BLACK_SPIDER_NEST).m_211101_(new ResourceKey[]{Biomes.f_48202_, Biomes.f_48151_});
      this.m_206424_(TensuraBiomeTags.EntitySpawn.AQUA_FROG).m_211101_(new ResourceKey[]{Biomes.f_186760_}).m_206428_(net.minecraftforge.common.Tags.Biomes.IS_SWAMP).m_206428_(BiomeTags.f_215815_).m_206428_(BiomeTags.f_207620_);
      this.m_206424_(TensuraBiomeTags.EntitySpawn.ARMOURSAURUS).m_206428_(net.minecraftforge.common.Tags.Biomes.IS_UNDERGROUND).m_206428_(BiomeTags.f_207607_);
      this.m_206424_(TensuraBiomeTags.EntitySpawn.ARMY_WASP).m_211101_(new ResourceKey[]{Biomes.f_48179_, Biomes.f_48179_, TensuraBiomes.SAKURA_FOREST.getKey()});
      this.m_206424_(TensuraBiomeTags.EntitySpawn.BARGHEST).m_206428_(BiomeTags.f_207591_).m_206428_(BiomeTags.f_207611_).m_206428_(BiomeTags.f_207609_);
      this.m_206424_(TensuraBiomeTags.EntitySpawn.BEAST_GNOME).m_206428_(net.minecraftforge.common.Tags.Biomes.IS_UNDERGROUND).m_206428_(BiomeTags.f_207607_);
      this.m_206424_(TensuraBiomeTags.EntitySpawn.BLACK_SPIDER).m_206428_(net.minecraftforge.common.Tags.Biomes.IS_CAVE).m_206428_(BiomeTags.f_207595_);
      this.m_206424_(TensuraBiomeTags.EntitySpawn.BLADE_TIGER).m_206428_(BiomeTags.f_207611_);
      this.m_206424_(TensuraBiomeTags.EntitySpawn.BULLDEER).m_206428_(net.minecraftforge.common.Tags.Biomes.IS_PLAINS).m_206428_(BiomeTags.f_207611_).m_206428_(BiomeTags.f_215816_).m_206428_(BiomeTags.f_207609_);
      this.m_206424_(TensuraBiomeTags.EntitySpawn.DIREWOLF).m_206428_(net.minecraftforge.common.Tags.Biomes.IS_PLAINS).m_206428_(BiomeTags.f_207611_).m_206428_(BiomeTags.f_215816_).m_206428_(BiomeTags.f_207609_).m_206428_(BiomeTags.f_207608_).m_206428_(BiomeTags.f_207607_).m_206428_(BiomeTags.f_207606_).m_206428_(net.minecraftforge.common.Tags.Biomes.IS_CAVE).m_206428_(net.minecraftforge.common.Tags.Biomes.IS_SWAMP);
      this.m_206424_(TensuraBiomeTags.EntitySpawn.DRAGON_PEACOCK).m_206428_(BiomeTags.f_207610_).m_206428_(BiomeTags.f_215816_).m_206428_(BiomeTags.f_207611_);
      this.m_206424_(TensuraBiomeTags.EntitySpawn.EVIL_CENTIPEDE).m_206428_(BiomeTags.f_207610_).m_206428_(BiomeTags.f_207609_).m_206428_(net.minecraftforge.common.Tags.Biomes.IS_SWAMP).m_206428_(net.minecraftforge.common.Tags.Biomes.IS_CAVE);
      this.m_206424_(TensuraBiomeTags.EntitySpawn.FEATHERED_SERPENT).m_206428_(BiomeTags.f_207606_).m_206428_(BiomeTags.f_207608_);
      this.m_206424_(TensuraBiomeTags.EntitySpawn.GIANT_ANT).m_206428_(BiomeTags.f_207611_).m_206428_(BiomeTags.f_215816_);
      this.m_206424_(TensuraBiomeTags.EntitySpawn.GIANT_BAT).m_206428_(BiomeTags.f_207608_).m_206428_(BiomeTags.f_207606_).m_206428_(BiomeTags.f_207607_).m_206428_(BiomeTags.f_207595_);
      this.m_206424_(TensuraBiomeTags.EntitySpawn.GIANT_BEAR).m_206428_(BiomeTags.f_207611_).m_206428_(BiomeTags.f_207609_);
      this.m_206424_(TensuraBiomeTags.EntitySpawn.GIANT_COD).m_206428_(BiomeTags.f_207605_).m_206428_(BiomeTags.f_207603_);
      this.m_206424_(TensuraBiomeTags.EntitySpawn.GIANT_SALMON).m_206428_(BiomeTags.f_207605_).m_206428_(BiomeTags.f_207603_);
      this.m_206424_(TensuraBiomeTags.EntitySpawn.HELL_CATERPILLAR).m_206428_(BiomeTags.f_207595_);
      this.m_206424_(TensuraBiomeTags.EntitySpawn.HELL_MOTH).m_206428_(BiomeTags.f_207595_);
      this.m_206424_(TensuraBiomeTags.EntitySpawn.HORNED_BEAR).m_206428_(BiomeTags.f_207608_).m_206428_(BiomeTags.f_207609_);
      this.m_206424_(TensuraBiomeTags.EntitySpawn.HORNED_RABBIT).m_206428_(BiomeTags.f_207595_).m_206428_(BiomeTags.f_207609_);
      this.m_206424_(TensuraBiomeTags.EntitySpawn.HOUND_DOG).m_206428_(net.minecraftforge.common.Tags.Biomes.IS_PLAINS).m_206428_(BiomeTags.f_207611_).m_206428_(TensuraBiomeTags.IS_HELL_STONE);
      this.m_206424_(TensuraBiomeTags.EntitySpawn.HOVER_LIZARD).m_206428_(net.minecraftforge.common.Tags.Biomes.IS_SWAMP).m_206428_(BiomeTags.f_207605_);
      this.m_206424_(TensuraBiomeTags.EntitySpawn.KNIGHT_SPIDER).m_206428_(net.minecraftforge.common.Tags.Biomes.IS_DESERT).m_206428_(BiomeTags.f_207607_);
      this.m_206424_(TensuraBiomeTags.EntitySpawn.LANDFISH).m_206428_(BiomeTags.f_207605_).m_206428_(BiomeTags.f_207604_);
      this.m_206424_(TensuraBiomeTags.EntitySpawn.LEECH_LIZARD).m_206428_(BiomeTags.f_207611_).m_206428_(BiomeTags.f_207609_).m_206428_(net.minecraftforge.common.Tags.Biomes.IS_PLAINS);
      this.m_206424_(TensuraBiomeTags.EntitySpawn.MEGALODON).m_206428_(TensuraBiomeTags.IS_HELL);
      this.m_206424_(TensuraBiomeTags.EntitySpawn.ONE_EYED_OWL).m_206428_(net.minecraftforge.common.Tags.Biomes.IS_PLAINS).m_206428_(BiomeTags.f_207595_);
      this.m_206424_(TensuraBiomeTags.EntitySpawn.ORC).m_206428_(net.minecraftforge.common.Tags.Biomes.IS_DESERT).m_206428_(BiomeTags.f_207607_);
      this.m_206424_(TensuraBiomeTags.EntitySpawn.OTHERWORLDER).m_206428_(net.minecraftforge.common.Tags.Biomes.IS_PLAINS).m_206428_(BiomeTags.f_207608_).m_206428_(net.minecraftforge.common.Tags.Biomes.IS_MOUNTAIN).m_206428_(BiomeTags.f_215816_).m_206428_(net.minecraftforge.common.Tags.Biomes.IS_DESERT).m_206428_(BiomeTags.f_207607_);
      this.m_206424_(TensuraBiomeTags.EntitySpawn.SALAMANDER).m_206428_(BiomeTags.f_207612_);
      this.m_206424_(TensuraBiomeTags.EntitySpawn.SISSIE).m_206428_(BiomeTags.f_207602_);
      this.m_206424_(TensuraBiomeTags.EntitySpawn.SLIME).m_206428_(net.minecraftforge.common.Tags.Biomes.IS_PLAINS).m_206428_(BiomeTags.f_207611_).m_206428_(BiomeTags.f_215816_).m_206428_(BiomeTags.f_207609_).m_206428_(BiomeTags.f_207608_).m_206428_(BiomeTags.f_207607_).m_206428_(BiomeTags.f_207606_).m_206428_(net.minecraftforge.common.Tags.Biomes.IS_CAVE).m_206428_(net.minecraftforge.common.Tags.Biomes.IS_SWAMP);
      this.m_206424_(TensuraBiomeTags.EntitySpawn.SPEAR_TORO).m_206428_(BiomeTags.f_207603_);
      this.m_206424_(TensuraBiomeTags.EntitySpawn.TEMPEST_SERPENT).m_206428_(BiomeTags.f_207595_).m_206428_(net.minecraftforge.common.Tags.Biomes.IS_CAVE);
      this.m_206424_(TensuraBiomeTags.EntitySpawn.UNICORN).m_206428_(BiomeTags.f_207606_).m_211101_(new ResourceKey[]{Biomes.f_48179_}).m_206428_(BiomeTags.f_207595_);
      this.m_206424_(TensuraBiomeTags.EntitySpawn.WINGED_CAT).m_206428_(BiomeTags.f_207610_).m_206428_(BiomeTags.f_207601_);
   }
}
