package com.github.manasmods.tensura.data.gen;

import com.github.manasmods.manascore.api.data.gen.EntityLoot;
import com.github.manasmods.tensura.registry.blocks.TensuraBlocks;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.items.TensuraConsumableItems;
import com.github.manasmods.tensura.registry.items.TensuraMaterialItems;
import com.github.manasmods.tensura.registry.items.TensuraMobDropItems;
import com.github.manasmods.tensura.registry.items.TensuraSmithingSchematicItems;
import net.minecraft.advancements.critereon.EntityPredicate.Builder;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.item.Items;
import net.minecraft.world.level.ItemLike;
import net.minecraft.world.level.storage.loot.LootPool;
import net.minecraft.world.level.storage.loot.LootTable;
import net.minecraft.world.level.storage.loot.LootContext.EntityTarget;
import net.minecraft.world.level.storage.loot.entries.LootItem;
import net.minecraft.world.level.storage.loot.functions.LootingEnchantFunction;
import net.minecraft.world.level.storage.loot.functions.SetItemCountFunction;
import net.minecraft.world.level.storage.loot.functions.SmeltItemFunction;
import net.minecraft.world.level.storage.loot.predicates.LootItemEntityPropertyCondition;
import net.minecraft.world.level.storage.loot.providers.number.ConstantValue;
import net.minecraft.world.level.storage.loot.providers.number.UniformGenerator;

public class TensuraEntityLootProvider extends EntityLoot {
   protected void loadTables() {
      this.vanillaMob();
      this.tensuraMob();
      this.otherworlders();
   }

   private void vanillaMob() {
      this.m_124371_(EntityType.f_20523_, LootTable.m_79147_().m_79161_(LootPool.m_79043_().m_165133_(ConstantValue.m_165692_(1.0F)).m_79076_(LootItem.m_79579_((ItemLike)TensuraMaterialItems.SILVER_NUGGET.get()).m_79078_(SetItemCountFunction.m_165412_(UniformGenerator.m_165780_(0.0F, 1.0F))).m_79078_(LootingEnchantFunction.m_165229_(UniformGenerator.m_165780_(0.0F, 3.0F))))));
   }

   private void otherworlders() {
      this.m_124371_((EntityType)TensuraEntityTypes.KIRARA_MIZUTANI.get(), LootTable.m_79147_().m_79161_(LootPool.m_79043_().m_165133_(UniformGenerator.m_165780_(0.0F, 1.0F)).m_79076_(LootItem.m_79579_((ItemLike)TensuraMaterialItems.MAGIC_STONE.get()).m_79078_(SetItemCountFunction.m_165412_(UniformGenerator.m_165780_(0.0F, 1.0F))))));
      this.m_124371_((EntityType)TensuraEntityTypes.KYOYA_TACHIBANA.get(), LootTable.m_79147_().m_79161_(LootPool.m_79043_().m_165133_(ConstantValue.m_165692_(1.0F)).m_79076_(LootItem.m_79579_((ItemLike)TensuraSmithingSchematicItems.SPATIAL_BLADE.get()).m_79078_(SetItemCountFunction.m_165412_(UniformGenerator.m_165780_(0.0F, 1.0F))))));
      this.m_124371_((EntityType)TensuraEntityTypes.SHIZU.get(), LootTable.m_79147_().m_79161_(LootPool.m_79043_().m_165133_(ConstantValue.m_165692_(1.0F)).m_79076_(LootItem.m_79579_((ItemLike)TensuraSmithingSchematicItems.ANTI_MAGIC_MASK.get()).m_79078_(SetItemCountFunction.m_165412_(ConstantValue.m_165692_(1.0F))))));
   }

   private void tensuraMob() {
      this.m_124371_((EntityType)TensuraEntityTypes.AKASH.get(), LootTable.m_79147_().m_79161_(LootPool.m_79043_().m_165133_(ConstantValue.m_165692_(1.0F)).m_79076_(LootItem.m_79579_((ItemLike)TensuraMobDropItems.ELEMENTAL_ESSENCE.get()).m_79078_(SetItemCountFunction.m_165412_(ConstantValue.m_165692_(1.0F))))).m_79161_(LootPool.m_79043_().m_165133_(ConstantValue.m_165692_(1.0F)).m_79076_(LootItem.m_79579_((ItemLike)TensuraMaterialItems.SPACE_ELEMENTAL_SHARD.get()).m_79078_(SetItemCountFunction.m_165412_(UniformGenerator.m_165780_(1.0F, 3.0F))))));
      this.m_124371_((EntityType)TensuraEntityTypes.AQUA_FROG.get(), LootTable.m_79147_().m_79161_(LootPool.m_79043_().m_165133_(UniformGenerator.m_165780_(0.0F, 1.0F)).m_79076_(LootItem.m_79579_((ItemLike)TensuraMaterialItems.WATER_ELEMENTAL_SHARD.get()).m_79078_(SetItemCountFunction.m_165412_(UniformGenerator.m_165780_(0.0F, 1.0F))))));
      this.m_124371_((EntityType)TensuraEntityTypes.ARMOURSAURUS.get(), LootTable.m_79147_().m_79161_(LootPool.m_79043_().m_165133_(ConstantValue.m_165692_(1.0F)).m_79076_(LootItem.m_79579_((ItemLike)TensuraConsumableItems.RAW_ARMOURSAURUS_MEAT.get()).m_79078_(SetItemCountFunction.m_165412_(UniformGenerator.m_165780_(1.0F, 3.0F))).m_79078_(SmeltItemFunction.m_81271_().m_79080_(LootItemEntityPropertyCondition.m_81864_(EntityTarget.THIS, f_124366_))).m_79078_(LootingEnchantFunction.m_165229_(UniformGenerator.m_165780_(0.0F, 3.0F))))).m_79161_(LootPool.m_79043_().m_165133_(ConstantValue.m_165692_(1.0F)).m_79076_(LootItem.m_79579_((ItemLike)TensuraMobDropItems.ARMOURSAURUS_SCALE.get()).m_79078_(SetItemCountFunction.m_165412_(UniformGenerator.m_165780_(3.0F, 8.0F))).m_79078_(LootingEnchantFunction.m_165229_(UniformGenerator.m_165780_(0.0F, 3.0F))))).m_79161_(LootPool.m_79043_().m_165133_(ConstantValue.m_165692_(1.0F)).m_79076_(LootItem.m_79579_((ItemLike)TensuraMobDropItems.ARMOURSAURUS_SHELL.get()).m_79078_(SetItemCountFunction.m_165412_(UniformGenerator.m_165780_(2.0F, 3.0F)))).m_79078_(LootingEnchantFunction.m_165229_(UniformGenerator.m_165780_(0.0F, 3.0F)))));
      this.m_124371_((EntityType)TensuraEntityTypes.ARMY_WASP.get(), LootTable.m_79147_().m_79161_(LootPool.m_79043_().m_165133_(ConstantValue.m_165692_(1.0F)).m_79076_(LootItem.m_79579_((ItemLike)TensuraMobDropItems.INSECTAR_CARAPACE.get()).m_79078_(SetItemCountFunction.m_165412_(UniformGenerator.m_165780_(0.0F, 1.0F))).m_79078_(LootingEnchantFunction.m_165229_(UniformGenerator.m_165780_(0.0F, 1.0F))))));
      this.m_124371_((EntityType)TensuraEntityTypes.BARGHEST.get(), LootTable.m_79147_().m_79161_(LootPool.m_79043_().m_165133_(ConstantValue.m_165692_(1.0F)).m_79076_(LootItem.m_79579_((ItemLike)TensuraMobDropItems.MONSTER_LEATHER_C.get()).m_79078_(SetItemCountFunction.m_165412_(UniformGenerator.m_165780_(1.0F, 3.0F))).m_79078_(LootingEnchantFunction.m_165229_(UniformGenerator.m_165780_(0.0F, 3.0F))))));
      this.m_124371_((EntityType)TensuraEntityTypes.BEAST_GNOME.get(), LootTable.m_79147_().m_79161_(LootPool.m_79043_().m_165133_(UniformGenerator.m_165780_(0.0F, 1.0F)).m_79076_(LootItem.m_79579_((ItemLike)TensuraMaterialItems.EARTH_ELEMENTAL_SHARD.get()).m_79078_(SetItemCountFunction.m_165412_(UniformGenerator.m_165780_(0.0F, 1.0F))))));
      this.m_124371_((EntityType)TensuraEntityTypes.BLACK_SPIDER.get(), LootTable.m_79147_().m_79161_(LootPool.m_79043_().m_165133_(ConstantValue.m_165692_(1.0F)).m_79076_(LootItem.m_79579_((ItemLike)TensuraMobDropItems.STICKY_THREAD.get()).m_79078_(SetItemCountFunction.m_165412_(UniformGenerator.m_165780_(10.0F, 20.0F))).m_79078_(LootingEnchantFunction.m_165229_(UniformGenerator.m_165780_(0.0F, 5.0F))))).m_79161_(LootPool.m_79043_().m_165133_(ConstantValue.m_165692_(1.0F)).m_79076_(LootItem.m_79579_((ItemLike)TensuraMobDropItems.STEEL_THREAD.get()).m_79078_(SetItemCountFunction.m_165412_(UniformGenerator.m_165780_(10.0F, 20.0F))).m_79078_(LootingEnchantFunction.m_165229_(UniformGenerator.m_165780_(0.0F, 5.0F))))).m_79161_(LootPool.m_79043_().m_165133_(ConstantValue.m_165692_(1.0F)).m_79076_(LootItem.m_79579_((ItemLike)TensuraMobDropItems.SPIDER_FANG.get()).m_79078_(SetItemCountFunction.m_165412_(UniformGenerator.m_165780_(1.0F, 3.0F))))).m_79161_(LootPool.m_79043_().m_165133_(ConstantValue.m_165692_(1.0F)).m_79076_(LootItem.m_79579_(Items.f_42591_).m_79078_(SetItemCountFunction.m_165412_(UniformGenerator.m_165780_(6.0F, 8.0F))))));
      this.m_124371_((EntityType)TensuraEntityTypes.BLADE_TIGER.get(), LootTable.m_79147_().m_79161_(LootPool.m_79043_().m_165133_(ConstantValue.m_165692_(1.0F)).m_79076_(LootItem.m_79579_((ItemLike)TensuraMobDropItems.MONSTER_LEATHER_A.get()).m_79078_(SetItemCountFunction.m_165412_(UniformGenerator.m_165780_(0.0F, 3.0F))).m_79078_(LootingEnchantFunction.m_165229_(UniformGenerator.m_165780_(0.0F, 2.0F))))).m_79161_(LootPool.m_79043_().m_165133_(ConstantValue.m_165692_(1.0F)).m_79076_(LootItem.m_79579_((ItemLike)TensuraConsumableItems.RAW_BLADE_TIGER_MEAT.get()).m_79078_(SetItemCountFunction.m_165412_(UniformGenerator.m_165780_(1.0F, 3.0F))).m_79078_(SmeltItemFunction.m_81271_().m_79080_(LootItemEntityPropertyCondition.m_81864_(EntityTarget.THIS, f_124366_))).m_79078_(LootingEnchantFunction.m_165229_(UniformGenerator.m_165780_(0.0F, 3.0F))))).m_79161_(LootPool.m_79043_().m_165133_(ConstantValue.m_165692_(1.0F)).m_79076_(LootItem.m_79579_((ItemLike)TensuraMobDropItems.BLADE_TIGER_TAIL.get()).m_79078_(SetItemCountFunction.m_165412_(UniformGenerator.m_165780_(1.0F, 1.0F))))));
      this.m_124371_((EntityType)TensuraEntityTypes.BULLDEER.get(), LootTable.m_79147_().m_79161_(LootPool.m_79043_().m_165133_(ConstantValue.m_165692_(1.0F)).m_79076_(LootItem.m_79579_((ItemLike)TensuraMobDropItems.MONSTER_LEATHER_D.get()).m_79078_(SetItemCountFunction.m_165412_(UniformGenerator.m_165780_(0.0F, 2.0F))).m_79078_(LootingEnchantFunction.m_165229_(UniformGenerator.m_165780_(0.0F, 3.0F))))).m_79161_(LootPool.m_79043_().m_165133_(ConstantValue.m_165692_(1.0F)).m_79076_(LootItem.m_79579_((ItemLike)TensuraConsumableItems.BULLDEER_BEEF.get()).m_79078_(SetItemCountFunction.m_165412_(UniformGenerator.m_165780_(1.0F, 2.0F)))).m_79078_(SmeltItemFunction.m_81271_().m_79080_(LootItemEntityPropertyCondition.m_81864_(EntityTarget.THIS, f_124366_)))));
      this.m_124371_((EntityType)TensuraEntityTypes.CHARYBDIS.get(), LootTable.m_79147_().m_79161_(LootPool.m_79043_().m_165133_(ConstantValue.m_165692_(1.0F)).m_79076_(LootItem.m_79579_((ItemLike)TensuraMobDropItems.DRAGON_ESSENCE.get()).m_79078_(SetItemCountFunction.m_165412_(UniformGenerator.m_165780_(1.0F, 3.0F))))).m_79161_(LootPool.m_79043_().m_165133_(ConstantValue.m_165692_(1.0F)).m_79076_(LootItem.m_79579_((ItemLike)TensuraMobDropItems.CHARYBDIS_SCALE.get()).m_79078_(SetItemCountFunction.m_165412_(UniformGenerator.m_165780_(6.0F, 12.0F))).m_79078_(LootingEnchantFunction.m_165229_(UniformGenerator.m_165780_(0.0F, 3.0F))))).m_79161_(LootPool.m_79043_().m_165133_(ConstantValue.m_165692_(1.0F)).m_79076_(LootItem.m_79579_((ItemLike)TensuraConsumableItems.RAW_CHARYBDIS_MEAT.get()).m_79078_(SetItemCountFunction.m_165412_(UniformGenerator.m_165780_(12.0F, 24.0F)))).m_79078_(SmeltItemFunction.m_81271_().m_79080_(LootItemEntityPropertyCondition.m_81864_(EntityTarget.THIS, f_124366_)))));
      this.m_124371_((EntityType)TensuraEntityTypes.DIREWOLF.get(), LootTable.m_79147_().m_79161_(LootPool.m_79043_().m_165133_(ConstantValue.m_165692_(1.0F)).m_79076_(LootItem.m_79579_((ItemLike)TensuraMobDropItems.MONSTER_LEATHER_C.get()).m_79078_(SetItemCountFunction.m_165412_(UniformGenerator.m_165780_(1.0F, 3.0F))).m_79078_(LootingEnchantFunction.m_165229_(UniformGenerator.m_165780_(0.0F, 3.0F))))));
      this.m_124371_((EntityType)TensuraEntityTypes.DRAGON_PEACOCK.get(), LootTable.m_79147_().m_79161_(LootPool.m_79043_().m_165133_(ConstantValue.m_165692_(1.0F)).m_79076_(LootItem.m_79579_((ItemLike)TensuraMobDropItems.DRAGON_PEACOCK_FEATHER.get()).m_79078_(SetItemCountFunction.m_165412_(UniformGenerator.m_165780_(0.0F, 2.0F))).m_79078_(LootingEnchantFunction.m_165229_(UniformGenerator.m_165780_(0.0F, 3.0F))))));
      this.m_124371_((EntityType)TensuraEntityTypes.ELEMENTAL_COLOSSUS.get(), LootTable.m_79147_().m_79161_(LootPool.m_79043_().m_165133_(ConstantValue.m_165692_(1.0F)).m_79076_(LootItem.m_79579_((ItemLike)TensuraBlocks.Items.PURE_MAGISTEEL_BLOCK.get()).m_79078_(SetItemCountFunction.m_165412_(UniformGenerator.m_165780_(1.0F, 1.0F))))));
      this.m_124371_((EntityType)TensuraEntityTypes.EVIL_CENTIPEDE.get(), LootTable.m_79147_().m_79161_(LootPool.m_79043_().m_165133_(ConstantValue.m_165692_(1.0F)).m_79076_(LootItem.m_79579_((ItemLike)TensuraMobDropItems.CENTIPEDE_STINGER.get()).m_79078_(SetItemCountFunction.m_165412_(UniformGenerator.m_165780_(0.0F, 2.0F))))));
      this.m_124371_((EntityType)TensuraEntityTypes.FEATHERED_SERPENT.get(), LootTable.m_79147_().m_79161_(LootPool.m_79043_().m_165133_(UniformGenerator.m_165780_(0.0F, 1.0F)).m_79076_(LootItem.m_79579_((ItemLike)TensuraMaterialItems.WIND_ELEMENTAL_SHARD.get()).m_79078_(SetItemCountFunction.m_165412_(UniformGenerator.m_165780_(0.0F, 1.0F))))));
      this.m_124371_((EntityType)TensuraEntityTypes.GIANT_ANT.get(), LootTable.m_79147_().m_79161_(LootPool.m_79043_().m_165133_(ConstantValue.m_165692_(1.0F)).m_79076_(LootItem.m_79579_((ItemLike)TensuraMobDropItems.GIANT_ANT_CARAPACE.get()).m_79078_(SetItemCountFunction.m_165412_(UniformGenerator.m_165780_(2.0F, 3.0F))).m_79078_(LootingEnchantFunction.m_165229_(UniformGenerator.m_165780_(0.0F, 3.0F))))).m_79161_(LootPool.m_79043_().m_165133_(ConstantValue.m_165692_(1.0F)).m_79076_(LootItem.m_79579_((ItemLike)TensuraConsumableItems.GIANT_ANT_LEG.get()).m_79078_(SetItemCountFunction.m_165412_(UniformGenerator.m_165780_(2.0F, 6.0F)))).m_79078_(SmeltItemFunction.m_81271_().m_79080_(LootItemEntityPropertyCondition.m_81864_(EntityTarget.THIS, f_124366_)))));
      this.m_124371_((EntityType)TensuraEntityTypes.GIANT_BAT.get(), LootTable.m_79147_().m_79161_(LootPool.m_79043_().m_165133_(ConstantValue.m_165692_(1.0F)).m_79076_(LootItem.m_79579_((ItemLike)TensuraMobDropItems.GIANT_BAT_WING.get()).m_79078_(SetItemCountFunction.m_165412_(UniformGenerator.m_165780_(1.0F, 2.0F))).m_79078_(LootingEnchantFunction.m_165229_(UniformGenerator.m_165780_(0.0F, 1.0F))))).m_79161_(LootPool.m_79043_().m_165133_(ConstantValue.m_165692_(1.0F)).m_79076_(LootItem.m_79579_((ItemLike)TensuraConsumableItems.RAW_GIANT_BAT_MEAT.get()).m_79078_(SetItemCountFunction.m_165412_(UniformGenerator.m_165780_(1.0F, 4.0F))).m_79078_(LootingEnchantFunction.m_165229_(UniformGenerator.m_165780_(0.0F, 3.0F)))).m_79078_(SmeltItemFunction.m_81271_().m_79080_(LootItemEntityPropertyCondition.m_81864_(EntityTarget.THIS, f_124366_)))));
      this.m_124371_((EntityType)TensuraEntityTypes.GIANT_BEAR.get(), LootTable.m_79147_().m_79161_(LootPool.m_79043_().m_165133_(ConstantValue.m_165692_(1.0F)).m_79076_(LootItem.m_79579_((ItemLike)TensuraMobDropItems.MONSTER_LEATHER_C.get()).m_79078_(SetItemCountFunction.m_165412_(UniformGenerator.m_165780_(1.0F, 3.0F))).m_79078_(LootingEnchantFunction.m_165229_(UniformGenerator.m_165780_(0.0F, 3.0F))))));
      this.m_124371_((EntityType)TensuraEntityTypes.GIANT_SALMON.get(), LootTable.m_79147_().m_79161_(LootPool.m_79043_().m_165133_(ConstantValue.m_165692_(1.0F)).m_79076_(LootItem.m_79579_(Items.f_42527_).m_79078_(SetItemCountFunction.m_165412_(UniformGenerator.m_165780_(3.0F, 7.0F))).m_79078_(LootingEnchantFunction.m_165229_(UniformGenerator.m_165780_(0.0F, 3.0F)))).m_79078_(SmeltItemFunction.m_81271_().m_79080_(LootItemEntityPropertyCondition.m_81864_(EntityTarget.THIS, f_124366_)))).m_79161_(LootPool.m_79043_().m_165133_(ConstantValue.m_165692_(1.0F)).m_79076_(LootItem.m_79579_(Items.f_42500_).m_79078_(SetItemCountFunction.m_165412_(UniformGenerator.m_165780_(0.0F, 4.0F))).m_79078_(LootingEnchantFunction.m_165229_(UniformGenerator.m_165780_(0.0F, 3.0F))))));
      this.m_124371_((EntityType)TensuraEntityTypes.GIANT_COD.get(), LootTable.m_79147_().m_79161_(LootPool.m_79043_().m_165133_(ConstantValue.m_165692_(1.0F)).m_79076_(LootItem.m_79579_(Items.f_42526_).m_79078_(SetItemCountFunction.m_165412_(UniformGenerator.m_165780_(3.0F, 7.0F))).m_79078_(LootingEnchantFunction.m_165229_(UniformGenerator.m_165780_(0.0F, 3.0F)))).m_79078_(SmeltItemFunction.m_81271_().m_79080_(LootItemEntityPropertyCondition.m_81864_(EntityTarget.THIS, f_124366_)))).m_79161_(LootPool.m_79043_().m_165133_(ConstantValue.m_165692_(1.0F)).m_79076_(LootItem.m_79579_(Items.f_42500_).m_79078_(SetItemCountFunction.m_165412_(UniformGenerator.m_165780_(0.0F, 4.0F))).m_79078_(LootingEnchantFunction.m_165229_(UniformGenerator.m_165780_(0.0F, 3.0F))))));
      this.m_124371_((EntityType)TensuraEntityTypes.GOBLIN.get(), LootTable.m_79147_().m_79161_(LootPool.m_79043_().m_165133_(ConstantValue.m_165692_(1.0F)).m_79076_(LootItem.m_79579_(Items.f_42454_).m_79078_(SetItemCountFunction.m_165412_(UniformGenerator.m_165780_(0.0F, 2.0F))).m_79078_(LootingEnchantFunction.m_165229_(UniformGenerator.m_165780_(0.0F, 3.0F))))).m_79161_(LootPool.m_79043_().m_165133_(ConstantValue.m_165692_(1.0F)).m_79076_(LootItem.m_79579_(Items.f_42500_).m_79078_(SetItemCountFunction.m_165412_(UniformGenerator.m_165780_(0.0F, 2.0F))).m_79078_(LootingEnchantFunction.m_165229_(UniformGenerator.m_165780_(0.0F, 3.0F))))));
      this.m_124371_((EntityType)TensuraEntityTypes.ONE_EYED_OWL.get(), LootTable.m_79147_().m_79161_(LootPool.m_79043_().m_165133_(ConstantValue.m_165692_(1.0F)).m_79076_(LootItem.m_79579_(Items.f_42545_).m_79078_(SetItemCountFunction.m_165412_(UniformGenerator.m_165780_(1.0F, 1.0F))))).m_79161_(LootPool.m_79043_().m_165133_(ConstantValue.m_165692_(1.0F)).m_79076_(LootItem.m_79579_((ItemLike)TensuraMobDropItems.INVISIBLE_FEATHER.get()).m_79078_(SetItemCountFunction.m_165412_(UniformGenerator.m_165780_(0.0F, 1.0F))).m_79078_(LootingEnchantFunction.m_165229_(UniformGenerator.m_165780_(0.0F, 2.0F))))));
      this.m_124371_((EntityType)TensuraEntityTypes.ORC.get(), LootTable.m_79147_().m_79161_(LootPool.m_79043_().m_165133_(ConstantValue.m_165692_(1.0F)).m_79076_(LootItem.m_79579_(Items.f_42485_).m_79078_(SetItemCountFunction.m_165412_(UniformGenerator.m_165780_(0.0F, 2.0F))).m_79078_(LootingEnchantFunction.m_165229_(UniformGenerator.m_165780_(0.0F, 3.0F)))).m_79078_(SmeltItemFunction.m_81271_().m_79080_(LootItemEntityPropertyCondition.m_81864_(EntityTarget.THIS, f_124366_)))));
      this.m_124371_((EntityType)TensuraEntityTypes.ORC_DISASTER.get(), LootTable.m_79147_().m_79161_(LootPool.m_79043_().m_165133_(ConstantValue.m_165692_(1.0F)).m_79076_(LootItem.m_79579_(Items.f_42485_).m_79078_(SetItemCountFunction.m_165412_(UniformGenerator.m_165780_(2.0F, 7.0F))).m_79078_(LootingEnchantFunction.m_165229_(UniformGenerator.m_165780_(0.0F, 3.0F)))).m_79078_(SmeltItemFunction.m_81271_().m_79080_(LootItemEntityPropertyCondition.m_81864_(EntityTarget.THIS, f_124366_)))).m_79161_(LootPool.m_79043_().m_165133_(ConstantValue.m_165692_(1.0F)).m_79076_(LootItem.m_79579_((ItemLike)TensuraBlocks.Items.ORC_DISASTER_HEAD.get()))).m_79161_(LootPool.m_79043_().m_79076_(LootItem.m_79579_((ItemLike)TensuraMaterialItems.MUSIC_DISC_NANODA.get())).m_79080_(LootItemEntityPropertyCondition.m_81864_(EntityTarget.KILLER, Builder.m_36633_().m_36636_((EntityType)TensuraEntityTypes.CHARYBDIS.get())))));
      this.m_124371_((EntityType)TensuraEntityTypes.ORC_LORD.get(), LootTable.m_79147_().m_79161_(LootPool.m_79043_().m_165133_(ConstantValue.m_165692_(1.0F)).m_79076_(LootItem.m_79579_(Items.f_42485_).m_79078_(SetItemCountFunction.m_165412_(UniformGenerator.m_165780_(0.0F, 5.0F))).m_79078_(LootingEnchantFunction.m_165229_(UniformGenerator.m_165780_(0.0F, 3.0F)))).m_79078_(SmeltItemFunction.m_81271_().m_79080_(LootItemEntityPropertyCondition.m_81864_(EntityTarget.THIS, f_124366_)))));
      this.m_124371_((EntityType)TensuraEntityTypes.HELL_CATERPILLAR.get(), LootTable.m_79147_().m_79161_(LootPool.m_79043_().m_165133_(ConstantValue.m_165692_(1.0F)).m_79076_(LootItem.m_79579_((ItemLike)TensuraMobDropItems.HELL_MOTH_SILK.get()).m_79078_(SetItemCountFunction.m_165412_(UniformGenerator.m_165780_(2.0F, 4.0F))).m_79078_(LootingEnchantFunction.m_165229_(UniformGenerator.m_165780_(0.0F, 3.0F))))));
      this.m_124371_((EntityType)TensuraEntityTypes.HELL_MOTH.get(), LootTable.m_79147_().m_79161_(LootPool.m_79043_().m_165133_(ConstantValue.m_165692_(1.0F)).m_79076_(LootItem.m_79579_((ItemLike)TensuraMobDropItems.HELL_MOTH_SILK.get()).m_79078_(SetItemCountFunction.m_165412_(UniformGenerator.m_165780_(2.0F, 8.0F))).m_79078_(LootingEnchantFunction.m_165229_(UniformGenerator.m_165780_(0.0F, 3.0F))))));
      this.m_124371_((EntityType)TensuraEntityTypes.HOLY_COW.get(), LootTable.m_79147_().m_79161_(LootPool.m_79043_().m_165133_(ConstantValue.m_165692_(1.0F)).m_79076_(LootItem.m_79579_(Items.f_42579_).m_79078_(SetItemCountFunction.m_165412_(UniformGenerator.m_165780_(1.0F, 3.0F))).m_79078_(LootingEnchantFunction.m_165229_(UniformGenerator.m_165780_(0.0F, 3.0F)))).m_79078_(SmeltItemFunction.m_81271_().m_79080_(LootItemEntityPropertyCondition.m_81864_(EntityTarget.THIS, f_124366_)))).m_79161_(LootPool.m_79043_().m_165133_(ConstantValue.m_165692_(1.0F)).m_79076_(LootItem.m_79579_(Items.f_42454_).m_79078_(SetItemCountFunction.m_165412_(UniformGenerator.m_165780_(0.0F, 2.0F))).m_79078_(LootingEnchantFunction.m_165229_(UniformGenerator.m_165780_(0.0F, 3.0F))))));
      this.m_124371_((EntityType)TensuraEntityTypes.HORNED_RABBIT.get(), LootTable.m_79147_().m_79161_(LootPool.m_79043_().m_165133_(ConstantValue.m_165692_(1.0F)).m_79076_(LootItem.m_79579_((ItemLike)TensuraMobDropItems.MONSTER_LEATHER_C.get()).m_79078_(SetItemCountFunction.m_165412_(UniformGenerator.m_165780_(1.0F, 2.0F))).m_79078_(LootingEnchantFunction.m_165229_(UniformGenerator.m_165780_(0.0F, 3.0F))))).m_79161_(LootPool.m_79043_().m_165133_(ConstantValue.m_165692_(1.0F)).m_79076_(LootItem.m_79579_((ItemLike)TensuraMobDropItems.BEAST_HORN.get()).m_79078_(SetItemCountFunction.m_165412_(UniformGenerator.m_165780_(1.0F, 1.0F))))));
      this.m_124371_((EntityType)TensuraEntityTypes.HOUND_DOG.get(), LootTable.m_79147_().m_79161_(LootPool.m_79043_().m_165133_(ConstantValue.m_165692_(1.0F)).m_79076_(LootItem.m_79579_((ItemLike)TensuraMobDropItems.MONSTER_LEATHER_D.get()).m_79078_(SetItemCountFunction.m_165412_(UniformGenerator.m_165780_(1.0F, 2.0F))).m_79078_(LootingEnchantFunction.m_165229_(UniformGenerator.m_165780_(0.0F, 3.0F))))));
      this.m_124371_((EntityType)TensuraEntityTypes.HOVER_LIZARD.get(), LootTable.m_79147_().m_79161_(LootPool.m_79043_().m_165133_(ConstantValue.m_165692_(1.0F)).m_79076_(LootItem.m_79579_((ItemLike)TensuraMobDropItems.MONSTER_LEATHER_C.get()).m_79078_(SetItemCountFunction.m_165412_(UniformGenerator.m_165780_(1.0F, 2.0F))).m_79078_(LootingEnchantFunction.m_165229_(UniformGenerator.m_165780_(0.0F, 3.0F))))));
      this.m_124371_((EntityType)TensuraEntityTypes.HORNED_BEAR.get(), LootTable.m_79147_().m_79161_(LootPool.m_79043_().m_165133_(ConstantValue.m_165692_(1.0F)).m_79076_(LootItem.m_79579_((ItemLike)TensuraMobDropItems.MONSTER_LEATHER_C.get()).m_79078_(SetItemCountFunction.m_165412_(UniformGenerator.m_165780_(0.0F, 3.0F))).m_79078_(LootingEnchantFunction.m_165229_(UniformGenerator.m_165780_(0.0F, 3.0F))))));
      this.m_124371_((EntityType)TensuraEntityTypes.IFRIT.get(), LootTable.m_79147_().m_79161_(LootPool.m_79043_().m_165133_(ConstantValue.m_165692_(1.0F)).m_79076_(LootItem.m_79579_((ItemLike)TensuraMobDropItems.ELEMENTAL_ESSENCE.get()).m_79078_(SetItemCountFunction.m_165412_(ConstantValue.m_165692_(1.0F))))).m_79161_(LootPool.m_79043_().m_165133_(ConstantValue.m_165692_(1.0F)).m_79076_(LootItem.m_79579_((ItemLike)TensuraMaterialItems.FIRE_ELEMENTAL_SHARD.get()).m_79078_(SetItemCountFunction.m_165412_(UniformGenerator.m_165780_(1.0F, 3.0F))))));
      this.m_124371_((EntityType)TensuraEntityTypes.KNIGHT_SPIDER.get(), LootTable.m_79147_().m_79161_(LootPool.m_79043_().m_165133_(ConstantValue.m_165692_(1.0F)).m_79076_(LootItem.m_79579_((ItemLike)TensuraMobDropItems.KNIGHT_SPIDER_CARAPACE.get()).m_79078_(SetItemCountFunction.m_165412_(UniformGenerator.m_165780_(2.0F, 3.0F))).m_79078_(LootingEnchantFunction.m_165229_(UniformGenerator.m_165780_(0.0F, 3.0F))))).m_79161_(LootPool.m_79043_().m_165133_(ConstantValue.m_165692_(1.0F)).m_79076_(LootItem.m_79579_((ItemLike)TensuraConsumableItems.KNIGHT_SPIDER_LEG.get()).m_79078_(SetItemCountFunction.m_165412_(UniformGenerator.m_165780_(2.0F, 8.0F))))).m_79078_(SmeltItemFunction.m_81271_().m_79080_(LootItemEntityPropertyCondition.m_81864_(EntityTarget.THIS, f_124366_))));
      this.m_124371_((EntityType)TensuraEntityTypes.LANDFISH.get(), LootTable.m_79147_().m_79161_(LootPool.m_79043_().m_165133_(ConstantValue.m_165692_(1.0F)).m_79076_(LootItem.m_79579_(Items.f_42695_).m_79078_(SetItemCountFunction.m_165412_(UniformGenerator.m_165780_(0.0F, 3.0F))).m_79078_(LootingEnchantFunction.m_165229_(UniformGenerator.m_165780_(0.0F, 3.0F))))).m_79161_(LootPool.m_79043_().m_165133_(ConstantValue.m_165692_(1.0F)).m_79076_(LootItem.m_79579_(Items.f_42696_).m_79078_(SetItemCountFunction.m_165412_(UniformGenerator.m_165780_(0.0F, 2.0F))))).m_79078_(LootingEnchantFunction.m_165229_(UniformGenerator.m_165780_(0.0F, 3.0F))));
      this.m_124371_((EntityType)TensuraEntityTypes.LEECH_LIZARD.get(), LootTable.m_79147_().m_79161_(LootPool.m_79043_().m_165133_(ConstantValue.m_165692_(1.0F)).m_79076_(LootItem.m_79579_((ItemLike)TensuraMobDropItems.MONSTER_LEATHER_C.get()).m_79078_(SetItemCountFunction.m_165412_(UniformGenerator.m_165780_(1.0F, 2.0F))).m_79078_(LootingEnchantFunction.m_165229_(UniformGenerator.m_165780_(0.0F, 3.0F))))));
      this.m_124371_((EntityType)TensuraEntityTypes.LIZARDMAN.get(), LootTable.m_79147_().m_79161_(LootPool.m_79043_().m_165133_(ConstantValue.m_165692_(1.0F)).m_79076_(LootItem.m_79579_(Items.f_42526_).m_79078_(SetItemCountFunction.m_165412_(UniformGenerator.m_165780_(0.0F, 2.0F))).m_79078_(LootingEnchantFunction.m_165229_(UniformGenerator.m_165780_(0.0F, 3.0F)))).m_79078_(SmeltItemFunction.m_81271_().m_79080_(LootItemEntityPropertyCondition.m_81864_(EntityTarget.THIS, f_124366_)))).m_79161_(LootPool.m_79043_().m_165133_(ConstantValue.m_165692_(1.0F)).m_79076_(LootItem.m_79579_(Items.f_42527_).m_79078_(SetItemCountFunction.m_165412_(UniformGenerator.m_165780_(0.0F, 2.0F))).m_79078_(LootingEnchantFunction.m_165229_(UniformGenerator.m_165780_(0.0F, 3.0F)))).m_79078_(SmeltItemFunction.m_81271_().m_79080_(LootItemEntityPropertyCondition.m_81864_(EntityTarget.THIS, f_124366_)))));
      this.m_124371_((EntityType)TensuraEntityTypes.MEGALODON.get(), LootTable.m_79147_().m_79161_(LootPool.m_79043_().m_165133_(ConstantValue.m_165692_(1.0F)).m_79076_(LootItem.m_79579_((ItemLike)TensuraConsumableItems.RAW_MEGALODON_MEAT.get()).m_79078_(SetItemCountFunction.m_165412_(UniformGenerator.m_165780_(6.0F, 10.0F))).m_79078_(LootingEnchantFunction.m_165229_(UniformGenerator.m_165780_(0.0F, 3.0F)))).m_79078_(SmeltItemFunction.m_81271_().m_79080_(LootItemEntityPropertyCondition.m_81864_(EntityTarget.THIS, f_124366_)))).m_79161_(LootPool.m_79043_().m_165133_(ConstantValue.m_165692_(1.0F)).m_79076_(LootItem.m_79579_(Items.f_42500_).m_79078_(SetItemCountFunction.m_165412_(UniformGenerator.m_165780_(2.0F, 5.0F))).m_79078_(LootingEnchantFunction.m_165229_(UniformGenerator.m_165780_(0.0F, 3.0F))))));
      this.m_124371_((EntityType)TensuraEntityTypes.SLIME.get(), LootTable.m_79147_().m_79161_(LootPool.m_79043_().m_165133_(ConstantValue.m_165692_(1.0F)).m_79076_(LootItem.m_79579_((ItemLike)TensuraMobDropItems.SLIME_CHUNK.get()).m_79078_(SetItemCountFunction.m_165412_(UniformGenerator.m_165780_(1.0F, 1.0F))).m_79078_(LootingEnchantFunction.m_165229_(UniformGenerator.m_165780_(0.0F, 3.0F))))));
      this.m_124371_((EntityType)TensuraEntityTypes.METAL_SLIME.get(), LootTable.m_79147_().m_79161_(LootPool.m_79043_().m_165133_(ConstantValue.m_165692_(1.0F)).m_79076_(LootItem.m_79579_((ItemLike)TensuraMaterialItems.MAGIC_ORE.get()).m_79078_(SetItemCountFunction.m_165412_(UniformGenerator.m_165780_(5.0F, 8.0F))).m_79078_(LootingEnchantFunction.m_165229_(UniformGenerator.m_165780_(0.0F, 3.0F))))));
      this.m_124371_((EntityType)TensuraEntityTypes.SUPERMASSIVE_SLIME.get(), LootTable.m_79147_().m_79161_(LootPool.m_79043_().m_165133_(ConstantValue.m_165692_(1.0F)).m_79076_(LootItem.m_79579_((ItemLike)TensuraMobDropItems.SLIME_CHUNK.get()).m_79078_(SetItemCountFunction.m_165412_(UniformGenerator.m_165780_(30.0F, 50.0F))).m_79078_(LootingEnchantFunction.m_165229_(UniformGenerator.m_165780_(0.0F, 5.0F))))).m_79161_(LootPool.m_79043_().m_165133_(ConstantValue.m_165692_(1.0F)).m_79076_(LootItem.m_79579_((ItemLike)TensuraMobDropItems.SLIME_CORE.get()).m_79078_(SetItemCountFunction.m_165412_(UniformGenerator.m_165780_(1.0F, 1.0F))))));
      this.m_124371_((EntityType)TensuraEntityTypes.SISSIE.get(), LootTable.m_79147_().m_79161_(LootPool.m_79043_().m_165133_(UniformGenerator.m_165780_(0.0F, 1.0F)).m_79076_(LootItem.m_79579_((ItemLike)TensuraMobDropItems.SISSIE_TOOTH.get()).m_79078_(SetItemCountFunction.m_165412_(UniformGenerator.m_165780_(0.0F, 1.0F))))).m_79161_(LootPool.m_79043_().m_165133_(ConstantValue.m_165692_(1.0F)).m_79076_(LootItem.m_79579_((ItemLike)TensuraConsumableItems.SISSIE_FIN.get()).m_79078_(SetItemCountFunction.m_165412_(UniformGenerator.m_165780_(0.0F, 2.0F))).m_79078_(LootingEnchantFunction.m_165229_(UniformGenerator.m_165780_(0.0F, 2.0F))))).m_79161_(LootPool.m_79043_().m_165133_(ConstantValue.m_165692_(1.0F)).m_79076_(LootItem.m_79579_((ItemLike)TensuraConsumableItems.RAW_SISSIE_MEAT.get()).m_79078_(SetItemCountFunction.m_165412_(UniformGenerator.m_165780_(2.0F, 8.0F))).m_79078_(LootingEnchantFunction.m_165229_(UniformGenerator.m_165780_(0.0F, 3.0F))))).m_79161_(LootPool.m_79043_().m_165133_(ConstantValue.m_165692_(1.0F)).m_79076_(LootItem.m_79579_(Items.f_42500_).m_79078_(SetItemCountFunction.m_165412_(UniformGenerator.m_165780_(0.0F, 3.0F))))).m_79078_(LootingEnchantFunction.m_165229_(UniformGenerator.m_165780_(0.0F, 3.0F))));
      this.m_124371_((EntityType)TensuraEntityTypes.SALAMANDER.get(), LootTable.m_79147_().m_79161_(LootPool.m_79043_().m_165133_(UniformGenerator.m_165780_(0.0F, 1.0F)).m_79076_(LootItem.m_79579_((ItemLike)TensuraMaterialItems.FIRE_ELEMENTAL_SHARD.get()).m_79078_(SetItemCountFunction.m_165412_(UniformGenerator.m_165780_(0.0F, 1.0F))))));
      this.m_124371_((EntityType)TensuraEntityTypes.SPEAR_TORO.get(), LootTable.m_79147_().m_79161_(LootPool.m_79043_().m_165133_(ConstantValue.m_165692_(1.0F)).m_79076_(LootItem.m_79579_((ItemLike)TensuraConsumableItems.SPEAR_TORO_FIN.get()).m_79078_(SetItemCountFunction.m_165412_(UniformGenerator.m_165780_(0.0F, 2.0F))).m_79078_(LootingEnchantFunction.m_165229_(UniformGenerator.m_165780_(0.0F, 2.0F))))).m_79161_(LootPool.m_79043_().m_165133_(ConstantValue.m_165692_(1.0F)).m_79076_(LootItem.m_79579_((ItemLike)TensuraConsumableItems.RAW_SPEAR_TORO_MEAT.get()).m_79078_(SetItemCountFunction.m_165412_(UniformGenerator.m_165780_(2.0F, 8.0F))).m_79078_(LootingEnchantFunction.m_165229_(UniformGenerator.m_165780_(0.0F, 3.0F))))).m_79161_(LootPool.m_79043_().m_165133_(ConstantValue.m_165692_(1.0F)).m_79076_(LootItem.m_79579_(Items.f_42500_).m_79078_(SetItemCountFunction.m_165412_(UniformGenerator.m_165780_(0.0F, 2.0F))))).m_79078_(LootingEnchantFunction.m_165229_(UniformGenerator.m_165780_(0.0F, 3.0F))));
      this.m_124371_((EntityType)TensuraEntityTypes.SYLPHIDE.get(), LootTable.m_79147_().m_79161_(LootPool.m_79043_().m_165133_(ConstantValue.m_165692_(1.0F)).m_79076_(LootItem.m_79579_((ItemLike)TensuraMobDropItems.ELEMENTAL_ESSENCE.get()).m_79078_(SetItemCountFunction.m_165412_(ConstantValue.m_165692_(1.0F))))).m_79161_(LootPool.m_79043_().m_165133_(ConstantValue.m_165692_(1.0F)).m_79076_(LootItem.m_79579_((ItemLike)TensuraMaterialItems.WIND_ELEMENTAL_SHARD.get()).m_79078_(SetItemCountFunction.m_165412_(UniformGenerator.m_165780_(1.0F, 3.0F))))));
      this.m_124371_((EntityType)TensuraEntityTypes.TEMPEST_SERPENT.get(), LootTable.m_79147_().m_79161_(LootPool.m_79043_().m_165133_(ConstantValue.m_165692_(1.0F)).m_79076_(LootItem.m_79579_((ItemLike)TensuraMobDropItems.SERPENT_SCALE.get()).m_79078_(SetItemCountFunction.m_165412_(UniformGenerator.m_165780_(1.0F, 6.0F))).m_79078_(LootingEnchantFunction.m_165229_(UniformGenerator.m_165780_(0.0F, 3.0F))))).m_79161_(LootPool.m_79043_().m_165133_(ConstantValue.m_165692_(1.0F)).m_79076_(LootItem.m_79579_((ItemLike)TensuraConsumableItems.RAW_SERPENT_MEAT.get()).m_79078_(SetItemCountFunction.m_165412_(UniformGenerator.m_165780_(1.0F, 4.0F))).m_79078_(LootingEnchantFunction.m_165229_(UniformGenerator.m_165780_(0.0F, 3.0F)))).m_79078_(SmeltItemFunction.m_81271_().m_79080_(LootItemEntityPropertyCondition.m_81864_(EntityTarget.THIS, f_124366_)))));
      this.m_124371_((EntityType)TensuraEntityTypes.UNDINE.get(), LootTable.m_79147_().m_79161_(LootPool.m_79043_().m_165133_(ConstantValue.m_165692_(1.0F)).m_79076_(LootItem.m_79579_((ItemLike)TensuraMobDropItems.ELEMENTAL_ESSENCE.get()).m_79078_(SetItemCountFunction.m_165412_(ConstantValue.m_165692_(1.0F))))).m_79161_(LootPool.m_79043_().m_165133_(ConstantValue.m_165692_(1.0F)).m_79076_(LootItem.m_79579_((ItemLike)TensuraMaterialItems.WATER_ELEMENTAL_SHARD.get()).m_79078_(SetItemCountFunction.m_165412_(UniformGenerator.m_165780_(1.0F, 3.0F))))));
      this.m_124371_((EntityType)TensuraEntityTypes.UNICORN.get(), LootTable.m_79147_().m_79161_(LootPool.m_79043_().m_165133_(ConstantValue.m_165692_(1.0F)).m_79076_(LootItem.m_79579_((ItemLike)TensuraMobDropItems.MONSTER_LEATHER_B.get()).m_79078_(SetItemCountFunction.m_165412_(UniformGenerator.m_165780_(0.0F, 2.0F))).m_79078_(LootingEnchantFunction.m_165229_(UniformGenerator.m_165780_(0.0F, 3.0F))))).m_79161_(LootPool.m_79043_().m_165133_(ConstantValue.m_165692_(1.0F)).m_79076_(LootItem.m_79579_((ItemLike)TensuraMobDropItems.UNICORN_HORN.get()).m_79078_(SetItemCountFunction.m_165412_(UniformGenerator.m_165780_(1.0F, 1.0F))))));
      this.m_124371_((EntityType)TensuraEntityTypes.WAR_GNOME.get(), LootTable.m_79147_().m_79161_(LootPool.m_79043_().m_165133_(ConstantValue.m_165692_(1.0F)).m_79076_(LootItem.m_79579_((ItemLike)TensuraMobDropItems.ELEMENTAL_ESSENCE.get()).m_79078_(SetItemCountFunction.m_165412_(ConstantValue.m_165692_(1.0F))))).m_79161_(LootPool.m_79043_().m_165133_(ConstantValue.m_165692_(1.0F)).m_79076_(LootItem.m_79579_((ItemLike)TensuraMaterialItems.EARTH_ELEMENTAL_SHARD.get()).m_79078_(SetItemCountFunction.m_165412_(UniformGenerator.m_165780_(1.0F, 3.0F))))));
      this.m_124371_((EntityType)TensuraEntityTypes.WINGED_CAT.get(), LootTable.m_79147_().m_79161_(LootPool.m_79043_().m_165133_(UniformGenerator.m_165780_(0.0F, 1.0F)).m_79076_(LootItem.m_79579_((ItemLike)TensuraMaterialItems.SPACE_ELEMENTAL_SHARD.get()).m_79078_(SetItemCountFunction.m_165412_(UniformGenerator.m_165780_(0.0F, 1.0F))))));
   }
}
