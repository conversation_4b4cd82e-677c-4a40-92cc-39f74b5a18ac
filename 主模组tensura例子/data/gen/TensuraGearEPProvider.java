package com.github.manasmods.tensura.data.gen;

import com.github.manasmods.manascore.api.data.gen.CustomDataProvider;
import com.github.manasmods.tensura.data.pack.GearEPCount;
import com.github.manasmods.tensura.registry.blocks.TensuraBlocks;
import com.github.manasmods.tensura.registry.items.TensuraArmorItems;
import com.github.manasmods.tensura.registry.items.TensuraToolItems;
import com.google.gson.JsonElement;
import java.util.function.BiConsumer;
import java.util.function.Supplier;
import net.minecraft.resources.ResourceLocation;
import net.minecraftforge.data.event.GatherDataEvent;

public class TensuraGearEPProvider extends CustomDataProvider {
   public TensuraGearEPProvider(GatherDataEvent gatherDataEvent) {
      super("gear/ep", gatherDataEvent.getGenerator());
   }

   public String m_6055_() {
      return "Tensura Gear EP";
   }

   protected void run(BiConsumer<ResourceLocation, Supplier<JsonElement>> biConsumer) {
      monsterLeatherD(biConsumer);
      monsterLeatherC(biConsumer);
      monsterLeatherB(biConsumer);
      monsterLeatherA(biConsumer);
      monsterLeatherSpecialA(biConsumer);
      giantAntCarapace(biConsumer);
      lowMagiSteel(biConsumer);
      knightSpiderCarapace(biConsumer);
      armorsaurusScale(biConsumer);
      highMagiSteel(biConsumer);
      mithril(biConsumer);
      orichalcum(biConsumer);
      pureMagiSteel(biConsumer);
      charybdisScale(biConsumer);
      adamantite(biConsumer);
      hihiirokane(biConsumer);
      uniqueGears(biConsumer);
      uniqueArmors(biConsumer);
   }

   protected static void uniqueGears(BiConsumer<ResourceLocation, Supplier<JsonElement>> biConsumer) {
      GearEPCount.of(TensuraToolItems.AURA_SHIELD.getId(), 3000, 1000000, 0.006D).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.KANABO.getId(), 6000, 18000, 0.015D).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.PURE_MAGISTEEL_KUNAI.getId(), 52000, 225000, 0.035D).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.DRAGON_KNUCKLE.getId(), 52000, 1000000, 0.035D).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.DEAD_END_RAINBOW.getId(), 45000, 1000000, 0.03D).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.ICE_BLADE.getId(), 18000, 52000, 0.03D).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.MEAT_CRUSHER.getId(), 18000, 52000, 0.03D).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.MOONLIGHT.getId(), 60000, 1000000, 0.03D).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.SPATIAL_BLADE.getId(), 18000, 52000, 0.03D).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.VORTEX_SPEAR.getId(), 18000, 52000, 0.03D).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.WEB_GUN.getId(), 6000, 18000, 0.015D).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.SLIME_STAFF.getId(), 52000, 225000, 0.035D).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.SISSIE_TOOTH_PICKAXE.getId(), 18000, 52000, 0.03D).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.CENTIPEDE_DAGGER.getId(), 6000, 18000, 0.015D).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.SPIDER_DAGGER.getId(), 6000, 18000, 0.015D).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.BEAST_HORN_SPEAR.getId(), 6000, 18000, 0.015D).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.UNICORN_HORN_SPEAR.getId(), 18000, 52000, 0.03D).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.BLADE_TIGER_SCYTHE.getId(), 18000, 52000, 0.03D).buildJson(biConsumer);
   }

   protected static void uniqueArmors(BiConsumer<ResourceLocation, Supplier<JsonElement>> biConsumer) {
      GearEPCount.of(TensuraArmorItems.WINGED_SHOES.getId(), 4000, 100000, 0.01D).buildJson(biConsumer);
      GearEPCount.of(TensuraArmorItems.BAT_GLIDER.getId(), 2500, 100000, 0.01D).buildJson(biConsumer);
      GearEPCount.of(TensuraArmorItems.HOLY_ARMAMENTS_LEGGINGS.getId(), 50000, 750000, 0.03D).buildJson(biConsumer);
      GearEPCount.of(TensuraArmorItems.HOLY_ARMAMENTS_CHESTPLATE.getId(), 50000, 750000, 0.03D).buildJson(biConsumer);
      GearEPCount.of(TensuraArmorItems.HOLY_ARMAMENTS_BOOTS.getId(), 50000, 750000, 0.03D).buildJson(biConsumer);
      GearEPCount.of(TensuraArmorItems.DARK_BOOTS.getId(), 5000, 8000, 0.02D).buildJson(biConsumer);
      GearEPCount.of(TensuraArmorItems.DARK_LEGGINGS.getId(), 5000, 8000, 0.02D).buildJson(biConsumer);
      GearEPCount.of(TensuraArmorItems.DARK_JACKET.getId(), 5000, 8000, 0.02D).buildJson(biConsumer);
      GearEPCount.of(TensuraArmorItems.ANTI_MAGIC_MASK.getId(), 10400, 1000000, 0.09D).buildJson(biConsumer);
      GearEPCount.of(TensuraArmorItems.ANGRY_PIERROT_MASK.getId(), 10000, 1000000, 0.06D).buildJson(biConsumer);
      GearEPCount.of(TensuraArmorItems.CRAZY_PIERROT_MASK.getId(), 10000, 1000000, 0.06D).buildJson(biConsumer);
      GearEPCount.of(TensuraArmorItems.TEARY_PIERROT_MASK.getId(), 10000, 1000000, 0.06D).buildJson(biConsumer);
      GearEPCount.of(TensuraArmorItems.WONDER_PIERROT_MASK.getId(), 10000, 1000000, 0.06D).buildJson(biConsumer);
      GearEPCount.of(TensuraBlocks.Items.ORC_DISASTER_HEAD.getId(), 10000, 1000000, 0.05D).buildJson(biConsumer);
   }

   protected static void monsterLeatherD(BiConsumer<ResourceLocation, Supplier<JsonElement>> biConsumer) {
      GearEPCount.of(TensuraArmorItems.MONSTER_LEATHER_D_BOOTS.getId(), 1000, 2500, 0.005D, TensuraArmorItems.MONSTER_LEATHER_C_BOOTS.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraArmorItems.MONSTER_LEATHER_D_LEGGINGS.getId(), 1000, 2500, 0.005D, TensuraArmorItems.MONSTER_LEATHER_C_LEGGINGS.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraArmorItems.MONSTER_LEATHER_D_CHESTPLATE.getId(), 1000, 2500, 0.005D, TensuraArmorItems.MONSTER_LEATHER_C_CHESTPLATE.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraArmorItems.MONSTER_LEATHER_D_HELMET.getId(), 1000, 2500, 0.005D, TensuraArmorItems.MONSTER_LEATHER_C_HELMET.getId()).buildJson(biConsumer);
   }

   protected static void monsterLeatherC(BiConsumer<ResourceLocation, Supplier<JsonElement>> biConsumer) {
      GearEPCount.of(TensuraArmorItems.MONSTER_LEATHER_C_BOOTS.getId(), 2500, 5000, 0.01D, TensuraArmorItems.MONSTER_LEATHER_B_BOOTS.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraArmorItems.MONSTER_LEATHER_C_LEGGINGS.getId(), 2500, 5000, 0.01D, TensuraArmorItems.MONSTER_LEATHER_B_LEGGINGS.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraArmorItems.MONSTER_LEATHER_C_CHESTPLATE.getId(), 2500, 5000, 0.01D, TensuraArmorItems.MONSTER_LEATHER_B_CHESTPLATE.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraArmorItems.MONSTER_LEATHER_C_HELMET.getId(), 2500, 5000, 0.01D, TensuraArmorItems.MONSTER_LEATHER_B_HELMET.getId()).buildJson(biConsumer);
   }

   protected static void monsterLeatherB(BiConsumer<ResourceLocation, Supplier<JsonElement>> biConsumer) {
      GearEPCount.of(TensuraArmorItems.MONSTER_LEATHER_B_BOOTS.getId(), 5000, 8000, 0.02D, TensuraArmorItems.MONSTER_LEATHER_A_BOOTS.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraArmorItems.MONSTER_LEATHER_B_LEGGINGS.getId(), 5000, 8000, 0.02D, TensuraArmorItems.MONSTER_LEATHER_A_LEGGINGS.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraArmorItems.MONSTER_LEATHER_B_CHESTPLATE.getId(), 5000, 8000, 0.02D, TensuraArmorItems.MONSTER_LEATHER_A_CHESTPLATE.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraArmorItems.MONSTER_LEATHER_B_HELMET.getId(), 5000, 8000, 0.02D, TensuraArmorItems.MONSTER_LEATHER_A_HELMET.getId()).buildJson(biConsumer);
   }

   protected static void monsterLeatherA(BiConsumer<ResourceLocation, Supplier<JsonElement>> biConsumer) {
      GearEPCount.of(TensuraArmorItems.MONSTER_LEATHER_A_BOOTS.getId(), 8000, 80000, 0.025D, TensuraArmorItems.MONSTER_LEATHER_SPECIAL_A_BOOTS.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraArmorItems.MONSTER_LEATHER_A_LEGGINGS.getId(), 8000, 80000, 0.025D, TensuraArmorItems.MONSTER_LEATHER_SPECIAL_A_LEGGINGS.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraArmorItems.MONSTER_LEATHER_A_CHESTPLATE.getId(), 8000, 80000, 0.025D, TensuraArmorItems.MONSTER_LEATHER_SPECIAL_A_CHESTPLATE.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraArmorItems.MONSTER_LEATHER_A_HELMET.getId(), 8000, 80000, 0.025D, TensuraArmorItems.MONSTER_LEATHER_SPECIAL_A_HELMET.getId()).buildJson(biConsumer);
   }

   protected static void monsterLeatherSpecialA(BiConsumer<ResourceLocation, Supplier<JsonElement>> biConsumer) {
      GearEPCount.of(TensuraArmorItems.MONSTER_LEATHER_SPECIAL_A_BOOTS.getId(), 80000, 1000000, 0.035D).buildJson(biConsumer);
      GearEPCount.of(TensuraArmorItems.MONSTER_LEATHER_SPECIAL_A_LEGGINGS.getId(), 80000, 1000000, 0.035D).buildJson(biConsumer);
      GearEPCount.of(TensuraArmorItems.MONSTER_LEATHER_SPECIAL_A_CHESTPLATE.getId(), 80000, 1000000, 0.035D).buildJson(biConsumer);
      GearEPCount.of(TensuraArmorItems.MONSTER_LEATHER_SPECIAL_A_HELMET.getId(), 80000, 1000000, 0.035D).buildJson(biConsumer);
   }

   protected static void giantAntCarapace(BiConsumer<ResourceLocation, Supplier<JsonElement>> biConsumer) {
      GearEPCount.of(TensuraArmorItems.ANT_CARAPACE_BOOTS.getId(), 3000, 1000000, 0.015D).buildJson(biConsumer);
      GearEPCount.of(TensuraArmorItems.ANT_CARAPACE_LEGGINGS.getId(), 3000, 1000000, 0.015D).buildJson(biConsumer);
      GearEPCount.of(TensuraArmorItems.ANT_CARAPACE_CHESTPLATE.getId(), 3000, 1000000, 0.015D).buildJson(biConsumer);
      GearEPCount.of(TensuraArmorItems.ANT_CARAPACE_HELMET.getId(), 3000, 1000000, 0.015D).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.ANT_CROSSBOW.getId(), 3000, 1000000, 0.015D).buildJson(biConsumer);
   }

   protected static void lowMagiSteel(BiConsumer<ResourceLocation, Supplier<JsonElement>> biConsumer) {
      GearEPCount.of(TensuraToolItems.LOW_MAGISTEEL_SWORD.getId(), 6000, 18000, 0.015D, TensuraToolItems.HIGH_MAGISTEEL_SWORD.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.LOW_MAGISTEEL_SHORT_SWORD.getId(), 6000, 18000, 0.015D, TensuraToolItems.HIGH_MAGISTEEL_SHORT_SWORD.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.LOW_MAGISTEEL_LONG_SWORD.getId(), 6000, 18000, 0.015D, TensuraToolItems.HIGH_MAGISTEEL_LONG_SWORD.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.LOW_MAGISTEEL_GREAT_SWORD.getId(), 6000, 18000, 0.015D, TensuraToolItems.HIGH_MAGISTEEL_GREAT_SWORD.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.LOW_MAGISTEEL_KATANA.getId(), 6000, 18000, 0.015D, TensuraToolItems.HIGH_MAGISTEEL_KATANA.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.LOW_MAGISTEEL_KODACHI.getId(), 6000, 18000, 0.015D, TensuraToolItems.HIGH_MAGISTEEL_KODACHI.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.LOW_MAGISTEEL_TACHI.getId(), 6000, 18000, 0.015D, TensuraToolItems.HIGH_MAGISTEEL_TACHI.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.LOW_MAGISTEEL_ODACHI.getId(), 6000, 18000, 0.015D, TensuraToolItems.HIGH_MAGISTEEL_ODACHI.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.LOW_MAGISTEEL_SPEAR.getId(), 6000, 18000, 0.015D, TensuraToolItems.HIGH_MAGISTEEL_SPEAR.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.LOW_MAGISTEEL_SCYTHE.getId(), 6000, 18000, 0.015D, TensuraToolItems.HIGH_MAGISTEEL_SCYTHE.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.LOW_MAGISTEEL_AXE.getId(), 6000, 18000, 0.015D, TensuraToolItems.HIGH_MAGISTEEL_AXE.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.LOW_MAGISTEEL_PICKAXE.getId(), 6000, 18000, 0.015D, TensuraToolItems.HIGH_MAGISTEEL_PICKAXE.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.LOW_MAGISTEEL_SHOVEL.getId(), 6000, 18000, 0.015D, TensuraToolItems.HIGH_MAGISTEEL_SHOVEL.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.LOW_MAGISTEEL_HOE.getId(), 6000, 18000, 0.015D, TensuraToolItems.HIGH_MAGISTEEL_HOE.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.LOW_MAGISTEEL_SICKLE.getId(), 6000, 18000, 0.015D, TensuraToolItems.HIGH_MAGISTEEL_SICKLE.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraArmorItems.LOW_MAGISTEEL_BOOTS.getId(), 6000, 18000, 0.015D, TensuraArmorItems.HIGH_MAGISTEEL_BOOTS.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraArmorItems.LOW_MAGISTEEL_LEGGINGS.getId(), 6000, 18000, 0.015D, TensuraArmorItems.HIGH_MAGISTEEL_LEGGINGS.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraArmorItems.LOW_MAGISTEEL_CHESTPLATE.getId(), 6000, 18000, 0.015D, TensuraArmorItems.HIGH_MAGISTEEL_CHESTPLATE.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraArmorItems.LOW_MAGISTEEL_HELMET.getId(), 6000, 18000, 0.015D, TensuraArmorItems.HIGH_MAGISTEEL_HELMET.getId()).buildJson(biConsumer);
   }

   protected static void knightSpiderCarapace(BiConsumer<ResourceLocation, Supplier<JsonElement>> biConsumer) {
      GearEPCount.of(TensuraArmorItems.KNIGHT_SPIDER_CARAPACE_BOOTS.getId(), 9000, 1000000, 0.02D).buildJson(biConsumer);
      GearEPCount.of(TensuraArmorItems.KNIGHT_SPIDER_CARAPACE_LEGGINGS.getId(), 9000, 1000000, 0.02D).buildJson(biConsumer);
      GearEPCount.of(TensuraArmorItems.KNIGHT_SPIDER_CARAPACE_CHESTPLATE.getId(), 9000, 1000000, 0.02008D).buildJson(biConsumer);
      GearEPCount.of(TensuraArmorItems.KNIGHT_SPIDER_CARAPACE_HELMET.getId(), 9000, 1000000, 0.02D).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.SHORT_SPIDER_BOW.getId(), 9000, 1000000, 0.02D).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.SPIDER_BOW.getId(), 9000, 1000000, 0.02D).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.LONG_SPIDER_BOW.getId(), 9000, 1000000, 0.02D).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.WAR_SPIDER_BOW.getId(), 9000, 1000000, 0.02D).buildJson(biConsumer);
   }

   protected static void armorsaurusScale(BiConsumer<ResourceLocation, Supplier<JsonElement>> biConsumer) {
      GearEPCount.of(TensuraToolItems.ARMOURSAURUS_GAUNTLET.getId(), 4800, 100000, 0.015D).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.ARMOURSAURUS_SHIELD.getId(), 6000, 1000000, 0.015D).buildJson(biConsumer);
      GearEPCount.of(TensuraArmorItems.ARMOURSAURUS_BOOTS.getId(), 6000, 1000000, 0.015D).buildJson(biConsumer);
      GearEPCount.of(TensuraArmorItems.ARMOURSAURUS_LEGGINGS.getId(), 6000, 1000000, 0.015D).buildJson(biConsumer);
      GearEPCount.of(TensuraArmorItems.ARMOURSAURUS_CHESTPLATE.getId(), 6000, 1000000, 0.015D).buildJson(biConsumer);
      GearEPCount.of(TensuraArmorItems.ARMOURSAURUS_HELMET.getId(), 6000, 1000000, 0.015D).buildJson(biConsumer);
      GearEPCount.of(TensuraArmorItems.ARMOURSAURUS_SCALEMAIL_BOOTS.getId(), 6000, 1000000, 0.015D).buildJson(biConsumer);
      GearEPCount.of(TensuraArmorItems.ARMOURSAURUS_SCALEMAIL_LEGGINGS.getId(), 6000, 1000000, 0.015D).buildJson(biConsumer);
      GearEPCount.of(TensuraArmorItems.ARMOURSAURUS_SCALEMAIL_CHESTPLATE.getId(), 6000, 1000000, 0.015D).buildJson(biConsumer);
      GearEPCount.of(TensuraArmorItems.ARMOURSAURUS_SCALEMAIL_HELMET.getId(), 6000, 1000000, 0.015D).buildJson(biConsumer);
   }

   protected static void highMagiSteel(BiConsumer<ResourceLocation, Supplier<JsonElement>> biConsumer) {
      GearEPCount.of(TensuraToolItems.HIGH_MAGISTEEL_SWORD.getId(), 18000, 52000, 0.03D, TensuraToolItems.PURE_MAGISTEEL_SWORD.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.HIGH_MAGISTEEL_SHORT_SWORD.getId(), 18000, 52000, 0.03D, TensuraToolItems.PURE_MAGISTEEL_SHORT_SWORD.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.HIGH_MAGISTEEL_LONG_SWORD.getId(), 18000, 52000, 0.03D, TensuraToolItems.PURE_MAGISTEEL_LONG_SWORD.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.HIGH_MAGISTEEL_GREAT_SWORD.getId(), 18000, 52000, 0.03D, TensuraToolItems.PURE_MAGISTEEL_GREAT_SWORD.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.HIGH_MAGISTEEL_KATANA.getId(), 18000, 52000, 0.03D, TensuraToolItems.PURE_MAGISTEEL_KATANA.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.HIGH_MAGISTEEL_KODACHI.getId(), 18000, 52000, 0.03D, TensuraToolItems.PURE_MAGISTEEL_KODACHI.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.HIGH_MAGISTEEL_TACHI.getId(), 18000, 52000, 0.03D, TensuraToolItems.PURE_MAGISTEEL_TACHI.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.HIGH_MAGISTEEL_ODACHI.getId(), 18000, 52000, 0.03D, TensuraToolItems.PURE_MAGISTEEL_ODACHI.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.HIGH_MAGISTEEL_SPEAR.getId(), 18000, 52000, 0.03D, TensuraToolItems.PURE_MAGISTEEL_SPEAR.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.HIGH_MAGISTEEL_SCYTHE.getId(), 18000, 52000, 0.03D, TensuraToolItems.PURE_MAGISTEEL_SCYTHE.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.HIGH_MAGISTEEL_AXE.getId(), 18000, 52000, 0.03D, TensuraToolItems.PURE_MAGISTEEL_AXE.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.HIGH_MAGISTEEL_PICKAXE.getId(), 18000, 52000, 0.03D, TensuraToolItems.PURE_MAGISTEEL_PICKAXE.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.HIGH_MAGISTEEL_SHOVEL.getId(), 18000, 52000, 0.03D, TensuraToolItems.PURE_MAGISTEEL_SHOVEL.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.HIGH_MAGISTEEL_HOE.getId(), 18000, 52000, 0.03D, TensuraToolItems.PURE_MAGISTEEL_HOE.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.HIGH_MAGISTEEL_SICKLE.getId(), 18000, 52000, 0.03D, TensuraToolItems.PURE_MAGISTEEL_SICKLE.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraArmorItems.HIGH_MAGISTEEL_BOOTS.getId(), 18000, 52000, 0.03D, TensuraArmorItems.PURE_MAGISTEEL_BOOTS.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraArmorItems.HIGH_MAGISTEEL_LEGGINGS.getId(), 18000, 52000, 0.03D, TensuraArmorItems.PURE_MAGISTEEL_LEGGINGS.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraArmorItems.HIGH_MAGISTEEL_CHESTPLATE.getId(), 18000, 52000, 0.03D, TensuraArmorItems.PURE_MAGISTEEL_CHESTPLATE.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraArmorItems.HIGH_MAGISTEEL_HELMET.getId(), 18000, 52000, 0.03D, TensuraArmorItems.PURE_MAGISTEEL_HELMET.getId()).buildJson(biConsumer);
   }

   protected static void mithril(BiConsumer<ResourceLocation, Supplier<JsonElement>> biConsumer) {
      GearEPCount.of(TensuraToolItems.MITHRIL_SWORD.getId(), 45000, 225000, 0.03D, TensuraToolItems.ADAMANTITE_SWORD.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.MITHRIL_SHORT_SWORD.getId(), 45000, 225000, 0.03D, TensuraToolItems.ADAMANTITE_SHORT_SWORD.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.MITHRIL_LONG_SWORD.getId(), 45000, 225000, 0.03D, TensuraToolItems.ADAMANTITE_LONG_SWORD.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.MITHRIL_GREAT_SWORD.getId(), 45000, 225000, 0.03D, TensuraToolItems.ADAMANTITE_GREAT_SWORD.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.MITHRIL_KATANA.getId(), 45000, 225000, 0.03D, TensuraToolItems.ADAMANTITE_KATANA.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.MITHRIL_KODACHI.getId(), 45000, 225000, 0.03D, TensuraToolItems.ADAMANTITE_KODACHI.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.MITHRIL_TACHI.getId(), 45000, 225000, 0.03D, TensuraToolItems.ADAMANTITE_TACHI.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.MITHRIL_ODACHI.getId(), 45000, 225000, 0.03D, TensuraToolItems.ADAMANTITE_ODACHI.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.MITHRIL_SPEAR.getId(), 45000, 225000, 0.03D, TensuraToolItems.ADAMANTITE_SPEAR.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.MITHRIL_SCYTHE.getId(), 45000, 225000, 0.03D, TensuraToolItems.ADAMANTITE_SCYTHE.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.MITHRIL_AXE.getId(), 45000, 225000, 0.03D, TensuraToolItems.ADAMANTITE_AXE.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.MITHRIL_PICKAXE.getId(), 45000, 225000, 0.03D, TensuraToolItems.ADAMANTITE_PICKAXE.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.MITHRIL_SHOVEL.getId(), 45000, 225000, 0.03D, TensuraToolItems.ADAMANTITE_SHOVEL.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.MITHRIL_HOE.getId(), 45000, 225000, 0.03D, TensuraToolItems.ADAMANTITE_HOE.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.MITHRIL_SICKLE.getId(), 45000, 225000, 0.03D, TensuraToolItems.ADAMANTITE_SICKLE.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraArmorItems.MITHRIL_BOOTS.getId(), 45000, 225000, 0.03D, TensuraArmorItems.ADAMANTITE_BOOTS.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraArmorItems.MITHRIL_LEGGINGS.getId(), 45000, 225000, 0.03D, TensuraArmorItems.ADAMANTITE_LEGGINGS.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraArmorItems.MITHRIL_CHESTPLATE.getId(), 45000, 225000, 0.03D, TensuraArmorItems.ADAMANTITE_CHESTPLATE.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraArmorItems.MITHRIL_HELMET.getId(), 45000, 225000, 0.03D, TensuraArmorItems.ADAMANTITE_HELMET.getId()).buildJson(biConsumer);
   }

   protected static void orichalcum(BiConsumer<ResourceLocation, Supplier<JsonElement>> biConsumer) {
      GearEPCount.of(TensuraToolItems.ORICHALCUM_SWORD.getId(), 50000, 750000, 0.03D, TensuraToolItems.HIHIIROKANE_SWORD.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.ORICHALCUM_SHORT_SWORD.getId(), 50000, 750000, 0.03D, TensuraToolItems.HIHIIROKANE_SHORT_SWORD.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.ORICHALCUM_LONG_SWORD.getId(), 50000, 750000, 0.03D, TensuraToolItems.HIHIIROKANE_LONG_SWORD.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.ORICHALCUM_GREAT_SWORD.getId(), 50000, 750000, 0.03D, TensuraToolItems.HIHIIROKANE_GREAT_SWORD.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.ORICHALCUM_KATANA.getId(), 50000, 750000, 0.03D, TensuraToolItems.HIHIIROKANE_KATANA.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.ORICHALCUM_KODACHI.getId(), 50000, 750000, 0.03D, TensuraToolItems.HIHIIROKANE_KODACHI.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.ORICHALCUM_TACHI.getId(), 50000, 750000, 0.03D, TensuraToolItems.HIHIIROKANE_TACHI.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.ORICHALCUM_ODACHI.getId(), 50000, 750000, 0.03D, TensuraToolItems.HIHIIROKANE_ODACHI.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.ORICHALCUM_SPEAR.getId(), 50000, 750000, 0.03D, TensuraToolItems.HIHIIROKANE_SPEAR.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.ORICHALCUM_SCYTHE.getId(), 50000, 750000, 0.03D, TensuraToolItems.HIHIIROKANE_SCYTHE.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.ORICHALCUM_AXE.getId(), 50000, 750000, 0.03D, TensuraToolItems.HIHIIROKANE_AXE.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.ORICHALCUM_PICKAXE.getId(), 50000, 750000, 0.03D, TensuraToolItems.HIHIIROKANE_PICKAXE.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.ORICHALCUM_SHOVEL.getId(), 50000, 750000, 0.03D, TensuraToolItems.HIHIIROKANE_SHOVEL.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.ORICHALCUM_HOE.getId(), 50000, 750000, 0.03D, TensuraToolItems.HIHIIROKANE_HOE.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.ORICHALCUM_SICKLE.getId(), 50000, 750000, 0.03D, TensuraToolItems.HIHIIROKANE_SICKLE.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraArmorItems.ORICHALCUM_BOOTS.getId(), 50000, 750000, 0.03D, TensuraArmorItems.HIHIIROKANE_BOOTS.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraArmorItems.ORICHALCUM_LEGGINGS.getId(), 50000, 750000, 0.03D, TensuraArmorItems.HIHIIROKANE_LEGGINGS.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraArmorItems.ORICHALCUM_CHESTPLATE.getId(), 50000, 750000, 0.03D, TensuraArmorItems.HIHIIROKANE_CHESTPLATE.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraArmorItems.ORICHALCUM_HELMET.getId(), 50000, 750000, 0.03D, TensuraArmorItems.HIHIIROKANE_HELMET.getId()).buildJson(biConsumer);
   }

   protected static void pureMagiSteel(BiConsumer<ResourceLocation, Supplier<JsonElement>> biConsumer) {
      GearEPCount.of(TensuraToolItems.PURE_MAGISTEEL_SWORD.getId(), 52000, 225000, 0.035D, TensuraToolItems.ADAMANTITE_SWORD.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.PURE_MAGISTEEL_SHORT_SWORD.getId(), 52000, 225000, 0.035D, TensuraToolItems.ADAMANTITE_SHORT_SWORD.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.PURE_MAGISTEEL_LONG_SWORD.getId(), 52000, 225000, 0.035D, TensuraToolItems.ADAMANTITE_LONG_SWORD.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.PURE_MAGISTEEL_GREAT_SWORD.getId(), 52000, 225000, 0.035D, TensuraToolItems.ADAMANTITE_GREAT_SWORD.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.PURE_MAGISTEEL_KATANA.getId(), 52000, 225000, 0.035D, TensuraToolItems.ADAMANTITE_KATANA.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.PURE_MAGISTEEL_KODACHI.getId(), 52000, 225000, 0.035D, TensuraToolItems.ADAMANTITE_KODACHI.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.PURE_MAGISTEEL_TACHI.getId(), 52000, 225000, 0.035D, TensuraToolItems.ADAMANTITE_TACHI.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.PURE_MAGISTEEL_ODACHI.getId(), 52000, 225000, 0.035D, TensuraToolItems.ADAMANTITE_ODACHI.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.PURE_MAGISTEEL_SPEAR.getId(), 52000, 225000, 0.035D, TensuraToolItems.ADAMANTITE_SPEAR.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.PURE_MAGISTEEL_SCYTHE.getId(), 52000, 225000, 0.035D, TensuraToolItems.ADAMANTITE_SCYTHE.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.PURE_MAGISTEEL_AXE.getId(), 52000, 225000, 0.035D, TensuraToolItems.ADAMANTITE_AXE.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.PURE_MAGISTEEL_PICKAXE.getId(), 52000, 225000, 0.035D, TensuraToolItems.ADAMANTITE_PICKAXE.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.PURE_MAGISTEEL_SHOVEL.getId(), 52000, 225000, 0.035D, TensuraToolItems.ADAMANTITE_SHOVEL.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.PURE_MAGISTEEL_HOE.getId(), 52000, 225000, 0.035D, TensuraToolItems.ADAMANTITE_HOE.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.PURE_MAGISTEEL_SICKLE.getId(), 52000, 225000, 0.035D, TensuraToolItems.ADAMANTITE_SICKLE.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraArmorItems.PURE_MAGISTEEL_BOOTS.getId(), 52000, 225000, 0.035D, TensuraArmorItems.ADAMANTITE_BOOTS.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraArmorItems.PURE_MAGISTEEL_LEGGINGS.getId(), 52000, 225000, 0.035D, TensuraArmorItems.ADAMANTITE_LEGGINGS.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraArmorItems.PURE_MAGISTEEL_CHESTPLATE.getId(), 52000, 225000, 0.035D, TensuraArmorItems.ADAMANTITE_CHESTPLATE.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraArmorItems.PURE_MAGISTEEL_HELMET.getId(), 52000, 225000, 0.035D, TensuraArmorItems.ADAMANTITE_HELMET.getId()).buildJson(biConsumer);
   }

   protected static void charybdisScale(BiConsumer<ResourceLocation, Supplier<JsonElement>> biConsumer) {
      GearEPCount.of(TensuraArmorItems.CHARYBDIS_SCALEMAIL_BOOTS.getId(), 60000, 225000, 0.035D, TensuraArmorItems.ADAMANTITE_BOOTS.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraArmorItems.CHARYBDIS_SCALEMAIL_LEGGINGS.getId(), 60000, 225000, 0.035D, TensuraArmorItems.ADAMANTITE_LEGGINGS.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraArmorItems.CHARYBDIS_SCALEMAIL_CHESTPLATE.getId(), 60000, 225000, 0.035D, TensuraArmorItems.ADAMANTITE_CHESTPLATE.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraArmorItems.CHARYBDIS_SCALEMAIL_HELMET.getId(), 60000, 225000, 0.035D, TensuraArmorItems.ADAMANTITE_HELMET.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.TEMPEST_SCALE_KNIFE.getId(), 60000, 1000000, 0.035D).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.TEMPEST_SCALE_SWORD.getId(), 60000, 1000000, 0.035D).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.TEMPEST_SCALE_SHIELD.getId(), 60000, 1000000, 0.035D).buildJson(biConsumer);
   }

   protected static void adamantite(BiConsumer<ResourceLocation, Supplier<JsonElement>> biConsumer) {
      GearEPCount.of(TensuraToolItems.ADAMANTITE_SWORD.getId(), 225000, 750000, 0.04D, TensuraToolItems.HIHIIROKANE_SWORD.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.ADAMANTITE_SHORT_SWORD.getId(), 225000, 750000, 0.04D, TensuraToolItems.HIHIIROKANE_SHORT_SWORD.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.ADAMANTITE_LONG_SWORD.getId(), 225000, 750000, 0.04D, TensuraToolItems.HIHIIROKANE_LONG_SWORD.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.ADAMANTITE_GREAT_SWORD.getId(), 225000, 750000, 0.04D, TensuraToolItems.HIHIIROKANE_GREAT_SWORD.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.ADAMANTITE_KATANA.getId(), 225000, 750000, 0.04D, TensuraToolItems.HIHIIROKANE_KATANA.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.ADAMANTITE_KODACHI.getId(), 225000, 750000, 0.04D, TensuraToolItems.HIHIIROKANE_KODACHI.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.ADAMANTITE_TACHI.getId(), 225000, 750000, 0.04D, TensuraToolItems.HIHIIROKANE_TACHI.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.ADAMANTITE_ODACHI.getId(), 225000, 750000, 0.04D, TensuraToolItems.HIHIIROKANE_ODACHI.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.ADAMANTITE_SPEAR.getId(), 225000, 750000, 0.04D, TensuraToolItems.HIHIIROKANE_SPEAR.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.ADAMANTITE_SCYTHE.getId(), 225000, 750000, 0.04D, TensuraToolItems.HIHIIROKANE_SCYTHE.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.ADAMANTITE_AXE.getId(), 225000, 750000, 0.04D, TensuraToolItems.HIHIIROKANE_AXE.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.ADAMANTITE_PICKAXE.getId(), 225000, 750000, 0.04D, TensuraToolItems.HIHIIROKANE_PICKAXE.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.ADAMANTITE_SHOVEL.getId(), 225000, 750000, 0.04D, TensuraToolItems.HIHIIROKANE_SHOVEL.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.ADAMANTITE_HOE.getId(), 225000, 750000, 0.04D, TensuraToolItems.HIHIIROKANE_HOE.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.ADAMANTITE_SICKLE.getId(), 225000, 750000, 0.04D, TensuraToolItems.HIHIIROKANE_SICKLE.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraArmorItems.ADAMANTITE_BOOTS.getId(), 225000, 750000, 0.04D, TensuraArmorItems.HIHIIROKANE_BOOTS.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraArmorItems.ADAMANTITE_LEGGINGS.getId(), 225000, 750000, 0.04D, TensuraArmorItems.HIHIIROKANE_LEGGINGS.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraArmorItems.ADAMANTITE_CHESTPLATE.getId(), 225000, 750000, 0.04D, TensuraArmorItems.HIHIIROKANE_CHESTPLATE.getId()).buildJson(biConsumer);
      GearEPCount.of(TensuraArmorItems.ADAMANTITE_HELMET.getId(), 225000, 750000, 0.04D, TensuraArmorItems.HIHIIROKANE_HELMET.getId()).buildJson(biConsumer);
   }

   protected static void hihiirokane(BiConsumer<ResourceLocation, Supplier<JsonElement>> biConsumer) {
      GearEPCount.of(TensuraToolItems.HIHIIROKANE_SWORD.getId(), 750000, 1000000, 0.05D).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.HIHIIROKANE_SHORT_SWORD.getId(), 750000, 1000000, 0.05D).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.HIHIIROKANE_LONG_SWORD.getId(), 750000, 1000000, 0.05D).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.HIHIIROKANE_GREAT_SWORD.getId(), 750000, 1000000, 0.05D).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.HIHIIROKANE_KATANA.getId(), 750000, 1000000, 0.05D).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.HIHIIROKANE_KODACHI.getId(), 750000, 1000000, 0.05D).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.HIHIIROKANE_TACHI.getId(), 750000, 1000000, 0.05D).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.HIHIIROKANE_ODACHI.getId(), 750000, 1000000, 0.05D).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.HIHIIROKANE_SPEAR.getId(), 750000, 1000000, 0.05D).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.HIHIIROKANE_SCYTHE.getId(), 750000, 1000000, 0.05D).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.HIHIIROKANE_AXE.getId(), 750000, 1000000, 0.05D).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.HIHIIROKANE_PICKAXE.getId(), 750000, 1000000, 0.05D).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.HIHIIROKANE_SHOVEL.getId(), 750000, 1000000, 0.05D).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.HIHIIROKANE_HOE.getId(), 750000, 1000000, 0.05D).buildJson(biConsumer);
      GearEPCount.of(TensuraToolItems.HIHIIROKANE_SICKLE.getId(), 750000, 1000000, 0.05D).buildJson(biConsumer);
      GearEPCount.of(TensuraArmorItems.HIHIIROKANE_BOOTS.getId(), 750000, 1000000, 0.05D).buildJson(biConsumer);
      GearEPCount.of(TensuraArmorItems.HIHIIROKANE_LEGGINGS.getId(), 750000, 1000000, 0.05D).buildJson(biConsumer);
      GearEPCount.of(TensuraArmorItems.HIHIIROKANE_CHESTPLATE.getId(), 750000, 1000000, 0.05D).buildJson(biConsumer);
      GearEPCount.of(TensuraArmorItems.HIHIIROKANE_HELMET.getId(), 750000, 1000000, 0.05D).buildJson(biConsumer);
   }
}
