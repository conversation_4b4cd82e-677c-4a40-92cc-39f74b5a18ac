package com.github.manasmods.tensura.data.gen;

import com.github.manasmods.manascore.api.data.gen.LanguageProvider;
import com.github.manasmods.tensura.ability.TensuraSkill;
import com.github.manasmods.tensura.client.keybind.TensuraKeybinds;
import com.github.manasmods.tensura.race.Race;
import com.github.manasmods.tensura.registry.battlewill.MeleeArts;
import com.github.manasmods.tensura.registry.battlewill.ProjectileArts;
import com.github.manasmods.tensura.registry.battlewill.UtilityArts;
import com.github.manasmods.tensura.registry.blocks.TensuraBlockEntities;
import com.github.manasmods.tensura.registry.blocks.TensuraBlocks;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.enchantment.TensuraEnchantments;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.fluids.TensuraFluidTypes;
import com.github.manasmods.tensura.registry.items.TensuraArmorItems;
import com.github.manasmods.tensura.registry.items.TensuraConsumableItems;
import com.github.manasmods.tensura.registry.items.TensuraMaterialItems;
import com.github.manasmods.tensura.registry.items.TensuraMobDropItems;
import com.github.manasmods.tensura.registry.items.TensuraSmithingSchematicItems;
import com.github.manasmods.tensura.registry.items.TensuraSpawnEggs;
import com.github.manasmods.tensura.registry.items.TensuraToolItems;
import com.github.manasmods.tensura.registry.magic.SpiritualMagics;
import com.github.manasmods.tensura.registry.race.TensuraRaces;
import com.github.manasmods.tensura.registry.skill.CommonSkills;
import com.github.manasmods.tensura.registry.skill.ExtraSkills;
import com.github.manasmods.tensura.registry.skill.IntrinsicSkills;
import com.github.manasmods.tensura.registry.skill.ResistanceSkills;
import com.github.manasmods.tensura.registry.skill.UniqueSkills;
import com.github.manasmods.tensura.util.TensuraAdvancementsHelper;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.enchantment.Enchantment;
import net.minecraftforge.data.event.GatherDataEvent;
import net.minecraftforge.fluids.FluidType;
import net.minecraftforge.registries.RegistryObject;

public class TensuraLanguageProvider extends LanguageProvider {
   public TensuraLanguageProvider(GatherDataEvent gatherDataEvent) {
      super(gatherDataEvent, "Tensura");
   }

   protected void generate() {
      this.worldGens();
      this.structureBlocks();
      this.blocksOfStorage();
      this.customBlocks();
      this.woodenThings();
      this.miscBlocks();
      this.oreBlocks();
      this.consumableItems();
      this.uniqueGearItems();
      this.schematicItems();
      this.mobDropItems();
      this.armorItems();
      this.miscItems();
      this.gearItems();
      this.entitiesAndSpawnEggs();
      this.moltenMaterials();
      this.creativeTabs();
      this.effects();
      this.races();
      this.magic();
      this.skill();
      this.skillModes();
      this.battlewill();
      this.attributes();
      this.advancements();
      this.gameRules();
      this.commandMessages();
      this.deathMessages();
      this.soundSubtitle();
      this.enchantments();
      this.chatMessages();
      this.menuTexts();
      this.tooltips();
      this.keybind();
      this.miscTexts();
   }

   private void worldGens() {
      this.add("biome.tensura.sakura_forest", "Cherry Blossom Forest");
      this.add("biome.tensura.underworld_barrens", "Underworld Barrens");
      this.add("biome.tensura.underworld_sands", "Underworld Sands");
      this.add("biome.tensura.underworld_spikes", "Underworld Spikes");
      this.add("biome.tensura.underworld_red_sands", "Underworld Red Sands");
   }

   private void woodenThings() {
      this.addBlock(TensuraBlocks.PALM_SAPLING, "Palm Sapling");
      this.addBlock(TensuraBlocks.PALM_LEAVES, "Palm Leaves");
      this.addBlock(TensuraBlocks.PALM_LOG, "Palm Log");
      this.addBlock(TensuraBlocks.PALM_WOOD, "Palm Wood");
      this.addBlock(TensuraBlocks.STRIPPED_PALM_LOG, "Stripped Palm Log");
      this.addBlock(TensuraBlocks.STRIPPED_PALM_WOOD, "Stripped Palm Wood");
      this.addBlock(TensuraBlocks.PALM_PLANKS, "Palm Planks");
      this.addBlock(TensuraBlocks.PALM_STAIRS, "Palm Stairs");
      this.addBlock(TensuraBlocks.PALM_SLAB, "Palm Slab");
      this.addBlock(TensuraBlocks.PALM_DOOR, "Palm Door");
      this.addBlock(TensuraBlocks.PALM_TRAPDOOR, "Palm Trapdoor");
      this.addBlock(TensuraBlocks.PALM_FENCE, "Palm Fence");
      this.addBlock(TensuraBlocks.PALM_FENCE_GATE, "Palm Fence Gate");
      this.addBlock(TensuraBlocks.PALM_BUTTON, "Palm Button");
      this.addBlock(TensuraBlocks.PALM_PRESSURE_PLATE, "Palm Pressure Plate");
      this.addBlock(TensuraBlockEntities.Blocks.PALM_STANDING_SIGN, "Palm Sign");
      this.addItem(TensuraMaterialItems.PALM_BOAT, "Palm Boat");
      this.addItem(TensuraMaterialItems.PALM_CHEST_BOAT, "Palm Boat with Chest");
      this.addBlock(TensuraBlocks.SAKURA_SAPLING, "Cherry Blossom Sapling");
      this.addBlock(TensuraBlocks.SAKURA_LEAVES, "Cherry Blossom Leaves");
      this.addBlock(TensuraBlocks.SAKURA_LOG, "Cherry Blossom Log");
      this.addBlock(TensuraBlocks.SAKURA_WOOD, "Cherry Blossom Wood");
      this.addBlock(TensuraBlocks.STRIPPED_SAKURA_LOG, "Stripped Cherry Blossom Log");
      this.addBlock(TensuraBlocks.STRIPPED_SAKURA_WOOD, "Stripped Cherry Blossom Wood");
      this.addBlock(TensuraBlocks.SAKURA_PLANKS, "Cherry Blossom Planks");
      this.addBlock(TensuraBlocks.SAKURA_STAIRS, "Cherry Blossom Stairs");
      this.addBlock(TensuraBlocks.SAKURA_SLAB, "Cherry Blossom Slab");
      this.addBlock(TensuraBlocks.SAKURA_DOOR, "Cherry Blossom Door");
      this.addBlock(TensuraBlocks.SAKURA_TRAPDOOR, "Cherry Blossom Trapdoor");
      this.addBlock(TensuraBlocks.SAKURA_FENCE, "Cherry Blossom Fence");
      this.addBlock(TensuraBlocks.SAKURA_FENCE_GATE, "Cherry Blossom Fence Gate");
      this.addBlock(TensuraBlocks.SAKURA_BUTTON, "Cherry Blossom Button");
      this.addBlock(TensuraBlocks.SAKURA_PRESSURE_PLATE, "Cherry Blossom Pressure Plate");
      this.addBlock(TensuraBlockEntities.Blocks.SAKURA_STANDING_SIGN, "Cherry Blossom Sign");
      this.addItem(TensuraMaterialItems.SAKURA_BOAT, "Cherry Blossom Boat");
      this.addItem(TensuraMaterialItems.SAKURA_CHEST_BOAT, "Cherry Blossom Boat with Chest");
      this.addItem(TensuraMaterialItems.MARIONETTE_HEART, "Marionette Heart");
      this.addItem(TensuraMaterialItems.BATTLEWILL_MANUAL, "Battlewill Manual");
      this.addItem(TensuraMaterialItems.RACE_RESET_SCROLL, "Race Reset Scroll");
      this.addItem(TensuraMaterialItems.SKILL_RESET_SCROLL, "Skill Reset Scroll");
      this.addItem(TensuraMaterialItems.CHARACTER_RESET_SCROLL, "Character Reset Scroll");
   }

   private void oreBlocks() {
      this.addBlock(TensuraBlocks.MAGIC_ORE, "Magic Ore");
      this.addBlock(TensuraBlocks.SILVER_ORE, "Silver Ore");
      this.addBlock(TensuraBlocks.DEEPSLATE_MAGIC_ORE, "Deepslate Magic Ore");
      this.addBlock(TensuraBlocks.DEEPSLATE_SILVER_ORE, "Deepslate Silver Ore");
   }

   private void blocksOfStorage() {
      this.addBlock(TensuraBlocks.RAW_SILVER_BLOCK, "Block of Raw Silver");
      this.addBlock(TensuraBlocks.SILVER_BLOCK, "Block of Silver");
      this.addBlock(TensuraBlocks.MAGIC_ORE_BLOCK, "Block of Magic Ore");
      this.addBlock(TensuraBlocks.LOW_MAGISTEEL_BLOCK, "Block of Low Magisteel");
      this.addBlock(TensuraBlocks.HIGH_MAGISTEEL_BLOCK, "Block of High Magisteel");
      this.addBlock(TensuraBlocks.MITHRIL_BLOCK, "Block of Mithril");
      this.addBlock(TensuraBlocks.ORICHALCUM_BLOCK, "Block of Orichalcum");
      this.addBlock(TensuraBlocks.PURE_MAGISTEEL_BLOCK, "Block of Pure Magisteel");
      this.addBlock(TensuraBlocks.ADAMANTITE_BLOCK, "Block of Adamantite");
      this.addBlock(TensuraBlocks.HIHIIROKANE_BLOCK, "Block of Hihi'Irokane");
   }

   private void customBlocks() {
      this.addBlock(TensuraBlocks.SMITHING_BENCH, "Smithing Bench");
      this.addBlock(TensuraBlockEntities.Blocks.KILN, "Kiln");
   }

   private void miscBlocks() {
      this.addBlock(TensuraBlocks.BRICK_MAGIC_ENGINE, "Brick Magic Engine");
      this.addBlock(TensuraBlocks.STONE_BRICK_MAGIC_ENGINE, "Stone Brick Magic Engine");
      this.addBlock(TensuraBlocks.THATCH_BLOCK, "Thatch Block");
      this.addBlock(TensuraBlocks.THATCH_STAIRS, "Thatch Stairs");
      this.addBlock(TensuraBlocks.THATCH_SLAB, "Thatch Slab");
      this.addBlock(TensuraBlocks.SARASA_SAND, "Sarasa Sand");
      this.addBlock(TensuraBlocks.SARASA_SANDSTONE, "Sarasa Sandstone");
      this.addBlock(TensuraBlocks.SARASA_SANDSTONE_STAIRS, "Sarasa Sandstone Stairs");
      this.addBlock(TensuraBlocks.SARASA_SANDSTONE_SLAB, "Sarasa Sandstone Slab");
      this.addBlock(TensuraBlocks.SARASA_SANDSTONE_WALL, "Sarasa Sandstone Wall");
      this.addBlock(TensuraBlocks.CUT_SARASA_SANDSTONE, "Cut Sarasa Sandstone");
      this.addBlock(TensuraBlocks.CUT_SARASA_SANDSTONE_SLAB, "Cut Sarasa Sandstone Slab");
      this.addBlock(TensuraBlocks.SMOOTH_SARASA_SANDSTONE, "Smooth Sarasa Sandstone");
      this.addBlock(TensuraBlocks.SMOOTH_SARASA_SANDSTONE_STAIRS, "Smooth Sarasa Sandstone Stairs");
      this.addBlock(TensuraBlocks.SMOOTH_SARASA_SANDSTONE_SLAB, "Smooth Sarasa Sandstone Slab");
      this.addBlock(TensuraBlocks.CHISELED_SARASA_SANDSTONE, "Chiseled Sarasa Sandstone");
      this.addBlock(TensuraBlocks.STICKY_COBWEB, "Sticky Cobweb");
      this.addBlock(TensuraBlocks.STICKY_STEEL_COBWEB, "Sticky Steel Cobweb");
      this.addBlock(TensuraBlocks.WEB_BLOCK, "Web Block");
      this.addBlock(TensuraBlocks.WEB_STAIRS, "Web Stairs");
      this.addBlock(TensuraBlocks.WEB_SLAB, "Web Slab");
      this.addBlock(TensuraBlocks.WEBBED_COBBLESTONE, "Webbed Cobblestone");
      this.addBlock(TensuraBlocks.WEBBED_COBBLESTONE_STAIRS, "Webbed Cobblestone Stairs");
      this.addBlock(TensuraBlocks.WEBBED_COBBLESTONE_SLAB, "Webbed Cobblestone Slab");
      this.addBlock(TensuraBlocks.WEBBED_COBBLESTONE_WALL, "Webbed Cobblestone Wall");
      this.addBlock(TensuraBlocks.WEBBED_STONE_BRICKS, "Webbed Stone Bricks");
      this.addBlock(TensuraBlocks.WEBBED_STONE_BRICK_STAIRS, "Webbed Stone Brick Stairs");
      this.addBlock(TensuraBlocks.WEBBED_STONE_BRICK_SLAB, "Webbed Stone Brick Slab");
      this.addBlock(TensuraBlocks.WEBBED_STONE_BRICK_WALL, "Webbed Stone Brick Wall");
      this.addBlock(TensuraBlocks.LOW_QUALITY_MAGIC_CRYSTAL_BLOCK, "Low Quality Magic Crystal Block");
      this.addBlock(TensuraBlocks.LOW_QUALITY_MAGIC_CRYSTAL_STAIRS, "Low Quality Magic Crystal Stairs");
      this.addBlock(TensuraBlocks.LOW_QUALITY_MAGIC_CRYSTAL_SLAB, "Low Quality Magic Crystal Slab");
      this.addBlock(TensuraBlocks.LOW_QUALITY_MAGIC_CRYSTAL_BRICKS, "Low Quality Magic Crystal Bricks");
      this.addBlock(TensuraBlocks.LOW_QUALITY_MAGIC_CRYSTAL_BRICK_STAIRS, "Low Quality Magic Crystal Brick Stairs");
      this.addBlock(TensuraBlocks.LOW_QUALITY_MAGIC_CRYSTAL_BRICK_SLAB, "Low Quality Magic Crystal Brick Slab");
      this.addBlock(TensuraBlocks.LOW_QUALITY_MAGIC_CRYSTAL_BRICK_WALL, "Low Quality Magic Crystal Brick Wall");
      this.addBlock(TensuraBlocks.CHISELED_LOW_QUALITY_MAGIC_CRYSTAL_BRICKS, "Chiseled Low Quality Magic Crystal Bricks");
      this.addBlock(TensuraBlocks.MEDIUM_QUALITY_MAGIC_CRYSTAL_BLOCK, "Medium Quality Magic Crystal Block");
      this.addBlock(TensuraBlocks.MEDIUM_QUALITY_MAGIC_CRYSTAL_STAIRS, "Medium Quality Magic Crystal Stairs");
      this.addBlock(TensuraBlocks.MEDIUM_QUALITY_MAGIC_CRYSTAL_SLAB, "Medium Quality Magic Crystal Slab");
      this.addBlock(TensuraBlocks.MEDIUM_QUALITY_MAGIC_CRYSTAL_BRICKS, "Medium Quality Magic Crystal Bricks");
      this.addBlock(TensuraBlocks.MEDIUM_QUALITY_MAGIC_CRYSTAL_BRICK_STAIRS, "Medium Quality Magic Crystal Brick Stairs");
      this.addBlock(TensuraBlocks.MEDIUM_QUALITY_MAGIC_CRYSTAL_BRICK_SLAB, "Medium Quality Magic Crystal Brick Slab");
      this.addBlock(TensuraBlocks.MEDIUM_QUALITY_MAGIC_CRYSTAL_BRICK_WALL, "Medium Quality Magic Crystal Brick Wall");
      this.addBlock(TensuraBlocks.CHISELED_MEDIUM_QUALITY_MAGIC_CRYSTAL_BRICKS, "Chiseled Medium Quality Magic Crystal Bricks");
      this.addBlock(TensuraBlocks.HIGH_QUALITY_MAGIC_CRYSTAL_BLOCK, "High Quality Magic Crystal Block");
      this.addBlock(TensuraBlocks.HIGH_QUALITY_MAGIC_CRYSTAL_STAIRS, "High Quality Magic Crystal Stairs");
      this.addBlock(TensuraBlocks.HIGH_QUALITY_MAGIC_CRYSTAL_SLAB, "High Quality Magic Crystal Slab");
      this.addBlock(TensuraBlocks.HIGH_QUALITY_MAGIC_CRYSTAL_BRICKS, "High Quality Magic Crystal Bricks");
      this.addBlock(TensuraBlocks.HIGH_QUALITY_MAGIC_CRYSTAL_BRICK_STAIRS, "High Quality Magic Crystal Brick Stairs");
      this.addBlock(TensuraBlocks.HIGH_QUALITY_MAGIC_CRYSTAL_BRICK_SLAB, "High Quality Magic Crystal Brick Slab");
      this.addBlock(TensuraBlocks.HIGH_QUALITY_MAGIC_CRYSTAL_BRICK_WALL, "High Quality Magic Crystal Brick Wall");
      this.addBlock(TensuraBlocks.CHISELED_HIGH_QUALITY_MAGIC_CRYSTAL_BRICKS, "Chiseled High Quality Magic Crystal Bricks");
      this.addBlock(TensuraBlocks.SLIME_CHUNK_BLOCK, "Slime Chunk Block");
      this.addBlock(TensuraBlocks.CHILLED_SLIME_BLOCK, "Chilled Slime Block");
      this.addBlock(TensuraBlocks.CHARYBDIS_CORE, "Charybdis Core");
      this.addBlock(TensuraBlocks.MOTH_EGG, "Moth Egg");
      this.addBlock(TensuraBlocks.SPIDER_EGG, "Spider Egg");
      this.addBlock(TensuraBlocks.BLACK_FIRE, "Black Fire");
      this.addBlock(TensuraBlocks.HOLY_FIRE, "Holy Fire");
      this.addBlock(TensuraBlocks.THATCH_BED, "Thatch Bed");
      this.addBlock(TensuraBlocks.UNLIT_TORCH, "Unlit Torch");
      this.addBlock(TensuraBlocks.UNLIT_LANTERN, "Unlit Lantern");
      this.addBlock(TensuraBlocks.HOT_SPRING_WATER, "Hot Spring Water");
      this.addFluid(TensuraFluidTypes.HOT_SPRING_WATER, "Hot Spring Water");
      this.addBlock(TensuraBlocks.HIPOKUTE_GRASS, "Hipokute Grass");
      this.addBlock(TensuraBlocks.POTTED_HIPOKUTE_FLOWER, "Potted Hipokute Grass");
      this.addBlock(TensuraBlocks.POTTED_SAKURA_SAPLING, "Potted Sakura Sapling");
      this.addBlock(TensuraBlocks.POTTED_PALM_SAPLING, "Potted Palm Sapling");
      this.addBlock(TensuraBlocks.LIGHT_AIR, "Light Air");
      this.addBlock(TensuraBlocks.SOLID_SPACE, "Solid Space");
   }

   private void structureBlocks() {
      this.addBlock(TensuraBlocks.LABYRINTH_LAMP, "Labyrinth Lamp");
      this.addBlock(TensuraBlocks.LABYRINTH_LAMP_BL, "Labyrinth Lamp Bottom Left");
      this.addBlock(TensuraBlocks.LABYRINTH_LAMP_BR, "Labyrinth Lamp Bottom Right");
      this.addBlock(TensuraBlocks.LABYRINTH_LAMP_TL, "Labyrinth Lamp Top Left");
      this.addBlock(TensuraBlocks.LABYRINTH_LAMP_TR, "Labyrinth Lamp Top Right");
      this.addBlock(TensuraBlocks.LABYRINTH_LIT_LAMP, "Labyrinth Lit Lamp");
      this.addBlock(TensuraBlocks.LABYRINTH_LIT_LAMP_BL, "Labyrinth Lit Lamp Bottom Left");
      this.addBlock(TensuraBlocks.LABYRINTH_LIT_LAMP_BR, "Labyrinth Lit Lamp Bottom Right");
      this.addBlock(TensuraBlocks.LABYRINTH_LIT_LAMP_TL, "Labyrinth Lit Lamp Top Left");
      this.addBlock(TensuraBlocks.LABYRINTH_LIT_LAMP_TR, "Labyrinth Lit Lamp Top Right");
      this.addBlock(TensuraBlocks.LABYRINTH_BRICK, "Labyrinth Bricks");
      this.addBlock(TensuraBlocks.LABYRINTH_BRICK_STAIR, "Labyrinth Brick Stairs");
      this.addBlock(TensuraBlocks.LABYRINTH_BRICK_SLAB, "Labyrinth Brick Slab");
      this.addBlock(TensuraBlocks.LABYRINTH_BRICK_BL, "Labyrinth Bricks Bottom Left");
      this.addBlock(TensuraBlocks.LABYRINTH_BRICK_BR, "Labyrinth Bricks Bottom Right");
      this.addBlock(TensuraBlocks.LABYRINTH_BRICK_TL, "Labyrinth Bricks Top Left");
      this.addBlock(TensuraBlocks.LABYRINTH_BRICK_TR, "Labyrinth Bricks Top Right");
      this.addBlock(TensuraBlocks.LABYRINTH_STONE, "Labyrinth Stone");
      this.addBlock(TensuraBlocks.LABYRINTH_STONE_STAIR, "Labyrinth Stone Stairs");
      this.addBlock(TensuraBlocks.LABYRINTH_STONE_SLAB, "Labyrinth Stone Slab");
      this.addBlock(TensuraBlocks.LABYRINTH_STONE_BL, "Labyrinth Stone Bottom Left");
      this.addBlock(TensuraBlocks.LABYRINTH_STONE_BR, "Labyrinth Stone Bottom Right");
      this.addBlock(TensuraBlocks.LABYRINTH_STONE_TL, "Labyrinth Stone Top Left");
      this.addBlock(TensuraBlocks.LABYRINTH_STONE_TR, "Labyrinth Stone Top Right");
      this.addBlock(TensuraBlocks.CREAM_LABYRINTH_BRICK, "Cream Labyrinth Bricks");
      this.addBlock(TensuraBlocks.CREAM_LABYRINTH_BRICK_STAIR, "Cream Labyrinth Brick Stairs");
      this.addBlock(TensuraBlocks.CREAM_LABYRINTH_BRICK_SLAB, "Cream Labyrinth Brick Slab");
      this.addBlock(TensuraBlocks.CREAM_LABYRINTH_BRICK_BL, "Cream Labyrinth Bricks Bottom Left");
      this.addBlock(TensuraBlocks.CREAM_LABYRINTH_BRICK_BR, "Cream Labyrinth Bricks Bottom Right");
      this.addBlock(TensuraBlocks.CREAM_LABYRINTH_BRICK_TL, "Cream Labyrinth Bricks Top Left");
      this.addBlock(TensuraBlocks.CREAM_LABYRINTH_BRICK_TR, "Cream Labyrinth Bricks Top Right");
      this.addBlock(TensuraBlocks.CREAM_LABYRINTH_STONE, "Cream Labyrinth Stone");
      this.addBlock(TensuraBlocks.CREAM_LABYRINTH_STONE_STAIR, "Cream Labyrinth Stone Stairs");
      this.addBlock(TensuraBlocks.CREAM_LABYRINTH_STONE_SLAB, "Cream Labyrinth Stone Slab");
      this.addBlock(TensuraBlocks.CREAM_LABYRINTH_STONE_BL, "Cream Labyrinth Stone Bottom Left");
      this.addBlock(TensuraBlocks.CREAM_LABYRINTH_STONE_BR, "Cream Labyrinth Stone Bottom Right");
      this.addBlock(TensuraBlocks.CREAM_LABYRINTH_STONE_TL, "Cream Labyrinth Stone Top Left");
      this.addBlock(TensuraBlocks.CREAM_LABYRINTH_STONE_TR, "Cream Labyrinth Stone Top Right");
      this.addBlock(TensuraBlocks.DARK_LABYRINTH_BRICK, "Dark Labyrinth Bricks");
      this.addBlock(TensuraBlocks.DARK_LABYRINTH_BRICK_STAIR, "Dark Labyrinth Brick Stairs");
      this.addBlock(TensuraBlocks.DARK_LABYRINTH_BRICK_SLAB, "Dark Labyrinth Brick Slab");
      this.addBlock(TensuraBlocks.DARK_LABYRINTH_BRICK_BL, "Dark Labyrinth Bricks Bottom Left");
      this.addBlock(TensuraBlocks.DARK_LABYRINTH_BRICK_BR, "Dark Labyrinth Bricks Bottom Right");
      this.addBlock(TensuraBlocks.DARK_LABYRINTH_BRICK_TL, "Dark Labyrinth Bricks Top Left");
      this.addBlock(TensuraBlocks.DARK_LABYRINTH_BRICK_TR, "Dark Labyrinth Bricks Top Right");
      this.addBlock(TensuraBlocks.DARK_LABYRINTH_STONE, "Dark Labyrinth Stone");
      this.addBlock(TensuraBlocks.DARK_LABYRINTH_STONE_STAIR, "Dark Labyrinth Stone Stairs");
      this.addBlock(TensuraBlocks.DARK_LABYRINTH_STONE_SLAB, "Dark Labyrinth Stone Slab");
      this.addBlock(TensuraBlocks.DARK_LABYRINTH_STONE_BL, "Dark Labyrinth Stone Bottom Left");
      this.addBlock(TensuraBlocks.DARK_LABYRINTH_STONE_BR, "Dark Labyrinth Stone Bottom Right");
      this.addBlock(TensuraBlocks.DARK_LABYRINTH_STONE_TL, "Dark Labyrinth Stone Top Left");
      this.addBlock(TensuraBlocks.DARK_LABYRINTH_STONE_TR, "Dark Labyrinth Stone Top Right");
      this.addBlock(TensuraBlocks.LABYRINTH_PORTAL, "Labyrinth Portal");
      this.addBlock(TensuraBlocks.LABYRINTH_BARRIER_BLOCK, "Labyrinth Barrier");
      this.addBlock(TensuraBlocks.LABYRINTH_CRYSTAL, "Labyrinth Crystal");
      this.addBlock(TensuraBlocks.LABYRINTH_PRAYING_PATH, "Labyrinth Praying Path");
      this.addBlock(TensuraBlocks.LABYRINTH_LIGHT_PATH, "Labyrinth Light Path");
      this.addBlock(TensuraBlocks.LABYRINTH_LIGHT_PATH_STAIRS, "Labyrinth Light Path Stairs");
      this.addBlock(TensuraBlocks.LABYRINTH_LIGHT_PATH_SLAB, "Labyrinth Light Path Slab");
      this.addBlock(TensuraBlocks.HELL_PORTAL, "Hell Portal");
   }

   private void armorItems() {
      this.addItem(TensuraArmorItems.MONSTER_LEATHER_D_HELMET, "Monster Leather Helmet (D)");
      this.addItem(TensuraArmorItems.MONSTER_LEATHER_D_CHESTPLATE, "Monster Leather Chestplate (D)");
      this.addItem(TensuraArmorItems.MONSTER_LEATHER_D_LEGGINGS, "Monster Leather Leggings (D)");
      this.addItem(TensuraArmorItems.MONSTER_LEATHER_D_BOOTS, "Monster Leather Boots (D)");
      this.addItem(TensuraArmorItems.MONSTER_LEATHER_C_HELMET, "Monster Leather Helmet (C)");
      this.addItem(TensuraArmorItems.MONSTER_LEATHER_C_CHESTPLATE, "Monster Leather Chestplate (C)");
      this.addItem(TensuraArmorItems.MONSTER_LEATHER_C_LEGGINGS, "Monster Leather Leggings (C)");
      this.addItem(TensuraArmorItems.MONSTER_LEATHER_C_BOOTS, "Monster Leather Boots (C)");
      this.addItem(TensuraArmorItems.MONSTER_LEATHER_B_HELMET, "Monster Leather Helmet (B)");
      this.addItem(TensuraArmorItems.MONSTER_LEATHER_B_CHESTPLATE, "Monster Leather Chestplate (B)");
      this.addItem(TensuraArmorItems.MONSTER_LEATHER_B_LEGGINGS, "Monster Leather Leggings (B)");
      this.addItem(TensuraArmorItems.MONSTER_LEATHER_B_BOOTS, "Monster Leather Boots (B)");
      this.addItem(TensuraArmorItems.MONSTER_LEATHER_A_HELMET, "Monster Leather Helmet (A)");
      this.addItem(TensuraArmorItems.MONSTER_LEATHER_A_CHESTPLATE, "Monster Leather Chestplate (A)");
      this.addItem(TensuraArmorItems.MONSTER_LEATHER_A_LEGGINGS, "Monster Leather Leggings (A)");
      this.addItem(TensuraArmorItems.MONSTER_LEATHER_A_BOOTS, "Monster Leather Boots (A)");
      this.addItem(TensuraArmorItems.MONSTER_LEATHER_SPECIAL_A_HELMET, "Monster Leather Helmet (Special A)");
      this.addItem(TensuraArmorItems.MONSTER_LEATHER_SPECIAL_A_CHESTPLATE, "Monster Leather Chestplate (Special A)");
      this.addItem(TensuraArmorItems.MONSTER_LEATHER_SPECIAL_A_LEGGINGS, "Monster Leather Leggings (Special A)");
      this.addItem(TensuraArmorItems.MONSTER_LEATHER_SPECIAL_A_BOOTS, "Monster Leather Boots (Special A)");
      this.addItem(TensuraArmorItems.SILVER_HELMET, "Silver Helmet");
      this.addItem(TensuraArmorItems.SILVER_CHESTPLATE, "Silver Chestplate");
      this.addItem(TensuraArmorItems.SILVER_LEGGINGS, "Silver Leggings");
      this.addItem(TensuraArmorItems.SILVER_BOOTS, "Silver Boots");
      this.addItem(TensuraArmorItems.ANT_CARAPACE_HELMET, "Ant Carapace Helmet");
      this.addItem(TensuraArmorItems.ANT_CARAPACE_CHESTPLATE, "Ant Carapace Chestplate");
      this.addItem(TensuraArmorItems.ANT_CARAPACE_LEGGINGS, "Ant Carapace Leggings");
      this.addItem(TensuraArmorItems.ANT_CARAPACE_BOOTS, "Ant Carapace Boots");
      this.addItem(TensuraArmorItems.SERPENT_SCALEMAIL_HELMET, "Serpent Scalemail Helmet");
      this.addItem(TensuraArmorItems.SERPENT_SCALEMAIL_CHESTPLATE, "Serpent Scalemail Chestplate");
      this.addItem(TensuraArmorItems.SERPENT_SCALEMAIL_LEGGINGS, "Serpent Scalemail Leggings");
      this.addItem(TensuraArmorItems.SERPENT_SCALEMAIL_BOOTS, "Serpent Scalemail Boots");
      this.addItem(TensuraArmorItems.LOW_MAGISTEEL_HELMET, "Low Magisteel Helmet");
      this.addItem(TensuraArmorItems.LOW_MAGISTEEL_CHESTPLATE, "Low Magisteel Chestplate");
      this.addItem(TensuraArmorItems.LOW_MAGISTEEL_LEGGINGS, "Low Magisteel Leggings");
      this.addItem(TensuraArmorItems.LOW_MAGISTEEL_BOOTS, "Low Magisteel Boots");
      this.addItem(TensuraArmorItems.ARMOURSAURUS_HELMET, "Armoursaurus Helmet");
      this.addItem(TensuraArmorItems.ARMOURSAURUS_CHESTPLATE, "Armoursaurus Chestplate");
      this.addItem(TensuraArmorItems.ARMOURSAURUS_LEGGINGS, "Armoursaurus Leggings");
      this.addItem(TensuraArmorItems.ARMOURSAURUS_BOOTS, "Armoursaurus Boots");
      this.addItem(TensuraArmorItems.ARMOURSAURUS_SCALEMAIL_HELMET, "Armoursaurus Scalemail Helmet");
      this.addItem(TensuraArmorItems.ARMOURSAURUS_SCALEMAIL_CHESTPLATE, "Armoursaurus Scalemail Chestplate");
      this.addItem(TensuraArmorItems.ARMOURSAURUS_SCALEMAIL_LEGGINGS, "Armoursaurus Scalemail Leggings");
      this.addItem(TensuraArmorItems.ARMOURSAURUS_SCALEMAIL_BOOTS, "Armoursaurus Scalemail Boots");
      this.addItem(TensuraArmorItems.KNIGHT_SPIDER_CARAPACE_HELMET, "Knight Spider Carapace Helmet");
      this.addItem(TensuraArmorItems.KNIGHT_SPIDER_CARAPACE_CHESTPLATE, "Knight Spider Carapace Chestplate");
      this.addItem(TensuraArmorItems.KNIGHT_SPIDER_CARAPACE_LEGGINGS, "Knight Spider Carapace Leggings");
      this.addItem(TensuraArmorItems.KNIGHT_SPIDER_CARAPACE_BOOTS, "Knight Spider Carapace Boots");
      this.addItem(TensuraArmorItems.HIGH_MAGISTEEL_HELMET, "High Magisteel Helmet");
      this.addItem(TensuraArmorItems.HIGH_MAGISTEEL_CHESTPLATE, "High Magisteel Chestplate");
      this.addItem(TensuraArmorItems.HIGH_MAGISTEEL_LEGGINGS, "High Magisteel Leggings");
      this.addItem(TensuraArmorItems.HIGH_MAGISTEEL_BOOTS, "High Magisteel Boots");
      this.addItem(TensuraArmorItems.CHARYBDIS_SCALEMAIL_HELMET, "Charybdis Scalemail Helmet");
      this.addItem(TensuraArmorItems.CHARYBDIS_SCALEMAIL_CHESTPLATE, "Charybdis Scalemail Chestplate");
      this.addItem(TensuraArmorItems.CHARYBDIS_SCALEMAIL_LEGGINGS, "Charybdis Scalemail Leggings");
      this.addItem(TensuraArmorItems.CHARYBDIS_SCALEMAIL_BOOTS, "Charybdis Scalemail Boots");
      this.addItem(TensuraArmorItems.MITHRIL_HELMET, "Mithril Helmet");
      this.addItem(TensuraArmorItems.MITHRIL_CHESTPLATE, "Mithril Chestplate");
      this.addItem(TensuraArmorItems.MITHRIL_LEGGINGS, "Mithril Leggings");
      this.addItem(TensuraArmorItems.MITHRIL_BOOTS, "Mithril Boots");
      this.addItem(TensuraArmorItems.ORICHALCUM_HELMET, "Orichalcum Helmet");
      this.addItem(TensuraArmorItems.ORICHALCUM_CHESTPLATE, "Orichalcum Chestplate");
      this.addItem(TensuraArmorItems.ORICHALCUM_LEGGINGS, "Orichalcum Leggings");
      this.addItem(TensuraArmorItems.ORICHALCUM_BOOTS, "Orichalcum Boots");
      this.addItem(TensuraArmorItems.PURE_MAGISTEEL_HELMET, "Pure Magisteel Helmet");
      this.addItem(TensuraArmorItems.PURE_MAGISTEEL_CHESTPLATE, "Pure Magisteel Chestplate");
      this.addItem(TensuraArmorItems.PURE_MAGISTEEL_LEGGINGS, "Pure Magisteel Leggings");
      this.addItem(TensuraArmorItems.PURE_MAGISTEEL_BOOTS, "Pure Magisteel Boots");
      this.addItem(TensuraArmorItems.ADAMANTITE_HELMET, "Adamantite Helmet");
      this.addItem(TensuraArmorItems.ADAMANTITE_CHESTPLATE, "Adamantite Chestplate");
      this.addItem(TensuraArmorItems.ADAMANTITE_LEGGINGS, "Adamantite Leggings");
      this.addItem(TensuraArmorItems.ADAMANTITE_BOOTS, "Adamantite Boots");
      this.addItem(TensuraArmorItems.HIHIIROKANE_HELMET, "Hihi'Irokane Helmet");
      this.addItem(TensuraArmorItems.HIHIIROKANE_CHESTPLATE, "Hihi'Irokane Chestplate");
      this.addItem(TensuraArmorItems.HIHIIROKANE_LEGGINGS, "Hihi'Irokane Leggings");
      this.addItem(TensuraArmorItems.HIHIIROKANE_BOOTS, "Hihi'Irokane Boots");
      this.addItem(TensuraArmorItems.HOLY_ARMAMENTS_CHESTPLATE, "Holy Armaments Chestplate");
      this.addItem(TensuraArmorItems.HOLY_ARMAMENTS_LEGGINGS, "Holy Armaments Leggings");
      this.addItem(TensuraArmorItems.HOLY_ARMAMENTS_BOOTS, "Holy Armaments Boots");
   }

   private void gearItems() {
      this.addItem(TensuraToolItems.SHORT_BOW, "Short Bow");
      this.addItem(TensuraToolItems.LONG_BOW, "Long Bow");
      this.addItem(TensuraToolItems.WAR_BOW, "War Bow");
      this.addItem(TensuraToolItems.SHORT_SPIDER_BOW, "Short Spider Bow");
      this.addItem(TensuraToolItems.SPIDER_BOW, "Spider Bow");
      this.addItem(TensuraToolItems.LONG_SPIDER_BOW, "Long Spider Bow");
      this.addItem(TensuraToolItems.WAR_SPIDER_BOW, "War Spider Bow");
      this.addItem(TensuraToolItems.ANT_CROSSBOW, "Ant Crossbow");
      this.addItem(TensuraToolItems.INVISIBLE_ARROW, "Invisible Arrow");
      this.addItem(TensuraToolItems.SPEARED_FIN_ARROW, "Speared Fin Arrow");
      this.addItem(TensuraToolItems.KUNAI, "Kunai");
      this.addItem(TensuraToolItems.PURE_MAGISTEEL_KUNAI, "Pure Magisteel Kunai");
      this.addItem(TensuraToolItems.GOBLIN_CLUB, "Goblin Club");
      this.addItem(TensuraToolItems.KANABO, "Kanabo");
      this.addItem(TensuraToolItems.AURA_SHIELD, "Aura Shield");
      this.addItem(TensuraToolItems.ARMOURSAURUS_SHIELD, "Armoursaurus Shield");
      this.addItem(TensuraToolItems.TEMPEST_SCALE_SHIELD, "Tempest Scale Shield");
      this.addItem(TensuraToolItems.TEMPEST_SCALE_SWORD, "Tempest Scale Sword");
      this.addItem(TensuraToolItems.TEMPEST_SCALE_KNIFE, "Tempest Scale Knife");
      this.addItem(TensuraToolItems.SISSIE_TOOTH_PICKAXE, "Sissie Tooth Pickaxe");
      this.addItem(TensuraToolItems.CENTIPEDE_DAGGER, "Centipede Dagger");
      this.addItem(TensuraToolItems.SPIDER_DAGGER, "Spider Dagger");
      this.addItem(TensuraToolItems.BEAST_HORN_SPEAR, "Beast Horn Spear");
      this.addItem(TensuraToolItems.UNICORN_HORN_SPEAR, "Unicorn Horn Spear");
      this.addItem(TensuraToolItems.BLADE_TIGER_SCYTHE, "Blade Tiger Scythe");
      this.addItem(TensuraToolItems.WOODEN_SHORT_SWORD, "Wooden Short Sword");
      this.addItem(TensuraToolItems.WOODEN_LONG_SWORD, "Wooden Long Sword");
      this.addItem(TensuraToolItems.WOODEN_GREAT_SWORD, "Wooden Great Sword");
      this.addItem(TensuraToolItems.WOODEN_KATANA, "Wooden Katana");
      this.addItem(TensuraToolItems.WOODEN_KODACHI, "Wooden Kodachi");
      this.addItem(TensuraToolItems.WOODEN_TACHI, "Wooden Tachi");
      this.addItem(TensuraToolItems.WOODEN_ODACHI, "Wooden Odachi");
      this.addItem(TensuraToolItems.WOODEN_SICKLE, "Wooden Sickle");
      this.addItem(TensuraToolItems.WOODEN_SPEAR, "Wooden Spear");
      this.addItem(TensuraToolItems.WOODEN_SCYTHE, "Wooden Scythe");
      this.addItem(TensuraToolItems.STONE_SHORT_SWORD, "Stone Short Sword");
      this.addItem(TensuraToolItems.STONE_LONG_SWORD, "Stone Long Sword");
      this.addItem(TensuraToolItems.STONE_GREAT_SWORD, "Stone Great Sword");
      this.addItem(TensuraToolItems.STONE_KATANA, "Stone Katana");
      this.addItem(TensuraToolItems.STONE_KODACHI, "Stone Kodachi");
      this.addItem(TensuraToolItems.STONE_TACHI, "Stone Tachi");
      this.addItem(TensuraToolItems.STONE_ODACHI, "Stone Odachi");
      this.addItem(TensuraToolItems.STONE_SICKLE, "Stone Sickle");
      this.addItem(TensuraToolItems.STONE_SPEAR, "Stone Spear");
      this.addItem(TensuraToolItems.STONE_SCYTHE, "Stone Scythe");
      this.addItem(TensuraToolItems.GOLDEN_SHORT_SWORD, "Golden Short Sword");
      this.addItem(TensuraToolItems.GOLDEN_LONG_SWORD, "Golden Long Sword");
      this.addItem(TensuraToolItems.GOLDEN_GREAT_SWORD, "Golden Great Sword");
      this.addItem(TensuraToolItems.GOLDEN_KATANA, "Golden Katana");
      this.addItem(TensuraToolItems.GOLDEN_KODACHI, "Golden Kodachi");
      this.addItem(TensuraToolItems.GOLDEN_TACHI, "Golden Tachi");
      this.addItem(TensuraToolItems.GOLDEN_ODACHI, "Golden Odachi");
      this.addItem(TensuraToolItems.GOLDEN_SICKLE, "Golden Sickle");
      this.addItem(TensuraToolItems.GOLDEN_SPEAR, "Golden Spear");
      this.addItem(TensuraToolItems.GOLDEN_SCYTHE, "Golden Scythe");
      this.addItem(TensuraToolItems.SILVER_SWORD, "Silver Sword");
      this.addItem(TensuraToolItems.SILVER_SHORT_SWORD, "Silver Short Sword");
      this.addItem(TensuraToolItems.SILVER_LONG_SWORD, "Silver Long Sword");
      this.addItem(TensuraToolItems.SILVER_GREAT_SWORD, "Silver Great Sword");
      this.addItem(TensuraToolItems.SILVER_KATANA, "Silver Katana");
      this.addItem(TensuraToolItems.SILVER_KODACHI, "Silver Kodachi");
      this.addItem(TensuraToolItems.SILVER_TACHI, "Silver Tachi");
      this.addItem(TensuraToolItems.SILVER_ODACHI, "Silver Odachi");
      this.addItem(TensuraToolItems.SILVER_PICKAXE, "Silver Pickaxe");
      this.addItem(TensuraToolItems.SILVER_AXE, "Silver Axe");
      this.addItem(TensuraToolItems.SILVER_SHOVEL, "Silver Shovel");
      this.addItem(TensuraToolItems.SILVER_HOE, "Silver Hoe");
      this.addItem(TensuraToolItems.SILVER_SICKLE, "Silver Sickle");
      this.addItem(TensuraToolItems.SILVER_SPEAR, "Silver Spear");
      this.addItem(TensuraToolItems.SILVER_SCYTHE, "Silver Scythe");
      this.addItem(TensuraToolItems.IRON_SHORT_SWORD, "Iron Short Sword");
      this.addItem(TensuraToolItems.IRON_LONG_SWORD, "Iron Long Sword");
      this.addItem(TensuraToolItems.IRON_GREAT_SWORD, "Iron Great Sword");
      this.addItem(TensuraToolItems.IRON_KATANA, "Iron Katana");
      this.addItem(TensuraToolItems.IRON_KODACHI, "Iron Kodachi");
      this.addItem(TensuraToolItems.IRON_TACHI, "Iron Tachi");
      this.addItem(TensuraToolItems.IRON_ODACHI, "Iron Odachi");
      this.addItem(TensuraToolItems.IRON_SICKLE, "Iron Sickle");
      this.addItem(TensuraToolItems.IRON_SPEAR, "Iron Spear");
      this.addItem(TensuraToolItems.IRON_SCYTHE, "Iron Scythe");
      this.addItem(TensuraToolItems.DIAMOND_SHORT_SWORD, "Diamond Short Sword");
      this.addItem(TensuraToolItems.DIAMOND_LONG_SWORD, "Diamond Long Sword");
      this.addItem(TensuraToolItems.DIAMOND_GREAT_SWORD, "Diamond Great Sword");
      this.addItem(TensuraToolItems.DIAMOND_KATANA, "Diamond Katana");
      this.addItem(TensuraToolItems.DIAMOND_KODACHI, "Diamond Kodachi");
      this.addItem(TensuraToolItems.DIAMOND_TACHI, "Diamond Tachi");
      this.addItem(TensuraToolItems.DIAMOND_ODACHI, "Diamond Odachi");
      this.addItem(TensuraToolItems.DIAMOND_SICKLE, "Diamond Sickle");
      this.addItem(TensuraToolItems.DIAMOND_SPEAR, "Diamond Spear");
      this.addItem(TensuraToolItems.DIAMOND_SCYTHE, "Diamond Scythe");
      this.addItem(TensuraToolItems.LOW_MAGISTEEL_SWORD, "Low Magisteel Sword");
      this.addItem(TensuraToolItems.LOW_MAGISTEEL_SHORT_SWORD, "Low Magisteel Short Sword");
      this.addItem(TensuraToolItems.LOW_MAGISTEEL_LONG_SWORD, "Low Magisteel Long Sword");
      this.addItem(TensuraToolItems.LOW_MAGISTEEL_GREAT_SWORD, "Low Magisteel Great Sword");
      this.addItem(TensuraToolItems.LOW_MAGISTEEL_KATANA, "Low Magisteel Katana");
      this.addItem(TensuraToolItems.LOW_MAGISTEEL_KODACHI, "Low Magisteel Kodachi");
      this.addItem(TensuraToolItems.LOW_MAGISTEEL_TACHI, "Low Magisteel Tachi");
      this.addItem(TensuraToolItems.LOW_MAGISTEEL_ODACHI, "Low Magisteel Odachi");
      this.addItem(TensuraToolItems.LOW_MAGISTEEL_PICKAXE, "Low Magisteel Pickaxe");
      this.addItem(TensuraToolItems.LOW_MAGISTEEL_AXE, "Low Magisteel Axe");
      this.addItem(TensuraToolItems.LOW_MAGISTEEL_SHOVEL, "Low Magisteel Shovel");
      this.addItem(TensuraToolItems.LOW_MAGISTEEL_HOE, "Low Magisteel Hoe");
      this.addItem(TensuraToolItems.LOW_MAGISTEEL_SICKLE, "Low Magisteel Sickle");
      this.addItem(TensuraToolItems.LOW_MAGISTEEL_SPEAR, "Low Magisteel Spear");
      this.addItem(TensuraToolItems.LOW_MAGISTEEL_SCYTHE, "Low Magisteel Scythe");
      this.addItem(TensuraToolItems.NETHERITE_SHORT_SWORD, "Netherite Short Sword");
      this.addItem(TensuraToolItems.NETHERITE_LONG_SWORD, "Netherite Long Sword");
      this.addItem(TensuraToolItems.NETHERITE_GREAT_SWORD, "Netherite Great Sword");
      this.addItem(TensuraToolItems.NETHERITE_KATANA, "Netherite Katana");
      this.addItem(TensuraToolItems.NETHERITE_KODACHI, "Netherite Kodachi");
      this.addItem(TensuraToolItems.NETHERITE_TACHI, "Netherite Tachi");
      this.addItem(TensuraToolItems.NETHERITE_ODACHI, "Netherite Odachi");
      this.addItem(TensuraToolItems.NETHERITE_SICKLE, "Netherite Sickle");
      this.addItem(TensuraToolItems.NETHERITE_SPEAR, "Netherite Spear");
      this.addItem(TensuraToolItems.NETHERITE_SCYTHE, "Netherite Scythe");
      this.addItem(TensuraToolItems.HIGH_MAGISTEEL_SWORD, "High Magisteel Sword");
      this.addItem(TensuraToolItems.HIGH_MAGISTEEL_SHORT_SWORD, "High Magisteel Short Sword");
      this.addItem(TensuraToolItems.HIGH_MAGISTEEL_LONG_SWORD, "High Magisteel Long Sword");
      this.addItem(TensuraToolItems.HIGH_MAGISTEEL_GREAT_SWORD, "High Magisteel Great Sword");
      this.addItem(TensuraToolItems.HIGH_MAGISTEEL_KATANA, "High Magisteel Katana");
      this.addItem(TensuraToolItems.HIGH_MAGISTEEL_KODACHI, "High Magisteel Kodachi");
      this.addItem(TensuraToolItems.HIGH_MAGISTEEL_TACHI, "High Magisteel Tachi");
      this.addItem(TensuraToolItems.HIGH_MAGISTEEL_ODACHI, "High Magisteel Odachi");
      this.addItem(TensuraToolItems.HIGH_MAGISTEEL_PICKAXE, "High Magisteel Pickaxe");
      this.addItem(TensuraToolItems.HIGH_MAGISTEEL_AXE, "High Magisteel Axe");
      this.addItem(TensuraToolItems.HIGH_MAGISTEEL_SHOVEL, "High Magisteel Shovel");
      this.addItem(TensuraToolItems.HIGH_MAGISTEEL_HOE, "High Magisteel Hoe");
      this.addItem(TensuraToolItems.HIGH_MAGISTEEL_SICKLE, "High Magisteel Sickle");
      this.addItem(TensuraToolItems.HIGH_MAGISTEEL_SPEAR, "High Magisteel Spear");
      this.addItem(TensuraToolItems.HIGH_MAGISTEEL_SCYTHE, "High Magisteel Scythe");
      this.addItem(TensuraToolItems.MITHRIL_SWORD, "Mithril Sword");
      this.addItem(TensuraToolItems.MITHRIL_SHORT_SWORD, "Mithril Short Sword");
      this.addItem(TensuraToolItems.MITHRIL_LONG_SWORD, "Mithril Long Sword");
      this.addItem(TensuraToolItems.MITHRIL_GREAT_SWORD, "Mithril Great Sword");
      this.addItem(TensuraToolItems.MITHRIL_KATANA, "Mithril Katana");
      this.addItem(TensuraToolItems.MITHRIL_KODACHI, "Mithril Kodachi");
      this.addItem(TensuraToolItems.MITHRIL_TACHI, "Mithril Tachi");
      this.addItem(TensuraToolItems.MITHRIL_ODACHI, "Mithril Odachi");
      this.addItem(TensuraToolItems.MITHRIL_PICKAXE, "Mithril Pickaxe");
      this.addItem(TensuraToolItems.MITHRIL_AXE, "Mithril Axe");
      this.addItem(TensuraToolItems.MITHRIL_SHOVEL, "Mithril Shovel");
      this.addItem(TensuraToolItems.MITHRIL_HOE, "Mithril Hoe");
      this.addItem(TensuraToolItems.MITHRIL_SICKLE, "Mithril Sickle");
      this.addItem(TensuraToolItems.MITHRIL_SPEAR, "Mithril Spear");
      this.addItem(TensuraToolItems.MITHRIL_SCYTHE, "Mithril Scythe");
      this.addItem(TensuraToolItems.ORICHALCUM_SWORD, "Orichalcum Sword");
      this.addItem(TensuraToolItems.ORICHALCUM_SHORT_SWORD, "Orichalcum Short Sword");
      this.addItem(TensuraToolItems.ORICHALCUM_LONG_SWORD, "Orichalcum Long Sword");
      this.addItem(TensuraToolItems.ORICHALCUM_GREAT_SWORD, "Orichalcum Great Sword");
      this.addItem(TensuraToolItems.ORICHALCUM_KATANA, "Orichalcum Katana");
      this.addItem(TensuraToolItems.ORICHALCUM_KODACHI, "Orichalcum Kodachi");
      this.addItem(TensuraToolItems.ORICHALCUM_TACHI, "Orichalcum Tachi");
      this.addItem(TensuraToolItems.ORICHALCUM_ODACHI, "Orichalcum Odachi");
      this.addItem(TensuraToolItems.ORICHALCUM_PICKAXE, "Orichalcum Pickaxe");
      this.addItem(TensuraToolItems.ORICHALCUM_AXE, "Orichalcum Axe");
      this.addItem(TensuraToolItems.ORICHALCUM_SHOVEL, "Orichalcum Shovel");
      this.addItem(TensuraToolItems.ORICHALCUM_HOE, "Orichalcum Hoe");
      this.addItem(TensuraToolItems.ORICHALCUM_SICKLE, "Orichalcum Sickle");
      this.addItem(TensuraToolItems.ORICHALCUM_SPEAR, "Orichalcum Spear");
      this.addItem(TensuraToolItems.ORICHALCUM_SCYTHE, "Orichalcum Scythe");
      this.addItem(TensuraToolItems.PURE_MAGISTEEL_SWORD, "Pure Magisteel Sword");
      this.addItem(TensuraToolItems.PURE_MAGISTEEL_SHORT_SWORD, "Pure Magisteel Short Sword");
      this.addItem(TensuraToolItems.PURE_MAGISTEEL_LONG_SWORD, "Pure Magisteel Long Sword");
      this.addItem(TensuraToolItems.PURE_MAGISTEEL_GREAT_SWORD, "Pure Magisteel Great Sword");
      this.addItem(TensuraToolItems.PURE_MAGISTEEL_KATANA, "Pure Magisteel Katana");
      this.addItem(TensuraToolItems.PURE_MAGISTEEL_KODACHI, "Pure Magisteel Kodachi");
      this.addItem(TensuraToolItems.PURE_MAGISTEEL_TACHI, "Pure Magisteel Tachi");
      this.addItem(TensuraToolItems.PURE_MAGISTEEL_ODACHI, "Pure Magisteel Odachi");
      this.addItem(TensuraToolItems.PURE_MAGISTEEL_PICKAXE, "Pure Magisteel Pickaxe");
      this.addItem(TensuraToolItems.PURE_MAGISTEEL_AXE, "Pure Magisteel Axe");
      this.addItem(TensuraToolItems.PURE_MAGISTEEL_SHOVEL, "Pure Magisteel Shovel");
      this.addItem(TensuraToolItems.PURE_MAGISTEEL_HOE, "Pure Magisteel Hoe");
      this.addItem(TensuraToolItems.PURE_MAGISTEEL_SICKLE, "Pure Magisteel Sickle");
      this.addItem(TensuraToolItems.PURE_MAGISTEEL_SPEAR, "Pure Magisteel Spear");
      this.addItem(TensuraToolItems.PURE_MAGISTEEL_SCYTHE, "Pure Magisteel Scythe");
      this.addItem(TensuraToolItems.ADAMANTITE_SWORD, "Adamantite Sword");
      this.addItem(TensuraToolItems.ADAMANTITE_SHORT_SWORD, "Adamantite Short Sword");
      this.addItem(TensuraToolItems.ADAMANTITE_LONG_SWORD, "Adamantite Long Sword");
      this.addItem(TensuraToolItems.ADAMANTITE_GREAT_SWORD, "Adamantite Great Sword");
      this.addItem(TensuraToolItems.ADAMANTITE_KATANA, "Adamantite Katana");
      this.addItem(TensuraToolItems.ADAMANTITE_KODACHI, "Adamantite Kodachi");
      this.addItem(TensuraToolItems.ADAMANTITE_TACHI, "Adamantite Tachi");
      this.addItem(TensuraToolItems.ADAMANTITE_ODACHI, "Adamantite Odachi");
      this.addItem(TensuraToolItems.ADAMANTITE_PICKAXE, "Adamantite Pickaxe");
      this.addItem(TensuraToolItems.ADAMANTITE_AXE, "Adamantite Axe");
      this.addItem(TensuraToolItems.ADAMANTITE_SHOVEL, "Adamantite Shovel");
      this.addItem(TensuraToolItems.ADAMANTITE_HOE, "Adamantite Hoe");
      this.addItem(TensuraToolItems.ADAMANTITE_SICKLE, "Adamantite Sickle");
      this.addItem(TensuraToolItems.ADAMANTITE_SPEAR, "Adamantite Spear");
      this.addItem(TensuraToolItems.ADAMANTITE_SCYTHE, "Adamantite Scythe");
      this.addItem(TensuraToolItems.HIHIIROKANE_SWORD, "Hihi'Irokane Sword");
      this.addItem(TensuraToolItems.HIHIIROKANE_SHORT_SWORD, "Hihi'Irokane Short Sword");
      this.addItem(TensuraToolItems.HIHIIROKANE_LONG_SWORD, "Hihi'Irokane Long Sword");
      this.addItem(TensuraToolItems.HIHIIROKANE_GREAT_SWORD, "Hihi'Irokane Great Sword");
      this.addItem(TensuraToolItems.HIHIIROKANE_KATANA, "Hihi'Irokane Katana");
      this.addItem(TensuraToolItems.HIHIIROKANE_KODACHI, "Hihi'Irokane Kodachi");
      this.addItem(TensuraToolItems.HIHIIROKANE_TACHI, "Hihi'Irokane Tachi");
      this.addItem(TensuraToolItems.HIHIIROKANE_ODACHI, "Hihi'Irokane Odachi");
      this.addItem(TensuraToolItems.HIHIIROKANE_PICKAXE, "Hihi'Irokane Pickaxe");
      this.addItem(TensuraToolItems.HIHIIROKANE_AXE, "Hihi'Irokane Axe");
      this.addItem(TensuraToolItems.HIHIIROKANE_SHOVEL, "Hihi'Irokane Shovel");
      this.addItem(TensuraToolItems.HIHIIROKANE_HOE, "Hihi'Irokane Hoe");
      this.addItem(TensuraToolItems.HIHIIROKANE_SICKLE, "Hihi'Irokane Sickle");
      this.addItem(TensuraToolItems.HIHIIROKANE_SPEAR, "Hihi'Irokane Spear");
      this.addItem(TensuraToolItems.HIHIIROKANE_SCYTHE, "Hihi'Irokane Scythe");
   }

   private void uniqueGearItems() {
      this.addItem(TensuraArmorItems.ANTI_MAGIC_MASK, "Anti-Magic Mask");
      this.addItem(TensuraArmorItems.DARK_JACKET, "Dark Jacket");
      this.addItem(TensuraArmorItems.DARK_LEGGINGS, "Dark Leggings");
      this.addItem(TensuraArmorItems.DARK_BOOTS, "Dark Boots");
      this.addItem(TensuraArmorItems.WINGED_SHOES, "Winged Shoes");
      this.addItem(TensuraArmorItems.BAT_GLIDER, "Bat Glider");
      this.addItem(TensuraArmorItems.CRAZY_PIERROT_MASK, "Crazy Pierrot Mask");
      this.addItem(TensuraArmorItems.ANGRY_PIERROT_MASK, "Angry Pierrot Mask");
      this.addItem(TensuraArmorItems.WONDER_PIERROT_MASK, "Wonder Pierrot Mask");
      this.addItem(TensuraArmorItems.TEARY_PIERROT_MASK, "Teardrop Mask");
      this.addItem(TensuraToolItems.ORB_OF_DOMINATION, "Orb of Domination");
      this.addItem(TensuraToolItems.ARMOURSAURUS_GAUNTLET, "Armoursaurus Gauntlet");
      this.addItem(TensuraToolItems.DRAGON_KNUCKLE, "Dragon Knuckle");
      this.addItem(TensuraToolItems.DEAD_END_RAINBOW, "Dead End Rainbow");
      this.addItem(TensuraToolItems.ICE_BLADE, "Ice Blade");
      this.addItem(TensuraToolItems.MEAT_CRUSHER, "Meat Crusher");
      this.addItem(TensuraToolItems.MOONLIGHT, "Moonlight");
      this.addItem(TensuraToolItems.SPATIAL_BLADE, "Spatial Blade");
      this.addItem(TensuraToolItems.SEVERER_BLADE, "Severer Blade");
      this.addItem(TensuraToolItems.SLIME_STAFF, "Staff of Slime");
      this.addItem(TensuraToolItems.SNIPER_PISTOL, "Sniper Pistol");
      this.addItem(TensuraToolItems.COPPER_SHELL, "Copper Shell");
      this.addItem(TensuraToolItems.VORTEX_SPEAR, "Vortex Spear");
      this.addItem(TensuraToolItems.WEB_GUN, "Web Gun");
      this.addItem(TensuraToolItems.WEB_CARTRIDGE, "Web Cartridge");
      this.addItem(TensuraToolItems.STICKY_WEB_CARTRIDGE, "Sticky Web Cartridge");
      this.addItem(TensuraToolItems.STICKY_STEEL_WEB_CARTRIDGE, "Sticky Steel Web Cartridge");
   }

   private void consumableItems() {
      this.addItem(TensuraConsumableItems.DUBIOUS_FOOD, "Dubious Food");
      this.addItem(TensuraConsumableItems.RAW_ARMOURSAURUS_MEAT, "Raw Armoursaurus Meat");
      this.addItem(TensuraConsumableItems.COOKED_ARMOURSAURUS_MEAT, "Cooked Armoursaurus Meat");
      this.addItem(TensuraConsumableItems.BUCKET_OF_BULLDEER_MILK, "Bulldeer Milk Bucket");
      this.addItem(TensuraConsumableItems.BULLDEER_BEEF, "Bulldeer Beef");
      this.addItem(TensuraConsumableItems.BULLDEER_STEAK, "Bulldeer Steak");
      this.addItem(TensuraConsumableItems.BLADE_TIGER_STEAK, "Blade Tiger Steak");
      this.addItem(TensuraConsumableItems.RAW_BLADE_TIGER_MEAT, "Raw Blade Tiger Meat");
      this.addItem(TensuraConsumableItems.RAW_CHARYBDIS_MEAT, "Raw Charybdis Meat");
      this.addItem(TensuraConsumableItems.COOKED_CHARYBDIS_MEAT, "Cooked Charybdis Meat");
      this.addItem(TensuraConsumableItems.GIANT_ANT_LEG, "Giant Ant Leg");
      this.addItem(TensuraConsumableItems.COOKED_GIANT_ANT_LEG, "Cooked Giant Ant Leg");
      this.addItem(TensuraConsumableItems.RAW_GIANT_BAT_MEAT, "Raw Giant Bat Meat");
      this.addItem(TensuraConsumableItems.COOKED_GIANT_BAT_MEAT, "Cooked Giant Bat Meat");
      this.addItem(TensuraConsumableItems.KNIGHT_SPIDER_LEG, "Knight Spider Leg");
      this.addItem(TensuraConsumableItems.COOKED_KNIGHT_SPIDER_LEG, "Cooked Knight Spider Leg");
      this.addItem(TensuraConsumableItems.RAW_MEGALODON_MEAT, "Raw Megalodon Meat");
      this.addItem(TensuraConsumableItems.COOKED_MEGALODON_MEAT, "Cooked Megalodon Meat");
      this.addItem(TensuraConsumableItems.RAW_SERPENT_MEAT, "Raw Serpent Meat");
      this.addItem(TensuraConsumableItems.COOKED_SERPENT_MEAT, "Cooked Serpent Meat");
      this.addItem(TensuraConsumableItems.RAW_SPEAR_TORO_MEAT, "Raw Spear Toro Meat");
      this.addItem(TensuraConsumableItems.COOKED_SPEAR_TORO_MEAT, "Cooked Spear Toro Meat");
      this.addItem(TensuraConsumableItems.SPEAR_TORO_FIN, "Spear Toro Fin");
      this.addItem(TensuraConsumableItems.COOKED_SPEAR_TORO_FIN, "Cooked Spear Toro Fin");
      this.addItem(TensuraConsumableItems.RAW_SISSIE_MEAT, "Raw Sissie Meat");
      this.addItem(TensuraConsumableItems.COOKED_SISSIE_MEAT, "Cooked Sissie Meat");
      this.addItem(TensuraConsumableItems.SISSIE_FIN, "Sissie Fin");
      this.addItem(TensuraConsumableItems.COOKED_SISSIE_FIN, "Cooked Sissie Fin");
      this.addItem(TensuraConsumableItems.CHILLED_SLIME, "Chilled Slime");
      this.addItem(TensuraConsumableItems.SILVER_APPLE, "Silver Apple");
      this.addItem(TensuraConsumableItems.ENCHANTED_SILVER_APPLE, "Enchanted Silver Apple");
      this.addItem(TensuraConsumableItems.MAGIC_BOTTLE, "Magic Bottle");
      this.addItem(TensuraConsumableItems.WATER_MAGIC_BOTTLE, "Magic Bottle of Water");
      this.addItem(TensuraConsumableItems.VACUUMED_WATER_MAGIC_BOTTLE, "Vacuumed Magic Bottle of Water");
      this.addItem(TensuraConsumableItems.LOW_POTION, "Low Potion");
      this.addItem(TensuraConsumableItems.HIGH_POTION, "High Potion");
      this.addItem(TensuraConsumableItems.FULL_POTION, "Full Potion");
      this.addItem(TensuraConsumableItems.REVIVAL_ELIXER, "Revival Elixir");
      this.addItem(TensuraConsumableItems.HOLY_WATER, "Holy Water");
      this.addItem(TensuraConsumableItems.GREATER_HOLY_WATER, "Greater Holy Water");
      this.addItem(TensuraConsumableItems.HOLY_MILK, "Holy Milk");
      this.addItem(TensuraConsumableItems.HOLY_MILK_BUCKET, "Holy Milk Bucket");
   }

   private void mobDropItems() {
      this.addItem(TensuraMobDropItems.ARMOURSAURUS_SCALE, "Armoursaurus Scale");
      this.addItem(TensuraMobDropItems.ARMOURSAURUS_SHELL, "Armoursaurus Shell");
      this.addItem(TensuraMobDropItems.MONSTER_LEATHER_D, "Monster Leather (D)");
      this.addItem(TensuraMobDropItems.MONSTER_LEATHER_C, "Monster Leather (C)");
      this.addItem(TensuraMobDropItems.MONSTER_LEATHER_B, "Monster Leather (B)");
      this.addItem(TensuraMobDropItems.MONSTER_LEATHER_A, "Monster Leather (A)");
      this.addItem(TensuraMobDropItems.MONSTER_LEATHER_SPECIAL_A, "Monster Leather (Special A)");
      this.addItem(TensuraMobDropItems.CHARYBDIS_SCALE, "Charybdis Scale");
      this.addItem(TensuraMobDropItems.DRAGON_PEACOCK_FEATHER, "Dragon Peacock Feather");
      this.addItem(TensuraMobDropItems.GIANT_ANT_CARAPACE, "Giant Ant Carapace");
      this.addItem(TensuraMobDropItems.GIANT_BAT_WING, "Giant Bat Wing");
      this.addItem(TensuraMobDropItems.HELL_MOTH_SILK, "Hell-Moth Silk");
      this.addItem(TensuraMobDropItems.GEHENNA_MOTH_SILK, "Gehenna-Moth Silk");
      this.addItem(TensuraMobDropItems.INSECTAR_CARAPACE, "Insectar Carapace");
      this.addItem(TensuraMobDropItems.INVISIBLE_FEATHER, "Invisible Feather");
      this.addItem(TensuraMobDropItems.KNIGHT_SPIDER_CARAPACE, "Knight Spider Carapace");
      this.addItem(TensuraMobDropItems.SERPENT_SCALE, "Serpent Scale");
      this.addItem(TensuraMobDropItems.CENTIPEDE_STINGER, "Centipede Stinger");
      this.addItem(TensuraMobDropItems.SISSIE_TOOTH, "Sissie Tooth");
      this.addItem(TensuraMobDropItems.SPIDER_FANG, "Spider Fang");
      this.addItem(TensuraMobDropItems.BLADE_TIGER_TAIL, "Blade Tiger Tail");
      this.addItem(TensuraMobDropItems.SLIME_CHUNK, "Slime Chunk");
      this.addItem(TensuraMobDropItems.SLIME_CORE, "Slime Core");
      this.addItem(TensuraMobDropItems.STICKY_THREAD, "Sticky Thread");
      this.addItem(TensuraMobDropItems.STEEL_THREAD, "Steel Thread");
      this.addItem(TensuraMobDropItems.BEAST_HORN, "Beast Horn");
      this.addItem(TensuraMobDropItems.UNICORN_HORN, "Unicorn Horn");
      this.addItem(TensuraMobDropItems.LOW_QUALITY_MAGIC_CRYSTAL, "Low Quality Magic Crystal");
      this.addItem(TensuraMobDropItems.MEDIUM_QUALITY_MAGIC_CRYSTAL, "Medium Quality Magic Crystal");
      this.addItem(TensuraMobDropItems.HIGH_QUALITY_MAGIC_CRYSTAL, "High Quality Magic Crystal");
      this.addItem(TensuraMobDropItems.DEMON_ESSENCE, "Demon Essence");
      this.addItem(TensuraMobDropItems.DRAGON_ESSENCE, "Dragon Essence");
      this.addItem(TensuraMobDropItems.ELEMENTAL_ESSENCE, "Elemental Essence");
      this.addItem(TensuraMobDropItems.ROYAL_BLOOD, "Royal Blood");
      this.addItem(TensuraMobDropItems.ZANE_BLOOD, "Zane Blood");
      this.addItem(TensuraBlocks.Items.ORC_DISASTER_HEAD, "Orc Disaster Head");
   }

   private void schematicItems() {
      this.addItem(TensuraSmithingSchematicItems.BASIC_BOWS, "Basic Bows Schematic");
      this.addItem(TensuraSmithingSchematicItems.SPIDER_BOWS, "Spider Bows Schematic");
      this.addItem(TensuraSmithingSchematicItems.LEATHER_GEAR, "Leather Gear Schematic");
      this.addItem(TensuraSmithingSchematicItems.MONSTER_LEATHER_GEAR, "Monster Leather Gear Schematic");
      this.addItem(TensuraSmithingSchematicItems.GOLD_GEAR, "Gold Gear Schematic");
      this.addItem(TensuraSmithingSchematicItems.SILVER_GEAR, "Silver Gear Schematic");
      this.addItem(TensuraSmithingSchematicItems.IRON_GEAR, "Iron Gear Schematic");
      this.addItem(TensuraSmithingSchematicItems.ANT_CARAPACE_GEAR, "Ant Carapace Gear Schematic");
      this.addItem(TensuraSmithingSchematicItems.SERPENT_SCALEMAIL_GEAR, "Serpent Scalemail Gear Schematic");
      this.addItem(TensuraSmithingSchematicItems.DIAMOND_GEAR, "Diamond Gear Schematic");
      this.addItem(TensuraSmithingSchematicItems.KNIGHT_SPIDER_CARAPACE_GEAR, "Knight Spider Carapace Gear Schematic");
      this.addItem(TensuraSmithingSchematicItems.LOW_MAGISTEEL_GEAR, "Low Magisteel Gear Schematic");
      this.addItem(TensuraSmithingSchematicItems.ARMOURSAURUS_SCALEMAIL_GEAR, "Armoursaurus Scalemail Gear Schematic");
      this.addItem(TensuraSmithingSchematicItems.HIGH_MAGISTEEL_GEAR, "High Magisteel Gear Schematic");
      this.addItem(TensuraSmithingSchematicItems.CHARYBDIS_SCALEMAIL_GEAR, "Charybdis Scalemail Gear Schematic");
      this.addItem(TensuraSmithingSchematicItems.MITHRIL_GEAR, "Mithril Gear Schematic");
      this.addItem(TensuraSmithingSchematicItems.ORICHALCUM_GEAR, "Orichalcum Gear Schematic");
      this.addItem(TensuraSmithingSchematicItems.PURE_MAGISTEEL_GEAR, "Pure Magisteel Gear Schematic");
      this.addItem(TensuraSmithingSchematicItems.ADAMANTITE_GEAR, "Adamantite Gear Schematic");
      this.addItem(TensuraSmithingSchematicItems.HIHIIROKANE_GEAR, "Hihi'Irokane Gear Schematic");
      this.addItem(TensuraSmithingSchematicItems.ANTI_MAGIC_MASK, "Anti-Magic Mask Schematic");
      this.addItem(TensuraSmithingSchematicItems.DARK_SET, "Dark Set Schematic");
      this.addItem(TensuraSmithingSchematicItems.PIERROT_MASK, "Pierrot Mask Schematic");
      this.addItem(TensuraSmithingSchematicItems.JAPANESE_SWORD, "Japanese Sword Schematic");
      this.addItem(TensuraSmithingSchematicItems.SHORT_SWORD, "Short Sword Schematic");
      this.addItem(TensuraSmithingSchematicItems.LONG_SWORD, "Long Sword Schematic");
      this.addItem(TensuraSmithingSchematicItems.GREAT_SWORD, "Great Sword Schematic");
      this.addItem(TensuraSmithingSchematicItems.SPEAR, "Spear Schematic");
      this.addItem(TensuraSmithingSchematicItems.KUNAI, "Kunai Schematic");
      this.addItem(TensuraSmithingSchematicItems.SPATIAL_BLADE, "Spatial Blade Schematic");
      this.addItem(TensuraSmithingSchematicItems.WEB_GUN, "Web Gun Schematic");
      this.addItem(TensuraSmithingSchematicItems.SHIELD, "Shield Schematic");
      this.addItem(TensuraSmithingSchematicItems.WINGED_SHOES, "Winged Shoes Schematic");
      this.addItem(TensuraSmithingSchematicItems.HUNTING_KNIFE, "Dagger Schematic");
   }

   private void miscItems() {
      this.add("tensura.map.labyrinth", "Labyrinth Explorer Map");
      this.add("tensura.map.hell_gate", "Hell Gate Explorer Map");
      this.addItem(TensuraMaterialItems.RAW_SILVER, "Raw Silver");
      this.addItem(TensuraMaterialItems.SILVER_NUGGET, "Silver Nugget");
      this.addItem(TensuraMaterialItems.SILVER_INGOT, "Silver Ingot");
      this.addItem(TensuraMaterialItems.MAGIC_ORE, "Magic Ore");
      this.addItem(TensuraMaterialItems.LOW_MAGISTEEL_NUGGET, "Low Magisteel Nugget");
      this.addItem(TensuraMaterialItems.LOW_MAGISTEEL_INGOT, "Low Magisteel Ingot");
      this.addItem(TensuraMaterialItems.HIGH_MAGISTEEL_NUGGET, "High Magisteel Nugget");
      this.addItem(TensuraMaterialItems.HIGH_MAGISTEEL_INGOT, "High Magisteel Ingot");
      this.addItem(TensuraMaterialItems.MITHRIL_NUGGET, "Mithril Nugget");
      this.addItem(TensuraMaterialItems.MITHRIL_INGOT, "Mithril Ingot");
      this.addItem(TensuraMaterialItems.ORICHALCUM_NUGGET, "Orichalcum Nugget");
      this.addItem(TensuraMaterialItems.ORICHALCUM_INGOT, "Orichalcum Ingot");
      this.addItem(TensuraMaterialItems.PURE_MAGISTEEL_NUGGET, "Pure Magisteel Nugget");
      this.addItem(TensuraMaterialItems.ADAMANTITE_NUGGET, "Adamantite Nugget");
      this.addItem(TensuraMaterialItems.HIHIIROKANE_NUGGET, "Hihi'Irokane Nugget");
      this.addItem(TensuraMaterialItems.PURE_MAGISTEEL_INGOT, "Pure Magisteel Ingot");
      this.addItem(TensuraMaterialItems.ADAMANTITE_INGOT, "Adamantite Ingot");
      this.addItem(TensuraMaterialItems.HIHIIROKANE_INGOT, "Hihi'Irokane Ingot");
      this.addItem(TensuraMaterialItems.MAGIC_STONE, "Magic Stone");
      this.addItem(TensuraMaterialItems.ELEMENT_CORE_EMPTY, "Element Core (Empty)");
      this.addItem(TensuraMaterialItems.EARTH_ELEMENTAL_SHARD, "Elemental Shard (Earth)");
      this.addItem(TensuraMaterialItems.ELEMENT_CORE_EARTH, "Element Core (Earth)");
      this.addItem(TensuraMaterialItems.FIRE_ELEMENTAL_SHARD, "Elemental Shard (Fire)");
      this.addItem(TensuraMaterialItems.ELEMENT_CORE_FIRE, "Element Core (Fire)");
      this.addItem(TensuraMaterialItems.SPACE_ELEMENTAL_SHARD, "Elemental Shard (Space)");
      this.addItem(TensuraMaterialItems.ELEMENT_CORE_SPACE, "Element Core (Space)");
      this.addItem(TensuraMaterialItems.WATER_ELEMENTAL_SHARD, "Elemental Shard (Water)");
      this.addItem(TensuraMaterialItems.ELEMENT_CORE_WATER, "Element Core (Water)");
      this.addItem(TensuraMaterialItems.WIND_ELEMENTAL_SHARD, "Elemental Shard (Wind)");
      this.addItem(TensuraMaterialItems.ELEMENT_CORE_WIND, "Element Core (Wind)");
      this.addItem(TensuraMaterialItems.BLACK_FIRE_CHARGE, "Black Fire Charge");
      this.addItem(TensuraMaterialItems.HOLY_FIRE_CHARGE, "Holy Fire Charge");
      this.addItem(TensuraMaterialItems.BRONZE_COIN, "Bronze Coin");
      this.addItem(TensuraMaterialItems.SILVER_COIN, "Silver Coin");
      this.addItem(TensuraMaterialItems.GOLD_COIN, "Gold Coin");
      this.addItem(TensuraMaterialItems.STELLAR_GOLD_COIN, "Stellar Gold Coin");
      this.addItem(TensuraMaterialItems.SLIME_IN_A_BUCKET, "Bucket of Slime");
      this.addItem(TensuraMaterialItems.SHADOW_STORAGE, "Shadow Storage");
      this.addItem(TensuraMaterialItems.HOT_SPRING_WATER_BUCKET, "Hot Spring Water Bucket");
      this.addItem(TensuraMaterialItems.MONSTER_SADDLE, "Monster Saddle");
      this.addItem(TensuraMaterialItems.THATCH, "Thatch");
      this.addItem(TensuraMaterialItems.HIPOKUTE_FLOWER, "Hipokute Flower");
      this.addItem(TensuraMaterialItems.HIPOKUTE_GRASS, "Hipokute Grass");
      this.addItem(TensuraMaterialItems.HIPOKUTE_SEEDS, "Hipokute Seeds");
      this.addItem(TensuraMaterialItems.MUSIC_DISC_NANODA, "Music Disc");
      this.add("item.tensura.music_disc_nanoda.desc", "Nanoda!");
   }

   private void entitiesAndSpawnEggs() {
      this.addEntity(TensuraEntityTypes.BOAT_ENTITY, "Boat");
      this.addEntity(TensuraEntityTypes.CHEST_BOAT_ENTITY, "Boat with Chest");
      this.addEntity(TensuraEntityTypes.CLONE_DEFAULT, "Clone");
      this.addEntity(TensuraEntityTypes.CLONE_SLIM, "Clone");
      this.addEntity(TensuraEntityTypes.IFRIT_CLONE, "Ifrit");
      this.addEntity(TensuraEntityTypes.FALMUTH_KNIGHT, "Falmuth Knight");
      this.addEntity(TensuraEntityTypes.FOLGEN, "Folgen");
      this.add((Item)TensuraSpawnEggs.OTHERWORLDER_FOLGEN.get(), "Otherworlder Folgen Spawn Egg");
      this.addEntity(TensuraEntityTypes.HINATA_SAKAGUCHI, "Hinata Sakaguchi");
      this.add((Item)TensuraSpawnEggs.OTHERWORLDER_HINATA_SAKAGUCHI.get(), "Otherworlder Hinata Sakaguchi Spawn Egg");
      this.addEntity(TensuraEntityTypes.KIRARA_MIZUTANI, "Kirara Mizutani");
      this.add((Item)TensuraSpawnEggs.OTHERWORLDER_KIRARA_MIZUTANI.get(), "Otherworlder Kirara Mizutani Spawn Egg");
      this.addEntity(TensuraEntityTypes.KYOYA_TACHIBANA, "Kyoya Tachibana");
      this.add((Item)TensuraSpawnEggs.OTHERWORLDER_KYOYA_TACHIBANA.get(), "Otherworlder Kyoya Tachibana Spawn Egg");
      this.addEntity(TensuraEntityTypes.MAI_FURUKI, "Mai Furuki");
      this.add((Item)TensuraSpawnEggs.OTHERWORLDER_MAI_FURUKI.get(), "Otherworlder Mai Furuki Spawn Egg");
      this.addEntity(TensuraEntityTypes.MARK_LAUREN, "Mark Lauren");
      this.add((Item)TensuraSpawnEggs.OTHERWORLDER_MARK_LAUREN.get(), "Otherworlder Mark Lauren Spawn Egg");
      this.addEntity(TensuraEntityTypes.SHINJI_TANIMURA, "Shinji Tanimura");
      this.add((Item)TensuraSpawnEggs.OTHERWORLDER_SHINJI_TANIMURA.get(), "Otherworlder Shinji Tanimura Spawn Egg");
      this.addEntity(TensuraEntityTypes.SHIN_RYUSEI, "Shin Ryusei");
      this.add((Item)TensuraSpawnEggs.OTHERWORLDER_SHIN_RYUSEI.get(), "Otherworlder Shin Ryusei Spawn Egg");
      this.addEntity(TensuraEntityTypes.SHIZU, "Shizu");
      this.add((Item)TensuraSpawnEggs.OTHERWORLDER_SHIZU.get(), "Otherworlder Shizu Spawn Egg");
      this.addEntity(TensuraEntityTypes.SHOGO_TAGUCHI, "Shogo Taguchi");
      this.add((Item)TensuraSpawnEggs.OTHERWORLDER_SHOGO_TAGUCHI.get(), "Otherworlder Shogo Taguchi Spawn Egg");
      this.addEntityAndSpawnEgg(TensuraEntityTypes.AKASH, "Akash");
      this.addEntityAndSpawnEgg(TensuraEntityTypes.AQUA_FROG, "Aqua Frog");
      this.addEntityAndSpawnEgg(TensuraEntityTypes.ARMOURSAURUS, "Armoursaurus");
      this.addEntityAndSpawnEgg(TensuraEntityTypes.ARMY_WASP, "Army Wasp");
      this.addEntityAndSpawnEgg(TensuraEntityTypes.BARGHEST, "Barghest");
      this.addEntityAndSpawnEgg(TensuraEntityTypes.BEAST_GNOME, "Beast Gnome");
      this.addEntityAndSpawnEgg(TensuraEntityTypes.BLACK_SPIDER, "Black Spider");
      this.addEntityAndSpawnEgg(TensuraEntityTypes.BLADE_TIGER, "Blade Tiger");
      this.addEntityAndSpawnEgg(TensuraEntityTypes.BULLDEER, "Bulldeer");
      this.addEntityAndSpawnEgg(TensuraEntityTypes.CHARYBDIS, "Charybdis");
      this.addEntityAndSpawnEgg(TensuraEntityTypes.DIREWOLF, "Direwolf");
      this.addEntityAndSpawnEgg(TensuraEntityTypes.DRAGON_PEACOCK, "Dragon Peacock");
      this.addEntityAndSpawnEgg(TensuraEntityTypes.ELEMENTAL_COLOSSUS, "Elemental Colossus");
      this.addEntityAndSpawnEgg(TensuraEntityTypes.EVIL_CENTIPEDE, "Evil Centipede");
      this.addEntity(TensuraEntityTypes.EVIL_CENTIPEDE_BODY, "Evil Centipede");
      this.addEntityAndSpawnEgg(TensuraEntityTypes.FEATHERED_SERPENT, "Feathered Serpent");
      this.addEntityAndSpawnEgg(TensuraEntityTypes.GIANT_ANT, "Giant Ant");
      this.addEntityAndSpawnEgg(TensuraEntityTypes.GIANT_BAT, "Giant Bat");
      this.addEntityAndSpawnEgg(TensuraEntityTypes.GIANT_BEAR, "Giant Bear");
      this.addEntityAndSpawnEgg(TensuraEntityTypes.GIANT_COD, "Giant Cod");
      this.addEntityAndSpawnEgg(TensuraEntityTypes.GIANT_SALMON, "Giant Salmon");
      this.addEntityAndSpawnEgg(TensuraEntityTypes.GOBLIN, "Goblin");
      this.addEntityAndSpawnEgg(TensuraEntityTypes.HELL_CATERPILLAR, "Hell Caterpillar");
      this.addEntityAndSpawnEgg(TensuraEntityTypes.HELL_MOTH, "Hell Moth");
      this.addEntityAndSpawnEgg(TensuraEntityTypes.HOLY_COW, "Holy Cow");
      this.addEntityAndSpawnEgg(TensuraEntityTypes.HORNED_RABBIT, "Horned Rabbit");
      this.addEntityAndSpawnEgg(TensuraEntityTypes.HOUND_DOG, "Hound Dog");
      this.addEntityAndSpawnEgg(TensuraEntityTypes.HOVER_LIZARD, "Hover Lizard");
      this.addEntityAndSpawnEgg(TensuraEntityTypes.HORNED_BEAR, "Horned Bear");
      this.addEntityAndSpawnEgg(TensuraEntityTypes.IFRIT, "Ifrit");
      this.addEntityAndSpawnEgg(TensuraEntityTypes.KNIGHT_SPIDER, "Knight Spider");
      this.addEntityAndSpawnEgg(TensuraEntityTypes.LANDFISH, "Landfish");
      this.addEntityAndSpawnEgg(TensuraEntityTypes.LEECH_LIZARD, "Leech Lizard");
      this.addEntityAndSpawnEgg(TensuraEntityTypes.LIZARDMAN, "Lizardman");
      this.addEntityAndSpawnEgg(TensuraEntityTypes.MEGALODON, "Megalodon");
      this.addEntityAndSpawnEgg(TensuraEntityTypes.ONE_EYED_OWL, "One Eyed Owl");
      this.addEntityAndSpawnEgg(TensuraEntityTypes.ORC, "Orc");
      this.addEntityAndSpawnEgg(TensuraEntityTypes.ORC_LORD, "Orc Lord");
      this.addEntityAndSpawnEgg(TensuraEntityTypes.ORC_DISASTER, "Orc Disaster");
      this.addEntityAndSpawnEgg(TensuraEntityTypes.SALAMANDER, "Salamander");
      this.addEntityAndSpawnEgg(TensuraEntityTypes.SISSIE, "Sissie");
      this.addEntityAndSpawnEgg(TensuraEntityTypes.SLIME, "Slime");
      this.addEntityAndSpawnEgg(TensuraEntityTypes.METAL_SLIME, "Metal Slime");
      this.addEntityAndSpawnEgg(TensuraEntityTypes.SUPERMASSIVE_SLIME, "Supermassive Slime");
      this.addEntityAndSpawnEgg(TensuraEntityTypes.SPEAR_TORO, "Spear Toro");
      this.addEntityAndSpawnEgg(TensuraEntityTypes.SYLPHIDE, "Sylphide");
      this.addEntityAndSpawnEgg(TensuraEntityTypes.TEMPEST_SERPENT, "Tempest Serpent");
      this.addEntity(TensuraEntityTypes.TEMPEST_SERPENT_BODY, "Tempest Serpent");
      this.addEntityAndSpawnEgg(TensuraEntityTypes.UNDINE, "Undine");
      this.addEntityAndSpawnEgg(TensuraEntityTypes.UNICORN, "Unicorn");
      this.addEntityAndSpawnEgg(TensuraEntityTypes.WAR_GNOME, "War Gnome");
      this.addEntityAndSpawnEgg(TensuraEntityTypes.WINGED_CAT, "Winged Cat");
      this.addEntity(TensuraEntityTypes.CHARYBDIS_CORE, "Charybdis Core");
      this.addEntity(TensuraEntityTypes.FALLING_BLOCK, "Falling Block");
      this.addEntity(TensuraEntityTypes.MONSTER_SPIT, "Monster Spit");
      this.addEntity(TensuraEntityTypes.HEALING_POTION, "Healing Potion");
      this.addEntity(TensuraEntityTypes.HOLY_WATER, "Holy Water");
      this.addEntity(TensuraEntityTypes.INVISIBLE_ARROW, "Invisible Arrow");
      this.addEntity(TensuraEntityTypes.KUNAI, "Kunai");
      this.addEntity(TensuraEntityTypes.SEVERER_BLADE, "Severer Blade");
      this.addEntity(TensuraEntityTypes.SPEARED_FIN_ARROW, "Speared Fin Arrow");
      this.addEntity(TensuraEntityTypes.SPEAR, "Spear");
      this.addEntity(TensuraEntityTypes.UNICORN_HORN, "Unicorn Horn");
      this.addEntity(TensuraEntityTypes.WEB_BULLET, "Web Bullet");
      this.addEntity(TensuraEntityTypes.BLACK_FLAME_BREATH, "Black Flame Breath");
      this.addEntity(TensuraEntityTypes.FLAME_BREATH, "Flame Breath");
      this.addEntity(TensuraEntityTypes.GLUTTONY_MIST, "Gluttony Mist");
      this.addEntity(TensuraEntityTypes.PARALYSING_BREATH, "Paralysing Breath");
      this.addEntity(TensuraEntityTypes.PREDATOR_MIST, "Predator Mist");
      this.addEntity(TensuraEntityTypes.THUNDER_BREATH, "Thunder Breath");
      this.addEntity(TensuraEntityTypes.WATER_BREATH, "Water Blow");
      this.addEntity(TensuraEntityTypes.WIND_BREATH, "Wind Blow");
      this.addEntity(TensuraEntityTypes.LIGHTNING_BOLT, "Lightning Bolt");
      this.addEntity(TensuraEntityTypes.BLACK_LIGHTNING_BOLT, "Black Lightning Bolt");
      this.addEntity(TensuraEntityTypes.BARRIER, "Barrier");
      this.addEntity(TensuraEntityTypes.BLIZZARD, "Blizzard");
      this.addEntity(TensuraEntityTypes.DARK_CUBE, "Dark Cube");
      this.addEntity(TensuraEntityTypes.DISINTEGRATION, "Disintegration");
      this.addEntity(TensuraEntityTypes.FLARE_CIRCLE, "Flare Circle");
      this.addEntity(TensuraEntityTypes.HOLY_FIELD, "Holy Field");
      this.addEntity(TensuraEntityTypes.MEGIDDO_BUBBLE, "Megiddo Bubble");
      this.addEntity(TensuraEntityTypes.MAGIC_ENGINE_BARRIER, "Magic Engine Barrier");
      this.addEntity(TensuraEntityTypes.BLOOD_MIST, "Blood Mist");
      this.addEntity(TensuraEntityTypes.DEATH_STORM_TORNADO, "Death Storm Tornado");
      this.addEntity(TensuraEntityTypes.LANDMINE, "Landmine");
      this.addEntity(TensuraEntityTypes.GRAVITY_FIELD, "Gravity Field");
      this.addEntity(TensuraEntityTypes.HELLFIRE, "Hellfire");
      this.addEntity(TensuraEntityTypes.HELL_FLARE, "Hell Flare");
      this.addEntity(TensuraEntityTypes.MAD_OGRE_ORBS, "Flame Orbs");
      this.addEntity(TensuraEntityTypes.SNIPER_BULLET, "Sniper Bullet");
      this.addEntity(TensuraEntityTypes.SNIPER_GRENADE, "Sniper Grenade");
      this.addEntity(TensuraEntityTypes.WARP_PORTAL, "Warp Portal");
      this.addEntity(TensuraEntityTypes.AURA_SLASH, "Aura Slash");
      this.addEntity(TensuraEntityTypes.ACID_BALL, "Acid Ball");
      this.addEntity(TensuraEntityTypes.AURA_BULLET, "Aura Bullet");
      this.addEntity(TensuraEntityTypes.BLACK_FLAME_BALL, "Black Flame Ball");
      this.addEntity(TensuraEntityTypes.BLACK_LIGHTNING_BLAST, "Black Lightning Blast");
      this.addEntity(TensuraEntityTypes.BLOOD_RAY, "Blood Ray");
      this.addEntity(TensuraEntityTypes.BOG_SHOT, "Bog Shot");
      this.addEntity(TensuraEntityTypes.BOULDER_SHOT, "Boulder Shot");
      this.addEntity(TensuraEntityTypes.CHAOS_EATER, "Chaos Eater");
      this.addEntity(TensuraEntityTypes.DARKNESS_CANNON, "Darkness Cannon");
      this.addEntity(TensuraEntityTypes.DIMENSION_CUT, "Dimension Cut");
      this.addEntity(TensuraEntityTypes.EARTH_SPIKE, "Earth Spike");
      this.addEntity(TensuraEntityTypes.EARTH_PILLAR, "Earth Pillar");
      this.addEntity(TensuraEntityTypes.ELECTRO_BLAST, "Electro Blast");
      this.addEntity(TensuraEntityTypes.FIRE_BALL, "Fire Ball");
      this.addEntity(TensuraEntityTypes.FIRE_BOLT, "Fire Bolt");
      this.addEntity(TensuraEntityTypes.FLAME_ORB, "Flame Orb");
      this.addEntity(TensuraEntityTypes.FROST_BALL, "Frost Ball");
      this.addEntity(TensuraEntityTypes.FUSIONIST_PROJECTILE, "Fusionist Stone");
      this.addEntity(TensuraEntityTypes.GRAVITY_SPHERE, "Gravity Sphere");
      this.addEntity(TensuraEntityTypes.HEAT_SPHERE, "Heat Sphere");
      this.addEntity(TensuraEntityTypes.HELL_FLARE_PROJECTILE, "Hell Flare");
      this.addEntity(TensuraEntityTypes.ICE_LANCE, "Ice Lance");
      this.addEntity(TensuraEntityTypes.INVISIBLE_FIRE_BOLT, "Invisible Fire Bolt");
      this.addEntity(TensuraEntityTypes.LIGHT_ARROW, "Light Arrow");
      this.addEntity(TensuraEntityTypes.LIGHTNING_LANCE, "Lightning Lance");
      this.addEntity(TensuraEntityTypes.LIGHTNING_SPHERE, "Lightning Sphere");
      this.addEntity(TensuraEntityTypes.MAGMA_SHOT, "Magma Shot");
      this.addEntity(TensuraEntityTypes.MUD_SHOT, "Mud Shot");
      this.addEntity(TensuraEntityTypes.OBSIDIAN_SHOT, "Obsidian Shot");
      this.addEntity(TensuraEntityTypes.PLASMA_BALL, "Plasma Ball");
      this.addEntity(TensuraEntityTypes.POISON_BALL, "Poison Ball");
      this.addEntity(TensuraEntityTypes.POISON_CUTTER, "Poison Cutter");
      this.addEntity(TensuraEntityTypes.SEVERANCE_CUTTER, "Severance Cutter");
      this.addEntity(TensuraEntityTypes.SOLAR_GRENADE, "Solar Grenade");
      this.addEntity(TensuraEntityTypes.SOLAR_BEAM, "Solar Beam");
      this.addEntity(TensuraEntityTypes.SPACE_CUT, "Space Cut");
      this.addEntity(TensuraEntityTypes.SPATIAL_ARROW, "Spatial Arrow");
      this.addEntity(TensuraEntityTypes.SPATIAL_RAY, "Spatial Ray");
      this.addEntity(TensuraEntityTypes.STEAM_BALL, "Steam Ball");
      this.addEntity(TensuraEntityTypes.STONE_SHOT, "Stone Shot");
      this.addEntity(TensuraEntityTypes.TEMPEST_SCALE, "Tempest Scale");
      this.addEntity(TensuraEntityTypes.REFLECTOR_ECHO, "Reflector Echo");
      this.addEntity(TensuraEntityTypes.THROWN_ITEM, "Thrown Item");
      this.addEntity(TensuraEntityTypes.WATER_BALL, "Water Ball");
      this.addEntity(TensuraEntityTypes.WIND_BLADE, "Wind Blade");
      this.addEntity(TensuraEntityTypes.WIND_SPHERE, "Wind Sphere");
      this.add("entity.minecraft.villager.tensura.gearsmith", "Gearsmith");
   }

   private void soundSubtitle() {
      this.add("sounds.tensura.caterpillar_ambient", "Caterpillar Ambient");
      this.add("sounds.tensura.bird_death", "Bird Death");
      this.add("sounds.tensura.bird_flap", "Bird Flapping");
      this.add("sounds.tensura.bird_hurt", "Bird Hurt");
      this.add("sounds.tensura.lizard_ambient", "Lizard Ambient");
      this.add("sounds.tensura.lizard_baby_ambient", "Baby Lizard Ambient");
      this.add("sounds.tensura.lizard_hurt", "Lizard Hurt");
      this.add("sounds.tensura.lizard_angry", "Lizard Angry");
      this.add("sounds.tensura.moth_ambient", "Moth Ambient");
      this.add("sounds.tensura.moth_hurt", "Moth Line Hurt");
      this.add("sounds.tensura.moth_death", "Moth Line Death");
      this.add("sounds.tensura.owl_ambient", "Owl Hooting");
      this.add("sounds.tensura.peacock_ambient", "Peacock Ambient");
      this.add("sounds.tensura.eating_sound", "Eating Sound");
      this.add("sounds.tensura.small_chew", "Small Chewing Sound");
      this.add("sounds.tensura.small_jump_impact", "Small Jump Impact");
   }

   private void chatMessages() {
      this.add("tensura.evolve.demon_lord.seed", "You became a Demon Lord Seed.");
      this.add("tensura.evolve.demon_lord.seed_lost", "You lost your Demon Lord Seed due to Naming.");
      this.add("tensura.evolve.demon_lord.not_seed", "You are not a Demon Lord Seed.");
      this.add("tensura.evolve.demon_lord.lack_soul", "You don't have enough soul points.");
      this.add("tensura.evolve.demon_lord.already", "You are already a True Demon Lord.");
      this.add("tensura.evolve.demon_lord.hero", "You are a True Hero.");
      this.add("tensura.evolve.demon_lord.success", "%s has awakened into a True Demon Lord.");
      this.add("tensura.evolve.demon_lord.subordinate_evolve", "You have evolved thanks to your master's Harvest Festival.");
      this.add("tensura.evolve.hero.egg", "You became a Hero Egg.");
      this.add("tensura.evolve.hero.egg_lost", "You lost your Hero Egg due to Naming.");
      this.add("tensura.evolve.hero.egg_lost.evolution", "You lost your Hero Egg upon evolution.");
      this.add("tensura.evolve.hero.not_egg", "You are not a Hero Egg.");
      this.add("tensura.evolve.hero.already", "You are already a True Hero.");
      this.add("tensura.evolve.hero.demon_lord", "You are a True Demon Lord.");
      this.add("tensura.evolve.hero.boss_requirement", "You don't meet the correct situation requirement.");
      this.add("tensura.evolve.hero.success", "%s has awakened into a True Hero.");
      this.add("tensura.schematic.unlocked", "Unlocked Schematic");
      this.add("tensura.message.pet.follow", "%s is set to Follow.");
      this.add("tensura.message.pet.stay", "%s is set to Stay.");
      this.add("tensura.message.pet.wander", "%s is set to Wander.");
      this.add("tensura.message.pet.self_destruct", "%s is set to Self-Destruct.");
      this.add("tensura.message.pet.neutral", "%s's behaviour is set to Neutral.");
      this.add("tensura.message.pet.passive", "%s's behaviour is set to Passive.");
      this.add("tensura.message.pet.aggressive", "%s's behaviour is set to Aggressive.");
      this.add("tensura.message.pet.protect", "%s's behaviour is set to Protect.");
      this.add("tensura.message.slime_staff.sit", "Every slime in 30-block radius is set to Stay.");
      this.add("tensura.message.slime_staff.follow", "Every slime in 30-block radius is set to Follow.");
      this.add("tensura.message.slime.chest", "Slime Chest");
      this.add("tensura.message.slime.merge", "Your slimes have been merged!");
      this.add("tensura.message.slime.massive", "Your slime has became a Supermassive Slime!");
      this.add("tensura.message.slime.maxsize", "Your slime has reached its Max Size!");
      this.add("tensura.message.slime.despawn", "Your summoned slime has despawned.");
      this.add("tensura.naming.cannot_name", "The target cannot be named.");
      this.add("tensura.naming.name_owner", "You cannot name your master.");
      this.add("tensura.naming.already_named", "The target is already named.");
      this.add("tensura.naming.had_owner", "The target is faithful with its current owner.");
      this.add("tensura.naming.not_submit", "The target does not want to submit.");
      this.add("tensura.naming.insane", "The target is not sane enough.");
      this.add("tensura.naming.lack_EP", "Not enough EP.");
      this.add("tensura.naming.name_success.no_namer", "You have been given the name %s.");
      this.add("tensura.naming.name_success", "You have been given the name %s by %s.");
      this.add("tensura.naming.nameable_status", "Your nameable status has been set to %s.");
      this.add("tensura.naming.subdue", "Subdue the target");
      this.add("tensura.naming.evolve", "Evolve the target");
      this.add("tensura.naming.endow", "Endow the target");
      this.add("tensura.naming.name", "Name");
      this.add("tensura.naming.randomize", "Randomize");
      this.add("tensura.dimension.teleportation_fail", "Couldn't teleport: %s is null");
      this.add("tensura.item.scroll_not_allowed", "%s is not allowed by the server");
   }

   private void gameRules() {
      this.add("gamerule.maxMP", "Maximum Magicule possible.");
      this.add("gamerule.maxAP", "Maximum Aura possible");
      this.add("gamerule.demonLordSeed", "Demon Lord Seed EP");
      this.add("gamerule.demonLordSeed.description", "The amount of EP needed to be a Demon Lord Seed");
      this.add("gamerule.demonLordAwaken", "True Demon Lord Soul");
      this.add("gamerule.demonLordAwaken.description", "The amount of Soul Points needed to be a True Demon Lord");
      this.add("gamerule.labyrinthPvp", "Labyrinth PVP");
      this.add("gamerule.labyrinthPvp.description", "Allows players to pvp in the Labyrinth Dimension");
      this.add("gamerule.labyrinthDeath", "Labyrinth Death");
      this.add("gamerule.labyrinthDeath.description", "Allows players to die in the Labyrinth Dimension instead of getting teleported out at 1HP");
      this.add("gamerule.colossusRespawn", "Colossus Respawn");
      this.add("gamerule.colossusRespawn.description", "Respawns the Elemental Colossus when a player who hasn't won against a colossus before reaches the arena in the Labyrinth");
      this.add("gamerule.epDeathPenalty", "EP Death Penalty");
      this.add("gamerule.epDeathPenalty.description", "The percentage of EP that the player will lose when respawning");
      this.add("gamerule.mpSkillCost", "Skill's MP cost");
      this.add("gamerule.mpSkillCost.description", "The percentage of MP that the player will lose when gaining a new skill");
      this.add("gamerule.minEP", "Minimum EP");
      this.add("gamerule.minEP.description", "Minimum EP a Player can reach while playing");
      this.add("gamerule.epGain", "EP Gain Percentage");
      this.add("gamerule.epGain.description", "How much percentage of EP that mobs can give when killed");
      this.add("gamerule.maxMpGain", "Maximum Magicule Gain");
      this.add("gamerule.maxMpGain.description", "Maximum amount of Magicule an entity can gain at once from killing a target");
      this.add("gamerule.maxApGain", "Maximum Aura Gain");
      this.add("gamerule.maxApGain.description", "Maximum amount of Aura an entity can gain at once from killing a target");
      this.add("gamerule.playerEP", "Player EP Drop Percentage");
      this.add("gamerule.playerEP.description", "How much percentage of default EP value that fallen players can be used for EP gain calculation");
      this.add("gamerule.vanillaEP", "Vanilla EP Percentage");
      this.add("gamerule.vanillaEP.description", "How much percentage of default EP value that vanilla mobs can be used for EP gain calculation");
      this.add("gamerule.spawnerEP", "Spawner EP Percentage");
      this.add("gamerule.spawnerEP.description", "How much percentage of default EP value that mobs spawned from Spawners can be used for EP gain calculation");
      this.add("gamerule.rimuruMode", "Starts as Rimuru");
      this.add("gamerule.noUniqueStart", "No Unique Start");
      this.add("gamerule.noUniqueStart.description", "Starts with a buff in MP/AP but no Unique Skills");
      this.add("gamerule.trulyUnique", "Truly Unique");
      this.add("gamerule.trulyUnique.description", "Removes owned Unique Skills from reincarnation skill list");
      this.add("gamerule.resetIncompletePenalty", "Penalty for an incomplete reset");
      this.add("gamerule.resetIncompletePenalty.description", "Number of points gets removed from the Reset Counter when a player uses any reset scroll while not meeting the requirement");
      this.add("gamerule.resetCounterBonusUnique", "Bonus Unique with Reset Counter");
      this.add("gamerule.resetCounterBonusUnique.description", "Gains more Unique skills on resetting based on how many Reset Counters per skill");
      this.add("gamerule.skillBeforeRace", "Skill Before Race");
      this.add("gamerule.skillBeforeRace.description", "Gains Unique Skills before choosing race");
      this.add("gamerule.hardcoreRace", "Hardcore Race");
      this.add("gamerule.hardcoreRace.description", "Makes some Races harder to play as");
      this.add("gamerule.skillGriefing", "Skill/Magic/Battlewill Griefing");
      this.add("gamerule.skillSteal", "Skill Steal");
      this.add("gamerule.skillSteal.description", "Enables certain skills to steal Skills from Players instead of copying");
      this.add("gamerule.epSteal", "EP Steal");
      this.add("gamerule.epSteal.description", "Enables certain skills to steal EP from Players");
      this.add("gamerule.playerMindControl", "Player Mind Control");
      this.add("gamerule.playerMindControl.description", "Enables certain skills to mind control Players");
      this.add("gamerule.playerNaming", "Player Naming");
      this.add("gamerule.playerNaming.description", "Allows players to be named by others");
      this.add("gamerule.tensuraDisplayName", "Tensura Display Name");
      this.add("gamerule.tensuraDisplayName.description", "Display the tensura name if the player is named");
      this.add("gamerule.experimentalFeature", "Experimental Features");
      this.add("gamerule.experimentalFeature.description", "Allow experimental features from Tensura:Reincarnated to be used");
   }

   private void commandMessages() {
      this.add("tensura.argument.race.invalid", "Input Race was invalid.");
      this.add("tensura.argument.skill.invalid", "Input Instance was invalid.");
      this.add("tensura.command.despawn", "%s is despawned.");
      this.add("tensura.command.despawn.all", "%s entities are despawned.");
      this.add("tensura.command.despawn.fail", "Input Entity cannot be despawned.");
      this.add("tensura.command.reset.all", "%s is fully reset.");
      this.add("tensura.command.reset.awakening", "%s's Awakening Status is reset.");
      this.add("tensura.command.reset.race", "%s's Race is reset.");
      this.add("tensura.command.reset.skill", "%s's Skills are rerolled.");
      this.add("tensura.command.reset.schematic", "%s's Schematics and Advancements are reset.");
      this.add("tensura.command.reset.stat", "%s's Statistics is reset.");
      this.add("tensura.command.race.get", "%s's Race is %s.");
      this.add("tensura.command.race.no_race", "%s does not have a race.");
      this.add("tensura.command.race.edit", "%s has been reborn as %s.");
      this.add("tensura.command.race.majin", "%s's Majin status has been set to %s.");
      this.add("tensura.command.human_kill", "%s's Human Kill point has been set to %s.");
      this.add("tensura.command.human_kill.get", "%s's Human Kill point is currently %s.");
      this.add("tensura.command.demon_lord.soul", "%s's Soul Point has been set to %s.");
      this.add("tensura.command.demon_lord.soul.get", "%s's Soul Point is currently %s.");
      this.add("tensura.command.demon_lord.seed", "%s's Demon Lord Seed status has been set to %s.");
      this.add("tensura.command.demon_lord.seed.get", "%s's Demon Lord Seed status is currently %s.");
      this.add("tensura.command.demon_lord.awakened", "%s's True Demon Lord status has been set to %s.");
      this.add("tensura.command.demon_lord.awakened.get", "%s's True Demon Lord status is currently %s.");
      this.add("tensura.command.hero.egg", "%s's Hero Egg status has been set to %s.");
      this.add("tensura.command.hero.egg.get", "%s's Hero Egg status is currently %s.");
      this.add("tensura.command.hero.awakened", "%s's True Hero status has been set to %s.");
      this.add("tensura.command.hero.awakened.get", "%s's True Hero status is currently %s.");
      this.add("tensura.command.aura.get", "%s's Aura is %s.");
      this.add("tensura.command.aura.get_max", "%s's Max Aura is %s.");
      this.add("tensura.command.aura.set", "%s's Aura has been set to %s.");
      this.add("tensura.command.aura.set_max", "%s's Max Aura has been set to %s.");
      this.add("tensura.command.magicule.get", "%s's Magicule is %s.");
      this.add("tensura.command.magicule.get_max", "%s's Max Magicule is %s.");
      this.add("tensura.command.magicule.set", "%s's Magicule has been set to %s.");
      this.add("tensura.command.magicule.set_max", "%s's Max Magicule has been set to %s.");
      this.add("tensura.command.ep.get", "%s's EP is %s.");
      this.add("tensura.command.ep.set", "%s's EP has been set to %s.");
      this.add("tensura.command.spiritual.get", "%s's Spiritual Health is %s.");
      this.add("tensura.command.spiritual.set", "%s's Spiritual Health has been set to %s.");
      this.add("tensura.command.severance.set", "%s's Severance Health amount has been set to %s.");
      this.add("tensura.command.reset_counter.get", "%s's Reset Counter is %s.");
      this.add("tensura.command.reset_counter.set", "%s's Reset Counter has been set to %s.");
      this.add("tensura.command.reset_counter.check", "%s's Reset Counter requirements:\n- Final Evolution: %s\n- Awakened: %s\n- Bosses defeated:\n+ Ifrit: %s\n+ Orc Disaster: %s\n+ Elemental Colossus: %s\n+ Charybdis: %s\n+ Hinata Sakaguchi: %s");
      this.add("tensura.command.reset_counter.check.met", "%s's Reset Counter requirements are all met.");
      this.add("tensura.command.owner.name.set", "%s's Name is set to %s.");
      this.add("tensura.command.owner.name.remove", "%s's Name is removed.");
      this.add("tensura.command.owner.permanent.set", "%s's Permanent Owner is set to %s.");
      this.add("tensura.command.owner.permanent.remove", "%s's Permanent Owner is removed.");
      this.add("tensura.command.owner.temporary.set", "%s's Temporary Owner is set to %s.");
      this.add("tensura.command.owner.temporary.remove", "%s's Temporary Owner is removed.");
      this.add("tensura.command.owner.neutral.add", "%s is now neutral to %s.");
      this.add("tensura.command.owner.neutral.remove", "%s is not longer neutral to %s if true.");
      this.add("tensura.skill.already_has", "%s already has %s.");
      this.add("tensura.skill.no_skill", "%s does not have %s.");
      this.add("tensura.skill.invalid", "%s was invalid.");
      this.add("tensura.skill.check_flight", "%s is Flying legitimately.");
      this.add("tensura.skill.check_flight.false", "%s is not Flying legitimately.");
      this.add("tensura.skill.granted", "%s has been granted to %s.");
      this.add("tensura.skill.granted_all", "%s instances have been granted to %s.");
      this.add("tensura.skill.revoked", "%s has been revoked from %s.");
      this.add("tensura.skill.revoked_all", "%s Instances have been revoked from %s.");
      this.add("tensura.skill.mastery_point", "%s's Mastery points of %s has been set to %s.");
      this.add("tensura.skill.mastery_point.all", "%s's Mastery points of %s abilities have been set to %s.");
      this.add("tensura.skill.mastery.no_changes", "%s's Mastery points have not been affected.");
      this.add("tensura.skill.mastery.above_max", "[%s]'s maximum Mastery points are %s.");
      this.add("tensura.skill.set_cooldown", "%s's Cooldown of %s has been set to %s seconds.");
      this.add("tensura.skill.set_cooldown.all", "%s's Cooldown of all instances has been set to %s seconds.");
      this.add("tensura.skill.set_cooldown.no_changes", "%s's Cooldown have not been affected.");
      this.add("tensura.skill.set_cooldown.all.no_changes", "None of %s's abilities are affected.");
      this.add("tensura.skill.mode_learning.no_mode", "%s doesn't have Mode %s.");
      this.add("tensura.skill.mode_learning.no_learn", "Mode %s of %s is available without learning.");
      this.add("tensura.skill.mode_learning.no_learn_all", "All Modes of %s is available without learning.");
      this.add("tensura.skill.mode_learning.success", "Learn points in mode %s of %s has been set to %s for %s.");
      this.add("tensura.skill.mode_learning.success_all", "Learn points in %s modes of %s has been set to %s for %s.");
      this.add("tensura.skill.mode_learning.success_everything", "Learn points in every mode of all instance has been set to Max for %s.");
      this.add("tensura.skill.empty_storage", "%s has no abilities.");
      this.add("tensura.skill.skill_set", "%s has been set on slot %s.");
      this.add("tensura.skill.skill_set.failed", "%s cannot be set in slots.");
      this.add("tensura.skill.do_not_have", "%s does not have %s.");
      this.add("tensura.skill.skill_get", "%s's %s has the following data: %s.");
      this.add("tensura.skill.skill_list", "%s's list of searched abilities: %s.");
      this.add("tensura.skill.skill_list.empty", "%s has no abilities met the requirement.");
      this.add("tensura.skill.toggle_on", "%s has been toggled on for %s.");
      this.add("tensura.skill.toggle_all.on", "%s Skills have been toggled on for %s.");
      this.add("tensura.skill.toggle_off", "%s has been toggled off for %s.");
      this.add("tensura.skill.toggle_all.off", "%s Skills have been toggled off for %s.");
      this.add("tensura.skill.toggle.failed", "%s cannot be toggled.");
      this.add("tensura.skill.spatial_storage.failed", "%s is not Spatial Storage ability.");
      this.add("tensura.skill.spatial_storage.full", "%s's Spatial Storage is full.");
      this.add("tensura.command.spirit.blessed", "%s's Spirit Blessed status has been set to %s.");
      this.add("tensura.command.spirit.blessed.get", "%s's Spirit Blessed status is currently %s.");
      this.add("tensura.command.spirit.get", "%s's Spirit Level of %s is %s.");
      this.add("tensura.command.spirit.set", "%s's Spirit Level of %s has been set to %s.");
      this.add("tensura.command.spirit.clear", "Removed all spirits from %s.");
      this.add("tensura.command.spirit.cooldown.set", "%s's Spirit Praying cooldown has been set to %s.");
      this.add("tensura.command.spirit.cooldown.get", "%s's Spirit Praying cooldown is %s.");
      this.add("tensura.command.spirit.list", "%s's Levels in each Elemental Spirit are %s.");
      this.add("tensura.command.spirit.list.empty", "%s has no Spirits contracted.");
      this.add("tensura.command.force_evo.demon_lord", "%s was forced to go through True Demon Lord awakening.");
      this.add("tensura.command.force_evo.hero", "%s was forced to go through True Hero awakening.");
      this.add("tensura.command.force_evo.race", "%s was forced to evolve their race.");
      this.add("tensura.command.force_evo.race.fail", "%s failed to evolve.");
      this.add("tensura.command.force_evo.race.fail_specific", "%s failed to evolve to %s.");
      this.add("tensura.command.clear_skill_effect.success.single", "Removed every Ability status effect from %s");
      this.add("tensura.command.clear_skill_effect.success.multiple", "Removed every Ability status effect from %s targets");
      this.add("tensura.telepathy.subordinate.not_found", "You need to look at a subordinate or a pet.");
      this.add("tensura.telepathy.subordinate_all.no_target", "You need to look at a target to attack.");
      this.add("tensura.telepathy.subordinate_all.not_found", "Can not find any subordinate reachable from your place.");
      this.add("tensura.telepathy.subordinate_all.success", "All reached subordinates have listened to your command.");
      this.add("tensura.telepathy.subordinate_all.stay", "All reached subordinates have been ordered to Stay.");
      this.add("tensura.telepathy.subordinate_all.follow", "All reached subordinates have been ordered to Follow.");
      this.add("tensura.telepathy.subordinate_all.wander", "All reached subordinates have been ordered to Wander.");
      this.add("tensura.telepathy.subordinate_all.rampage", "All reached subordinates have been ordered to Rampage.");
      this.add("tensura.telepathy.subordinate_all.meat_shield", "All reached subordinates have been ordered to form a Meat Shield.");
      this.add("tensura.telepathy.subordinate_all.neutral", "All reached subordinates' behaviour have been set to Neutral.");
      this.add("tensura.telepathy.subordinate_all.passive", "All reached subordinates' behaviour have been set to to Passive.");
      this.add("tensura.telepathy.subordinate_all.aggressive", "All reached subordinates' behaviour have been set to to Aggressive.");
      this.add("tensura.telepathy.subordinate_all.protect", "All reached subordinates' behaviour have been set to to Protect.");
      this.add("tensura.telepathy.no_telepathy", "You don't have required abilities for this command.");
      this.add("tensura.truly_unique.off", "The gamerule Truly Unique is off.");
      this.add("tensura.truly_unique.added", "%s is added to the list with %s as owner.");
      this.add("tensura.truly_unique.already_have", "%s is in the list, owned by %s.");
      this.add("tensura.truly_unique.do_not_have", "%s isn't in the list.");
      this.add("tensura.truly_unique.removed", "%s is removed from the list.");
      this.add("tensura.truly_unique.list.taken", "List of Unique Skill acquired: %s.");
      this.add("tensura.truly_unique.list.taken.empty", "No Unique Skill has been acquired.");
      this.add("tensura.truly_unique.list.remain", "List of Unique Skill remained: %s.");
      this.add("tensura.truly_unique.list.remain.empty", "No Unique Skill is remained.");
      this.add("tensura.command.magicule.area_get", "The current Magicule Level of this chunk is %s.");
      this.add("tensura.command.magicule.area_get.max", "The maximum Magicule Level of this chunk is %s.");
      this.add("tensura.command.magicule.reinitialized", "Reinitialized magicule chunk data of %s chunks.");
      this.add("tensura.command.magicule.reinitialized.all", "Reinitialized magicule chunk data of all chunks.");
      this.add("tensura.command.labyrinth.missing", "The Labyrinth is not found.");
      this.add("tensura.command.labyrinth.entrance.get", "The Labyrinth's entrance point is currently %s, %s, %s");
      this.add("tensura.command.labyrinth.entrance.set", "The Labyrinth's entrance point is set to %s, %s, %s");
      this.add("tensura.command.labyrinth.passed_entrance.get", "The Labyrinth's entrance point when passed is currently %s, %s, %s");
      this.add("tensura.command.labyrinth.passed_entrance.set", "The Labyrinth's entrance point when passed is set to %s, %s, %s");
      this.add("tensura.command.labyrinth.colossus_pos.get", "The Labyrinth's Colossus spawn point is currently %s, %s, %s");
      this.add("tensura.command.labyrinth.colossus_pos.set", "The Labyrinth's Colossus spawn point is set to %s, %s, %s");
      this.add("tensura.command.labyrinth.fall_off.get", "The Labyrinth's fall-off point is currently %s, %s, %s");
      this.add("tensura.command.labyrinth.fall_off.set", "The Labyrinth's fall-off point is set to %s, %s, %s");
      this.add("tensura.command.labyrinth.fall_off_y.get", "The Labyrinth's fall-off start height is currently %s");
      this.add("tensura.command.labyrinth.fall_off_y.set", "The Labyrinth's fall-off start height is set to %s");
      this.add("tensura.command.labyrinth.passed_list.check_true", "%s is marked as passed the The Labyrinth's Elemental Colossus");
      this.add("tensura.command.labyrinth.passed_list.check_false", "%s is not marked as passed the The Labyrinth's Elemental Colossus");
      this.add("tensura.command.labyrinth.passed_list.add", "%s is now marked as passed the The Labyrinth's Elemental Colossus");
      this.add("tensura.command.labyrinth.passed_list.remove", "%s is no longer marked as passed the The Labyrinth's Elemental Colossus");
      this.add("tensura.command.labyrinth.passed_list.won", "%s's mark as won the The Labyrinth's Elemental Colossus is now set to %s");
      this.add("tensura.command.labyrinth.regenerate", "The Labyrinth will regenerate during the next Server/World launch.");
      this.add("tensura.command.labyrinth.regenerate.false", "The Labyrinth will not regenerate during the next Server/World launch.");
   }

   private void magic() {
      this.add("tensura.magic.cast_time.max", "Casting time: %s/%s");
      this.add("tensura.magic.cast_time", "Casting time: %s");
      this.add("tensura.magic.spiritual.chosen.cooldown", "The Spirits have already listened to your prayers today.");
      this.add("tensura.magic.spiritual.chosen.failed", "The Spirits remain silent. Seek them again at the start of a new day.");
      this.add("tensura.magic.spiritual.chosen", "A %s has heard your prayers.");
      this.add("tensura.magic.spiritual.spirit_name", "%s Spirit of %s");
      this.add("tensura.magic.spiritual.spirit_name.lord", "Spirit %s of %s");
      this.add("tensura.magic.elemental.darkness", "Darkness");
      this.add("tensura.magic.elemental.earth", "Earth");
      this.add("tensura.magic.elemental.fire", "Fire");
      this.add("tensura.magic.elemental.light", "Light");
      this.add("tensura.magic.elemental.space", "Space");
      this.add("tensura.magic.elemental.water", "Water");
      this.add("tensura.magic.elemental.wind", "Wind");
      this.add("tensura.magic.spiritual.level.lesser", "Lesser");
      this.add("tensura.magic.spiritual.level.medium", "Medium");
      this.add("tensura.magic.spiritual.level.greater", "Greater");
      this.add("tensura.magic.spiritual.level.lord", "Lord");
      this.add("tensura.magic.type.spiritual", "Spiritual");
      this.add("tensura.magic.type.summoning", "Summoning");
      this.addSkill(SpiritualMagics.DARKNESS, "Darkness", "Call on your spirit to reduce the enemy's vision in a wide area.");
      this.addSkill(SpiritualMagics.SHADOW_BIND, "Shadow Bind", "If the target is standing in shadows, bind them and deal spiritual damage.");
      this.addSkill(SpiritualMagics.DARK_CUBE, "Dark Cube", "Create a cube of darkness that slows movement and deals constant damage.");
      this.addSkill(SpiritualMagics.DARKNESS_CANNON, "Darkness Cannon", "Shoot a long beam which deals massive damage, destroys armor and debilitates enemies.");
      this.addSkill(SpiritualMagics.TRUE_DARKNESS, "True Darkness", "Inflict Blindness on all entities and deal massive spiritual damage.");
      this.addSkill(SpiritualMagics.EARTH, "Earth", "Place down blocks which mimic those from the surrounding environment.");
      this.addSkill(SpiritualMagics.EARTH_SPIKES, "Earth Spikes", "Raises a spike under your opponent launching them into the air.");
      this.addSkill(SpiritualMagics.EARTH_STORM, "Earth Storm", "Summons a viscous sandstorm and falling rocks around you.");
      this.addSkill(SpiritualMagics.MAGMA_SURGE, "Magma Surge", "Fire a spread of lava that burns and melts anything for a limited time.");
      this.addSkill(SpiritualMagics.EARTH_JAIL, "Earth Jail", "Restrict and weaken a single target, applying debuffs to allow you to finish them off.");
      this.addSkill(SpiritualMagics.FIRE, "Fire", "Use your spirit to start a small fire.");
      this.addSkill(SpiritualMagics.FIRE_BREATH, "Fire Breath", "Breathe flames and incinerate your enemies.");
      this.addSkill(SpiritualMagics.FIRE_BOLT, "Fire Bolt", "Shoots a flaming bolt.");
      this.addSkill(SpiritualMagics.FLARE_CIRCLE, "Flare Circle", "Opens a Gate to Hell from which wicked flames erupt to turn your enemies to ash.");
      this.addSkill(SpiritualMagics.HELLFIRE, "Hellfire", "Summon a sphere of hellfire to deal massive damage in a small area.");
      this.addSkill(SpiritualMagics.LIGHT, "Light", "Summons a temporary light to block out the darkness.");
      this.addSkill(SpiritualMagics.SOLAR_BEAM, "Solar Beam", "Fire a light beam that deals massive damage to undead entities and burns blocks.");
      this.addSkill(SpiritualMagics.SOLAR_WAVE, "Solar Wave", "Shoot a wave of light energy that blinds and slows hit enemies.");
      this.addSkill(SpiritualMagics.SOLAR_RAIN, "Solar Rain", "Shoot many light projectiles that deal massive damage to undead creatures.");
      this.addSkill(SpiritualMagics.SOLAR_FLARE, "Solar Flare", "Shoot a shockwave of light energy to give nausea, blindness and slowness to all targets in a wide AOE.");
      this.addSkill(SpiritualMagics.SPACE, "Space", "Form invisible platforms to create footholds midair.");
      this.addSkill(SpiritualMagics.GATE, "Gate", "Tear space asunder connecting two points in space.");
      this.addSkill(SpiritualMagics.SHRINK, "Shrink", "Reduce your size to enhance your evasion but increase your vulnerability");
      this.addSkill(SpiritualMagics.TELEPORT, "Teleport", "Quickly blink forward in space to a location within view.");
      this.addSkill(SpiritualMagics.SWIPE, "Swipe", "Slashing through space in a straight line, displacing entities or teleporting the caster.");
      this.addSkill(SpiritualMagics.WATER, "Water", "Call forth or manipulate small amounts of water.");
      this.addSkill(SpiritualMagics.WATER_CUTTER, "Water Cutter", "Fires a concentrated blade to cut your enemies with the power of water.");
      this.addSkill(SpiritualMagics.ACID_RAIN, "Acid Rain", "Summon an acidic cloud which will corrode any afflicted entities.");
      this.addSkill(SpiritualMagics.MEGIDDO, "Megiddo", "Unleash powerful sun blasts using water to kill any nearby foes. It can also be used manually to concentrate fire for any stronger foes.");
      this.addSkill(SpiritualMagics.BLIZZARD, "Blizzard", "Creates a powerful storm of ice to slow your enemies and turn the tides of battle.");
      this.addSkill(SpiritualMagics.WIND, "Wind", "Calls forth a small gust of wind which can push targets away or the player up.");
      this.addSkill(SpiritualMagics.LIGHTNING_LANCE, "Lightning Lance", "Launch a lighting projectile which will electrify your enemies.");
      this.addSkill(SpiritualMagics.WIND_BLADE, "Wind Blade", "Fires a concentrated blade of wind which has heavy knockback.");
      this.addSkill(SpiritualMagics.ELECTRO_BLAST, "Electro Blast", "Fire a beam attack that pierces through enemies and deals devastating damage while paralyzing your enemies.");
      this.addSkill(SpiritualMagics.AERIAL_BLADE, "Aerial Blade", "Pull in any nearby entities before dealing massive damage by unleashing the power of your spirit.");
      this.addSkill(SpiritualMagics.SUMMON_HOUND_DOG, "Summon Hound Dog", "Show everyone you have that dawg in you.");
      this.addSkill(SpiritualMagics.SUMMON_MEDIUM_ELEMENTAL, "Summon Medium Elemental", "Summon your contracted elemental spirit to have it temporarily fight for you.");
      this.addSkill(SpiritualMagics.SUMMON_GREATER_ELEMENTAL, "Summon Greater Elemental", "Summon your contracted greater elemental spirit to have it purge the world of heretics for you.");
   }

   private void skill() {
      this.add("tensura.ep.acquire", "%s EP has been acquired.");
      this.add("tensura.ep.acquire_fallen", "%s EP has been acquired from fallen %s.");
      this.add("tensura.ep.acquire_mp", "%s Magicule has been acquired.");
      this.add("tensura.ep.acquire_max_mp", "%s Max Magicule has been acquired.");
      this.add("tensura.ep.acquire_ap", "%s Max Aura has been acquired.");
      this.add("tensura.ep.acquire_max_ap", "%s Max Aura has been acquired.");
      this.add("tensura.ability.activation_failed", "Ability activation failed.");
      this.add("tensura.targeting.not_targeted", "You need to target a Living Entity.");
      this.add("tensura.targeting.not_allowed", "This ability doesn't work on the target.");
      this.add("tensura.targeting.ep_not_meet", "The target's EP doesn't meet the requirement for this ability.");
      this.add("tensura.skill.acquire", "%s has been acquired.");
      this.add("tensura.skill.acquire_fallen", "%s has been acquired from fallen %s.");
      this.add("tensura.skill.acquire_learning", "%s has been acquired through learning.");
      this.add("tensura.skill.acquire_failed.mp", "Failed to acquire %s due to the lack of Magicule.");
      this.add("tensura.skill.learn_available", "%s is now available to learn.");
      this.add("tensura.skill.learn_points_added", "You have become closer to acquiring %s.");
      this.add("tensura.skill.cooldown", "%s is on Cooldown.");
      this.add("tensura.skill.mastery", "You have mastered the usage of %s.");
      this.add("tensura.skill.mastery.all", "%s has mastered the usage of %s abilities.");
      this.add("tensura.skill.mastery.point_added", "You have become closer to mastering %s.");
      this.add("tensura.skill.output_number", "Output Number: %s");
      this.add("tensura.skill.power_scale", "Power Scale: %s");
      this.add("tensura.skill.time_held.max", "Activation time: %s/%s");
      this.add("tensura.skill.time_held", "Activation time: %s");
      this.add("tensura.skill.range", "Range: %s");
      this.add("tensura.skill.lack_aura", "Out of Aura.");
      this.add("tensura.skill.lack_aura.toggled_off", "%s has been toggled off due to the lack of Aura.");
      this.add("tensura.skill.lack_magicule", "Out of Magicule.");
      this.add("tensura.skill.lack_magicule.toggled_off", "%s has been toggled off due to the lack of Magicule.");
      this.add("tensura.skill.lack_requirement.toggled_off", "%s has been toggled off due to unmet requirements.");
      this.add("tensura.skill.magic_interference", "You have been affected by Magic Interference.");
      this.add("tensura.skill.spatial_blockade", "Your spatial moves are blocked.");
      this.add("tensura.skill.teleport.out_border", "You cannot go outside the world border.");
      this.add("tensura.skill.temporary.already_have", "You've already had %s.");
      this.add("tensura.skill.temporary.success_drain", "You've temporary gained %s.");
      this.add("tensura.skill.temporary.remove", "Temporary %s has been removed.");
      this.add("tensura.skill.type.resistance", "Resistance");
      this.add("tensura.skill.type.intrinsic", "Intrinsic");
      this.add("tensura.skill.type.common", "Common");
      this.add("tensura.skill.type.extra", "Extra");
      this.add("tensura.skill.type.unique", "Unique");
      this.add("tensura.skill.type.ultimate", "Ultimate");
      this.addSkill(ResistanceSkills.ABNORMAL_CONDITION_RESISTANCE, "Abnormal Condition Resistance", "Resist the effects of weaker abnormal conditions or reduce the severity of stronger ones.");
      this.addSkill(ResistanceSkills.COLD_RESISTANCE, "Cold Resistance", "Ignore weak cold damage or resist the damage of powerful attacks.");
      this.addSkill(ResistanceSkills.CORROSION_RESISTANCE, "Corrosion Resistance", "Ignore weak corrosive effects or reduce the damage from stronger corrosive attacks.");
      this.addSkill(ResistanceSkills.DARKNESS_ATTACK_RESISTANCE, "Darkness Attack Resistance", "Ignore weaker darkness damage or reduce the damage of stronger attacks.");
      this.addSkill(ResistanceSkills.EARTH_ATTACK_RESISTANCE, "Earth Attack Resistance", "Ignore weak earth-based damage or resist the damage from powerful attacks.");
      this.addSkill(ResistanceSkills.ELECTRICITY_RESISTANCE, "Electricity Resistance", "Ignore weak electrical damage or resist the damage from stronger attacks.");
      this.addSkill(ResistanceSkills.FLAME_ATTACK_RESISTANCE, "Flame Attack Resistance", "Ignore weaker flame damage or resist the damage from more powerful flames.");
      this.addSkill(ResistanceSkills.GRAVITY_ATTACK_RESISTANCE, "Gravity Attack Resistance", "Ignore weaker gravity-based damage or reduce the damage from stronger attacks.");
      this.addSkill(ResistanceSkills.HEAT_RESISTANCE, "Heat Resistance", "Ignore weaker heat-based damage or reduce the damage from stronger heat sources.");
      this.addSkill(ResistanceSkills.HOLY_ATTACK_RESISTANCE, "Holy Attack Resistance", "Ignore weaker holy attacks or reduce the damage from stronger divine powers.");
      this.addSkill(ResistanceSkills.LIGHT_ATTACK_RESISTANCE, "Light Attack Resistance", "Ignore weaker light damage or reduce the damage from more powerful attacks.");
      this.addSkill(ResistanceSkills.MAGIC_RESISTANCE, "Magic Resistance", "Ignore weaker magical attacks or reduce the damage from stronger spells.");
      this.addSkill(ResistanceSkills.PARALYSIS_RESISTANCE, "Paralysis Resistance", "Ignore weaker paralysis effects or reduce the duration of stronger paralysis.");
      this.addSkill(ResistanceSkills.PAIN_RESISTANCE, "Pain Resistance", "Resist pain effects.");
      this.addSkill(ResistanceSkills.PIERCE_RESISTANCE, "Pierce Resistance", "Ignore weaker piercing damage or reduce the damage from stronger attacks.");
      this.addSkill(ResistanceSkills.PHYSICAL_ATTACK_RESISTANCE, "Physical Attack Resistance", "Ignore weaker physical attacks or reduce the damage from stronger blows.");
      this.addSkill(ResistanceSkills.POISON_RESISTANCE, "Poison Resistance", "Ignore weaker poisons or reduce the damage from more potent toxins.");
      this.addSkill(ResistanceSkills.SPATIAL_ATTACK_RESISTANCE, "Spatial Attack Resistance", "Ignore weaker spatial attacks or reduce the damage from stronger dimensional strikes.");
      this.addSkill(ResistanceSkills.SPIRITUAL_ATTACK_RESISTANCE, "Spiritual Attack Resistance", "Ignore weaker spiritual attacks or resist stronger ones.");
      this.addSkill(ResistanceSkills.THERMAL_FLUCTUATION_RESISTANCE, "Thermal Fluctuation Resistance", "Ignore weaker thermal damage regardless of its type or resist stronger attacks.");
      this.addSkill(ResistanceSkills.WATER_ATTACK_RESISTANCE, "Water Attack Resistance", "Ignore weaker water-based damage or reduce the damage from stronger attacks.");
      this.addSkill(ResistanceSkills.WIND_ATTACK_RESISTANCE, "Wind Attack Resistance", "Ignore weaker wind damage or reduce the damage from stronger attacks.");
      this.addSkill(ResistanceSkills.ABNORMAL_CONDITION_NULLIFICATION, "Abnormal Condition Nullification", "Completely negate all abnormal conditions and their effects.");
      this.addSkill(ResistanceSkills.COLD_NULLIFICATION, "Cold Nullification", "Completely ignore cold damage.");
      this.addSkill(ResistanceSkills.CORROSION_NULLIFICATION, "Corrosion Nullification", "Completely negate all corrosion damage.");
      this.addSkill(ResistanceSkills.DARKNESS_ATTACK_NULLIFICATION, "Darkness Attack Nullification", "Completely nullify all darkness damage.");
      this.addSkill(ResistanceSkills.EARTH_ATTACK_NULLIFICATION, "Earth Attack Nullification", "Completely nullify all earth damage.");
      this.addSkill(ResistanceSkills.ELECTRICITY_NULLIFICATION, "Electricity Nullification", "Completely nullify all electricity damage.");
      this.addSkill(ResistanceSkills.FLAME_ATTACK_NULLIFICATION, "Flame Attack Nullification", "Completely nullify all flame damage.");
      this.addSkill(ResistanceSkills.GRAVITY_ATTACK_NULLIFICATION, "Gravity Attack Nullification", "Completely nullify all gravity damage.");
      this.addSkill(ResistanceSkills.HEAT_NULLIFICATION, "Heat Nullification", "Completely negate all heat damage.");
      this.addSkill(ResistanceSkills.HOLY_ATTACK_NULLIFICATION, "Holy Attack Nullification", "Completely nullify all holy damage.");
      this.addSkill(ResistanceSkills.LIGHT_ATTACK_NULLIFICATION, "Light Attack Nullification", "Completely nullify all light damage.");
      this.addSkill(ResistanceSkills.MAGIC_NULLIFICATION, "Magic Nullification", "Completely negate all magic damage.");
      this.addSkill(ResistanceSkills.PARALYSIS_NULLIFICATION, "Paralysis Nullification", "Completely negate all paralysis effects.");
      this.addSkill(ResistanceSkills.PIERCE_NULLIFICATION, "Pierce Nullification", "Completely negate all piercing damage.");
      this.addSkill(ResistanceSkills.PAIN_NULLIFICATION, "Pain Nullification", "Completely negate all pain effects.");
      this.addSkill(ResistanceSkills.PHYSICAL_ATTACK_NULLIFICATION, "Physical Attack Nullification", "Completely negate all physical damage.");
      this.addSkill(ResistanceSkills.POISON_NULLIFICATION, "Poison Nullification", "Completely nullify all poison damage.");
      this.addSkill(ResistanceSkills.SPATIAL_ATTACK_NULLIFICATION, "Spatial Attack Nullification", "Completely nullify all spatial damage.");
      this.addSkill(ResistanceSkills.SPIRITUAL_ATTACK_NULLIFICATION, "Spiritual Attack Nullification", "Completely negate all spiritual damage.");
      this.addSkill(ResistanceSkills.THERMAL_FLUCTUATION_NULLIFICATION, "Thermal Fluctuation Nullification", "Completely negate all thermal damage.");
      this.addSkill(ResistanceSkills.WATER_ATTACK_NULLIFICATION, "Water Attack Nullification", "Completely nullify all water damage.");
      this.addSkill(ResistanceSkills.WIND_ATTACK_NULLIFICATION, "Wind Attack Nullification", "Completely nullify all wind damage.");
      this.addSkill(IntrinsicSkills.ABSORB_DISSOLVE, "Absorb & Dissolve", "Dissolve specific items to instantly consume them.");
      this.addSkill(IntrinsicSkills.BEAST_TRANSFORMATION, "Beast Transformation", "Transform into a beast to restore your vitality and boost your physical body. The transformation will grant great physical prowess but leave a toll on your body.");
      this.addSkill(IntrinsicSkills.BODY_ARMOR, "Body Armor", "Protect yourself from harm by summoning armoursaurus scales around your body.");
      this.addSkill(IntrinsicSkills.CHARM, "Charm", "Use your inherent power to turn your opponents neutral or to temporarily dominate weak opponents.");
      this.addSkill(IntrinsicSkills.DARKNESS_TRANSFORM, "Darkness Transform", "Channel your inner darkness to burn and blind nearby opponents. ");
      this.addSkill(IntrinsicSkills.DIVINE_KI_RELEASE, "Divine Ki Release", "Use your Divine Ki to amplify your battlewill to deal more damage and destroy weaker equipment.");
      this.addSkill(IntrinsicSkills.BLOOD_MIST, "Blood Mist", "Spill your own blood to summon a mist which steals the vitality of your enemies and can be blown up to harm any nearby entities or shoot a powerful blood beam.");
      this.addSkill(IntrinsicSkills.DRAGON_EAR, "Dragon Ear", "Use your sensitive hearing to precisely pin the location of any nearby mobs which make sound.");
      this.addSkill(IntrinsicSkills.DRAGON_EYE, "Dragon Eye", "Use your powerful sight to access the power of any entities and see in the far distance.");
      this.addSkill(IntrinsicSkills.DRAGON_MODE, "Dragon Mode", "Once a day, draw out your monstrous potential to double your EP and to boost your physical prowess. Doing this will leave a temporary toll on your body.");
      this.addSkill(IntrinsicSkills.DRAGON_SKIN, "Dragon Skin", "Transform your skin into scales which become tougher as you gain more EP.");
      this.addSkill(IntrinsicSkills.DRAIN, "Drain", "Steal the vitality of your opponent and temporarily transform this skill into one of theirs to potentially turn the tables.");
      this.addSkill(IntrinsicSkills.EARTH_TRANSFORM, "Earth Transform", "Channel the powers of the earth to deal increased damage and increase local gravity around you.");
      this.addSkill(IntrinsicSkills.EYE_OF_TRUTH, "Eye of Truth", "By obtaining the mythical Hero Egg, the veil of deception is torn apart. No illusion or concealment can deceive your sight, and your vision becomes perfect.");
      this.addSkill(IntrinsicSkills.FLAME_BREATH, "Flame Breath", "Spew fire to burn away enemies and set fire to the land.");
      this.addSkill(IntrinsicSkills.FLAME_TRANSFORM, "Flame Transform", "Channel the powers of fire to burn all nearby foes.");
      this.addSkill(IntrinsicSkills.LIGHT_TRANSFORM, "Light Transform", "Channel your inner light to deal holy damage and nauseate all nearby entities. ");
      this.addSkill(IntrinsicSkills.OGRE_BERSERKER, "Ogre Berserker", "Allow rage to consume you to massively improve your physical powers for a time before the aftereffects set in.");
      this.addSkill(IntrinsicSkills.PARALYSING_BREATH, "Paralysing Breath", "Spew a horrifying breath to paralyze your prey.");
      this.addSkill(IntrinsicSkills.POISONOUS_BREATH, "Poisonous Breath", "Use your disgusting breath to corrode and nauseate your targets. ");
      this.addSkill(IntrinsicSkills.POSSESSION, "Possession", "Possess a weakened material body to gain access to a whole new world.");
      this.addSkill(IntrinsicSkills.SCALE_ARMOR, "Scale Armor", "Like the lizardmen, move through water and mud with no penalties while receiving a slight defense buff. ");
      this.addSkill(IntrinsicSkills.SPACE_TRANSFORM, "Space Transform", "Channel the powers of space to weaken and damage all nearby entities.");
      this.addSkill(IntrinsicSkills.THUNDER_BREATH, "Thunder Breath", "Release thunder from your mouth to damage foes in front of you.");
      this.addSkill(IntrinsicSkills.ULTRASONIC_WAVES, "Ultrasonic Waves", "Shoot out a screech of sound which damages physical bodies or use echolocation to highlight nearby entities.");
      this.addSkill(IntrinsicSkills.UNPREDICTABILITY, "Unpredictability", "In the hands of a True Hero, unpredictability reigns supreme. The Hero sees all, becoming able to ignore dodge and even read the movements of their opponent to disable their critical attacks or their dodge avoidance.");
      this.addSkill(IntrinsicSkills.WATER_BREATHING, "Water Breathing", "Remove the requirement of air underwater.");
      this.addSkill(IntrinsicSkills.WATER_TRANSFORM, "Water Transform", "Channel the powers of water to damage and poison all nearby foes.");
      this.addSkill(IntrinsicSkills.WIND_TRANSFORM, "Wind Transform", "Channel the powers of wind to paralyze and damage all nearby entities.");
      this.addSkill(CommonSkills.COERCION, "Coercion", "Shoot a blast of roar in front of you scaring any afflicted entities.");
      this.addSkill(CommonSkills.CORROSION, "Corrosion", "Empower your attacks with the deadly effect of Corrosion, toggleable when mastered.");
      this.addSkill(CommonSkills.FARSIGHT, "Farsight", "Focus your eyes and become able to see things far away.");
      this.addSkill(CommonSkills.GRAVITY_FIELD, "Gravity Field", "Weaken gravity around yourself to make movement easier or create a variously sized sphere granting previous effects while debuffing enemies.");
      this.addSkill(CommonSkills.GRAVITY_FLIGHT, "Gravity Flight", "Manipulate gravity to allow flight, your momentum from before will be continued.");
      this.addSkill(CommonSkills.HYDRAULIC_PROPULSION, "Hydraulic Propulsion", "Propel yourself at high speeds underwater.");
      this.addSkill(CommonSkills.PARALYSIS, "Paralysis", "Empower your attacks with the effect of Paralysis, toggleable when mastered.");
      this.addSkill(CommonSkills.POISON, "Poison", "Empower your attacks with the effect of Poison, toggleable when mastered.");
      this.addSkill(CommonSkills.RANGED_BARRIER, "Ranged Barrier", "Place down differently sized barriers which block enemies in or out. Strong attacks can still destroy them.");
      this.addSkill(CommonSkills.SELF_REGENERATION, "Self-regeneration", "Speed up your body’s natural regeneration to increase your survivability.");
      this.addSkill(CommonSkills.STRENGTH, "Strength", "Use magicule to strengthen your muscles.");
      this.addSkill(CommonSkills.TELEPATHY, "Telepathy", "Give orders to tames when looking at them through commands.");
      this.addSkill(CommonSkills.THOUGHT_COMMUNICATION, "Thought Communication", "Send commands to nearby allies and become able to instruct them to attack players.");
      this.addSkill(CommonSkills.WATER_BLADE, "Water Blade", "Shoot out a blade of water which flies in a straight line.");
      this.addSkill(CommonSkills.WATER_CURRENT_CONTROL, "Water Current Control", "Manipulate water around you to propel yourself in any direction.");
      this.addSkill(ExtraSkills.ALL_SEEING_EYE, "All-seeing Eye", "Go into a 3rd person mode, with your increased POV you gain movement and action buffs as well as an upgrade to Presence Sense.");
      this.addSkill(ExtraSkills.ANALYTICAL_APPRAISAL, "Analytical Appraisal", "Assess the strength of your opponents to gain insight into their overall strength.");
      this.addSkill(ExtraSkills.BLACK_FLAME, "Black Flame", "Command the tempest infused flames to spew out black flames at your enemies or even shoot deadly fireballs and hell flares.");
      this.addSkill(ExtraSkills.BLACK_LIGHTNING, "Black Lightning", "Shoot black lightning at varying strength, or summon a massive storm which attacks any non-allied mobs. A short range plasma blast can also be used to melt even the strongest of foes.");
      this.addSkill(ExtraSkills.BODY_DOUBLE, "Body Double", "Use a tenth of your magical power to summon an identical clone which takes increased damage but will fight for you until the end. The user can exchange places with the clone but risk taking increased damage. ");
      this.addSkill(ExtraSkills.CHANT_ANNULMENT, "Chant Annulment", "Become able to cast mastered magic instantly, in water or even under the effects of silence.");
      this.addSkill(ExtraSkills.DANGER_SENSE, "Danger Sense", "Receive a mental warning when entities with hostile intent enter your proximity.");
      this.addSkill(ExtraSkills.DEMON_LORD_HAKI, "Demon Lord Haki", "Unleash your Demonic aura causing those around you to quake in fear.");
      this.addSkill(ExtraSkills.EARTH_DOMINATION, "Earth Domination", "Boosts the power of  Earth abilities by a large amount.");
      this.addSkill(ExtraSkills.EARTH_MANIPULATION, "Earth Manipulation", "Boosts the power of Earth abilities by a decent amount.");
      this.addSkill(ExtraSkills.FLAME_DOMINATION, "Flame Domination", "Boosts the power of fire abilities by a large amount.");
      this.addSkill(ExtraSkills.FLAME_MANIPULATION, "Flame Manipulation", "Boosts the power of fire abilities by a decent amount.");
      this.addSkill(ExtraSkills.GODWOLF_SENSE, "Godwolf Sense", "Improve your senses to be able to see in the dark or even spot invisible entities nearby.");
      this.addSkill(ExtraSkills.GRAVITY_DOMINATION, "Gravity Domination", "Boosts Gravity abilities by a large amount and allows you to fly without hindrance.");
      this.addSkill(ExtraSkills.GRAVITY_MANIPULATION, "Gravity Manipulation", "Boosts Gravity abilities by a decent amount and allows you to fly without hindrance.");
      this.addSkill(ExtraSkills.HAKI, "Haki", "Project your aura to cow nearby foes into submission.");
      this.addSkill(ExtraSkills.HEAT_WAVE, "Heat Wave", "Shoot a fiery projectile or summon a short-ranged fire storm which will damage all nearby entities.");
      this.addSkill(ExtraSkills.HEAVENLY_EYE, "Heavenly Eye", "Project your otherworldly gaze to see all nearby entities and partially ignore dodging abilities.");
      this.addSkill(ExtraSkills.HERO_HAKI, "Hero Haki", "Use your Hero Aura to inspire your allies and terrify your enemies.");
      this.addSkill(ExtraSkills.INFINITE_REGENERATION, "Infinite Regeneration", "Use your massive amount of magicule to instantly regenerate from all but the most grievous of injuries.");
      this.addSkill(ExtraSkills.LIGHTNING_DOMINATION, "Lightning Domination", "Boosts Lightning abilities by a large amount.");
      this.addSkill(ExtraSkills.LIGHTNING_MANIPULATION, "Lightning Manipulation", "Boosts Lightning abilities by a decent amount.");
      this.addSkill(ExtraSkills.MAGIC_AURA, "Magic Aura", "Empower your attacks with many elemental aura.");
      this.addSkill(ExtraSkills.MAGIC_DARKNESS_TRANSFORM, "Magic Darkness Transform", "Gain access to the intermediate Darkness Spirit Magic and boost your darkness attacks while buffing yourself and debuffing enemies.");
      this.addSkill(ExtraSkills.MAGIC_EARTH_TRANSFORM, "Magic Earth Transform", "Gain access to the intermediate Earth Spirit Magic and boost your earth attacks while buffing yourself and debuffing enemies.");
      this.addSkill(ExtraSkills.MAGIC_FLAME_TRANSFORM, "Magic Flame Transform", "Gain access to the intermediate Flame Spirit Magic and boost your fire attacks while buffing yourself and debuffing enemies.");
      this.addSkill(ExtraSkills.MAGIC_JAMMING, "Magic Jamming", "Interferes with skills, magics, flight and transformation with mastery.");
      this.addSkill(ExtraSkills.MAGIC_LIGHT_TRANSFORM, "Magic Light Transform", "Gain access to the intermediate Light Spirit Magic and boost your light attacks while buffing yourself and debuffing enemies.");
      this.addSkill(ExtraSkills.MAGIC_SENSE, "Magic Sense", "Using magicule to sense entities around.");
      this.addSkill(ExtraSkills.MAGIC_SPACE_TRANSFORM, "Magic Space Transform", "Gain access to the intermediate Space Spirit Magic and boost your spatial attacks while buffing yourself and debuffing enemies.");
      this.addSkill(ExtraSkills.MAGIC_WATER_TRANSFORM, "Magic Water Transform", "Gain access to the intermediate Water Spirit Magic and boost your water attacks while buffing yourself and debuffing enemies.");
      this.addSkill(ExtraSkills.MAGIC_WIND_TRANSFORM, "Magic Wind Transform", "Gain access to the intermediate Wind Spirit Magic and boost your wind attacks while buffing yourself and debuffing enemies.");
      this.addSkill(ExtraSkills.MAJESTY, "Majesty", "By boosting your reputation with the villagers, gain a permanent Hero of the Village effect.");
      this.addSkill(ExtraSkills.MOLECULAR_MANIPULATION, "Molecular Manipulation", "Use your intricate knowledge and power over the building blocks of the world to obtain blocks and move entities.");
      this.addSkill(ExtraSkills.MORTAL_FEAR, "Mortal Fear", "Inspire a gripping fear of death in weaker enemies while empowering your allies.");
      this.addSkill(ExtraSkills.MULTILAYER_BARRIER, "Multilayer Barrier", "Protect yourself with a multitude of powerful defensive barriers.");
      this.addSkill(ExtraSkills.SACRED_HAKI, "Sacred Haki", "Strike fear into the hearts of evil and give strength to your allies.");
      this.addSkill(ExtraSkills.SAGE, "Sage", "Increase the speed at which you learn and master skills, magic and arts.");
      this.addSkill(ExtraSkills.SENSE_HEAT_SOURCE, "Sense Heat Source", "Highlights entities that generate heat nearby.");
      this.addSkill(ExtraSkills.SENSE_SOUNDWAVE, "Sense Soundwave", "Empower your ears to precisely locate any nearby entities.");
      this.addSkill(ExtraSkills.SHADOW_MOTION, "Shadow Motion", "Melt into the shadows and move unseen by all but the most keen observers.");
      this.addSkill(ExtraSkills.SNAKE_EYE, "Snake Eye", "Apply various debuff on targets on sight.");
      this.addSkill(ExtraSkills.SOUND_DOMINATION, "Sound Domination", "Boosts Sound abilities by a large amount.");
      this.addSkill(ExtraSkills.SOUND_MANIPULATION, "Sound Manipulation", "Boosts Sound abilities by a large amount.");
      this.addSkill(ExtraSkills.SPATIAL_DOMINATION, "Spatial Domination", "Boosts Space abilities by a large amount and deals massive damage by splitting a target with dimensional attacks.");
      this.addSkill(ExtraSkills.SPATIAL_MANIPULATION, "Spatial Manipulation", "Boosts Space abilities by a decent amount and manipulate space to empower your ranged attacks.");
      this.addSkill(ExtraSkills.SPATIAL_MOTION, "Spatial Motion", "Tear space asunder granting yourself and your allies safe passage.");
      this.addSkill(ExtraSkills.STICKY_STEEL_THREAD, "Stick Steel Thread", "Manipulate threads in various form.");
      this.addSkill(ExtraSkills.STEEL_STRENGTH, "Steel Strength", "Strengthen your muscles and gain an increase in damage, toggleable when mastered.");
      this.addSkill(ExtraSkills.STRENGTHEN_BODY, "Strengthen Body", "Strengthen your body to better protect yourself against attacks.");
      this.addSkill(ExtraSkills.THOUGHT_ACCELERATION, "Thought Acceleration", "Increase the speed at which you think to cast magic more quickly as well as to increase your reaction speed.");
      this.addSkill(ExtraSkills.ULTRA_INSTINCT, "Ultra-Instinct", "Enhance your sense to dodge physical impact.");
      this.addSkill(ExtraSkills.ULTRASPEED_REGENERATION, "Ultraspeed Regeneration", "Boost your bodies healing to nearly impossible levels ");
      this.addSkill(ExtraSkills.UNIVERSAL_PERCEPTION, "Universal Perception", "Combine all magic, sound and heat sense to detect entities around.");
      this.addSkill(ExtraSkills.WATER_DOMINATION, "Water Domination", "Boosts Water abilities by a large amount.");
      this.addSkill(ExtraSkills.WATER_MANIPULATION, "Water Manipulation", "Boosts Water abilities by a decent amount.");
      this.addSkill(ExtraSkills.WIND_DOMINATION, "Wind Domination", "Boosts Wind abilities by a large amount.");
      this.addSkill(ExtraSkills.WIND_MANIPULATION, "Wind Manipulation", "Boosts Wind abilities by a decent amount.");
      this.addSkill(ExtraSkills.WEATHER_DOMINATION, "Weather Domination", "Use magicule to manipulate the weather with a higher efficiency.");
      this.addSkill(ExtraSkills.WEATHER_MANIPULATION, "Weather Manipulation", "Use magicule to manipulate the weather.");
      this.addSkill(UniqueSkills.ABSOLUTE_SEVERANCE, "Absolute Severance", "Wield the power to cut through any who stand in your path, whether by coating your strikes or launching slashing projectiles that sever all in their path.");
      this.addSkill(UniqueSkills.ANTI_SKILL, "Anti-Skill", "Become immune to damage from Magic, Skills and Battlewill, destroy barriers and block skill usage of other entities");
      this.addSkill(UniqueSkills.BERSERK, "Berserk", "Massively empower your body and infuse it with a flame aura. Allows you to go into a risky but overwhelmingly powerful berserker mode.");
      this.addSkill(UniqueSkills.BERSERKER, "Berserker", "Feed on your enemies' defeat. Gain EP from kills, destroy equipment faster, and boost your physical stats based on your power level.");
      this.addSkill(UniqueSkills.BEWILDER, "Bewilder", "Manipulate friends and foes, compelling them to act according to your will.");
      this.addSkill(UniqueSkills.CHEF, "Chef", "Purify and renew. Remove all negative effects and restore vitality to those under your care.");
      this.addSkill(UniqueSkills.CHOSEN_ONE, "Chosen One", "Radiate authority and charisma to force fear into the hearts of your enemies and to make them follow you instead, while empowering yourself and your allies.");
      this.addSkill(UniqueSkills.COMMANDER, "Commander", "Lead the charge with your allies. Empower yourself and your allies to give yourself an overwhelming advantage.");
      this.addSkill(UniqueSkills.COOK, "Cook", "Bend reality to ensure your enemies meet their demise by ignoring their dodge and barriers.");
      this.addSkill(UniqueSkills.CREATOR, "Creator", "Create any Unique Skill and use it for a limited amount of time.");
      this.addSkill(UniqueSkills.DEGENERATE, "Degenerate", "Craft, decraft, customize items and absorb strength from weakened enemies.");
      this.addSkill(UniqueSkills.DIVINE_BERSERKER, "Divine Berserker", "Empower your body by a massive amount but beware the aftereffects. ");
      this.addSkill(UniqueSkills.ENGORGER, "Engorger", "Increase your size and boost your physical stats.");
      this.addSkill(UniqueSkills.ENVY, "Envy", "Absorb strength from enemies, buff yourself and debilitate your enemies.");
      this.addSkill(UniqueSkills.FALSIFIER, "Falsifier", "See through falsehoods and invisibility, conceal your presence from prying eyes and create illusions.");
      this.addSkill(UniqueSkills.FIGHTER, "Fighter", "Achieve mastery through discipline. Learn combat and magic abilities instantly, gain more mastery points, and hit much harder.");
      this.addSkill(UniqueSkills.FUSIONIST, "Fusionist", "Turn the world into a weapon, absorb the environment to create powerful mines and grenades.");
      this.addSkill(UniqueSkills.GLUTTONY, "Gluttony", "Absorb all in your path. None can win. Receive and provide skills, gain access to a spatial storage and mimic entities.");
      this.addSkill(UniqueSkills.GODLY_CRAFTSMAN, "Godly Craftsman", "Utilize your years of experience to create masterpieces with ease and engrave your weapons for terrifying efficiency.");
      this.addSkill(UniqueSkills.GOURMAND, "Gourmand", "Feast on the energy of your opponents. Steal magicule with your attacks and become more powerful when killing others.");
      this.addSkill(UniqueSkills.GOURMET, "Gourmet", "Absorb your enemies, gain new powers, and break down anything that stands in your way.");
      this.addSkill(UniqueSkills.GREAT_SAGE, "Great Sage", "Improve your cognitive skills to learn and cast faster, become able to appraise targets and use analysis to copy skills and process materials to craft or clone items or equipment.");
      this.addSkill(UniqueSkills.GREED, "Greed", "Take everything. Conquer the world and your enemies with it. Kill anyone who stands in your path while strengthening yourself or your allies.");
      this.addSkill(UniqueSkills.GUARDIAN, "Guardian", "Stand as a bastion against the force of your enemies. Absorb the damage from your allies and fortify their defenses.");
      this.addSkill(UniqueSkills.HEALER, "Healer", "Mend wounds with a touch or unleash devastating afflictions, capable of both salvation and suffering.");
      this.addSkill(UniqueSkills.INFINITY_PRISON, "Infinity Prison", "Trap enemies in an unbreakable dimensional cage or shield yourself from any threat with your dimensional barrier. Gain access to spatial storage.");
      this.addSkill(UniqueSkills.LUST, "Lust", "Assert your control over life and death. Drain your enemies of their strength or invigorate those under your wing.");
      this.addSkill(UniqueSkills.MARTIAL_MASTER, "Martial Master", "Empower your physical attacks, accelerate your thought process to react and dodge better while beating your enemies to submission. ");
      this.addSkill(UniqueSkills.MATHEMATICIAN, "Mathematician", "Use mathematics to improve your combat abilities, become able to use analytical appraisal to assess all.");
      this.addSkill(UniqueSkills.MERCILESS, "Merciless", "Instantly kill weakened enemies or drain the life force of those who lack the will to fight or of those much weaker than you.");
      this.addSkill(UniqueSkills.MURDERER, "Murderer", "Disappear into the shadows and deal massive damage, also become undetectable by sound.");
      this.addSkill(UniqueSkills.MUSICIAN, "Musician", "Turn beautiful music into an instrument of destruction. Use sound to create powerful blasts which ignore armor. ");
      this.addSkill(UniqueSkills.OBSERVER, "Observer", "See all, evade all. Instinctively avoid attacks, detect concealed dangers, and identify hidden entities. Nothing escapes your watchful gaze.");
      this.addSkill(UniqueSkills.OPPRESSOR, "Oppressor", "Dominate gravity yourself, control attractive and repulsive forces or unleash devastating ranged attacks which will crush anyone unfortunate enough to be in your path.");
      this.addSkill(UniqueSkills.PREDATOR, "Predator", "Become the monster you were meant to be. Eat all. Analyze, craft and refine items, gain access to a spatial storage and mimic entities.");
      this.addSkill(UniqueSkills.PRIDE, "Pride", "Turn the strength of your opponents into your own and turn the tides of battle. Copy abilities when you are struck by them, provided you meet the necessary conditions.");
      this.addSkill(UniqueSkills.REAPER, "Reaper", "Do recon by becoming smaller and more agile while seeing all enemies nearby. Spawn clones, or consume the essence of your enemies to obliterate them in a single strike.");
      this.addSkill(UniqueSkills.REVERSER, "Reverser", "Flip the rules. Change alignments, invert buffs and debuffs while shifting strengths and weaknesses to your benefit.");
      this.addSkill(UniqueSkills.REFLECTOR, "Reflector", "Turn back the tides of battle by reflecting all of the received damage. Unleash a devastating projectile attack or counter an attack directly.");
      this.addSkill(UniqueSkills.RESEARCHER, "Researcher", "Study ancient magic tomes, reverse engineer them, and create wonders beyond imagination.");
      this.addSkill(UniqueSkills.ROYAL_BEAST, "Royal Beast", "Unleash a primal fury and gain a massive physical boost to crush your enemies.");
      this.addSkill(UniqueSkills.SEER, "Seer", "See everything. Foresee your opponent’s moves. Dodge or mitigate their attacks and predict their movement to score critical strikes.");
      this.addSkill(UniqueSkills.SEVERER, "Severer", "Slice through reality and manifest spatial blades which cut through armor and unleash devastating blade storms.");
      this.addSkill(UniqueSkills.SHADOW_STRIKER, "Shadow Striker", "Become one with the shadows and deal massive spiritual damage and become immune to lesser presence detection.");
      this.addSkill(UniqueSkills.SLOTH, "Sloth", "Grind the world to a halt. Put your enemies into a deadly sleep, drain their power and rest to regain any lost vitality. May lethargy take over.");
      this.addSkill(UniqueSkills.SNIPER, "Sniper", "Deal damage from afar while using different bullets to get past resistances and ensure a quick kill.");
      this.addSkill(UniqueSkills.SPEARHEAD, "Spearhead", "Empower and command your allies, and then collect their abilities once they pass on.");
      this.addSkill(UniqueSkills.STARVED, "Starved", "Consume all in your path or have your rampaging subordinates do it. Corrode your enemies, devour their strength, and add their abilities to your own.");
      this.addSkill(UniqueSkills.SUPPRESSOR, "Suppressor", "Restrict teleportation, confuse enemies by swapping places, and use your blink as well as the spatial gate to cross large distances.");
      this.addSkill(UniqueSkills.SURVIVOR, "Survivor", "Endure and outheal anything, nullify physical damage and withstand overwhelming odds.");
      this.addSkill(UniqueSkills.THROWER, "Thrower", "Use your skill and your precision to throw anything and deal massive damage. You can even shove air or push back your foes.");
      this.addSkill(UniqueSkills.TRAVELER, "Traveler", "Move freely through space, teleport large distances or create spatial gates. Manipulate stardust to immense damage.");
      this.addSkill(UniqueSkills.TUNER, "Tuner", "Change your fate survive fatal blows, regenerate instantly and manipulate probability.");
      this.addSkill(UniqueSkills.UNYIELDING, "Unyielding", "Draw power from loyal allies. Fortify subordinates and seamlessly switch to a backup body to stay in the fight.");
      this.addSkill(UniqueSkills.USURPER, "Usurper", "Seize your enemies' power or their summons. Steal their abilities and take control of their skills, draining their strength for your own.");
      this.addSkill(UniqueSkills.VILLAIN, "Villain", "Embody malice and fight on the side of evil. Gain increased power from killing your foes, manipulate your enemies into joining your and empower your allies.");
      this.addSkill(UniqueSkills.WRATH, "Wrath", "Unleash pure rage and use it to get infinitely stronger the longer your anger persists. Beware of the devastating drawback.");
   }

   private void battlewill() {
      this.addSkill(MeleeArts.AURA_SLASH, "Aura Slash", "Experimental Feature! - Condense your aura along your blade and release ranged slash.");
      this.addSkill(MeleeArts.AURA_SWORD, "Aura Sword", "Experimental Feature! - Coat your weapon in aura enhancing its blows.");
      this.addSkill(MeleeArts.EARTHSHATTER_KICK, "Earthshatter Kick", "Experimental Feature! - Stomp your foot down upheaving the land around you.");
      this.addSkill(MeleeArts.HEAVY_SLASH, "Heavy Slash", "Experimental Feature! - Channel your aura into your arms and bring down a mountain-splitting slash.");
      this.addSkill(MeleeArts.OGRE_SWORD_GUILLOTINE, "Ogre-sword Guillotine", "Experimental Feature! - Coat your weapon in aura enhancing its blows.");
      this.addSkill(MeleeArts.ROARING_LION_PUNCH, "Roaring Lion Punch", "Experimental Feature! - Focus your aura into a fearsome blow with the regalness of a lion.");
      this.addSkill(ProjectileArts.DARK_EIGHT_PALMS, "Dark Eight Palms", "Experimental Feature! - Launch up to eight devastating aura blasts at foes.");
      this.addSkill(ProjectileArts.DEATH_MARCH_DANCE, "Death March Dance", "Experimental Feature! - Gather your aura into a ring of devastating aura spheres that come crashing down.");
      this.addSkill(ProjectileArts.ELEPHANT_STAMPEDE, "Elephant Stampede", "Experimental Feature! - Throw a ring of aura spheres around you.");
      this.addSkill(ProjectileArts.MAGIC_BULLET, "Magic Bullet", "Experimental Feature! - Gather your aura into a powerful blast.");
      this.addSkill(ProjectileArts.MAXIMUM_MAGIC_BULLET, "Maximum Magic Bullet", "Experimental Feature! - Gather your aura into a gargantuan blast obliterating all who dare oppose you.");
      this.addSkill(ProjectileArts.OGRE_FLAME, "Ogre Flame", "Experimental Feature! - Use your aura to create a pillar of fire.");
      this.addSkill(ProjectileArts.OGRE_SWORD_CANNON, "Ogre-sword Cannon", "Experimental Feature! - Condense your aura into a blade projectile.");
      this.addSkill(UtilityArts.AIR_FLIGHT, "Air Flight", "Experimental Feature! - Use your aura to propel you forward, and hover in air.");
      this.addSkill(UtilityArts.AURA_SHIELD, "Aura Shield", "Experimental Feature! - Create a shield of condensed aura to block attacks.");
      this.addSkill(UtilityArts.BATTLEWILL, "Battlewill", "Experimental Feature! - Channel your will, converting magicules into aura.");
      this.addSkill(UtilityArts.DIAMOND_PATH, "Diamond Path", "Experimental Feature! - Harden your aura around you to block incoming attacks.");
      this.addSkill(UtilityArts.FORMHIDE, "Formhide", "Experimental Feature! - Match your aura to the surroundings, which makes you imperceptible.");
      this.addSkill(UtilityArts.HAZE, "Haze", "Experimental Feature! - Wrap yourself in a cloak of aura concealing yourself from even the most heightened of senses.");
      this.addSkill(UtilityArts.INSTANT_MOVE, "Instant-move", "Experimental Feature! - Gather your aura at your feet to travel faster than the eye can see.");
      this.addSkill(UtilityArts.VIOLENT_BREAK, "Violent Break", "Experimental Feature! - Channel your aura, recklessly enhancing your strength and cleansing you of any negative effects.");
   }

   private void skillModes() {
      this.add("tensura.skill.mode.default", "Default");
      this.add("tensura.skill.mode.changed", "Changed the mode of %s to %s.");
      this.add("tensura.skill.mode.no_mode", "%s doesn't have another mode.");
      this.add("tensura.skill.mode.cannot_change", "You don't meet conditions to change modes of %s.");
      this.add("tensura.skill.mode.need_toggle_off", "%s's Passive Ability needs to be toggled off to use this ability.");
      this.add("tensura.skill.mode.need_toggle_on", "%s's Passive Ability needs to be toggled on to use this ability.");
      this.add("tensura.skill.preset.changed", "Changed active preset to %s.");
      this.add("tensura.skill.preset.no_change", "Your active preset is already %s.");
      this.add("tensura.skill.mode.megiddo.single", "Single Target");
      this.add("tensura.skill.mode.megiddo.autonomous", "Auto Target");
      this.add("tensura.skill.mode.absolute_severance.coat", "Severance Coat");
      this.add("tensura.skill.mode.absolute_severance.projectile", "Severance Cutter");
      this.add("tensura.skill.mode.berserk.rage", "Rage");
      this.add("tensura.skill.mode.berserk.mad_ogre", "Mad Ogre");
      this.add("tensura.skill.mode.bewilder.target", "Target");
      this.add("tensura.skill.mode.bewilder.area", "Area");
      this.add("tensura.skill.mode.bewilder.charm", "Charm");
      this.add("tensura.skill.mode.bewilder.kill", "Kill");
      this.add("tensura.skill.mode.chosen_one.haki", "Hero's Haki");
      this.add("tensura.skill.mode.chosen_one.charisma", "Hero's Charisma");
      this.add("tensura.skill.mode.commander.movement_communication", "Communication: Movement");
      this.add("tensura.skill.mode.commander.targeting_communication", "Communication: Targeting");
      this.add("tensura.skill.mode.commander.thought_domination", "Thought Domination");
      this.add("tensura.skill.mode.creator.analytical_appraisal", "Analytical Appraisal");
      this.add("tensura.skill.mode.creator.skill_creation", "Skill Creation");
      this.add("tensura.skill.mode.degenerate.crafting", "Crafting");
      this.add("tensura.skill.mode.degenerate.synthesise", "Synthesise");
      this.add("tensura.skill.mode.degenerate.separate", "Separate");
      this.add("tensura.skill.mode.envy.absorb", "Absorb");
      this.add("tensura.skill.mode.envy.strength_sap", "Strength Sap");
      this.add("tensura.skill.mode.falsifier.concealment", "Presence Concealment");
      this.add("tensura.skill.mode.falsifier.illusion", "Illusion");
      this.add("tensura.skill.mode.falsifier.fake_death", "Fake Death");
      this.add("tensura.skill.falsifier.clear_slot", "Clear Slot");
      this.add("tensura.skill.falsifier.empty", "Empty");
      this.add("tensura.skill.mode.fusionist.disassemble", "Disassemble");
      this.add("tensura.skill.mode.fusionist.fuse", "Fuse");
      this.add("tensura.skill.mode.fusionist.projectile", "Stone");
      this.add("tensura.skill.fusionist.matter_amount", "Fusionist Matters: %s");
      this.add("tensura.skill.fusionist.out_of_matter", "Out of matters to fuse.");
      this.add("tensura.skill.mode.great_sage.analytical_appraisal", "Analytical Appraisal");
      this.add("tensura.skill.mode.great_sage.analysis", "Analysis");
      this.add("tensura.skill.mode.greed.spiritual", "Spiritual Domination");
      this.add("tensura.skill.mode.greed.flare", "Greed Flare");
      this.add("tensura.skill.mode.greed.death", "Death Wish");
      this.add("tensura.skill.mode.guardian.grant", "Grant Protection");
      this.add("tensura.skill.mode.guardian.iron_wall", "Iron Wall");
      this.add("tensura.skill.guardian.substitution_notification", "%s damage taken on behalf of %s");
      this.add("tensura.skill.mode.healer.heal", "Heal");
      this.add("tensura.skill.mode.healer.virus", "Infection");
      this.add("tensura.skill.mode.healer.plague", "Plague");
      this.add("tensura.skill.mode.infinity_prison.imprison", "Imprison");
      this.add("tensura.skill.mode.infinity_prison.imaginary_space", "Imaginary Space");
      this.add("tensura.skill.mode.lust.drain", "Drain");
      this.add("tensura.skill.mode.lust.invigorate", "Invigorate");
      this.add("tensura.skill.mode.lust.rebirth", "Rebirth");
      this.add("tensura.skill.mode.lust.embracing_drain", "Embracing Drain");
      this.add("tensura.skill.mode.lust.death_blessing", "Death Blessing");
      this.add("tensura.skill.mode.master_chef.chaotic_fate", "Chaotic Fate");
      this.add("tensura.skill.mode.merciless.steal", "Soul Steal");
      this.add("tensura.skill.mode.merciless.consume", "Soul Consume");
      this.add("tensura.skill.mode.musician.sonic_blast", "Sonic Blast");
      this.add("tensura.skill.mode.musician.sound_wave", "Sound Wave");
      this.add("tensura.skill.mode.musician.mind_requiem", "Mind Requiem");
      this.add("tensura.skill.mode.observer.trap", "Trap Detection");
      this.add("tensura.skill.mode.observer.monster", "Monster Detection");
      this.add("tensura.skill.mode.observer.presence", "Presence Detection");
      this.add("tensura.skill.mode.oppressor.repel", "Repel");
      this.add("tensura.skill.mode.oppressor.attract", "Attract");
      this.add("tensura.skill.mode.oppressor.oppress", "Oppress");
      this.add("tensura.skill.mode.oppressor.bleve", "Bleve");
      this.add("tensura.skill.mode.oppressor.flicker", "Flicker");
      this.add("tensura.skill.mode.predator.predation", "Predation");
      this.add("tensura.skill.mode.predator.analysis", "Analysis");
      this.add("tensura.skill.mode.predator.stomach", "Stomach");
      this.add("tensura.skill.mode.predator.mimicry", "Mimicry");
      this.add("tensura.skill.mode.predator.isolation", "Isolation");
      this.add("tensura.skill.predator.block_mode.none", "Changed %s's Block Consuming to None.");
      this.add("tensura.skill.predator.block_mode.blocks", "Changed %s's Block Consuming to Blocks.");
      this.add("tensura.skill.predator.block_mode.fluid", "Changed %s's Block Consuming to Fluid.");
      this.add("tensura.skill.predator.block_mode.all", "Changed %s's Block Consuming to All.");
      this.add("tensura.skill.mode.reaper.attack", "Attack");
      this.add("tensura.skill.mode.reaper.eater", "Infinite Eater");
      this.add("tensura.skill.mode.reflector.reflection", "Echo Reflection");
      this.add("tensura.skill.mode.reflector.counter", "Echo Counter");
      this.add("tensura.skill.reflector.remaining_echo", "Echo Points: %s");
      this.add("tensura.skill.mode.reverser.reverse", "Alignment Reverse");
      this.add("tensura.skill.mode.reverser.buff", "Inverted Fusion [Buff]");
      this.add("tensura.skill.mode.reverser.debuff", "Inverted Fusion [Debuff]");
      this.add("tensura.skill.mode.severer.sword", "Dummy Sword");
      this.add("tensura.skill.mode.severer.blade_storm", "Blade Storm");
      this.add("tensura.skill.mode.severer.severance", "Severance");
      this.add("tensura.skill.mode.shadow_striker.ultra_acceleration", "Ultra Acceleration");
      this.add("tensura.skill.mode.shadow_striker.insta_kill", "Insta-kill");
      this.add("tensura.skill.mode.shadow_striker.espionage", "Espionage");
      this.add("tensura.skill.mode.sloth.deep_hypno", "Deep Hypno");
      this.add("tensura.skill.mode.sloth.fallen_hypno", "Fallen Hypno");
      this.add("tensura.skill.mode.sloth.deprive", "Deprive");
      this.add("tensura.skill.mode.sloth.rest", "Rest");
      this.add("tensura.skill.mode.sloth.phantasmal_strike", "Phantasmal Strike");
      this.add("tensura.skill.mode.sloth.fallen_strike", "Fallen Strike");
      this.add("tensura.skill.mode.sniper.weapon", "Create Weapon");
      this.add("tensura.skill.mode.sniper.spatial", "Spatial Manipulation");
      this.add("tensura.skill.mode.starved.corrosion", "Corrosion");
      this.add("tensura.skill.mode.starved.stomach", "Stomach");
      this.add("tensura.skill.mode.starved.receive", "Receive");
      this.add("tensura.skill.mode.starved.provide", "Provide");
      this.add("tensura.skill.mode.spiritual_domination", "Spiritual Domination");
      this.add("tensura.skill.mode.suppressor.blockade", "Spatial Suppression");
      this.add("tensura.skill.mode.suppressor.swap", "Swap");
      this.add("tensura.skill.mode.suppressor.motion", "Spatial Motion");
      this.add("tensura.skill.mode.traveler.instant_motion", "Instant Motion");
      this.add("tensura.skill.mode.traveler.teleport", "Teleport");
      this.add("tensura.skill.mode.traveler.stardust_arrow", "Stardust Arrow");
      this.add("tensura.skill.mode.traveler.stardust_rain", "Stardust Rain");
      this.add("tensura.skill.mode.unyielding.return", "Default");
      this.add("tensura.skill.mode.unyielding.backup", "Backup");
      this.add("tensura.skill.mode.unyielding.check_point", "The number of Unyielding Points with %s: %s");
      this.add("tensura.skill.mode.unyielding.backup_remove", "Backup vessel removed.");
      this.add("tensura.skill.mode.unyielding.backup_different_dimension", "The Backup vessel is in a different dimension.");
      this.add("tensura.skill.mode.usurper.rob", "Rob");
      this.add("tensura.skill.mode.usurper.copy", "Copy");
      this.add("tensura.skill.mode.usurper.force_takeover", "Force Takeover");
      this.add("tensura.skill.mode.villain.haki", "Demon Lord's Haki");
      this.add("tensura.skill.mode.villain.charisma", "Villain's Charisma");
      this.add("tensura.skill.mode.wrath.breader", "Breeder Reactor");
      this.add("tensura.skill.mode.wrath.enrage", "Enrage");
      this.add("tensura.skill.analytical.analyzing_mode.both", "Changed Analyzing Mode to All.");
      this.add("tensura.skill.analytical.analyzing_mode.entity", "Changed Analyzing Mode to Entities.");
      this.add("tensura.skill.analytical.analyzing_mode.block", "Changed Analyzing Mode to Blocks.");
      this.add("tensura.skill.mode.black_flame.breath", "Flame Breath");
      this.add("tensura.skill.mode.black_flame.ball", "Fireball");
      this.add("tensura.skill.mode.black_flame.hell_flare", "Hell Flare");
      this.add("tensura.skill.mode.black_flame.limited_hell_flare", "Limited Hell Flare");
      this.add("tensura.skill.mode.black_lightning.default", "Default Power");
      this.add("tensura.skill.mode.black_lightning.weak", "Decreased Power");
      this.add("tensura.skill.mode.black_lightning.strong", "Increased Power");
      this.add("tensura.skill.mode.black_lightning.blast", "Blast");
      this.add("tensura.skill.mode.black_lightning.storm", "Death Storm");
      this.add("tensura.skill.mode.blood_mist.ray", "Blood Ray");
      this.add("tensura.skill.mode.body_double.creation", "Clone Creation");
      this.add("tensura.skill.mode.body_double.control", "Clone Control");
      this.add("tensura.skill.mode.body_double.main_too_far", "The main body is too far away.");
      this.add("tensura.skill.mode.earth_manipulation.wall", "Earth Wall");
      this.add("tensura.skill.mode.earth_manipulation.break", "Blocks Break");
      this.add("tensura.skill.mode.earth_manipulation.pit", "Earth Pit");
      this.add("tensura.skill.mode.gravity_field.self", "Self-radius");
      this.add("tensura.skill.mode.gravity_field.5", "10x10");
      this.add("tensura.skill.mode.gravity_field.10", "20x20");
      this.add("tensura.skill.mode.haki.release", "Magicule Release");
      this.add("tensura.skill.mode.haki.coat", "Magicule Coat");
      this.add("tensura.skill.mode.heat_wave.sphere", "Heat Sphere");
      this.add("tensura.skill.mode.heat_wave.storm", "Heat Storm");
      this.add("tensura.skill.mode.magic_aura.default", "Default");
      this.add("tensura.skill.mode.magic_aura.holy", "Holy");
      this.add("tensura.skill.mode.magic_aura.earth", "Earth");
      this.add("tensura.skill.mode.magic_aura.fire", "Fire");
      this.add("tensura.skill.mode.magic_aura.space", "Space");
      this.add("tensura.skill.mode.magic_aura.water", "Water");
      this.add("tensura.skill.mode.magic_aura.wind", "Wind");
      this.add("tensura.skill.mode.molecular_manipulation.block", "Block");
      this.add("tensura.skill.mode.molecular_manipulation.entity", "Entity");
      this.add("tensura.skill.mode.shadow_motion.default", "Default");
      this.add("tensura.skill.mode.shadow_motion.step", "Shadow Step");
      this.add("tensura.skill.mode.shadow_motion.storage", "Shadow Storage");
      this.add("tensura.skill.mode.snake_eye.corrosion", "Corrosion");
      this.add("tensura.skill.mode.snake_eye.poison", "Poison");
      this.add("tensura.skill.mode.snake_eye.paralysis", "Paralysis");
      this.add("tensura.skill.mode.snake_eye.petrification", "Petrification");
      this.add("tensura.skill.mode.snake_eye.insanity", "Insanity");
      this.add("tensura.skill.mode.spatial_domination.warp_shot", "Warp Shot");
      this.add("tensura.skill.mode.spatial_domination.ray", "Dimension Ray");
      this.add("tensura.skill.mode.spatial_domination.storm", "Dimension Storm");
      this.add("tensura.skill.mode.spatial_domination.fault_field", "Fault Field");
      this.add("tensura.skill.mode.spatial_motion.blink", "Blink");
      this.add("tensura.skill.mode.spatial_motion.warp", "Warp");
      this.add("tensura.skill.mode.sticky_steel_thread.sticky", "Sticky");
      this.add("tensura.skill.mode.sticky_steel_thread.steel", "Steel");
      this.add("tensura.skill.mode.sticky_steel_thread.slinger", "Slinger");
      this.add("tensura.skill.mode.sticky_steel_thread.arcane_thread", "Arcane Thread Fetters");
      this.add("tensura.skill.mode.thought_communication.movement", "Movement Behaviour");
      this.add("tensura.skill.mode.thought_communication.targeting", "Targeting Behaviour");
      this.add("tensura.skill.mode.ranged_barrier.5", "5x5");
      this.add("tensura.skill.mode.ranged_barrier.10", "10x10");
      this.add("tensura.skill.mode.ranged_barrier.20", "20x20");
      this.add("tensura.skill.mode.ultrasonic_waves.sonic_boom", "Sonic Wave");
      this.add("tensura.skill.mode.ultrasonic_waves.auditory_sense", "Auditory Sense");
      this.add("tensura.skill.mode.weather_manipulation.clear", "Clear Weather");
      this.add("tensura.skill.mode.weather_manipulation.rain", "Rain");
      this.add("tensura.skill.mode.weather_manipulation.thunder", "Thunder Storm");
   }

   private void deathMessages() {
      this.addDeathMessage("kunai", "%1$s was pierced by a kunai from %2$s", "%1$s was pierced by %3$s from %2$s");
      this.addDeathMessage("severer_blade", "%1$s was stricken by a flying blade from %2$s", "%1$s was stricken by a flying blade from %2$s using %3$s");
      this.addDeathMessage("shot", "%1$s was shot by %2$s", "%1$s was shot by %2$s using %3$s");
      this.addDeathMessage("spear", "%1$s was speared by %2$s", "%1$s was speared by %2$s using %3$s");
      this.addDeathMessage("tempest_scale", "%1$s was pierced by a tempest scale from %2$s", "%1$s was pierced by a tempest scale from %2$s using %3$s");
      this.addDeathMessage("unicorn_horn", "%1$s was turned into glitter by %2$s", "%1$s was turned into glitter by %2$s using %3$s");
      this.addDeathMessage(TensuraDamageSources.CURSE, "%1$s died of curse");
      this.addDeathMessage(TensuraDamageSources.MAGICULE_POISON, "%1$s died of Magicule Poison");
      this.addDeathMessage(TensuraDamageSources.SUFFOCATE, "%1$s suffocated");
      this.addDeathMessage(TensuraDamageSources.CORROSION, "%1$s corroded to death", "%1$s corroded to death by %2$s");
      this.addDeathMessage(TensuraDamageSources.FATAL_POISON, "%1$s died of poison", "%1$s died of %2$s's poison");
      this.addDeathMessage(TensuraDamageSources.FEAR, "%1$s died of fear", "%1$s was scared to death by %2$s");
      this.addDeathMessage(TensuraDamageSources.HOLY_DAMAGE, "%1$s was reduced to ashes after feeling the embrace of the divine", "%1$s was reduced to ashes after feeling the embrace of the divine from %2$s");
      this.addDeathMessage(TensuraDamageSources.INSANITY, "%1$s crazed to death", "%1$s crazed to death by %2$s");
      this.addDeathMessage(TensuraDamageSources.INFECTION, "%1$s could not find the cure", "%1$s could not find the cure for %2$s's virus");
      this.addDeathMessage(TensuraDamageSources.PETRIFICATION, "%1$s was turned into stone", "%1$s was turned into stone by %2$s");
      this.addDeathMessage(TensuraDamageSources.STEEL_THREAD, "%1$s was sliced into pieces by Steel Thread", "%1$s was sliced into pieces by %2$s's Steel Thread");
      this.addDeathMessage(TensuraDamageSources.SOUL_SCATTER, "%1$s's soul was scattered", "%1$s's soul was scattered by %2$s");
      this.addDeathMessage("aura_bullet", "%1$s was shot by %2$s's Aura");
      this.addDeathMessage("aura_slash", "%1$s was sliced by %2$s's Aura");
      this.addDeathMessage("blood_ray", "%1$s was pierced through by %2$s's Blood Ray");
      this.addDeathMessage("generic_magic", "%1$s was killed by %2$s's Magic");
      this.addDeathMessage("lightning", "%1$s was struck by %2$s's lightning");
      this.addDeathMessage("burn", "%1$s was burnt alive by %2$s");
      this.addDeathMessage("dimension_ray", "%1$s was torn to shreds by %2$s's Dimension Ray");
      this.addDeathMessage("reflector_echo", "%1$s was destroyed by %2$s's reflected attack");
      this.addDeathMessage("flame_breath", "%1$s was scorched by %2$s's breath");
      this.addDeathMessage("mind_requiem", "%1$s had their final requiem sung by %2$s");
      this.addDeathMessage("megiddo", "%1$s's heart was pierced through by %2$s's Megiddo");
      this.addDeathMessage("poisonous_breath", "%1$s was corroded by %2$s's breath");
      this.addDeathMessage("oppression", "%1$s snapped under the pressure from %2$s");
      this.addDeathMessage("oppression_bleve", "%1$s exploded from the inside out by %2$s's gravity");
      this.addDeathMessage("severance_blade", "%1$s was torn to shreds by %2$s");
      this.addDeathMessage("sonic_blast", "%1$s was blasted apart by %2$s's sonic waves");
      this.addDeathMessage("steel_thread", "%1$s was sliced into pieces by %2$s");
      this.addDeathMessage("thunder_breath", "%1$s was electrocuted by %2$s's breath");
      this.addDeathMessage("water_blade", "%1$s was cut by %2$s's water blade");
      this.addDeathMessage("water_breath", "%1$s was crushed by %2$s's high-pressure water");
      this.addDeathMessage("wicked_light_ray", "%1$s was blasted into bits by %2$s's Wicked Light Ray");
      this.addDeathMessage("wind_breath", "%1$s was swept away by %2$s's wind power");
      this.addDeathMessage("death_bless", "%1$s was gently put to sleep by %2$s");
      this.addDeathMessage("death_wish", "%1$s's Death Wish was granted by %2$s");
      this.addDeathMessage("drain_EP", "%1$s's life energy was drained by %2$s");
      this.addDeathMessage("heart_eaten", "%1$s had their heart eaten by %2$s");
      this.addDeathMessage("infinite_eater", "%1$s was eaten out of existence by %2$s");
      this.addDeathMessage("self_kill", "%1$s listened to %2$s and ended their life");
      this.addDeathMessage("synthesise", "%1$s was synthesised with %2$s");
      this.addDeathMessage("no_energy_source", "%1$s's Energy Source was cut off");
      this.addDeathMessage(TensuraDamageSources.SEVERANCE_UPDATE, "%1$s was severed");
      this.addDeathMessage(TensuraDamageSources.OUT_OF_ENERGY, "%1$s was out of energy");
      this.addDeathMessage(TensuraDamageSources.BLACK_FLAME, "%1$s was burnt by Black Flame", "%1$s was burnt by %2$s's Black Flame");
      this.addDeathMessage(TensuraDamageSources.BLACK_LIGHTNING, "%1$s was struck by Black Lightning", "%1$s was struck by %2$s's Black Lightning");
      this.addDeathMessage(TensuraDamageSources.BLOOD_DRAIN, "%1$s was out of blood", "%1$s's blood was drained by %2$s");
      this.addDeathMessage(TensuraDamageSources.DEATH_TORNADO, "%1$s was swept away by a tornado", "%1$s was swept away by %2$s's tornado");
      this.addDeathMessage(TensuraDamageSources.DROWSY_DEATH, "%1$s drifted into a deep sleep", "%1$s drifted into a deep sleep by %2$s");
      this.addDeathMessage(TensuraDamageSources.HEAT_WAVE, "%1$s tried to surf the heat wave", "%1$s couldn't stand %2$s's heat");
      this.addDeathMessage(TensuraDamageSources.DEVOURED, "%1$s was devoured alive", "%1$s was devoured by %2$s");
      this.addDeathMessage(TensuraDamageSources.SOUL_CONSUMED, "%1$s was deprived of their Soul", "%1$s's Soul was deprived by %2$s");
      this.addElementalDeathMessage("tensura.dark_attack", "%1$s was sent to the abyss by %2$s using Darkness Element");
      this.addElementalDeathMessage("tensura.earth_attack", "%1$s was buried by %2$s using Earth Element");
      this.addElementalDeathMessage("tensura.gravity_attack", "%1$s was crushed by %2$s using Earth Element");
      this.addElementalDeathMessage("tensura.fire_attack", "%1$s was incinerated by %2$s using Fire Element");
      this.addElementalDeathMessage("tensura.light_attack", "%1$s was sent to the heaven by %2$s using Light Element");
      this.addElementalDeathMessage("tensura.space_attack", "%1$s was sliced into pieces by %2$s using Space Element");
      this.addElementalDeathMessage("tensura.water_attack", "%1$s was drowned by %2$s using Water Element");
      this.addElementalDeathMessage("tensura.wind_attack", "%1$s was swept away by %2$s using Wind Element");
      this.addElementalDeathMessage("tensura.lightning_attack", "%1$s was struck by %2$s using Wind Element with %3$s");
   }

   private void effects() {
      this.add("effect.tensura.insanity.voices", "The voices won't let you sleep...");
      this.add("effect.tensura.insanity.gaze", "You feel a cold gaze...");
      this.add("effect.tensura.insanity.fear", "You awaken in fear...");
      this.add("effect.tensura.insanity.unknown", "There is something in the dark...");
      this.addPotionItems("glowing", "Glowing");
      this.addPotionItems("night_owl", "Night owl");
      this.addEffectAndPotions(TensuraMobEffects.CHILL, "Chill");
      this.addEffectAndPotions(TensuraMobEffects.CORROSION, "Corrosion");
      this.addEffectAndPotions(TensuraMobEffects.FATAL_POISON, "Fatal Poison");
      this.addEffectAndPotions(TensuraMobEffects.FRAGILITY, "Fragility");
      this.addEffectAndPotions(TensuraMobEffects.PARALYSIS, "Paralysis");
      this.add((MobEffect)TensuraMobEffects.BURDEN.get(), "Burden");
      this.add((MobEffect)TensuraMobEffects.CURSE.get(), "Curse");
      this.add((MobEffect)TensuraMobEffects.FEAR.get(), "Fear");
      this.add((MobEffect)TensuraMobEffects.FROST.get(), "Frost");
      this.add((MobEffect)TensuraMobEffects.HOLY_DAMAGE.get(), "Holy Damage");
      this.add((MobEffect)TensuraMobEffects.INFECTION.get(), "Infection");
      this.add((MobEffect)TensuraMobEffects.INSANITY.get(), "Insanity");
      this.add((MobEffect)TensuraMobEffects.MAGICULE_POISON.get(), "Magicule Poison");
      this.add((MobEffect)TensuraMobEffects.MAGICULE_REGENERATION.get(), "Magicule Regeneration");
      this.add((MobEffect)TensuraMobEffects.PETRIFICATION.get(), "Petrification");
      this.add((MobEffect)TensuraMobEffects.RAMPAGE.get(), "Rampage");
      this.add((MobEffect)TensuraMobEffects.SILENCE.get(), "Silence");
      this.add((MobEffect)TensuraMobEffects.TRUE_BLINDNESS.get(), "True Blindness");
      this.add((MobEffect)TensuraMobEffects.WEBBED.get(), "Webbed");
      this.add((MobEffect)TensuraMobEffects.SLEEP_MODE.get(), "Sleep Mode");
      this.add((MobEffect)TensuraMobEffects.BATS_MODE.get(), "Bats Mode");
      this.add((MobEffect)TensuraMobEffects.ALL_SEEING.get(), "All-seeing");
      this.add((MobEffect)TensuraMobEffects.ALLY_BOOST.get(), "Ally Boost");
      this.add((MobEffect)TensuraMobEffects.AUDITORY_SENSE.get(), "Auditory Sense");
      this.add((MobEffect)TensuraMobEffects.BEAST_TRANSFORMATION.get(), "Beast Transformation");
      this.add((MobEffect)TensuraMobEffects.BLACK_BURN.get(), "Black Burn");
      this.add((MobEffect)TensuraMobEffects.DRAGON_MODE.get(), "Dragon Mode");
      this.add((MobEffect)TensuraMobEffects.DROWSINESS.get(), "Drowsiness");
      this.add((MobEffect)TensuraMobEffects.ENGORGEMENT.get(), "Engorgement");
      this.add((MobEffect)TensuraMobEffects.FALSIFIER.get(), "Falsifier");
      this.add((MobEffect)TensuraMobEffects.FATE_CHANGE.get(), "Fate Change");
      this.add((MobEffect)TensuraMobEffects.FUTURE_VISION.get(), "Future Vision");
      this.add((MobEffect)TensuraMobEffects.HAKI_COAT.get(), "Haki Coat");
      this.add((MobEffect)TensuraMobEffects.HEAT_SENSE.get(), "Heat Sense");
      this.add((MobEffect)TensuraMobEffects.INSPIRATION.get(), "Inspiration");
      this.add((MobEffect)TensuraMobEffects.INSTANT_REGENERATION.get(), "Instant Regeneration");
      this.add((MobEffect)TensuraMobEffects.LUST_EMBRACEMENT.get(), "Lust Embracement");
      this.add((MobEffect)TensuraMobEffects.MAD_OGRE.get(), "Mad Ogre");
      this.add((MobEffect)TensuraMobEffects.MAGIC_AURA.get(), "Magic Aura");
      this.add((MobEffect)TensuraMobEffects.MAGIC_DARKNESS.get(), "Magic Darkness");
      this.add((MobEffect)TensuraMobEffects.MAGIC_EARTH.get(), "Magic Earth");
      this.add((MobEffect)TensuraMobEffects.MAGIC_FLAME.get(), "Magic Flame");
      this.add((MobEffect)TensuraMobEffects.MAGIC_LIGHT.get(), "Magic Light");
      this.add((MobEffect)TensuraMobEffects.MAGIC_SPACE.get(), "Magic Space");
      this.add((MobEffect)TensuraMobEffects.MAGIC_WATER.get(), "Magic Water");
      this.add((MobEffect)TensuraMobEffects.MAGIC_WIND.get(), "Magic Wind");
      this.add((MobEffect)TensuraMobEffects.MIND_CONTROL.get(), "Mind Control");
      this.add((MobEffect)TensuraMobEffects.DANGER_DETECTION.get(), "Danger Detection");
      this.add((MobEffect)TensuraMobEffects.OGRE_BERSERKER.get(), "Ogre Berserker");
      this.add((MobEffect)TensuraMobEffects.PRESENCE_CONCEALMENT.get(), "Presence Concealment");
      this.add((MobEffect)TensuraMobEffects.PRESENCE_SENSE.get(), "Presence Sense");
      this.add((MobEffect)TensuraMobEffects.PROTECTION.get(), "Protection");
      this.add((MobEffect)TensuraMobEffects.REAPER_RECON.get(), "Reaper Recon");
      this.add((MobEffect)TensuraMobEffects.REST.get(), "Rest");
      this.add((MobEffect)TensuraMobEffects.SELF_REGENERATION.get(), "Self-regeneration");
      this.add((MobEffect)TensuraMobEffects.SEVERANCE_BLADE.get(), "Severance Blade");
      this.add((MobEffect)TensuraMobEffects.SHADOW_STEP.get(), "Shadow Step");
      this.add((MobEffect)TensuraMobEffects.SPEARHEAD.get(), "Spearhead");
      this.add((MobEffect)TensuraMobEffects.STRENGTHEN.get(), "Strengthen");
      this.add((MobEffect)TensuraMobEffects.WARPING.get(), "Warping");
      this.add((MobEffect)TensuraMobEffects.ANTI_SKILL.get(), "Anti-Skill");
      this.add((MobEffect)TensuraMobEffects.DISINTEGRATING.get(), "Disintegrating");
      this.add((MobEffect)TensuraMobEffects.ENERGY_BLOCKADE.get(), "Energy Blockade");
      this.add((MobEffect)TensuraMobEffects.INFINITE_IMPRISONMENT.get(), "Infinite Imprisonment");
      this.add((MobEffect)TensuraMobEffects.MAGIC_INTERFERENCE.get(), "Magic Interference");
      this.add((MobEffect)TensuraMobEffects.MOVEMENT_INTERFERENCE.get(), "Movement Interference");
      this.add((MobEffect)TensuraMobEffects.OPPRESSION.get(), "Oppression");
      this.add((MobEffect)TensuraMobEffects.SOUL_DRAIN.get(), "Soul Drain");
      this.add((MobEffect)TensuraMobEffects.SPATIAL_BLOCKADE.get(), "Spatial Blockade");
      this.add((MobEffect)TensuraMobEffects.AURA_SWORD.get(), "Aura Sword");
      this.add((MobEffect)TensuraMobEffects.DIAMOND_PATH.get(), "Diamond Path");
      this.add((MobEffect)TensuraMobEffects.OGRE_GUILLOTINE.get(), "Ogre Guillotine");
      this.add((MobEffect)TensuraMobEffects.SHADOW_BIND.get(), "Shadow Bind");
   }

   private void races() {
      this.addRace(TensuraRaces.BEASTFOLK, "Beastfolk", "A race that can freely change between their true animal form and a more a human form. They possess immense physical prowess and regenerative capabilities that let them fight without rest.");
      this.addRace(TensuraRaces.BEAST_LORD, "Beast Lord", "Beastfolk that has \"evolved the correct way\" and became a Demi-Spiritual Lifeform.");
      this.addRace(TensuraRaces.SPIRIT_BEAST, "Spirit Beast", "Beastfolk that has \"evolved the correct way\" and became a Spiritual Lifeform.");
      this.addRace(TensuraRaces.DIVINE_BEAST, "Divine Beast", "Beastfolk that achieved divinity, possessing an immortal physical body that will never age.");
      this.addRace(TensuraRaces.ELF, "Elf", "A sprite race descended from wind elementals. They possess a fierce talent for elemental magic and are more in tune with nature than most.");
      this.addRace(TensuraRaces.ENLIGHTENED_ELF, "Enlightened Elf", "Elf that has \"evolved the correct way\" and became a Demi-Spiritual Lifeform.");
      this.addRace(TensuraRaces.ELF_SAINT, "Elf Saint", "Elf that has \"evolved the correct way\" and became a Spiritual Lifeform.");
      this.addRace(TensuraRaces.DIVINE_ELF, "Divine Elf", "Elf that achieved divinity, possessing an immortal physical body that will never age.");
      this.addRace(TensuraRaces.DWARF, "Dwarf", "A sprite race descended from earth elementals. They possess an immense will and make for fierce albeit rather short soldiers.");
      this.addRace(TensuraRaces.ENLIGHTENED_DWARF, "Enlightened Dwarf", "Dwarf that has \"evolved the correct way\" and became a Demi-Spiritual Lifeform.");
      this.addRace(TensuraRaces.DWARF_SAINT, "Dwarf Saint", "Dwarf that has \"evolved the correct way\" and became a Spiritual Lifeform.");
      this.addRace(TensuraRaces.DIVINE_DWARF, "Divine Dwarf", "Dwarf that achieved divinity, possessing an immortal physical body that will never age.");
      this.addRace(TensuraRaces.ORC, "Orc", "A race of beastfolk who lost the ability to shift between man and beast, resulting in a permanent mix of the two. Their physical strength is greater than average but pails in comparison to their original strength as beastfolk.");
      this.addRace(TensuraRaces.HIGH_ORC, "High Orc", "The evolved form of Orcs. They are smarter than Orcs while preserving the Orc race's special characteristics, with appearance virtually identical to regular Orcs.");
      this.addRace(TensuraRaces.ORC_LORD, "Orc Lord", "A very rare and powerful member of the Orc race with high intelligence who appears roughly every few centuries.");
      this.addRace(TensuraRaces.ORC_DISASTER, "Orc Disaster", "The evolution of an Orc Lord that is a Demon Lord Seed.");
      this.addRace(TensuraRaces.SPIRIT_BOAR, "Spirit Boar", "Orc that has \"evolved the correct way\" and became a Spiritual Lifeform.");
      this.addRace(TensuraRaces.DIVINE_BOAR, "Divine Boar", "Orc that achieved divinity, possessing an immortal physical body that will never age.");
      this.addRace(TensuraRaces.OGRE, "Ogre", "A sprite race descended from fire elementals. They possess immense physical capabilities and a strong Japanese lineage.");
      this.addRace(TensuraRaces.ENLIGHTENED_OGRE, "Enlightened Ogre", "Ogre that has \"evolved the correct way\" and became a Demi-Spiritual Lifeform.");
      this.addRace(TensuraRaces.KIJIN, "Kijin", "A race of Sprite Demi-Humans descended from Fire Elementals. They have an average lifespan of over a thousand years");
      this.addRace(TensuraRaces.MYSTIC_ONI, "Mystic Oni", "The result of Kijin fully returning to their roots as Elementals, making them Spiritual Lifeforms.");
      this.addRace(TensuraRaces.WICKED_ONI, "Wicked Oni", "A variant of Oni, possessing an essence that leans closer towards that of a Daemon rather than that of a pure Elemental.");
      this.addRace(TensuraRaces.SPIRIT_ONI, "Spirit Oni", "Oni that has \"evolved the correct way\" and became a Spiritual Lifeform.");
      this.addRace(TensuraRaces.DEATH_ONI, "Death Oni", "Wicked Oni that has \"evolved the correct way\" and became a Spiritual Lifeform.");
      this.addRace(TensuraRaces.DIVINE_ONI, "Divine Oni", "Oni that achieved divinity, possessing an immortal physical body that will never age.");
      this.addRace(TensuraRaces.DIVINE_FIGHTER, "Divine Fighter", "Wicked Oni that achieved divinity, possessing an immortal physical body that will never age.");
      this.addRace(TensuraRaces.GOBLIN, "Goblin", "A race of Sprite Demi-Humans. They seem to be descended from the offspring of Dwarves and Oni.");
      this.addRace(TensuraRaces.HOBGOBLIN, "Hobgoblin", "The evolved form of male Goblins that is ranked D on average.");
      this.addRace(TensuraRaces.ENLIGHTENED_HOBGOBLIN, "Enlightened Hobgoblin", "Hobgoblin that has \"evolved the correct way\" and became a Demi-Spiritual Lifeform.");
      this.addRace(TensuraRaces.HOBGOBLIN_SAINT, "Hobgoblin Saint", "Hobgoblin that has \"evolved the correct way\" and became a Spiritual Lifeform.");
      this.addRace(TensuraRaces.HUMAN, "Human", "A weak but populous race that relies more on technology and numbers than brute force. Their low magicule count makes skills and magic users a rarity among them, instead favouring battlewill.");
      this.addRace(TensuraRaces.ENLIGHTENED_HUMAN, "Enlightened Human", "Human that has \"evolved the correct way\" and became a Demi-Spiritual Lifeform.");
      this.addRace(TensuraRaces.HUMAN_SAINT, "Human Saint", "Human that has \"evolved the correct way\" and became a Spiritual Lifeform.");
      this.addRace(TensuraRaces.DIVINE_HUMAN, "Divine Human", "Human that achieved divinity, possessing an immortal physical body that will never age.");
      this.addRace(TensuraRaces.LIZARDMAN, "Lizardman", "A race of scaled people descended from dragons. Their webbed feet give them an advantage in wet terrain.");
      this.addRace(TensuraRaces.DRAGONEWT, "Dragonewt", " The evolved form of Lizardmen and descendants of dragons. They have an average lifespan of about two hundred years.");
      this.addRace(TensuraRaces.TRUE_DRAGONEWT, "True Dragonewt", "The evolution of Dragonewts and complete Spiritual Lifeforms. They are the second-highest evolutionary stage of Dragonewt known to exist.");
      this.addRace(TensuraRaces.DIVINE_DRAGON, "Divine Dragon", "Dragonewt that achieved divinity, possessing an immortal physical body that will never age.");
      this.addRace(TensuraRaces.MERFOLK, "Merfolk", "A sprite race descended from water elementals. Their fish-like bodies give them an insurmountable advantage in water.");
      this.addRace(TensuraRaces.ENLIGHTENED_MERFOLK, "Enlightened Merfolk", "Merfolk that has \"evolved the correct way\" and became a Demi-Spiritual Lifeform.");
      this.addRace(TensuraRaces.MERFOLK_SAINT, "Merfolk Saint", "Merfolk that has \"evolved the correct way\" and became a Spiritual Lifeform.");
      this.addRace(TensuraRaces.DIVINE_FISH, "Divine Fish", "Merfolk that achieved divinity, possessing an immortal physical body that will never age.");
      this.addRace(TensuraRaces.SLIME, "Slime", "A Spectral race of monster that lacks intelligence and ambition. They’re usually passive but are incredibly ruthless once provoked.");
      this.addRace(TensuraRaces.METAL_SLIME, "Metal Slime", "A slime that has taken in large quantities of Magic Ore. The dissolved ore inside of it provides a strong resistance towards most forms of damage.");
      this.addRace(TensuraRaces.DEMON_SLIME, "Demon Slime", "The ultimate stage of evolution of Slimes. They evolve to this stage by awakening as a True Demon Lord during the Harvest Festival.");
      this.addRace(TensuraRaces.GOD_SLIME, "God Slime", "The divine stage of evolution of Slimes.");
      this.addRace(TensuraRaces.GHOUL, "Ghoul", "A vampiric thrall brought about by Blood Raise, highly weakened by sunlight.");
      this.addRace(TensuraRaces.VAMPIRE, "Vampire", "A race of Majin with nearly eternal life. Although they can reproduce, they normally abstain. Hosting overwhelming magical energy and vitality, their flesh never decays.");
      this.addRace(TensuraRaces.VAMPIRE_OVERCOMER, "Vampire Overcomer", "Vampires that have evolved to overcome their weakness against the sun.");
      this.addRace(TensuraRaces.VAMPIRE_LORD, "Vampire Lord", "A vampire among the loftiest levels of power, comparable to a Demon Lord.");
      this.addRace(TensuraRaces.DIVINE_VAMPIRE, "Divine Vampire", "Vampire that achieved divinity, possessing an immortal physical body that will never age.");
      this.addRace(TensuraRaces.WIGHT, "Wight", "A Demi-Spiritual skeletal Undead Monster, highly weakened by sunlight.");
      this.addRace(TensuraRaces.WIGHT_KING, "Wight King", "A type of Undead Monster and an evolution of Wights. They possess fearsome power comparable to that of Demon Lord Seeds.");
      this.addRace(TensuraRaces.SPIRIT_SKELETON, "Spirit Skeleton", "Wight that has \"evolved the correct way\" and became a Spiritual Lifeform.");
      this.addRace(TensuraRaces.DIVINE_SKELETON, "Divine Skeleton", "Wight that achieved divinity, possessing an immortal physical body that will never age.");
      this.addRace(TensuraRaces.LESSER_DAEMON, "Lesser Daemon", "The lowest level of the daemon race. They spontaneously come into existence within the Daemon Realm where they slowly accumulate experience from fighting and being summoned before eventually evolving into greater daemons.");
      this.addRace(TensuraRaces.GREATER_DAEMON, "Greater Daemon", "The daemon race that lesser daemons evolve to when they develop their ego and accumulate enough power.");
      this.addRace(TensuraRaces.ARCH_DAEMON, "Arch Daemon", "The peak evolutionary stage that can be reached by daemons in the Daemon Realm. Only the most powerful of greater daemons can ascend to this height.");
      this.addRace(TensuraRaces.DAEMON_LORD, "Daemon Lord", "One of the most powerful evolutionary stages of daemons, achieved only by the most powerful arch daemons.");
      this.addRace(TensuraRaces.DEVIL_LORD, "Devil Lord", "The final stage of evolution for daemons. One emerges when a daemon meets all three requirements of a name, material body, and divinity.");
      this.add("tensura.race.difficulty.easy", "Easy");
      this.add("tensura.race.difficulty.intermediate", "Intermediate");
      this.add("tensura.race.difficulty.hard", "Hard");
      this.add("tensura.race.difficulty.extreme", "Extreme");
   }

   private void menuTexts() {
      this.add("tensura.settings", "Settings");
      this.add("tensura.settings.hud_vanilla", "Vanilla");
      this.add("tensura.settings.hud_tensura", "Tensura");
      this.add("tensura.settings.hud_combined_a", "Combined 1");
      this.add("tensura.settings.hud_combined_b", "Combined 2");
      this.add("tensura.settings.skill_position", "Edit Skill Overlay Position");
      this.add("tensura.settings.hud_position", "Edit HUD Position");
      this.add("tensura.settings.hud_position.desc1", "Only for [Combined 2] HUD option");
      this.add("tensura.settings.edit", "Edit");
      this.add("tensura.settings.edit_desc1", "Press Enter/Escape to Finish or Backspace to Cancel");
      this.add("tensura.settings.edit_desc2", "Right Click to flip the direction");
      this.add("tensura.settings.reset", "Reset to Default");
      this.add("tensura.settings.reset.warn", "Are you sure?");
      this.add("tensura.settings.left", "Left");
      this.add("tensura.settings.right", "Right");
      this.add("tensura.settings.custom", "Custom");
      this.addSetting("titleScreen", "Title Screen", "Defines if Tensura should render its title screen", "Default: True");
      this.addSetting("arachnophobia", "Arachnophobia Mode", "Defines if spider mobs from the mod should have a friendlier texture", "Default: False");
      this.addSetting("tensuraHud", "HUD to Render", "Defines which HUD should be rendered", "Default: Tensura");
      this.addSetting("leftSideStats", "Stats Overlay Side", "Defines the side where Stats should be rendered", "Default: Left");
      this.addSetting("leftSideAnalysis", "Analysis Overlay Side", "Defines the side where Appraisal (if unlocked) should be rendered", "Default: Right");
      this.addSetting("flippedSkills", "Skill Overlay Side", "Defines the side where Skills should be rendered", "Resets custom Skill Overlay Position!", "Default: Left");
      this.addSetting("appraisalOpacity", "Appraisal Opacity", "Defines the opacity value of Appraisal (if unlocked)", "Range: 0 ~ 100", "Default: 80");
      this.addSetting("hudSize", "HUD Size", "Defines the size multiplier of Tensura and Combined HUDs", "Range: 0.25 ~ 10", "Default: 1");
      this.add("tensura.anvil.menu.open", "Open Anvil");
      this.add("tensura.jei.smithing.title", "Smithing");
      this.add("tensura.smithing_bench.menu.open", "Open Smithing Bench");
      this.add("tensura.smithing_table.menu.open", "Open Smithing Table");
      this.add("tooltip.tensura.smithing_table.lack_schematic", "Schematic required.");
      this.add("tensura.kiln.label", "Kiln");
      this.add("tensura.kiln.smeltery_label", "Smeltery");
      this.add("tooltip.tensura.kiln.magisteel_output", "Convert to Pure Magisteel");
      this.add("tooltip.tensura.kiln.molten_output", "Convert to Ingot/Nugget");
      this.add("tooltip.tensura.kiln.mixing_left", "Previous");
      this.add("tooltip.tensura.kiln.mixing_right", "Next");
      this.add("tooltip.tensura.kiln.molten_item", "%s Molten %s");
      this.add("tooltip.tensura.kiln.empty", "Empty");
      this.add("tensura.jei.melting.title", "Melting");
      this.add("tensura.jei.mixing.title", "Mixing");
      this.add("tensura.jei.smelting.title", "Smelting");
      this.add("tensura.race.selection", "Race Selector");
      this.add("tensura.race.selection.submit", "Select Race");
      this.add("tensura.race.selection.infobox.1", "Difficulty: %s\nAura Range: %s - %s\nMagicule Range: %s - %s");
      this.add("tensura.race.selection.infobox.2", "Intrinsic Skills: %s");
      this.add("tensura.race.selection.skills.empty", "None");
      this.add("tensura.race.selection.random", "Random");
      this.add("tensura.race.selection.unknown", "Unknown");
      this.add("tensura.race.selection.random_race", "A race randomly selected from a configurable list of options, adding an element of unpredictability to the experience. Are you willing to take the risk?");
      this.add("tooltip.tensura.wip", "This feature is a work in progress. It will likely not work as intended and how it currently looks and works may be subject to change in the future.");
      this.add("tooltip.tensura.coming_soon", "Coming Soon");
      this.add("tooltip.tensura.coming_soon_feature", "Coming Soon: %s");
      this.add("tooltip.tensura.creative_only", "Creative Mode Only");
      this.add("tooltip.tensura.return", "Return");
      this.add("tensura.skill_menu", "Skills");
      this.add("tensura.skill_selection_screen.skill_mastery", "Mastery: %s");
      this.add("tensura.skill_selection_screen.skill_learning", "Learn Points: %s");
      this.add("tensura.skill_selection_screen.untoggleable", "Untoggleable");
      this.add("tensura.skill.on_cooldown", "Cooldown: %ss");
      this.add("tensura.magic_menu", "Magic");
      this.add("tensura.battlewill_menu", "Battlewill");
      this.add("tensura.main_menu", "Status");
      this.add("tensura.main_menu.speed", "Speed: %s");
      this.add("tensura.main_menu.souls", "Souls: %s");
      this.add("tensura.main_menu.magicule", "Magicule: %s/%s");
      this.add("tensura.main_menu.aura", "Aura: %s/%s");
      this.add("tensura.main_menu.existence_points", "EP: %s");
      this.add("tensura.main_menu.reset_counter", "Reset Counter: %s");
      this.add("tensura.main_menu.evolution_progress", "Evolution Progress: %s");
      this.add("tensura.main_menu.evolution_progress.ready", "Evolution Available");
      this.add("tensura.main_menu.evolution_progress.select", "Select Evolution");
      this.add("tensura.main_menu.evolution_progress.none", "No Evolutions");
      this.add("tensura.main_menu.billion_index", "%sB");
      this.add("tensura.main_menu.million_index", "%sM");
      this.add("tensura.main_menu.tdl_awaken", "Become Demon Lord");
      this.add("tensura.main_menu.th_awaken", "Become Chosen Hero");
      this.add("tensura.evolution_menu.requirements_fail", "Requirements not met");
      this.add("tensura.evolution_menu.track", "Track");
      this.add("tensura.evolution_menu.evolve", "Evolve");
      this.add("tensura.evolution_menu.ep_requirement", "Gain Existence Points");
      this.add("tensura.evolution_menu.mastery_requirement", "Master %s");
      this.add("tensura.evolution_menu.boss_kill_requirement", "Kill bosses");
      this.add("tensura.evolution_menu.consume_requirement", "Consume %s");
      this.add("tensura.evolution_menu.name_requirement", "Be named");
      this.add("tensura.evolution_menu.awaken_requirement", "Awaken [%s/%s]");
      this.add("tensura.evolution_menu.physical_body_requirement", "Have a physical body");
      this.add("tensura.evolution_menu.spirit_requirement", "Obtain a Spirit");
      this.add("tensura.evolution_menu.specific_kill_requirement", "Defeat %s(-es)");
      this.add("tensura.evolution_menu.battle_mob_requirement", "Battle %s(-s)");
      this.add("tensura.evolution_menu.requirements", "Requirements");
      this.add("tensura.degenerate_menu.synthesis", "Synthesis");
      this.add("tensura.degenerate_menu.separation", "Separation");
      this.add("tensura.degenerate_menu.crafting", "Crafting");
      this.add("tensura.degenerate_menu.uncrafting", "Uncrafting");
      this.add("tooltip.tensura.degenerate_menu.tab_1", "Crafting & Uncrafting");
      this.add("tooltip.tensura.degenerate_menu.tab_2", "Synthesis & Separation");
      this.add("tooltip.tensura.spatial_menu.get_coordinates", "Get coordinates");
      this.add("tooltip.tensura.spatial_menu.portal", "Portal");
      this.add("tooltip.tensura.spatial_menu.warp", "Warp");
      this.add("tooltip.tensura.great_sage_menu.automate", "Automate");
      this.add("tooltip.tensura.great_sage_menu.crafting", "Crafting");
      this.add("tooltip.tensura.great_sage_menu.refining", "Refining");
      this.add("tooltip.tensura.great_sage_menu.brew", "Start Brewing");
      this.add("tensura.skill_creator", "Skill Creator");
      this.add("tensura.skill_creator.create_skill", "Create");
      this.add("tensura.researcher_menu.storage_tab", "Spatial Storage");
      this.add("tensura.researcher_menu.enchantment_tab", "Enchanting");
      this.add("tensura.researcher_menu.enchantment_tab.xp_cost", "Xp cost: %s points");
   }

   private void moltenMaterials() {
      this.add("tensura.molten.iron.material", "Iron");
      this.add("tensura.molten.silver.material", "Silver");
      this.add("tensura.molten.gold.material", "Gold");
      this.add("tensura.molten.magisteel.material", "Magisteel");
      this.add("tensura.molten.netherite.material", "Netherite");
   }

   private void creativeTabs() {
      this.add("itemGroup.tensura_armour", "T:R Armours");
      this.add("itemGroup.tensura_gear", "T:R Gears");
      this.add("itemGroup.tensura_blocks", "T:R Blocks");
      this.add("itemGroup.tensura_dungeon", "T:R Dungeon Blocks");
      this.add("itemGroup.tensura_food", "T:R Consumables");
      this.add("itemGroup.tensura_drops", "T:R Mob Drops");
      this.add("itemGroup.tensura_misc", "T:R Misc");
      this.add("itemGroup.tensura_eggs", "T:R Spawn Eggs");
   }

   private void tooltips() {
      this.add("tooltip.tensura.iceblade.tooltip.shift", "A Unique Grade Weapon which once belonged to the Magic Swordsman of Ice.");
      this.add("tooltip.tensura.sniper_pistol.tooltip.mode_magic", "Mode: §bMagic Bullet§r.");
      this.add("tooltip.tensura.sniper_pistol.tooltip.mode_physical", "Mode: §bPhysical Bullet§r.");
      this.add("tooltip.tensura.sniper_pistol.tooltip.mode", "§aShift Right-click§r to change modes.");
      this.add("tooltip.tensura.spatial_blade.tooltip.blade", "§aBlade Mode§r: §eRight-click§r to shoot out blades.");
      this.add("tooltip.tensura.spatial_blade.tooltip.hilt", "§aHilt Mode§r: §eRight-click§r to replenish blades.");
      this.add("tooltip.tensura.web_gun.projectile", "Projectile:");
      this.add("tooltip.tensura.great_sword.tooltip", "§9Only usable with both hands.");
      this.add("tooltip.tensura.long_sword.tooltip", "§9Buffed stats when used with both hands.");
      this.add("tooltip.tensura.press.shift", "Press [§eShift§r] for more Information!");
      this.add("tooltip.tensura.gear_durability_EP", "§6EP: %s/%s");
      this.add("tooltip.tensura.marionette_heart", "A rare magic item that can turn the user into a Majin with the cost of half their max HP in damage and most of their current Magicule.");
      this.add("tooltip.tensura.shadow_storage.name", "Shadow: %s");
      this.add("tooltip.tensura.reset_scroll.race", "Reset the user's Statistic, Naming status, Awakening status, Spirits, Resistances and Race along with its Intrinsic Skills.");
      this.add("tooltip.tensura.reset_scroll.skill", "Reset every skill of every type from the user except their Race's Intrinsic Skills.");
      this.add("tooltip.tensura.reset_scroll.skill_warning", "This scroll will not cover your Unique SKill's MP cost. Be careful, or else you can end up with no Unique Skills.");
      this.add("tooltip.tensura.reset_scroll.character", "Reset everything from the user.");
      this.add("tooltip.tensura.reset_scroll.not_safe", "It's not safe to reset here.");
      this.add("tooltip.tensura.orc_disaster_head", "Trophy...?");
      this.add("tooltip.tensura.charybdis_core.inactive", "Inactive");
      this.add("tooltip.tensura.charybdis_core.active", "Active");
      this.add("tooltip.tensura.charybdis_core.inert", "Inert");
      this.add("tooltip.tensura.battlewill_manual", "Experimental feature! Proper way to obtain this will be added in the future. Edit the gamerule [experimentalFeature] to turn off features like this.");
      this.add("tooltip.tensura.battlewill_manual.description", "Right-click to start learning a random Battlewill decided by a config list.");
   }

   private void enchantments() {
      this.addEnchantment((Enchantment)TensuraEnchantments.BARRIER_PIERCING.get(), "Barrier Piercing", "Breaks through any barrier the weapon touches.");
      this.addEnchantment((Enchantment)TensuraEnchantments.BREATHING_SUPPORT.get(), "Breathing Support", "Stops air bubbles from draining.");
      this.addEnchantment((Enchantment)TensuraEnchantments.CRUSHING.get(), "Crushing", "Increases the weapon's damage along with damage dealt to armor at the cost durability.");
      this.addEnchantment((Enchantment)TensuraEnchantments.ELEMENTAL_BOOST.get(), "Elemental Boost", "Increases damage output of natural effects for each piece of gear equipped.");
      this.addEnchantment((Enchantment)TensuraEnchantments.ELEMENTAL_RESISTANCE.get(), "Elemental Resistance", "Decreases damage input of natural effects for each piece of gear equipped.");
      this.addEnchantment((Enchantment)TensuraEnchantments.ENERGY_STEAL.get(), "Energy Steal", "Drains a small percent of EP from the target.");
      this.addEnchantment((Enchantment)TensuraEnchantments.HOLY_WEAPON.get(), "Holy Weapon", "Imbues the weapon with holy damage.");
      this.addEnchantment((Enchantment)TensuraEnchantments.MAGIC_WEAPON.get(), "Magic Weapon", "Imbues the weapon with magic damage.");
      this.addEnchantment((Enchantment)TensuraEnchantments.SEVERANCE.get(), "Severance", "The target will receive damage which cannot be healed by regular means.");
      this.addEnchantment((Enchantment)TensuraEnchantments.SLOTTING.get(), "Slotting", "Allows for elemental cores to be inserted.");
      this.addEnchantment((Enchantment)TensuraEnchantments.SOUL_EATER.get(), "Soul Eater", "Imbues the weapon with spiritual damage.");
      this.addEnchantment((Enchantment)TensuraEnchantments.STURDY.get(), "Sturdy", "Increases the weapon's damage and equipments durability, and makes the item invulnerable to environmental damage.");
      this.addEnchantment((Enchantment)TensuraEnchantments.SWIFT.get(), "Swift", "Increases the weapon's damage, attack range and attack speed.");
      this.addEnchantment((Enchantment)TensuraEnchantments.DEAD_END_RAINBOW.get(), "Dead End Rainbow", "Ideally reduce the target's Spiritual Health to 0 after 7 hits.");
      this.addEnchantment((Enchantment)TensuraEnchantments.HOLY_COAT.get(), "Holy Coat", "Multiplies the damage against Monster targets.");
      this.addEnchantment((Enchantment)TensuraEnchantments.MAGIC_INTERFERENCE.get(), "Magic Interference", "Reduces Magic Damage from targets weaker than you, and partially bypasses Magic Barriers of targets.");
      this.addEnchantment((Enchantment)TensuraEnchantments.TSUKUMOGAMI.get(), "Tsukumogami", "Makes only the owner of the weapon be able to use its full potential.");
   }

   private void keybind() {
      this.add(TensuraKeybinds.KEY_0.m_90860_(), "Number Key 0");
      this.add("tensura.keybinding.main_gui", "Status Menu");
      this.add("tensura.keybinding.name", "Naming");
      this.add("tensura.keybinding.race_ability", "Race/Mount Ability");
      this.add("tensura.keybinding.mount_descending", "Mount Descending");
      this.add("tensura.keybinding.activate_slot_1", "Activate ability slot 1");
      this.add("tensura.keybinding.activate_slot_2", "Activate ability slot 2");
      this.add("tensura.keybinding.activate_slot_3", "Activate ability slot 3");
      this.add("tensura.keybinding.next_mode_change", "Next Ability Mode");
      this.add("tensura.keybinding.previous_mode_change", "Previous Ability Mode");
      this.add("key.categories.hidden", "Hidden");
      this.add("manascore.category.tensura", "Tensura:Reincarnated");
   }

   private void miscTexts() {
      this.add("tensura.time.hours", "%sh");
      this.add("tensura.time.minutes", "%sm");
      this.add("tensura.time.seconds", "%ss");
      this.add("tensura.failed_hipokute_grass", "Failed Growth");
      this.add("tensura.true", "True");
      this.add("tensura.false", "False");
   }

   private void attributes() {
      this.add("tensura.vanilla_attribute.health.name", "Health");
      this.add("tensura.vanilla_attribute.health.shortened_name", "HP");
      this.add("tensura.attribute.spiritual_health.name", "Spiritual Health");
      this.add("tensura.attribute.spiritual_health.shortened_name", "SHP");
      this.add("tensura.attribute.barrier.name", "Barrier");
      this.add("tensura.attribute.max_magicule.name", "Max Magicule");
      this.add("tensura.attribute.max_aura.name", "Max Aura");
      this.add("tensura.attribute.magicule.name", "Magicule");
      this.add("tensura.attribute.magicule.shortened_name", "MP");
      this.add("tensura.attribute.aura.name", "Aura");
      this.add("tensura.attribute.aura.shortened_name", "AP");
      this.add("tensura.attribute.existence_points.name", "Existence Points");
      this.add("tensura.attribute.existence_points.shortened_name", "EP");
      this.add("tensura.attribute.race.name", "Race");
      this.add("tensura.attribute.size.name", "Size");
      this.add("tensura.attribute.true_demon_lord.name", "True Demon Lord");
      this.add("tensura.attribute.true_hero.name", "True Hero");
      this.add("tensura.attribute.slot.name", "Ability Slot");
      this.add("tensura.attribute.slot.shortened_name", "Slot");
      this.add("tensura.attribute.block.hardness.name", "Hardness");
      this.add("tensura.attribute.block.correct_tool.name", "Correct Tool");
      this.add("tensura.attribute.block.light_level.name", "Light Level");
      this.add("tensura.attribute.block.redstone_strength.name", "Redstone Power");
      this.add("tensura.attribute.block.redstone_powered.name", "Powered");
      this.add("tensura.attribute.block.egg_hatch.name", "Hatching");
      this.add("tensura.attribute.block.explosion_resistance.name", "Blast Resistance");
      this.add("tensura.attribute.block.age.name", "Age");
   }

   private void advancements() {
      this.addAdvancement(TensuraAdvancementsHelper.Advancements.REINCARNATED, "TenSura", "Reincarnation... Successful.");
      this.addAdvancement(TensuraAdvancementsHelper.Advancements.D_RANK, "Just average Human", "Get 1000 EP and reach D Class.");
      this.addAdvancement(TensuraAdvancementsHelper.Advancements.C_RANK, "Can you C me!", "Get 3000 EP and reach C Class.");
      this.addAdvancement(TensuraAdvancementsHelper.Advancements.B_RANK, "I'm a Bsian", "Get 6000 EP and reach B Class.");
      this.addAdvancement(TensuraAdvancementsHelper.Advancements.A_RANK, "Such a Hazard!", "Get 10000 EP and reach A Class.");
      this.addAdvancement(TensuraAdvancementsHelper.Advancements.SA_RANK, "Calamity Monsters!", "Get 100000 EP and reach Special-A Class.");
      this.addAdvancement(TensuraAdvancementsHelper.Advancements.S_RANK, "Walking Disaster!", "Get 400000 EP and reach S Class.");
      this.addAdvancement(TensuraAdvancementsHelper.Advancements.SS_RANK, "Literal Catastrophe!", "Get 800000 EP and reach Special-S Class.");
      this.addAdvancement(TensuraAdvancementsHelper.Advancements.GROWTH_SPURT, "Growth Spurt", "Evolve your race once.");
      this.addAdvancement(TensuraAdvancementsHelper.Advancements.HIGHER_FORM, "The higher form of Existence!", "Awaken as a Demon Lord or Chosen Hero.");
      this.addAdvancement(TensuraAdvancementsHelper.Advancements.INFAMY_FAMOUS, "Infamy and Famous", "Achieve Mass Naming by finishing raids or killing human mobs without dying.");
      this.addAdvancement(TensuraAdvancementsHelper.Advancements.NANODA, "Nanoda!!!", "Obtain the Nanoda disk by having a Charybdis kill an Orc Disaster.");
      this.addAdvancement(TensuraAdvancementsHelper.Advancements.OBTAIN_HIHIIROKANE_HOE, "Why..?", "Acquire a Hihi'Irokane Hoe.");
      this.addAdvancement(TensuraAdvancementsHelper.Advancements.HEAR_ME_DIREWOLVES, "Hear me Direwolves!", "Tame a direwolf.");
      this.addAdvancement(TensuraAdvancementsHelper.Advancements.NAME_A_MOB, "From now on, your name is...", "Name a mob.");
      this.addAdvancement(TensuraAdvancementsHelper.Advancements.RULER_OF_MONSTERS, "Ruler of Monsters", "Make a subordinate of each of these races: Direwolf, Goblin, Lizardman, Orc and Slime.");
      this.addAdvancement(TensuraAdvancementsHelper.Advancements.TAMED_A_SLIME, "I'm not a bad slime!", "Tame a slime.");
      this.addAdvancement(TensuraAdvancementsHelper.Advancements.GET_BUCKETED, "Get bucketed!", "Pick up a slime with a bucket.");
      this.addAdvancement(TensuraAdvancementsHelper.Advancements.TRAITOR, "Traitor!", "Consume a slime in a bucket with skills.");
      this.addAdvancement(TensuraAdvancementsHelper.Advancements.GROW_A_SLIME, "Thrive, my child!", "Feed a Slime core to a tamed Slime to grow its size.");
      this.addAdvancement(TensuraAdvancementsHelper.Advancements.KING_SLIME, "The King has arrived!", "Get your own Supermassive Slime by growing a slime.");
      this.addAdvancement(TensuraAdvancementsHelper.Advancements.SLIME_ARMY, "Can you win against 50 Slimes?", "Obtain a Slime Staff.");
      this.addAdvancement(TensuraAdvancementsHelper.Advancements.EAT_OR_BE_EATEN, "Eat or be eaten", "Defeat an Orc Disaster.");
      this.addAdvancement(TensuraAdvancementsHelper.Advancements.CONQUEROR_OF_FLAMES, "Conqueror of Flames", "Defeat a natural Ifrit.");
      this.addAdvancement(TensuraAdvancementsHelper.Advancements.RULER_OF_THE_SKIES, "Ruler of the Skies", "Defeat a Charybdis.");
      this.addAdvancement(TensuraAdvancementsHelper.Advancements.GREAT_SAINT_OF_THE_WEST, "The Great Saint of the West", "Defeat Hinata Sakaguchi.");
      this.addAdvancement(TensuraAdvancementsHelper.Advancements.GETCHA_LEATHERS, "Getcha Leathers!", "Obtain a vanilla Leather.");
      this.addAdvancement(TensuraAdvancementsHelper.Advancements.GOLD_RUSH, "Gold Rush", "Smelt or find a Gold Ingot.");
      this.addAdvancement(TensuraAdvancementsHelper.Advancements.ACQUIRE_SILVERWARE, "Acquire Silverware", "Smelt or find a Silver Ingot.");
      this.addAdvancement(TensuraAdvancementsHelper.Advancements.GETCHA_BETTER_LEATHERS, "Getcha Better Leathers!", "Obtain a monster Leather.");
      this.addAdvancement(TensuraAdvancementsHelper.Advancements.BELIEVE_T0_FLY, "I believe I can fly", "Obtain a Dragon Peacock Feather.");
      this.addAdvancement(TensuraAdvancementsHelper.Advancements.RIPOFF_ELYTRA, "Ripoff Elytra, good enough?", "Obtain a Bat Glider.");
      this.addAdvancement(TensuraAdvancementsHelper.Advancements.MAGIC_ORE, "Ore! But Magic?!", "Mine a Magic Ore.");
      this.addAdvancement(TensuraAdvancementsHelper.Advancements.LOW_MAGISTEEL, "Feeling Low?", "Mix some Iron with Magic Ore in a Kiln and acquire a Low Magisteel Ingot!");
      this.addAdvancement(TensuraAdvancementsHelper.Advancements.HIGH_MAGISTEEL, "Don't get too High!", "Mix some Iron with Magic Ore in a Kiln and acquire a High Magisteel Ingot!");
      this.addAdvancement(TensuraAdvancementsHelper.Advancements.PURE_MAGISTEEL, "That's the quality!", "Acquire a Pure Magisteel Ingot!");
      this.addAdvancement(TensuraAdvancementsHelper.Advancements.MITHRIL, "Isn't this just Silver but Magic?", "Mix some Silver with a bit of Magic Ore in a Kiln and acquire a Mithril Ingot!");
      this.addAdvancement(TensuraAdvancementsHelper.Advancements.ORICHALCUM, "Divine Shining Gold!", "Mix some Gold with a bit of Magic Ore in a Kiln and acquire an Orichalcum Ingot!");
      this.addAdvancement(TensuraAdvancementsHelper.Advancements.ADAMANTITE, "Biological Steel?", "Acquire an Adamantite item!");
      this.addAdvancement(TensuraAdvancementsHelper.Advancements.HIHIIROKANE, "The Ultimate Metal!", "Acquire an Hihiirokane item!");
      this.addAdvancement(TensuraAdvancementsHelper.Advancements.VIGILANT, "VigilAnt", "Slay a Giant Ant and obtain its Carapace.");
      this.addAdvancement(TensuraAdvancementsHelper.Advancements.SHELL_LIZARD, "Shell Lizard", "Slay an Armoursaurus and obtain its Scales and Shell.");
      this.addAdvancement(TensuraAdvancementsHelper.Advancements.HISS_TORY, "Hiss-tory", "Slay a Tempest Serpent and obtain its Scales.");
      this.addAdvancement(TensuraAdvancementsHelper.Advancements.GOODNIGHT_SPIDER, "Goodnight Spider", "Slay a Knight Spider and obtain its Carapace.");
      this.addAdvancement(TensuraAdvancementsHelper.Advancements.ARACHNOPHOBIC, "Arachnophobic", "Slay a Black Spider.");
      this.addAdvancement(TensuraAdvancementsHelper.Advancements.LABYRINTH, "Dungeon Encroachment", "Enter the Labyrinth.");
      this.addAdvancement(TensuraAdvancementsHelper.Advancements.JUST_A_TEST, "Chill! It's just a test!", "Fight the Elemental Colossus and \"die\".");
      this.addAdvancement(TensuraAdvancementsHelper.Advancements.SPIRIT_PROTECTOR, "The fairy wont like this...", "Defeat the Elemental Colossus.");
      this.addAdvancement(TensuraAdvancementsHelper.Advancements.ELEMENTALIST, "Elementalist", "Form a contract with any Spirit.");
      this.addAdvancement(TensuraAdvancementsHelper.Advancements.BLESSED_ONE, "Blessed One!", "Have a Greater Spirit or above in every elemental.");
      this.addAdvancement(TensuraAdvancementsHelper.Advancements.INFINITY_CORES, "Infinity Cores!", "Use a weapon that has 3 Elemental cores slotted.");
      this.addAdvancement(TensuraAdvancementsHelper.Advancements.START_SMITHING, "Start Smithing and Crafting!", "Use a Schematic.");
      this.addAdvancement(TensuraAdvancementsHelper.Advancements.BECOME_NINJA, "Become a real ninja!", "Craft a Kunai.");
      this.addAdvancement(TensuraAdvancementsHelper.Advancements.UNHEALABLE_WOUND, "Unhealable wound...", "Craft a Spatial Blade.");
      this.addAdvancement(TensuraAdvancementsHelper.Advancements.A_BIT_COLD, "Isn't it a bit cold?", "Craft a Ice Blade.");
      this.addAdvancement(TensuraAdvancementsHelper.Advancements.MASTER_SMITH, "The Master Smith!", "Obtain every smithing schematic.");
      this.addAdvancement(TensuraAdvancementsHelper.Advancements.NO_NO_SQUARE, "This is my no no square", "Use a Magic Engine.");
      this.addAdvancement(TensuraAdvancementsHelper.Advancements.MONSTER_RIDER, "Monster Rider", "Craft a Monster Saddle.");
      this.addAdvancement(TensuraAdvancementsHelper.Advancements.CHOO_CHOO, "Choo Choo!", "Ride an Evil Centipede or Tempest Serpent.");
      this.addAdvancement(TensuraAdvancementsHelper.Advancements.GOOD_BOY, "Who's a good boy?", "Ride a Tempest Star Wolf.");
      this.addAdvancement(TensuraAdvancementsHelper.Advancements.BETTER_SMELTER, "The better smelter!", "Craft a Kiln.");
      this.addAdvancement(TensuraAdvancementsHelper.Advancements.PIERROT_MASK, "A Clown Troupe!", "Craft a Pierrot Mask.");
      this.addAdvancement(TensuraAdvancementsHelper.Advancements.TOO_STRONG, "It's bad to be too strong sometimes...", "Craft a Dragon Knuckle.");
      this.addAdvancement(TensuraAdvancementsHelper.Advancements.REWIND_TIME, "It's rewind time!", "Use a reset scroll.");
      this.addAdvancement(TensuraAdvancementsHelper.Advancements.MAGIC_SEEDY_PLACE, "A Magic Seedy Place", "Grow a Hipokute Seed.");
      this.addAdvancement(TensuraAdvancementsHelper.Advancements.HIPOKUTE_FLOWER, "Hipokute makes me hiccup-te", "Obtain a Hipokute Flower.");
      this.addAdvancement(TensuraAdvancementsHelper.Advancements.GOOD_AS_NEW, "As good as new!", "Consume a Full Potion.");
      this.addAdvancement(TensuraAdvancementsHelper.Advancements.FAST_LEARNER, "Fast Learner", "Learn an ability.");
      this.addAdvancement(TensuraAdvancementsHelper.Advancements.MASTER_SKILL, "Master what you learnt!", "Master an ability.");
      this.addAdvancement(TensuraAdvancementsHelper.Advancements.MASTER_UNIQUE_SKILL, "Master of one trade!", "Master a Unique Skill.");
      this.addAdvancement(TensuraAdvancementsHelper.Advancements.FORBIDDEN_MANUAL, "The Forbidden Manual!", "Use a Battlewill Manual.");
   }

   protected void addSetting(String id, String message, String... description) {
      this.add("tensura.settings." + id, message);

      for(int i = 0; i < description.length; ++i) {
         this.add("tensura.settings." + id + ".description" + i, description[i]);
      }

   }

   protected void addDeathMessage(String id, String message) {
      this.add("death.attack.tensura." + id, message);
      this.add("death.attack.tensura." + id + ".item", message);
   }

   protected void addDeathMessage(String id, String message, String itemMessage) {
      this.add("death.attack.tensura." + id, message);
      this.add("death.attack.tensura." + id + ".item", itemMessage);
   }

   protected void addDeathMessage(DamageSource damageSource, String message) {
      this.add("death.attack." + damageSource.m_19385_(), message);
      this.add("death.attack." + damageSource.m_19385_() + ".player", message + " whilst fighting %2$s");
      this.add("death.attack." + damageSource.m_19385_() + ".item", message + " whilst fighting %2$s wielding %3$s");
   }

   protected void addElementalDeathMessage(String elemental, String message) {
      this.add("death.attack." + elemental, message);
      this.add("death.attack." + elemental + ".item", message + " using %3$s");
   }

   protected void addDeathMessage(DamageSource damageSource, String message, String sourceMessage) {
      this.add("death.attack." + damageSource.m_19385_(), message);
      this.add("death.attack." + damageSource.m_19385_() + ".player", message + " whilst fighting %2$s");
      this.add("death.attack." + damageSource.m_19385_() + ".item", message + " whilst fighting %2$s wielding %3$s");
      this.add("death.attack." + damageSource.m_19385_() + ".source", sourceMessage);
      this.add("death.attack." + damageSource.m_19385_() + ".source.item", sourceMessage + " using %3$");
   }

   protected void addEffectAndPotions(RegistryObject<? extends MobEffect> effect, String name) {
      this.add((MobEffect)effect.get(), name);
      this.addPotionItems(effect.getId().m_135815_().replace('/', '.'), name);
   }

   protected void addPotionItems(String path, String name) {
      this.add("item.minecraft.potion.effect." + path, "Potion of " + name);
      this.add("item.minecraft.splash_potion.effect." + path, "Splash Potion of " + name);
      this.add("item.minecraft.lingering_potion.effect." + path, "Lingering Potion of " + name);
      this.add("item.minecraft.tipped_arrow.effect." + path, "Arrow of " + name);
   }

   protected void addAdvancement(ResourceLocation location, String name, String description) {
      this.add("tensura.advancements." + location.m_135815_() + ".title", name);
      this.add("tensura.advancements." + location.m_135815_() + ".description", description);
   }

   protected void addEnchantment(Enchantment enchantment, String name, String description) {
      this.add(enchantment, name);
      this.add(enchantment.m_44704_() + ".desc", description);
   }

   protected void addEntity(RegistryObject<? extends EntityType<?>> entity, String name) {
      this.add(String.format("entity.%s.%s", entity.getId().m_135827_(), entity.getId().m_135815_().replace('/', '.')), name);
   }

   protected void addEntityAndSpawnEgg(RegistryObject<? extends EntityType<?>> entity, String name) {
      String path = entity.getId().m_135815_().replace('/', '.');
      this.add(String.format("entity.%s.%s", entity.getId().m_135827_(), path), name);
      this.add(String.format("item.%s.%s_spawn_egg", entity.getId().m_135827_(), path), name + " Spawn Egg");
   }

   protected void addRace(RegistryObject<? extends Race> race, String name, String notes) {
      this.add(String.format("%s.race.%s", race.getId().m_135827_(), race.getId().m_135815_().replace('/', '.')), name);
      this.add(String.format("%s.race.%s.notes", race.getId().m_135827_(), race.getId().m_135815_().replace('/', '.')), notes);
   }

   protected void addSkill(RegistryObject<? extends TensuraSkill> skill, String name) {
      this.addSkill(skill, name, false);
   }

   protected void addSkill(RegistryObject<? extends TensuraSkill> skill, String name, boolean experimental) {
      String desc = "None";
      if (experimental) {
         desc = "Experimental Feature!";
      }

      this.addSkill(skill, name, desc);
   }

   protected void addSkill(RegistryObject<? extends TensuraSkill> skill, String name, String description) {
      this.add(String.format("%s.skill.%s", skill.getId().m_135827_(), skill.getId().m_135815_().replace('/', '.')), name);
      this.add(String.format("%s.skill.%s.description", skill.getId().m_135827_(), skill.getId().m_135815_().replace('/', '.')), description);
   }

   protected void addFluid(RegistryObject<? extends FluidType> fluidType, String name) {
      this.add(String.format("fluid_type.%s.%s", fluidType.getId().m_135827_(), fluidType.getId().m_135815_().replace('/', '.')), name);
   }
}
