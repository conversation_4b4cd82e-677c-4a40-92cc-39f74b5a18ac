package com.github.manasmods.tensura.data.pack;

import com.github.manasmods.tensura.api.magicule.MagiculeModifier;
import com.google.gson.JsonElement;
import com.mojang.serialization.Codec;
import com.mojang.serialization.JsonOps;
import com.mojang.serialization.codecs.RecordCodecBuilder;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.function.BiConsumer;
import java.util.function.Supplier;
import net.minecraft.resources.ResourceLocation;

public record BiomeMagiculeModifier(ResourceLocation biomeId, int priority, List<DataPackMagiculeModifier> modifiers, List<DataPackMagiculeModifier> regenModifiers) implements MagiculeModifier {
   public static final Codec<BiomeMagiculeModifier> CODEC = RecordCodecBuilder.create((instance) -> {
      return instance.group(ResourceLocation.f_135803_.fieldOf("biome").forGetter(BiomeMagiculeModifier::biomeId), Codec.intRange(0, 255).optionalFieldOf("priority", -1).forGetter(BiomeMagiculeModifier::priority), DataPackMagiculeModifier.CODEC.listOf().optionalFieldOf("modifiers", new ArrayList()).forGetter(BiomeMagiculeModifier::modifiers), DataPackMagiculeModifier.CODEC.listOf().optionalFieldOf("regen_modifiers", new ArrayList()).forGetter(BiomeMagiculeModifier::regenModifiers)).apply(instance, BiomeMagiculeModifier::new);
   });

   public BiomeMagiculeModifier(ResourceLocation biomeId, int priority, List<DataPackMagiculeModifier> modifiers, List<DataPackMagiculeModifier> regenModifiers) {
      this.biomeId = biomeId;
      this.priority = priority;
      this.modifiers = modifiers;
      this.regenModifiers = regenModifiers;
   }

   public double getMaxMagicule(double oldMaxMagicule) {
      Iterator var3 = this.modifiers.iterator();

      while(var3.hasNext()) {
         DataPackMagiculeModifier modifier = (DataPackMagiculeModifier)var3.next();
         switch(modifier.mode()) {
         case ADD:
            oldMaxMagicule += modifier.value();
            break;
         case MULTIPLY:
            oldMaxMagicule *= modifier.value();
         }
      }

      return oldMaxMagicule;
   }

   public double getRegenerationRate(double oldRegenerationRate) {
      Iterator var3 = this.regenModifiers.iterator();

      while(var3.hasNext()) {
         DataPackMagiculeModifier modifier = (DataPackMagiculeModifier)var3.next();
         switch(modifier.mode()) {
         case ADD:
            oldRegenerationRate += modifier.value();
            break;
         case MULTIPLY:
            oldRegenerationRate *= modifier.value();
         }
      }

      return oldRegenerationRate;
   }

   public int getPriority() {
      return this.priority;
   }

   public void buildJson(BiConsumer<ResourceLocation, Supplier<JsonElement>> consumer) {
      consumer.accept(this.biomeId, () -> {
         return (JsonElement)CODEC.encodeStart(JsonOps.INSTANCE, this).result().orElseThrow(() -> {
            return new IllegalStateException("Could not serialize " + this);
         });
      });
   }

   public ResourceLocation biomeId() {
      return this.biomeId;
   }

   public int priority() {
      return this.priority;
   }

   public List<DataPackMagiculeModifier> modifiers() {
      return this.modifiers;
   }

   public List<DataPackMagiculeModifier> regenModifiers() {
      return this.regenModifiers;
   }
}
