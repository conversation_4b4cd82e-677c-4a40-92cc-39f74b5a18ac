package com.github.manasmods.tensura.data.pack.Slotting;

import com.mojang.serialization.Codec;
import com.mojang.serialization.codecs.RecordCodecBuilder;
import net.minecraft.resources.ResourceLocation;

public class MagicProjectile {
   public static final Codec<MagicProjectile> CODEC = RecordCodecBuilder.create((instance) -> {
      return instance.group(ResourceLocation.f_135803_.fieldOf("entity").forGetter(MagicProjectile::getEntity), Codec.FLOAT.optionalFieldOf("speed", 0.0F).forGetter(MagicProjectile::getSpeed), Codec.FLOAT.optionalFieldOf("damage", 0.0F).forGetter(MagicProjectile::getDamage), Codec.FLOAT.optionalFieldOf("knockbackForce", 0.0F).forGetter(MagicProjectile::getKnockForce), Codec.FLOAT.optionalFieldOf("explosionRadius", 0.0F).forGetter(MagicProjectile::getExplosionRadius), Codec.INT.optionalFieldOf("burnTicks", 0).forGetter(MagicProjectile::getBurnTicks), Codec.BOOL.optionalFieldOf("noGravity", false).forGetter(MagicProjectile::isNoGravity), MagicEffect.CODEC.optionalFieldOf("effect", noEffect).forGetter(MagicProjectile::getMagicEffect)).apply(instance, MagicProjectile::new);
   });
   private final ResourceLocation entity;
   private final float speed;
   private final float damage;
   private final float knockForce;
   private final float explosionRadius;
   private final int burnTicks;
   private final boolean noGravity;
   private final MagicEffect magicEffect;
   public static final MagicEffect noEffect;
   public static final MagicProjectile empty;

   public MagicProjectile(ResourceLocation entity, float speed, float damage, float knockForce, float explosionRadius, int burnTicks, boolean noGravity, MagicEffect magicEffect) {
      this.entity = entity;
      this.speed = speed;
      this.damage = damage;
      this.knockForce = knockForce;
      this.explosionRadius = explosionRadius;
      this.burnTicks = burnTicks;
      this.noGravity = noGravity;
      this.magicEffect = magicEffect;
   }

   public ResourceLocation getEntity() {
      return this.entity;
   }

   public float getSpeed() {
      return this.speed;
   }

   public float getDamage() {
      return this.damage;
   }

   public float getKnockForce() {
      return this.knockForce;
   }

   public float getExplosionRadius() {
      return this.explosionRadius;
   }

   public int getBurnTicks() {
      return this.burnTicks;
   }

   public boolean isNoGravity() {
      return this.noGravity;
   }

   public MagicEffect getMagicEffect() {
      return this.magicEffect;
   }

   static {
      noEffect = new MagicEffect(MagicEffect.noEffect, 0, 0, 0.0F);
      empty = new MagicProjectile(new ResourceLocation("tensura:none"), 0.0F, 0.0F, 0.0F, 0.0F, 0, false, noEffect);
   }
}
