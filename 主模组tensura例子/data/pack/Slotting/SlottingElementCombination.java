package com.github.manasmods.tensura.data.pack.Slotting;

import com.google.gson.JsonElement;
import com.mojang.serialization.Codec;
import com.mojang.serialization.JsonOps;
import com.mojang.serialization.codecs.RecordCodecBuilder;
import java.util.function.BiConsumer;
import java.util.function.Supplier;
import net.minecraft.resources.ResourceLocation;

public class SlottingElementCombination {
   public static final Codec<SlottingElementCombination> CODEC = RecordCodecBuilder.create((instance) -> {
      return instance.group(Codec.INT.fieldOf("combinationID").forGetter(SlottingElementCombination::getCombinationID), Codec.INT.optionalFieldOf("earthCores", 0).forGetter(SlottingElementCombination::getEarthCores), Codec.INT.optionalFieldOf("fireCores", 0).forGetter(SlottingElementCombination::getFireCores), Codec.INT.optionalFieldOf("spaceCores", 0).forGetter(SlottingElementCombination::getSpaceCores), Codec.INT.optionalFieldOf("waterCores", 0).forGetter(SlottingElementCombination::getWaterCores), Codec.INT.optionalFieldOf("windCores", 0).forGetter(SlottingElementCombination::getWindCores), Codec.DOUBLE.optionalFieldOf("earthMultiplier", 0.0D).forGetter(SlottingElementCombination::getEarthMultiplier), Codec.DOUBLE.optionalFieldOf("fireMultiplier", 0.0D).forGetter(SlottingElementCombination::getFireMultiplier), Codec.DOUBLE.optionalFieldOf("spaceMultiplier", 0.0D).forGetter(SlottingElementCombination::getSpaceMultiplier), Codec.DOUBLE.optionalFieldOf("waterMultiplier", 0.0D).forGetter(SlottingElementCombination::getWaterMultiplier), Codec.DOUBLE.optionalFieldOf("windMultiplier", 0.0D).forGetter(SlottingElementCombination::getWindMultiplier), MagicProjectile.CODEC.optionalFieldOf("magicProjectile", MagicProjectile.empty).forGetter(SlottingElementCombination::getProjectile), Codec.BOOL.optionalFieldOf("dud", false).forGetter(SlottingElementCombination::isDud)).apply(instance, SlottingElementCombination::new);
   });
   private final int combinationID;
   private final int earthCores;
   private final int fireCores;
   private final int spaceCores;
   private final int waterCores;
   private final int windCores;
   private final double earthMultiplier;
   private final double fireMultiplier;
   private final double spaceMultiplier;
   private final double waterMultiplier;
   private final double windMultiplier;
   private final MagicProjectile projectile;
   private final boolean dud;

   public static SlottingElementCombination of(int combinationID, int earthCore, int fireCore, int spaceCore, int waterCore, int windCore, double earthMultiplier, double fireMultiplier, double spaceMultiplier, double waterMultiplier, double windMultiplier, String entity, float speed, float damage, float knockForce, float explosionRadius, int burnTicks, boolean noGravity, String effect, int level, int ticks, float range) {
      MagicProjectile projectile = new MagicProjectile(new ResourceLocation(entity), speed, damage, knockForce, explosionRadius, burnTicks, noGravity, new MagicEffect(new ResourceLocation(effect), level, ticks, range));
      return new SlottingElementCombination(combinationID, earthCore, fireCore, spaceCore, waterCore, windCore, earthMultiplier, fireMultiplier, spaceMultiplier, waterMultiplier, windMultiplier, projectile, false);
   }

   public static SlottingElementCombination of(int combinationID, int earthCore, int fireCore, int spaceCore, int waterCore, int windCore, double earthMultiplier, double fireMultiplier, double spaceMultiplier, double waterMultiplier, double windMultiplier, String entity, float speed, float damage, float knockForce, float explosionRadius, int burnTicks, boolean noGravity, String effect, int level, int ticks) {
      MagicProjectile projectile = new MagicProjectile(new ResourceLocation(entity), speed, damage, knockForce, explosionRadius, burnTicks, noGravity, new MagicEffect(new ResourceLocation(effect), level, ticks, 0.0F));
      return new SlottingElementCombination(combinationID, earthCore, fireCore, spaceCore, waterCore, windCore, earthMultiplier, fireMultiplier, spaceMultiplier, waterMultiplier, windMultiplier, projectile, false);
   }

   public static SlottingElementCombination of(int combinationID, int earthCore, int fireCore, int spaceCore, int waterCore, int windCore, double earthMultiplier, double fireMultiplier, double spaceMultiplier, double waterMultiplier, double windMultiplier, String entity, float speed, float damage, float knockForce, float explosionRadius, int burnTicks, boolean noGravity, String effect, int level) {
      MagicProjectile projectile = new MagicProjectile(new ResourceLocation(entity), speed, damage, knockForce, explosionRadius, burnTicks, noGravity, new MagicEffect(new ResourceLocation(effect), level, 100, 0.0F));
      return new SlottingElementCombination(combinationID, earthCore, fireCore, spaceCore, waterCore, windCore, earthMultiplier, fireMultiplier, spaceMultiplier, waterMultiplier, windMultiplier, projectile, false);
   }

   public static SlottingElementCombination of(int combinationID, int earthCore, int fireCore, int spaceCore, int waterCore, int windCore, double earthMultiplier, double fireMultiplier, double spaceMultiplier, double waterMultiplier, double windMultiplier, String entity, float speed, float damage, float knockForce, float explosionRadius, int burnTicks, boolean noGravity, String effect) {
      MagicProjectile projectile = new MagicProjectile(new ResourceLocation(entity), speed, damage, knockForce, explosionRadius, burnTicks, noGravity, new MagicEffect(new ResourceLocation(effect), 0, 100, 0.0F));
      return new SlottingElementCombination(combinationID, earthCore, fireCore, spaceCore, waterCore, windCore, earthMultiplier, fireMultiplier, spaceMultiplier, waterMultiplier, windMultiplier, projectile, false);
   }

   public static SlottingElementCombination of(int combinationID, int earthCore, int fireCore, int spaceCore, int waterCore, int windCore, double earthMultiplier, double fireMultiplier, double spaceMultiplier, double waterMultiplier, double windMultiplier, String entity, float speed, float damage, float knockForce, float explosionRadius, int burnTicks, boolean noGravity) {
      MagicProjectile projectile = new MagicProjectile(new ResourceLocation(entity), speed, damage, knockForce, explosionRadius, burnTicks, noGravity, MagicProjectile.noEffect);
      return new SlottingElementCombination(combinationID, earthCore, fireCore, spaceCore, waterCore, windCore, earthMultiplier, fireMultiplier, spaceMultiplier, waterMultiplier, windMultiplier, projectile, false);
   }

   public static SlottingElementCombination of(int combinationID, int earthCore, int fireCore, int spaceCore, int waterCore, int windCore, double earthMultiplier, double fireMultiplier, double spaceMultiplier, double waterMultiplier, double windMultiplier, boolean dud) {
      return new SlottingElementCombination(combinationID, earthCore, fireCore, spaceCore, waterCore, windCore, earthMultiplier, fireMultiplier, spaceMultiplier, waterMultiplier, windMultiplier, MagicProjectile.empty, dud);
   }

   public void buildJson(BiConsumer<ResourceLocation, Supplier<JsonElement>> consumer) {
      ResourceLocation combinationResourceLocation = new ResourceLocation("tensura", String.valueOf(this.combinationID));
      consumer.accept(combinationResourceLocation, () -> {
         return (JsonElement)CODEC.encodeStart(JsonOps.INSTANCE, this).result().orElseThrow(() -> {
            return new IllegalStateException("Could not serialize " + this);
         });
      });
   }

   public SlottingElementCombination(int combinationID, int earthCores, int fireCores, int spaceCores, int waterCores, int windCores, double earthMultiplier, double fireMultiplier, double spaceMultiplier, double waterMultiplier, double windMultiplier, MagicProjectile projectile, boolean dud) {
      this.combinationID = combinationID;
      this.earthCores = earthCores;
      this.fireCores = fireCores;
      this.spaceCores = spaceCores;
      this.waterCores = waterCores;
      this.windCores = windCores;
      this.earthMultiplier = earthMultiplier;
      this.fireMultiplier = fireMultiplier;
      this.spaceMultiplier = spaceMultiplier;
      this.waterMultiplier = waterMultiplier;
      this.windMultiplier = windMultiplier;
      this.projectile = projectile;
      this.dud = dud;
   }

   public String toString() {
      int var10000 = this.getCombinationID();
      return "SlottingElementCombination(combinationID=" + var10000 + ", earthCores=" + this.getEarthCores() + ", fireCores=" + this.getFireCores() + ", spaceCores=" + this.getSpaceCores() + ", waterCores=" + this.getWaterCores() + ", windCores=" + this.getWindCores() + ", earthMultiplier=" + this.getEarthMultiplier() + ", fireMultiplier=" + this.getFireMultiplier() + ", spaceMultiplier=" + this.getSpaceMultiplier() + ", waterMultiplier=" + this.getWaterMultiplier() + ", windMultiplier=" + this.getWindMultiplier() + ", projectile=" + this.getProjectile() + ", dud=" + this.isDud() + ")";
   }

   public int getCombinationID() {
      return this.combinationID;
   }

   public int getEarthCores() {
      return this.earthCores;
   }

   public int getFireCores() {
      return this.fireCores;
   }

   public int getSpaceCores() {
      return this.spaceCores;
   }

   public int getWaterCores() {
      return this.waterCores;
   }

   public int getWindCores() {
      return this.windCores;
   }

   public double getEarthMultiplier() {
      return this.earthMultiplier;
   }

   public double getFireMultiplier() {
      return this.fireMultiplier;
   }

   public double getSpaceMultiplier() {
      return this.spaceMultiplier;
   }

   public double getWaterMultiplier() {
      return this.waterMultiplier;
   }

   public double getWindMultiplier() {
      return this.windMultiplier;
   }

   public MagicProjectile getProjectile() {
      return this.projectile;
   }

   public boolean isDud() {
      return this.dud;
   }
}
