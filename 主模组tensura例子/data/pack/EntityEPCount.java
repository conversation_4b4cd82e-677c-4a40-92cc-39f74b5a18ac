package com.github.manasmods.tensura.data.pack;

import com.google.gson.JsonElement;
import com.mojang.serialization.Codec;
import com.mojang.serialization.JsonOps;
import com.mojang.serialization.codecs.RecordCodecBuilder;
import java.util.ArrayList;
import java.util.List;
import java.util.function.BiConsumer;
import java.util.function.Supplier;
import net.minecraft.resources.ResourceLocation;

public class EntityEPCount {
   public static final Codec<EntityEPCount> CODEC = RecordCodecBuilder.create((instance) -> {
      return instance.group(ResourceLocation.f_135803_.fieldOf("entity").forGetter(EntityEPCount::getEntity), Codec.INT.fieldOf("minEP").forGetter(EntityEPCount::getMinEP), Codec.INT.fieldOf("maxEP").forGetter(EntityEPCount::getMaxEP), Codec.list(ResourceLocation.f_135803_).optionalFieldOf("skills", noSkill).forGetter(EntityEPCount::getSkills), ResourceLocation.f_135803_.optionalFieldOf("evolution", noEvo).forGetter(EntityEPCount::getEvolution)).apply(instance, EntityEPCount::new);
   });
   private final ResourceLocation entity;
   private final int minEP;
   private final int maxEP;
   private final List<ResourceLocation> skills;
   private final ResourceLocation evolution;
   public static List<ResourceLocation> noSkill = new ArrayList();
   public static final ResourceLocation noEvo = new ResourceLocation("tensura:none");

   public static EntityEPCount of(ResourceLocation entityType, int minEP, int maxEP) {
      return new EntityEPCount(entityType, minEP, maxEP, noSkill, noEvo);
   }

   public static EntityEPCount of(ResourceLocation entityType, int minEP, int maxEP, List<ResourceLocation> skills) {
      return new EntityEPCount(entityType, minEP, maxEP, skills, noEvo);
   }

   public static EntityEPCount of(ResourceLocation entityType, int minEP, int maxEP, ResourceLocation evo) {
      return new EntityEPCount(entityType, minEP, maxEP, noSkill, evo);
   }

   public static EntityEPCount of(ResourceLocation entityType, int minEP, int maxEP, List<ResourceLocation> skills, ResourceLocation evo) {
      return new EntityEPCount(entityType, minEP, maxEP, skills, evo);
   }

   public void buildJson(BiConsumer<ResourceLocation, Supplier<JsonElement>> consumer) {
      consumer.accept(this.entity, () -> {
         return (JsonElement)CODEC.encodeStart(JsonOps.INSTANCE, this).result().orElseThrow(() -> {
            return new IllegalStateException("Could not serialize " + this);
         });
      });
   }

   public EntityEPCount(ResourceLocation entity, int minEP, int maxEP, List<ResourceLocation> skills, ResourceLocation evolution) {
      this.entity = entity;
      this.minEP = minEP;
      this.maxEP = maxEP;
      this.skills = skills;
      this.evolution = evolution;
   }

   public String toString() {
      ResourceLocation var10000 = this.getEntity();
      return "EntityEPCount(entity=" + var10000 + ", minEP=" + this.getMinEP() + ", maxEP=" + this.getMaxEP() + ", skills=" + this.getSkills() + ", evolution=" + this.getEvolution() + ")";
   }

   public ResourceLocation getEntity() {
      return this.entity;
   }

   public int getMinEP() {
      return this.minEP;
   }

   public int getMaxEP() {
      return this.maxEP;
   }

   public List<ResourceLocation> getSkills() {
      return this.skills;
   }

   public ResourceLocation getEvolution() {
      return this.evolution;
   }
}
