package com.github.manasmods.tensura.data.pack;

import com.google.gson.JsonElement;
import com.mojang.serialization.Codec;
import com.mojang.serialization.JsonOps;
import com.mojang.serialization.codecs.RecordCodecBuilder;
import java.awt.Color;
import java.util.function.BiConsumer;
import java.util.function.Supplier;
import net.minecraft.resources.ResourceLocation;

public class KilnMoltenMaterial {
   public static final Codec<KilnMoltenMaterial> CODEC = RecordCodecBuilder.create((instance) -> {
      return instance.group(Codec.BOOL.fieldOf("rightBar").forGetter(KilnMoltenMaterial::isRightBar), ResourceLocation.f_135803_.fieldOf("moltenType").forGetter(KilnMoltenMaterial::getMoltenType), Codec.INT.fieldOf("red").forGetter(KilnMoltenMaterial::getRed), Codec.INT.fieldOf("green").forGetter(KilnMoltenMaterial::getGreen), Codec.INT.fieldOf("blue").forGetter(KilnMoltenMaterial::getBlue), Codec.INT.fieldOf("alpha").forGetter(KilnMoltenMaterial::getAlpha)).apply(instance, KilnMoltenMaterial::new);
   });
   private final boolean rightBar;
   private final ResourceLocation moltenType;
   private final int red;
   private final int green;
   private final int blue;
   private final int alpha;

   public static KilnMoltenMaterial of(ResourceLocation moltenType, boolean isRightBar, Color color) {
      return new KilnMoltenMaterial(isRightBar, moltenType, color.getRed(), color.getGreen(), color.getBlue(), color.getAlpha());
   }

   public void buildJson(BiConsumer<ResourceLocation, Supplier<JsonElement>> consumer) {
      consumer.accept(this.moltenType, () -> {
         return (JsonElement)CODEC.encodeStart(JsonOps.INSTANCE, this).result().orElseThrow(() -> {
            return new IllegalStateException("Could not serialize " + this);
         });
      });
   }

   public KilnMoltenMaterial(boolean rightBar, ResourceLocation moltenType, int red, int green, int blue, int alpha) {
      this.rightBar = rightBar;
      this.moltenType = moltenType;
      this.red = red;
      this.green = green;
      this.blue = blue;
      this.alpha = alpha;
   }

   public String toString() {
      boolean var10000 = this.isRightBar();
      return "KilnMoltenMaterial(rightBar=" + var10000 + ", moltenType=" + this.getMoltenType() + ", red=" + this.getRed() + ", green=" + this.getGreen() + ", blue=" + this.getBlue() + ", alpha=" + this.getAlpha() + ")";
   }

   public boolean isRightBar() {
      return this.rightBar;
   }

   public ResourceLocation getMoltenType() {
      return this.moltenType;
   }

   public int getRed() {
      return this.red;
   }

   public int getGreen() {
      return this.green;
   }

   public int getBlue() {
      return this.blue;
   }

   public int getAlpha() {
      return this.alpha;
   }
}
