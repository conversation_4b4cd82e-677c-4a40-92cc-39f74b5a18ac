package com.github.manasmods.tensura.menu;

import com.github.manasmods.tensura.menu.container.DecraftingContainer;
import com.github.manasmods.tensura.menu.container.TensuraCraftingContainer;
import com.github.manasmods.tensura.menu.slot.DecraftingSlot;
import com.github.manasmods.tensura.menu.slot.DegenerateSlot;
import com.github.manasmods.tensura.registry.menu.TensuraMenuTypes;
import java.util.Optional;
import net.minecraft.network.FriendlyByteBuf;
import net.minecraft.network.protocol.game.ClientboundContainerSetSlotPacket;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.Container;
import net.minecraft.world.entity.player.Inventory;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.inventory.AbstractContainerMenu;
import net.minecraft.world.inventory.ContainerLevelAccess;
import net.minecraft.world.inventory.CraftingContainer;
import net.minecraft.world.inventory.MenuType;
import net.minecraft.world.inventory.ResultContainer;
import net.minecraft.world.inventory.Slot;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.crafting.CraftingRecipe;
import net.minecraft.world.item.crafting.RecipeType;
import net.minecraft.world.level.Level;

public class DegenerateCraftingMenu extends AbstractContainerMenu {
   private static final int HOTBAR_SLOT_COUNT = 9;
   private static final int INVENTORY_ROW_COUNT = 3;
   private static final int INVENTORY_COLUMN_COUNT = 9;
   private static final int PLAYER_INVENTORY_SLOT_COUNT = 27;
   private static final int VANILLA_SLOT_COUNT = 36;
   private final Player player;
   private final Slot resultSlot;
   public final TensuraCraftingContainer craftSlots;
   public final DecraftingContainer decraftSlots;
   public final ResultContainer resultContainer;
   private final ContainerLevelAccess access;

   public DegenerateCraftingMenu(int pContainerId, Inventory inventory, FriendlyByteBuf buf) {
      this(pContainerId, inventory, ContainerLevelAccess.f_39287_);
   }

   public DegenerateCraftingMenu(int pContainerId, Inventory inventory, ContainerLevelAccess access) {
      super((MenuType)TensuraMenuTypes.DEGENERATE_CRAFTING_MENU.get(), pContainerId);
      this.resultContainer = new ResultContainer();
      this.access = access;
      this.player = inventory.f_35978_;
      this.addPlayerInventorySlots(inventory);
      this.resultSlot = new DegenerateSlot(inventory.f_35978_, this, this.resultContainer, 0, 97, 65);
      this.m_38897_(this.resultSlot);
      this.craftSlots = new TensuraCraftingContainer(this, 3, 3);
      this.decraftSlots = new DecraftingContainer(this, 3, 3);
      this.addGrids();
   }

   public void m_6877_(Player pPlayer) {
      this.access.m_39292_((level, pos) -> {
         this.m_150411_(pPlayer, this.craftSlots);
         if (!this.resultSlot.m_6657_()) {
            this.m_150411_(pPlayer, this.decraftSlots);
         } else {
            if (!this.craftSlots.canPlace()) {
               this.m_150411_(pPlayer, this.resultContainer);
            }

            this.decraftSlots.m_6211_();
         }

      });
      super.m_6877_(pPlayer);
   }

   public boolean m_5882_(ItemStack pStack, Slot pSlot) {
      return pSlot.f_40218_ != this.resultContainer && super.m_5882_(pStack, pSlot);
   }

   protected static void slotChangedCraftingGrid(AbstractContainerMenu pMenu, Level pLevel, Player pPlayer, CraftingContainer pContainer, ResultContainer pResult) {
      if (!pLevel.m_5776_()) {
         MinecraftServer server = pLevel.m_7654_();
         if (server != null) {
            ServerPlayer serverPlayer = (ServerPlayer)pPlayer;
            ItemStack stack = ItemStack.f_41583_;
            Optional<CraftingRecipe> optional = server.m_129894_().m_44015_(RecipeType.f_44107_, pContainer, pLevel);
            if (optional.isPresent()) {
               CraftingRecipe recipe = (CraftingRecipe)optional.get();
               if (pResult.m_40135_(pLevel, serverPlayer, recipe)) {
                  stack = recipe.m_5874_(pContainer);
               }
            }

            pResult.m_6836_(0, stack);
            pMenu.m_150404_(0, stack);
            serverPlayer.f_8906_.m_9829_(new ClientboundContainerSetSlotPacket(pMenu.f_38840_, pMenu.m_182425_(), 0, stack));
         }
      }
   }

   public void m_6199_(Container pInventory) {
      super.m_6199_(pInventory);
      if (this.craftSlots.canPlace()) {
         this.access.m_39292_((level, pos) -> {
            slotChangedCraftingGrid(this, level, this.player, this.craftSlots, this.resultContainer);
         });
      }

   }

   public boolean m_6875_(Player pPlayer) {
      return true;
   }

   public boolean check() {
      return true;
   }

   private void addPlayerInventorySlots(Inventory playerInventory) {
      int i;
      for(i = 0; i < 3; ++i) {
         for(int l = 0; l < 9; ++l) {
            this.m_38897_(new Slot(playerInventory, l + i * 9 + 9, 25 + l * 18, 117 + i * 18));
         }
      }

      for(i = 0; i < 9; ++i) {
         this.m_38897_(new Slot(playerInventory, i, 25 + i * 18, 175));
      }

   }

   private void addGrids() {
      int row;
      int column;
      for(row = 0; row < 3; ++row) {
         for(column = 0; column < 3; ++column) {
            this.m_38897_(new Slot(this.craftSlots, column + row * 3, 20 + column * 18, 47 + row * 18) {
               public boolean m_5857_(ItemStack pStack) {
                  if (!DegenerateCraftingMenu.this.resultSlot.m_6657_() && DegenerateCraftingMenu.this.decraftSlots.m_7983_()) {
                     if (!DegenerateCraftingMenu.this.craftSlots.canPlace()) {
                        DegenerateCraftingMenu.this.craftSlots.setCanPlace(true);
                     }

                     return true;
                  } else {
                     return DegenerateCraftingMenu.this.craftSlots.canPlace();
                  }
               }
            });
         }
      }

      for(row = 0; row < 3; ++row) {
         for(column = 0; column < 3; ++column) {
            this.m_38897_(new DecraftingSlot(this.player, this, this.decraftSlots, column + row * 3, 138 + column * 18, 47 + row * 18));
         }
      }

   }

   public ItemStack m_7648_(Player pPlayer, int index) {
      Slot sourceSlot = (Slot)this.f_38839_.get(index);
      if (sourceSlot == null) {
         return ItemStack.f_41583_;
      } else if (!sourceSlot.m_6657_()) {
         return ItemStack.f_41583_;
      } else {
         ItemStack sourceStack = sourceSlot.m_7993_();
         ItemStack copyStack = sourceStack.m_41777_();
         if (index < 27) {
            return !this.m_38903_(sourceStack, 27, 36, false) ? ItemStack.f_41583_ : TensuraMenuHelper.quickMoveStack(pPlayer, sourceStack, sourceSlot, copyStack);
         } else if (index < 36) {
            return !this.m_38903_(sourceStack, 0, 27, false) ? ItemStack.f_41583_ : TensuraMenuHelper.quickMoveStack(pPlayer, sourceStack, sourceSlot, copyStack);
         } else {
            return ItemStack.f_41583_;
         }
      }
   }

   public ContainerLevelAccess getAccess() {
      return this.access;
   }
}
