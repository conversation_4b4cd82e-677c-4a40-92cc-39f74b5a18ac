package com.github.manasmods.tensura.menu;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.manascore.api.skills.capability.SkillStorage;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.TensuraSkill;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.ability.skill.resist.ResistSkill;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.capability.race.ITensuraPlayerCapability;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.config.TensuraConfig;
import com.github.manasmods.tensura.effect.template.MobEffectHelper;
import com.github.manasmods.tensura.race.Race;
import com.github.manasmods.tensura.race.RaceHelper;
import com.github.manasmods.tensura.registry.menu.TensuraMenuTypes;
import com.github.manasmods.tensura.registry.race.TensuraRaces;
import com.github.manasmods.tensura.registry.skill.ResistanceSkills;
import com.github.manasmods.tensura.registry.skill.UniqueSkills;
import com.github.manasmods.tensura.util.TensuraAdvancementsHelper;
import com.github.manasmods.tensura.world.TensuraGameRules;
import com.github.manasmods.tensura.world.savedata.UniqueSkillSaveData;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import javax.annotation.Nullable;
import net.minecraft.ChatFormatting;
import net.minecraft.network.FriendlyByteBuf;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.Style;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.Pose;
import net.minecraft.world.entity.player.Inventory;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.inventory.AbstractContainerMenu;
import net.minecraft.world.inventory.DataSlot;
import net.minecraft.world.inventory.MenuType;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.level.Level;
import net.minecraftforge.registries.IForgeRegistry;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class RaceSelectionMenu extends AbstractContainerMenu {
   private static final Logger log = LogManager.getLogger(RaceSelectionMenu.class);
   public final DataSlot selectedRaceIndex;
   public List<Race> registeredRaces;
   public static final int SUBMIT_BUTTON_ID = -1;
   public static final int RACE_ONLY_SUBMIT_ID = -2;
   private final Player player;
   private boolean raceOnly;
   public static final List<TensuraSkill> rimuruSkills;

   public RaceSelectionMenu(int pContainerId, Inventory inventory, FriendlyByteBuf buf) {
      this(pContainerId, inventory, inventory.f_35978_);
      this.raceOnly = buf.readBoolean();
      this.registeredRaces = buf.m_236845_(FriendlyByteBuf::m_130281_).stream().map((resourceLocation) -> {
         return (Race)((IForgeRegistry)TensuraRaces.RACE_REGISTRY.get()).getValue(resourceLocation);
      }).filter(Objects::nonNull).toList();
   }

   public RaceSelectionMenu(int pContainerId, Inventory inventory, Player player) {
      super((MenuType)TensuraMenuTypes.RACE_SELECTION.get(), pContainerId);
      this.selectedRaceIndex = DataSlot.m_39401_();
      this.raceOnly = false;
      this.player = player;
      this.m_38895_(this.selectedRaceIndex).m_6422_(0);
      this.registeredRaces = TensuraPlayerCapability.loadRaces().stream().map((resourceLocation) -> {
         return (Race)((IForgeRegistry)TensuraRaces.RACE_REGISTRY.get()).getValue(resourceLocation);
      }).filter(Objects::nonNull).toList();
   }

   public boolean m_6875_(Player pPlayer) {
      return true;
   }

   public boolean m_6366_(Player pPlayer, int pId) {
      if (this.isValidRaceIndex(pId)) {
         this.selectedRaceIndex.m_6422_(pId);
         return true;
      } else {
         if (pId == -1) {
            try {
               Race race = (Race)this.registeredRaces.get(this.selectedRaceIndex.m_6501_());
               setRace(pPlayer, race, true, !pPlayer.m_9236_().m_46469_().m_46207_(TensuraGameRules.SKILL_BEFORE_RACE));
               return true;
            } catch (NullPointerException | NoClassDefFoundError | IndexOutOfBoundsException var5) {
               log.error(var5);
            }
         } else if (pId == -2) {
            try {
               setRace(pPlayer, (Race)this.registeredRaces.get(this.selectedRaceIndex.m_6501_()), true, false);
               return true;
            } catch (NullPointerException | NoClassDefFoundError | IndexOutOfBoundsException var4) {
               log.error(var4);
            }
         }

         return super.m_6366_(pPlayer, pId);
      }
   }

   public static void setRace(Player pPlayer, Race race, boolean resetEP, boolean grantUnique) {
      if (!pPlayer.m_9236_().m_5776_()) {
         TensuraPlayerCapability.getFrom(pPlayer).ifPresent((cap) -> {
            cap.setRace(pPlayer, race, resetEP);
            cap.setTrackedEvolution(pPlayer, (Race)null);
            cap.setSpiritualForm(MobEffectHelper.inSpiritualWorld(race.getRespawnDimension()));
            applySpiritBlessingChance(pPlayer, cap);
            if (!race.getIntrinsicSkills(pPlayer).isEmpty()) {
               Iterator var5 = race.getIntrinsicSkills(pPlayer).iterator();

               while(var5.hasNext()) {
                  ManasSkill skill = (ManasSkill)var5.next();
                  TensuraSkillInstance instance = new TensuraSkillInstance(skill);
                  if (SkillUtils.learnSkill(pPlayer, (ManasSkillInstance)instance)) {
                     cap.addIntrinsicSkill(instance.getSkill());
                     pPlayer.m_5661_(Component.m_237110_("tensura.skill.acquire", new Object[]{skill.getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GOLD)), false);
                     if (instance.canBeToggled(pPlayer)) {
                        instance.setToggled(true);
                        instance.onToggleOn(pPlayer);
                     }
                  }
               }
            }

            if (grantUnique) {
               grantUniqueSkill(pPlayer);
            }

         });
         TensuraPlayerCapability.sync(pPlayer);
         TensuraEPCapability.updateEP(pPlayer);
         pPlayer.m_20331_(false);
         pPlayer.m_6210_();
         Pose pose = pPlayer.m_20089_();
         pPlayer.m_20124_(Pose.CROUCHING);
         pPlayer.m_20124_(pose);
         if (pPlayer instanceof ServerPlayer) {
            ServerPlayer player = (ServerPlayer)pPlayer;
            TensuraAdvancementsHelper.grant(player, TensuraAdvancementsHelper.Advancements.REINCARNATED);
         }

         RaceHelper.handleRespawnDimension(pPlayer, race);
      }
   }

   public ItemStack m_7648_(Player pPlayer, int pIndex) {
      return ItemStack.f_41583_;
   }

   private boolean isValidRaceIndex(int pId) {
      if (pId < 0) {
         return false;
      } else {
         return pId < this.registeredRaces.size();
      }
   }

   public static void grantUniqueSkill(Player player) {
      if (player.m_9236_().m_46469_().m_46207_(TensuraGameRules.NO_UNIQUE_START)) {
         int skills = (Integer)TensuraConfig.INSTANCE.skillsConfig.skillNumber.get();
         double cost = (double)(10000 * skills * player.f_19853_.m_46469_().m_46215_(TensuraGameRules.MP_SKILL_COST)) / 100.0D;
         TensuraPlayerCapability.getFrom(player).ifPresent((cap) -> {
            cap.setBaseMagicule(cap.getBaseMagicule() + cost, player);
            cap.setBaseAura(cap.getBaseAura() + cost, player);
            TensuraPlayerCapability.sync(player);
         });
      } else {
         randomUniqueSkill(player, true);
      }
   }

   public static void randomUniqueSkill(Player player, boolean coverEP) {
      Level var3 = player.m_9236_();
      if (var3 instanceof ServerLevel) {
         ServerLevel level = (ServerLevel)var3;
         int skills = (Integer)TensuraConfig.INSTANCE.skillsConfig.skillNumber.get();
         int counterGamerule = level.m_46469_().m_46215_(TensuraGameRules.RESET_COUNTER_BONUS_UNIQUE);
         int bonusReset = counterGamerule <= 0 ? 0 : Math.min(TensuraPlayerCapability.getResetCounter(player) / counterGamerule, (Integer)TensuraConfig.INSTANCE.skillsConfig.maxCounterBonus.get());

         for(int i = 1; i <= skills + bonusReset && (i < bonusReset + 2 || !((double)player.m_217043_().m_188503_(100) > (Double)TensuraConfig.INSTANCE.skillsConfig.additionalUniqueChance.get())); ++i) {
            List<ManasSkill> collection = getReincarnationSkills(level, false, player);
            if (skills == 2 && i == bonusReset + 2) {
               collection = getSecondReincarnationSkills(level, false);
            }

            if (!collection.isEmpty()) {
               ManasSkill manasSkill = (ManasSkill)collection.get(player.m_217043_().m_188503_(collection.size()));
               TensuraSkillInstance instance = new TensuraSkillInstance(manasSkill);
               if (coverEP) {
                  instance.getOrCreateTag().m_128379_("NoMagiculeCost", true);
               }

               if (SkillAPI.getSkillsFrom(player).learnSkill(instance)) {
                  player.m_5661_(Component.m_237110_("tensura.skill.acquire", new Object[]{manasSkill.getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GOLD)), false);
                  UniqueSkillSaveData.get(level.m_7654_().m_129783_()).addSkill(manasSkill.getRegistryName(), player.m_20148_());
               }
            }
         }

      }
   }

   public static List<ManasSkill> getReincarnationSkills(ServerLevel level, boolean ignoreGameRule, @Nullable Player roller) {
      return ((List)TensuraConfig.INSTANCE.skillsConfig.reincarnationSkills.get()).stream().filter((skill) -> {
         if (ignoreGameRule) {
            return true;
         } else if (!level.m_46469_().m_46207_(TensuraGameRules.TRULY_UNIQUE)) {
            return true;
         } else {
            return !UniqueSkillSaveData.get(level.m_7654_().m_129783_()).hasSkill(new ResourceLocation(skill));
         }
      }).map((skill) -> {
         return (ManasSkill)SkillAPI.getSkillRegistry().getValue(new ResourceLocation(skill));
      }).filter((skill) -> {
         if (skill == null) {
            return false;
         } else {
            return roller == null || !SkillUtils.hasSkill(roller, skill);
         }
      }).toList();
   }

   public static List<ManasSkill> getSecondReincarnationSkills(ServerLevel level, boolean ignoreGameRule) {
      return ((List)TensuraConfig.INSTANCE.skillsConfig.secondSkills.get()).stream().filter((manasSkill) -> {
         if (!ignoreGameRule && !level.m_46469_().m_46207_(TensuraGameRules.TRULY_UNIQUE)) {
            return true;
         } else {
            return !UniqueSkillSaveData.get(level.m_7654_().m_129783_()).hasSkill(new ResourceLocation(manasSkill));
         }
      }).map((skill) -> {
         return (ManasSkill)SkillAPI.getSkillRegistry().getValue(new ResourceLocation(skill));
      }).filter(Objects::nonNull).toList();
   }

   public static void reincarnateAsRimuru(Player pPlayer) {
      TensuraPlayerCapability.getFrom(pPlayer).ifPresent((cap) -> {
         Race race = (Race)TensuraRaces.SLIME.get();
         cap.setRace(pPlayer, race, true);
         cap.setTrackedEvolution(pPlayer, (Race)null);
         if (!race.getIntrinsicSkills(pPlayer).isEmpty()) {
            Iterator var3 = race.getIntrinsicSkills(pPlayer).iterator();

            while(var3.hasNext()) {
               ManasSkill skill = (ManasSkill)var3.next();
               TensuraSkillInstance instance = new TensuraSkillInstance(skill);
               if (SkillUtils.learnSkill(pPlayer, (ManasSkillInstance)instance)) {
                  cap.addIntrinsicSkill(instance.getSkill());
                  pPlayer.m_5661_(Component.m_237110_("tensura.skill.acquire", new Object[]{skill.getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GOLD)), false);
                  if (instance.canBeToggled(pPlayer)) {
                     instance.setToggled(true);
                     instance.onToggleOn(pPlayer);
                  }
               }
            }
         }

      });
      TensuraPlayerCapability.sync(pPlayer);
      TensuraEPCapability.updateEP(pPlayer);
      if (!pPlayer.m_9236_().m_5776_()) {
         Iterator var1 = rimuruSkills.iterator();

         while(var1.hasNext()) {
            ManasSkill skill = (ManasSkill)var1.next();
            TensuraSkillInstance instance = new TensuraSkillInstance(skill);
            instance.getOrCreateTag().m_128379_("NoMagiculeCost", true);
            if (SkillUtils.learnSkill(pPlayer, (ManasSkillInstance)instance)) {
               pPlayer.m_5661_(Component.m_237110_("tensura.skill.acquire", new Object[]{skill.getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GOLD)), false);
            }
         }
      }

      if (pPlayer instanceof ServerPlayer) {
         ServerPlayer player = (ServerPlayer)pPlayer;
         TensuraAdvancementsHelper.grant(player, TensuraAdvancementsHelper.Advancements.REINCARNATED);
      }

   }

   public static void applySpiritBlessingChance(Player player, ITensuraPlayerCapability cap) {
      if (cap.getRace() == null || !cap.getRace().isMajin()) {
         if ((double)(player.m_217043_().m_188501_() * 100.0F) <= (Double)TensuraConfig.INSTANCE.awakeningConfig.blessedPercentage.get()) {
            cap.setBlessed(true);
            TensuraPlayerCapability.sync(player);
         }

      }
   }

   public static void grantLearningResistance(LivingEntity entity) {
      if (!entity.m_9236_().m_5776_()) {
         SkillStorage storage = SkillAPI.getSkillsFrom(entity);
         Iterator var2 = SkillAPI.getSkillRegistry().getValues().iterator();

         while(var2.hasNext()) {
            ManasSkill skill = (ManasSkill)var2.next();
            if (!storage.getSkill(skill).isPresent() && skill instanceof ResistSkill) {
               ResistSkill resist = (ResistSkill)skill;
               if (!resist.getResistType().equals(ResistSkill.ResistType.NULLIFICATION) && resist.pointRequirement() != -1) {
                  ManasSkillInstance instance = new TensuraSkillInstance(skill);
                  instance.setMastery(resist.pointRequirement() * -1);
                  storage.learnSkill(instance);
                  storage.syncChanges();
               }
            }
         }

      }
   }

   public Player getPlayer() {
      return this.player;
   }

   public boolean isRaceOnly() {
      return this.raceOnly;
   }

   static {
      rimuruSkills = List.of((TensuraSkill)UniqueSkills.PREDATOR.get(), (TensuraSkill)UniqueSkills.GREAT_SAGE.get(), (TensuraSkill)ResistanceSkills.THERMAL_FLUCTUATION_RESISTANCE.get(), (TensuraSkill)ResistanceSkills.ELECTRICITY_RESISTANCE.get(), (TensuraSkill)ResistanceSkills.PIERCE_RESISTANCE.get(), (TensuraSkill)ResistanceSkills.PAIN_NULLIFICATION.get(), (TensuraSkill)ResistanceSkills.PHYSICAL_ATTACK_RESISTANCE.get());
   }
}
