package com.github.manasmods.tensura.menu.slot;

import com.github.manasmods.tensura.menu.DegenerateCraftingMenu;
import net.minecraft.core.NonNullList;
import net.minecraft.world.Container;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.inventory.Slot;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.crafting.RecipeType;
import net.minecraftforge.common.ForgeHooks;
import net.minecraftforge.event.ForgeEventFactory;
import org.jetbrains.annotations.NotNull;

public class DegenerateSlot extends Slot {
   private final DegenerateCraftingMenu menu;
   private final Player player;
   private int removeCount;

   public DegenerateSlot(Player pPlayer, DegenerateCraftingMenu menu, Container pContainer, int pSlot, int xPosition, int yPosition) {
      super(pContainer, pSlot, xPosition, yPosition);
      this.player = pPlayer;
      this.menu = menu;
   }

   public int m_6641_() {
      return 1;
   }

   public boolean m_5857_(@NotNull ItemStack stack) {
      return !this.menu.craftSlots.m_7983_() ? false : this.menu.decraftSlots.m_7983_();
   }

   public void m_5852_(ItemStack pStack) {
      super.m_5852_(pStack);
      if (this.menu.craftSlots.m_7983_()) {
         this.menu.craftSlots.setCanPlace(false);
         this.menu.decraftSlots.updateSlots(pStack, this.player.m_9236_());
      }

   }

   public void m_142406_(Player pPlayer, ItemStack pStack) {
      if (!this.menu.decraftSlots.m_7983_()) {
         this.menu.decraftSlots.m_6211_();
         this.menu.craftSlots.setCanPlace(true);
      } else {
         this.m_5845_(pStack);
         ForgeHooks.setCraftingPlayer(pPlayer);
         NonNullList<ItemStack> list = pPlayer.f_19853_.m_7465_().m_44069_(RecipeType.f_44107_, this.menu.craftSlots, pPlayer.f_19853_);
         ForgeHooks.setCraftingPlayer((Player)null);

         for(int i = 0; i < list.size(); ++i) {
            ItemStack slotStack = this.menu.craftSlots.m_8020_(i);
            ItemStack recipeStack = (ItemStack)list.get(i);
            if (!slotStack.m_41619_()) {
               this.menu.craftSlots.m_7407_(i, 1);
               slotStack = this.menu.craftSlots.m_8020_(i);
            }

            if (!recipeStack.m_41619_()) {
               if (slotStack.m_41619_()) {
                  this.menu.craftSlots.m_6836_(i, recipeStack);
               } else if (ItemStack.m_41746_(slotStack, recipeStack) && ItemStack.m_41658_(slotStack, recipeStack)) {
                  recipeStack.m_41769_(slotStack.m_41613_());
                  this.menu.craftSlots.m_6836_(i, recipeStack);
               } else if (!this.player.m_150109_().m_36054_(recipeStack)) {
                  this.player.m_36176_(recipeStack, false);
               }
            }
         }

      }
   }

   @NotNull
   public ItemStack m_6201_(int pAmount) {
      if (this.m_6657_()) {
         this.removeCount += Math.min(pAmount, this.m_7993_().m_41613_());
      }

      return super.m_6201_(pAmount);
   }

   protected void m_7169_(ItemStack pStack, int pAmount) {
      this.removeCount += pAmount;
      this.m_5845_(pStack);
   }

   protected void m_6405_(int pNumItemsCrafted) {
      this.removeCount += pNumItemsCrafted;
   }

   protected void m_5845_(ItemStack pStack) {
      if (this.removeCount > 0) {
         pStack.m_41678_(this.player.f_19853_, this.player, this.removeCount);
         ForgeEventFactory.firePlayerCraftingEvent(this.player, pStack, this.menu.craftSlots);
      }

      this.removeCount = 0;
   }
}
