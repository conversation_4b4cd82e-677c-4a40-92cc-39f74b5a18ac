package com.github.manasmods.tensura.menu;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.tensura.ability.ISpatialStorage;
import com.github.manasmods.tensura.data.recipe.GreatSageRefiningRecipe;
import com.github.manasmods.tensura.menu.container.SpatialStorageContainer;
import com.github.manasmods.tensura.registry.recipe.TensuraRecipeTypes;
import java.util.Optional;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.protocol.game.ClientboundContainerSetSlotPacket;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.SimpleContainer;
import net.minecraft.world.entity.player.Inventory;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.inventory.AbstractContainerMenu;
import net.minecraft.world.inventory.ContainerLevelAccess;
import net.minecraft.world.inventory.MenuType;
import net.minecraft.world.inventory.ResultContainer;
import net.minecraft.world.inventory.Slot;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.crafting.RecipeType;
import net.minecraft.world.level.Level;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.jetbrains.annotations.Nullable;

public class GreatSageRefiningMenu extends AbstractContainerMenu {
   private static final Logger log = LogManager.getLogger(GreatSageRefiningMenu.class);
   private final ManasSkill skill;
   private final Player player;
   private final Slot resultSlot;
   public final SpatialStorageContainer brewingContainer;
   public final ResultContainer resultContainer = new ResultContainer();
   private final ContainerLevelAccess access;

   public GreatSageRefiningMenu(int id, Inventory inv, Player player, SpatialStorageContainer container, ManasSkill skill, ContainerLevelAccess access) {
      super((MenuType)null, id);
      this.skill = skill;
      this.access = access;
      this.player = player;
      this.addPlayerInventory(inv);
      this.addPlayerHotBar(inv);
      this.brewingContainer = new SpatialStorageContainer(8, 128);

      for(int i = 0; i < 8; ++i) {
         this.brewingContainer.m_6836_(i, container.m_8020_(i + 11));
      }

      this.addSpatialSlots();
      this.resultSlot = new Slot(this.resultContainer, 0, 219, 56) {
         public boolean m_5857_(ItemStack pStack) {
            return false;
         }
      };
      this.m_38897_(this.resultSlot);
      this.resultContainer.m_6836_(0, container.m_8020_(19));
   }

   private void addPlayerInventory(Inventory inventory) {
      for(int row = 0; row < 3; ++row) {
         for(int slot = 0; slot < 9; ++slot) {
            this.m_38897_(new Slot(inventory, slot + row * 9 + 9, 45 + slot * 18, 110 + row * 18));
         }
      }

   }

   private void addPlayerHotBar(Inventory inventory) {
      for(int i = 0; i < 9; ++i) {
         this.m_38897_(new Slot(inventory, i, 45 + i * 18, 168));
      }

   }

   private void addSpatialSlots() {
      this.m_38897_(new Slot(this.brewingContainer, 0, 10, 38));
      this.m_38897_(new Slot(this.brewingContainer, 1, 10, 56));
      this.m_38897_(new Slot(this.brewingContainer, 2, 10, 74));
      this.m_38897_(new Slot(this.brewingContainer, 3, 41, 56));
      this.m_38897_(new Slot(this.brewingContainer, 4, 69, 56));
      this.m_38897_(new Slot(this.brewingContainer, 5, 97, 56));
      this.m_38897_(new Slot(this.brewingContainer, 6, 125, 56));
      this.m_38897_(new Slot(this.brewingContainer, 7, 153, 56));
   }

   public boolean m_6875_(Player player) {
      return player.m_6084_();
   }

   public boolean check() {
      return true;
   }

   @Nullable
   public ManasSkillInstance getSkillInstance(Player player) {
      Optional<ManasSkillInstance> optional = SkillAPI.getSkillsFrom(player).getSkill(this.skill);
      return (ManasSkillInstance)optional.orElse((Object)null);
   }

   public void m_6877_(Player pPlayer) {
      ManasSkillInstance instance = this.getSkillInstance(pPlayer);
      if (instance != null) {
         ManasSkill var4 = instance.getSkill();
         if (var4 instanceof ISpatialStorage) {
            ISpatialStorage spatialStorage = (ISpatialStorage)var4;

            for(int i = 0; i < 8; ++i) {
               spatialStorage.setItemInSpatialStorage(instance, pPlayer, this.brewingContainer.m_8020_(i), i + 11);
            }

            spatialStorage.setItemInSpatialStorage(instance, pPlayer, this.resultContainer.m_8020_(0), 19);
         }
      }

      super.m_6877_(pPlayer);
   }

   public boolean m_6366_(Player pPlayer, int pId) {
      ManasSkillInstance instance;
      CompoundTag tag;
      boolean repeating;
      if (pId == 1) {
         instance = this.getSkillInstance(pPlayer);
         if (instance != null) {
            tag = instance.getOrCreateTag();
            repeating = tag.m_128471_("RepeatBrewing");
            tag.m_128379_("RepeatBrewing", !repeating);
            tag.m_128379_("Brewing", !repeating);
            instance.markDirty();
            SkillAPI.getSkillsFrom(pPlayer).syncChanges();
         }

         return true;
      } else if (pId == 2) {
         instance = this.getSkillInstance(pPlayer);
         if (instance != null) {
            tag = instance.getOrCreateTag();
            repeating = tag.m_128471_("Brewing");
            tag.m_128379_("Brewing", !repeating);
            instance.markDirty();
            SkillAPI.getSkillsFrom(pPlayer).syncChanges();
         }

         return true;
      } else {
         return super.m_6366_(pPlayer, pId);
      }
   }

   public static boolean autoRefining(GreatSageRefiningMenu pMenu, Level pLevel, Player pPlayer, SimpleContainer pContainer, ResultContainer pResult) {
      if (pLevel.m_5776_()) {
         return false;
      } else {
         MinecraftServer server = pLevel.m_7654_();
         if (server == null) {
            return false;
         } else {
            ServerPlayer serverPlayer = (ServerPlayer)pPlayer;
            ItemStack stack = ItemStack.f_41583_;
            Optional<GreatSageRefiningRecipe> optional = server.m_129894_().m_44015_((RecipeType)TensuraRecipeTypes.REFINING.get(), pContainer, pLevel);
            if (optional.isEmpty()) {
               return false;
            } else {
               GreatSageRefiningRecipe recipe = (GreatSageRefiningRecipe)optional.get();
               if (pResult.m_40135_(pLevel, serverPlayer, recipe)) {
                  stack = recipe.m_5874_(pContainer);
               }

               if (stack.m_41619_()) {
                  return false;
               } else {
                  ItemStack result = pResult.m_8020_(0);
                  if (!result.m_41619_() && !ItemStack.m_150942_(pResult.m_8020_(0), stack)) {
                     return false;
                  } else {
                     if (pResult.m_7983_()) {
                        pResult.m_6836_(0, stack);
                     } else {
                        stack.m_41769_(result.m_41613_());
                        if (stack.m_41613_() > Math.max(stack.m_41741_() * 2, 16)) {
                           return false;
                        }

                        pResult.m_6836_(0, stack);
                     }

                     pMenu.m_150404_(0, stack);
                     serverPlayer.f_8906_.m_9829_(new ClientboundContainerSetSlotPacket(pMenu.f_38840_, pMenu.m_182425_(), 0, stack));
                     recipe.takeItemsFrom(pContainer);
                     pContainer.m_6596_();
                     return true;
                  }
               }
            }
         }
      }
   }

   public static boolean autoRefining(Level pLevel, Player pPlayer, ManasSkillInstance instance, SpatialStorageContainer spatialContainer) {
      if (pLevel.m_5776_()) {
         return false;
      } else {
         MinecraftServer server = pLevel.m_7654_();
         if (server == null) {
            return false;
         } else {
            ManasSkill var6 = instance.getSkill();
            if (!(var6 instanceof ISpatialStorage)) {
               return false;
            } else {
               ISpatialStorage spatialStorage = (ISpatialStorage)var6;
               SpatialStorageContainer container = new SpatialStorageContainer(8, 128);

               for(int i = 0; i < 8; ++i) {
                  container.m_6836_(i, spatialContainer.m_8020_(i + 11));
               }

               Optional<GreatSageRefiningRecipe> optional = server.m_129894_().m_44015_((RecipeType)TensuraRecipeTypes.REFINING.get(), container, pLevel);
               if (optional.isEmpty()) {
                  return false;
               } else {
                  GreatSageRefiningRecipe recipe = (GreatSageRefiningRecipe)optional.get();
                  ItemStack stack = recipe.m_5874_(container);
                  if (stack.m_41619_()) {
                     return false;
                  } else {
                     ItemStack result = spatialContainer.m_8020_(19);
                     if (!result.m_41619_() && !ItemStack.m_150942_(result, stack)) {
                        return false;
                     } else {
                        if (result.m_41619_()) {
                           spatialStorage.setItemInSpatialStorage(instance, pPlayer, stack, 19);
                        } else {
                           stack.m_41769_(result.m_41613_());
                           if (stack.m_41613_() > Math.max(stack.m_41741_() * 2, 16)) {
                              return false;
                           }

                           spatialStorage.setItemInSpatialStorage(instance, pPlayer, stack, 19);
                        }

                        recipe.takeItemsFrom(container);
                        container.m_6596_();

                        for(int i = 0; i < 8; ++i) {
                           spatialStorage.setItemInSpatialStorage(instance, pPlayer, container.m_8020_(i), i + 11);
                        }

                        return true;
                     }
                  }
               }
            }
         }
      }
   }

   public ItemStack m_7648_(Player pPlayer, int pIndex) {
      ItemStack copy = ItemStack.f_41583_;
      Slot slot = (Slot)this.f_38839_.get(pIndex);
      if (slot != null && slot.m_6657_()) {
         ItemStack stack = slot.m_7993_();
         copy = stack.m_41777_();
         if (pIndex >= 0 && pIndex < 36) {
            if (!this.m_38903_(stack, 36, 44, false)) {
               if (pIndex < 27) {
                  if (!this.m_38903_(stack, 27, 36, false)) {
                     return ItemStack.f_41583_;
                  }
               } else if (!this.m_38903_(stack, 0, 27, false)) {
                  return ItemStack.f_41583_;
               }
            }
         } else if (!this.m_38903_(stack, 0, 36, false)) {
            return ItemStack.f_41583_;
         }

         if (stack.m_41619_()) {
            slot.m_5852_(ItemStack.f_41583_);
         } else {
            slot.m_6654_();
         }

         if (stack.m_41613_() == copy.m_41613_()) {
            return ItemStack.f_41583_;
         }

         slot.m_142406_(pPlayer, stack);
      }

      return copy;
   }

   public boolean m_5882_(ItemStack pStack, Slot pSlot) {
      return pSlot.f_40218_ != this.resultContainer && super.m_5882_(pStack, pSlot);
   }

   public ManasSkill getSkill() {
      return this.skill;
   }

   public Player getPlayer() {
      return this.player;
   }

   public ContainerLevelAccess getAccess() {
      return this.access;
   }
}
