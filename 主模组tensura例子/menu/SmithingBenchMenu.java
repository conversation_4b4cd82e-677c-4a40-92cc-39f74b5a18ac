package com.github.manasmods.tensura.menu;

import com.github.manasmods.tensura.data.recipe.SmithingBenchRecipe;
import com.github.manasmods.tensura.menu.container.InventoryContainer;
import com.github.manasmods.tensura.registry.blocks.TensuraBlocks;
import com.github.manasmods.tensura.registry.menu.TensuraMenuTypes;
import com.github.manasmods.tensura.registry.recipe.TensuraRecipeTypes;
import com.google.common.collect.Lists;
import java.util.List;
import net.minecraft.network.FriendlyByteBuf;
import net.minecraft.world.entity.player.Inventory;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.inventory.AbstractContainerMenu;
import net.minecraft.world.inventory.ContainerLevelAccess;
import net.minecraft.world.inventory.DataSlot;
import net.minecraft.world.inventory.MenuType;
import net.minecraft.world.inventory.ResultContainer;
import net.minecraft.world.inventory.Slot;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.crafting.RecipeType;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.Block;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class SmithingBenchMenu extends AbstractContainerMenu {
   private static final Logger log = LogManager.getLogger(SmithingBenchMenu.class);
   private static final int PLAYER_INVENTORY_ROW_COUNT = 3;
   private static final int PLAYER_INVENTORY_COLUMN_COUNT = 9;
   private static final int HOTBAR_SLOT_COUNT = 9;
   private static final int PLAYER_INVENTORY_SLOT_COUNT = 27;
   private static final int VANILLA_SLOT_COUNT = 36;
   private final ContainerLevelAccess access;
   private final DataSlot selectedRecipeIndex;
   private final List<SmithingBenchRecipe> recipes;
   private final Player player;
   private final Level level;
   private final ResultContainer resultContainer;
   private final InventoryContainer inventoryContainer;
   private final Slot resultSlot;
   Runnable slotUpdateListener;

   public SmithingBenchMenu(int pContainerId, Inventory inv, FriendlyByteBuf extraData) {
      this(pContainerId, inv, ContainerLevelAccess.f_39287_);
   }

   public SmithingBenchMenu(int pContainerId, Inventory pPlayerInventory, ContainerLevelAccess access) {
      super((MenuType)TensuraMenuTypes.SMITHING_BENCH.get(), pContainerId);
      this.selectedRecipeIndex = DataSlot.m_39401_();
      this.recipes = Lists.newArrayList();
      this.resultContainer = new ResultContainer();
      this.slotUpdateListener = () -> {
      };
      this.access = access;
      this.player = pPlayerInventory.f_35978_;
      this.level = this.player.f_19853_;
      this.inventoryContainer = new InventoryContainer(pPlayerInventory);
      this.inventoryContainer.registerListener(this::m_6199_);
      this.inventoryContainer.registerListener((inventoryContainer1) -> {
         this.slotUpdateListener.run();
      });
      this.addPlayerInventory();
      this.addPlayerHotbar();
      this.m_38895_(this.selectedRecipeIndex).m_6422_(-1);
      this.resultSlot = this.m_38897_(new Slot(this.resultContainer, 0, 126, 81) {
         public boolean m_5857_(ItemStack pStack) {
            return false;
         }

         public boolean m_8010_(Player pPlayer) {
            return SmithingBenchMenu.this.canCraft();
         }

         public void m_142406_(Player pPlayer, ItemStack pStack) {
            if (SmithingBenchMenu.this.isValidRecipeIndex(SmithingBenchMenu.this.selectedRecipeIndex.m_6501_())) {
               ((SmithingBenchRecipe)SmithingBenchMenu.this.recipes.get(SmithingBenchMenu.this.selectedRecipeIndex.m_6501_())).takeItemsFrom(SmithingBenchMenu.this.inventoryContainer.getInventory());
               SmithingBenchMenu.this.inventoryContainer.m_6596_();
               SmithingBenchMenu.this.setupResultSlot();
            }

            super.m_142406_(pPlayer, pStack);
         }
      });
      this.setupRecipeList();
   }

   public ItemStack m_7648_(Player playerIn, int index) {
      Slot sourceSlot = (Slot)this.f_38839_.get(index);
      if (sourceSlot == null) {
         return ItemStack.f_41583_;
      } else if (!sourceSlot.m_6657_()) {
         return ItemStack.f_41583_;
      } else {
         ItemStack sourceStack = sourceSlot.m_7993_();
         ItemStack copyOfSourceStack = sourceStack.m_41777_();
         if (index == this.resultSlot.f_40219_) {
            if (!sourceSlot.m_8010_(playerIn)) {
               return ItemStack.f_41583_;
            } else {
               return !this.m_38903_(sourceStack, 0, 36, false) ? ItemStack.f_41583_ : TensuraMenuHelper.quickMoveStack(playerIn, sourceStack, sourceSlot, copyOfSourceStack);
            }
         } else if (index < 27) {
            return !this.m_38903_(sourceStack, 27, 36, false) ? ItemStack.f_41583_ : TensuraMenuHelper.quickMoveStack(playerIn, sourceStack, sourceSlot, copyOfSourceStack);
         } else if (index < 36) {
            return !this.m_38903_(sourceStack, 0, 27, false) ? ItemStack.f_41583_ : TensuraMenuHelper.quickMoveStack(playerIn, sourceStack, sourceSlot, copyOfSourceStack);
         } else {
            this.access.m_39292_((level, blockPos) -> {
               log.error("Invalid slotIndex {} for QuickCraft in Menu from Block at {} {}", index, blockPos, level.m_46472_().m_135782_());
            });
            return ItemStack.f_41583_;
         }
      }
   }

   private void addPlayerInventory() {
      for(int i = 0; i < 3; ++i) {
         for(int l = 0; l < 9; ++l) {
            this.m_38897_(new Slot(this.inventoryContainer, l + i * 9 + 9, 18 + l * 18, 112 + i * 18));
         }
      }

   }

   private void addPlayerHotbar() {
      for(int i = 0; i < 9; ++i) {
         this.m_38897_(new Slot(this.inventoryContainer, i, 18 + i * 18, 170));
      }

   }

   public boolean m_5882_(ItemStack pStack, Slot pSlot) {
      return pSlot.f_40218_ != this.inventoryContainer && super.m_5882_(pStack, pSlot);
   }

   public boolean m_6875_(Player pPlayer) {
      return (Boolean)this.access.m_39299_((level, blockPos) -> {
         return level.m_8055_(blockPos).m_60713_((Block)TensuraBlocks.SMITHING_BENCH.get()) && pPlayer.m_20275_((double)blockPos.m_123341_() + 0.5D, (double)blockPos.m_123342_() + 0.5D, (double)blockPos.m_123343_() + 0.5D) <= 64.0D;
      }, true);
   }

   public boolean canCraft() {
      return !this.isValidRecipeIndex(this.selectedRecipeIndex.m_6501_()) ? false : ((SmithingBenchRecipe)this.recipes.get(this.selectedRecipeIndex.m_6501_())).m_5818_(this.inventoryContainer.getInventory(), this.level);
   }

   public int getSelectedRecipeIndex() {
      return this.selectedRecipeIndex.m_6501_();
   }

   public boolean m_6366_(Player pPlayer, int pId) {
      if (this.isValidRecipeIndex(pId)) {
         this.selectedRecipeIndex.m_6422_(pId);
         this.setupResultSlot();
         return true;
      } else {
         return super.m_6366_(pPlayer, pId);
      }
   }

   private boolean isValidRecipeIndex(int pRecipeIndex) {
      return pRecipeIndex >= 0 && pRecipeIndex < this.recipes.size();
   }

   private void setupRecipeList() {
      this.recipes.clear();
      this.selectedRecipeIndex.m_6422_(-1);
      this.resultSlot.m_5852_(ItemStack.f_41583_);
      this.recipes.addAll(this.level.m_7465_().m_44013_((RecipeType)TensuraRecipeTypes.SMITHING.get()).stream().filter((recipe) -> {
         return recipe.hasUnlocked(this.player);
      }).toList());
      this.recipes.sort((o1, o2) -> {
         return o1.compare(o1, o2);
      });
   }

   void setupResultSlot() {
      if (this.recipes.isEmpty()) {
         this.resultSlot.m_5852_(ItemStack.f_41583_);
      } else if (this.isValidRecipeIndex(this.selectedRecipeIndex.m_6501_())) {
         SmithingBenchRecipe smithingBenchRecipe = (SmithingBenchRecipe)this.getRecipes().get(this.selectedRecipeIndex.m_6501_());
         this.resultContainer.m_6029_(smithingBenchRecipe);
         this.resultContainer.m_6836_(0, smithingBenchRecipe.m_5874_(this.inventoryContainer.getInventory()));
      }

      this.m_38946_();
   }

   public void m_6877_(Player pPlayer) {
      this.resultContainer.m_8016_(1);
      super.m_6877_(pPlayer);
   }

   public ContainerLevelAccess getAccess() {
      return this.access;
   }

   public List<SmithingBenchRecipe> getRecipes() {
      return this.recipes;
   }
}
