package com.github.manasmods.tensura.menu;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.tensura.ability.ISpatialStorage;
import com.github.manasmods.tensura.data.recipe.SmithingBenchRecipe;
import com.github.manasmods.tensura.registry.recipe.TensuraRecipeTypes;
import java.util.Iterator;
import java.util.Optional;
import net.minecraft.core.NonNullList;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.protocol.game.ClientboundContainerSetSlotPacket;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.Container;
import net.minecraft.world.SimpleContainer;
import net.minecraft.world.entity.player.Inventory;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.inventory.AbstractContainerMenu;
import net.minecraft.world.inventory.ContainerLevelAccess;
import net.minecraft.world.inventory.CraftingContainer;
import net.minecraft.world.inventory.MenuType;
import net.minecraft.world.inventory.ResultContainer;
import net.minecraft.world.inventory.ResultSlot;
import net.minecraft.world.inventory.Slot;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.crafting.CraftingRecipe;
import net.minecraft.world.item.crafting.RecipeType;
import net.minecraft.world.level.Level;
import net.minecraftforge.common.ForgeHooks;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.jetbrains.annotations.Nullable;

public class GreatSageCraftingMenu extends AbstractContainerMenu {
   private static final Logger log = LogManager.getLogger(GreatSageCraftingMenu.class);
   private final ManasSkill skill;
   private final Player player;
   private final Slot resultSlot;
   public final CraftingContainer craftingContainer;
   public final SimpleContainer copyContainer = new SimpleContainer(1);
   public final ResultContainer resultContainer = new ResultContainer();
   private final ContainerLevelAccess access;

   public GreatSageCraftingMenu(int id, Inventory inv, Player player, SimpleContainer container, ManasSkill skill, final ContainerLevelAccess access) {
      super((MenuType)null, id);
      this.skill = skill;
      this.access = access;
      this.player = player;
      this.addPlayerInventory(inv);
      this.addPlayerHotBar(inv);
      this.craftingContainer = new CraftingContainer(this, 3, 3);

      for(int i = 0; i < 9; ++i) {
         this.craftingContainer.m_6836_(i, container.m_8020_(i));
      }

      this.copyContainer.m_6836_(0, container.m_8020_(10));
      this.m_38897_(new Slot(this.copyContainer, 0, 50, 53) {
         public void m_142406_(Player pPlayer, ItemStack pStack) {
            if (!GreatSageCraftingMenu.this.isRepeatingCrafter(pPlayer)) {
               access.m_39292_((level, pos) -> {
                  GreatSageCraftingMenu.slotChangedCraftingGrid(GreatSageCraftingMenu.this, level, pPlayer, GreatSageCraftingMenu.this.craftingContainer, GreatSageCraftingMenu.this.resultContainer);
               });
            } else {
               super.m_142406_(pPlayer, pStack);
            }

         }
      });
      this.addSpatialSlots();
      this.resultSlot = new ResultSlot(player, this.craftingContainer, this.resultContainer, 0, 173, 53) {
         public void m_142406_(Player pPlayer, ItemStack pStack) {
            if (!GreatSageCraftingMenu.this.isRepeatingCrafter(pPlayer)) {
               SmithingBenchRecipe smithingRecipe = GreatSageCraftingMenu.getCopySmithingRecipe(pPlayer.f_19853_, GreatSageCraftingMenu.this.copyContainer.m_8020_(0), GreatSageCraftingMenu.this.craftingContainer);
               GreatSageCraftingMenu.takeCraftingItems(pPlayer.f_19853_, pPlayer, GreatSageCraftingMenu.this.craftingContainer, smithingRecipe);
               access.m_39292_((level, pos) -> {
                  GreatSageCraftingMenu.slotChangedCraftingGrid(GreatSageCraftingMenu.this, level, pPlayer, GreatSageCraftingMenu.this.craftingContainer, GreatSageCraftingMenu.this.resultContainer);
               });
            }

         }

         protected void m_7169_(ItemStack pStack, int pAmount) {
            if (!GreatSageCraftingMenu.this.isRepeatingCrafter(GreatSageCraftingMenu.this.getPlayer())) {
               super.m_7169_(pStack, pAmount);
            }

         }
      };
      this.m_38897_(this.resultSlot);
      if (this.isRepeatingCrafter(player)) {
         this.resultContainer.m_6836_(0, container.m_8020_(9));
      }

   }

   private void addPlayerInventory(Inventory inventory) {
      for(int row = 0; row < 3; ++row) {
         for(int slot = 0; slot < 9; ++slot) {
            this.m_38897_(new Slot(inventory, slot + row * 9 + 9, 45 + slot * 18, 110 + row * 18));
         }
      }

   }

   private void addPlayerHotBar(Inventory inventory) {
      for(int i = 0; i < 9; ++i) {
         this.m_38897_(new Slot(inventory, i, 45 + i * 18, 168));
      }

   }

   private void addSpatialSlots() {
      for(int row = 0; row < 3; ++row) {
         for(int column = 0; column < 3; ++column) {
            this.m_38897_(new Slot(this.craftingContainer, column + row * 3, 79 + column * 18, 35 + row * 18));
         }
      }

   }

   public boolean m_6875_(Player player) {
      return player.m_6084_();
   }

   public boolean check() {
      return true;
   }

   @Nullable
   public ManasSkillInstance getSkillInstance(Player player) {
      Optional<ManasSkillInstance> optional = SkillAPI.getSkillsFrom(player).getSkill(this.skill);
      return (ManasSkillInstance)optional.orElse((Object)null);
   }

   public void m_6877_(Player pPlayer) {
      ManasSkillInstance instance = this.getSkillInstance(pPlayer);
      if (instance != null) {
         ManasSkill var4 = instance.getSkill();
         if (var4 instanceof ISpatialStorage) {
            ISpatialStorage spatialStorage = (ISpatialStorage)var4;

            for(int i = 0; i < 9; ++i) {
               spatialStorage.setItemInSpatialStorage(instance, pPlayer, this.craftingContainer.m_8020_(i), i);
            }

            spatialStorage.setItemInSpatialStorage(instance, pPlayer, this.copyContainer.m_8020_(0), 10);
            if (this.isRepeatingCrafter(pPlayer)) {
               spatialStorage.setItemInSpatialStorage(instance, pPlayer, this.resultContainer.m_8020_(0), 9);
            }
         }
      }

      super.m_6877_(pPlayer);
   }

   @Nullable
   public static SmithingBenchRecipe getCopySmithingRecipe(Level pLevel, ItemStack toCopy, CraftingContainer container) {
      if (!toCopy.m_41619_()) {
         Iterator var3 = pLevel.m_7465_().m_44013_((RecipeType)TensuraRecipeTypes.SMITHING.get()).iterator();

         while(var3.hasNext()) {
            SmithingBenchRecipe recipe = (SmithingBenchRecipe)var3.next();
            if (recipe.m_8043_().m_150930_(toCopy.m_41720_()) && recipe.m_5818_(container, pLevel)) {
               return recipe;
            }
         }
      }

      return null;
   }

   protected static void slotChangedCraftingGrid(GreatSageCraftingMenu pMenu, Level pLevel, Player pPlayer, CraftingContainer pContainer, ResultContainer pResult) {
      if (!pLevel.m_5776_()) {
         MinecraftServer server = pLevel.m_7654_();
         if (server != null) {
            ServerPlayer serverPlayer = (ServerPlayer)pPlayer;
            ItemStack stack = ItemStack.f_41583_;
            SmithingBenchRecipe smithingRecipe = getCopySmithingRecipe(pLevel, pMenu.copyContainer.m_8020_(0), pContainer);
            if (smithingRecipe != null) {
               stack = smithingRecipe.m_8043_();
            }

            if (stack.m_41619_()) {
               Optional<CraftingRecipe> optional = server.m_129894_().m_44015_(RecipeType.f_44107_, pContainer, pLevel);
               if (optional.isPresent()) {
                  CraftingRecipe recipe = (CraftingRecipe)optional.get();
                  if (pResult.m_40135_(pLevel, serverPlayer, recipe)) {
                     stack = recipe.m_5874_(pContainer);
                  }
               }
            }

            pResult.m_6836_(0, stack);
            pMenu.m_150404_(0, stack);
            serverPlayer.f_8906_.m_9829_(new ClientboundContainerSetSlotPacket(pMenu.f_38840_, pMenu.m_182425_(), 0, stack));
         }
      }
   }

   public void m_6199_(Container pInventory) {
      super.m_6199_(pInventory);
      if (!this.isRepeatingCrafter(this.player)) {
         this.access.m_39292_((level, pos) -> {
            slotChangedCraftingGrid(this, level, this.player, this.craftingContainer, this.resultContainer);
         });
      }
   }

   private boolean isRepeatingCrafter(Player player) {
      ManasSkillInstance instance = this.getSkillInstance(player);
      return instance == null ? false : instance.getOrCreateTag().m_128471_("Repeating");
   }

   public boolean m_6366_(Player pPlayer, int pId) {
      if (pId == 1) {
         ManasSkillInstance instance = this.getSkillInstance(pPlayer);
         if (instance != null) {
            CompoundTag tag = instance.getOrCreateTag();
            boolean repeating = tag.m_128471_("Repeating");
            if (!this.resultContainer.m_7983_()) {
               if (repeating) {
                  pPlayer.m_6330_(SoundEvents.f_12315_, SoundSource.PLAYERS, 1.0F, 1.0F);
                  return false;
               }

               this.resultContainer.m_6211_();
               this.resultContainer.m_6596_();
            }

            tag.m_128379_("Repeating", !repeating);
            instance.markDirty();
            SkillAPI.getSkillsFrom(pPlayer).syncChanges();
         }

         return true;
      } else {
         return super.m_6366_(pPlayer, pId);
      }
   }

   public static boolean autoCrafting(GreatSageCraftingMenu pMenu, Level pLevel, Player pPlayer, CraftingContainer pContainer, ResultContainer pResult) {
      if (pLevel.m_5776_()) {
         return false;
      } else {
         MinecraftServer server = pLevel.m_7654_();
         if (server == null) {
            return false;
         } else {
            ServerPlayer serverPlayer = (ServerPlayer)pPlayer;
            ItemStack stack = ItemStack.f_41583_;
            SmithingBenchRecipe smithingRecipe = getCopySmithingRecipe(pLevel, pMenu.copyContainer.m_8020_(0), pContainer);
            if (smithingRecipe != null) {
               stack = smithingRecipe.m_8043_();
            }

            if (stack.m_41619_()) {
               Optional<CraftingRecipe> optional = server.m_129894_().m_44015_(RecipeType.f_44107_, pContainer, pLevel);
               if (optional.isPresent()) {
                  CraftingRecipe recipe = (CraftingRecipe)optional.get();
                  if (pResult.m_40135_(pLevel, serverPlayer, recipe)) {
                     stack = recipe.m_5874_(pContainer);
                  }
               }
            }

            if (stack.m_41619_()) {
               return false;
            } else {
               ItemStack result = pResult.m_8020_(0);
               if (!result.m_41619_() && !ItemStack.m_150942_(pResult.m_8020_(0), stack)) {
                  return false;
               } else {
                  if (pResult.m_7983_()) {
                     pResult.m_6836_(0, stack);
                  } else {
                     stack.m_41769_(result.m_41613_());
                     if (stack.m_41613_() > Math.max(stack.m_41741_() * 2, 16)) {
                        return false;
                     }

                     pResult.m_6836_(0, stack);
                  }

                  pMenu.m_150404_(0, stack);
                  serverPlayer.f_8906_.m_9829_(new ClientboundContainerSetSlotPacket(pMenu.f_38840_, pMenu.m_182425_(), 0, stack));
                  takeCraftingItems(pLevel, pPlayer, pContainer, smithingRecipe);
                  return true;
               }
            }
         }
      }
   }

   public static boolean autoCrafting(Level pLevel, Player pPlayer, ManasSkillInstance instance, SimpleContainer container) {
      if (pLevel.m_5776_()) {
         return false;
      } else {
         MinecraftServer server = pLevel.m_7654_();
         if (server == null) {
            return false;
         } else {
            ManasSkill var6 = instance.getSkill();
            if (!(var6 instanceof ISpatialStorage)) {
               return false;
            } else {
               ISpatialStorage spatialStorage = (ISpatialStorage)var6;
               CraftingContainer craftingContainer = new CraftingContainer(pPlayer.f_36096_, 3, 3);

               for(int i = 0; i < 9; ++i) {
                  craftingContainer.m_6836_(i, container.m_8020_(i));
               }

               ItemStack stack = ItemStack.f_41583_;
               SmithingBenchRecipe smithingRecipe = getCopySmithingRecipe(pLevel, container.m_8020_(10), craftingContainer);
               if (smithingRecipe != null) {
                  stack = smithingRecipe.m_8043_();
               }

               if (stack.m_41619_()) {
                  Optional<CraftingRecipe> optional = server.m_129894_().m_44015_(RecipeType.f_44107_, craftingContainer, pLevel);
                  if (optional.isPresent()) {
                     stack = ((CraftingRecipe)optional.get()).m_5874_(craftingContainer);
                  }
               }

               if (stack.m_41619_()) {
                  return false;
               } else {
                  ItemStack result = container.m_8020_(9);
                  if (!result.m_41619_() && !ItemStack.m_150942_(result, stack)) {
                     return false;
                  } else {
                     if (result.m_41619_()) {
                        spatialStorage.setItemInSpatialStorage(instance, pPlayer, stack, 9);
                     } else {
                        stack.m_41769_(result.m_41613_());
                        if (stack.m_41613_() > Math.max(stack.m_41741_() * 2, 16)) {
                           return false;
                        }

                        spatialStorage.setItemInSpatialStorage(instance, pPlayer, stack, 9);
                     }

                     takeCraftingItems(pLevel, pPlayer, craftingContainer, smithingRecipe);

                     for(int i = 0; i < 9; ++i) {
                        spatialStorage.setItemInSpatialStorage(instance, pPlayer, craftingContainer.m_8020_(i), i);
                     }

                     return true;
                  }
               }
            }
         }
      }
   }

   public static void takeCraftingItems(Level level, Player player, CraftingContainer pContainer, @Nullable SmithingBenchRecipe recipe) {
      if (recipe != null) {
         recipe.takeItemsFrom(pContainer);
         pContainer.m_6596_();
      } else {
         ForgeHooks.setCraftingPlayer(player);
         NonNullList<ItemStack> nonnulllist = level.m_7465_().m_44069_(RecipeType.f_44107_, pContainer, level);
         ForgeHooks.setCraftingPlayer((Player)null);

         for(int i = 0; i < nonnulllist.size(); ++i) {
            ItemStack containerItem = pContainer.m_8020_(i);
            ItemStack itemStack = (ItemStack)nonnulllist.get(i);
            if (!containerItem.m_41619_()) {
               pContainer.m_7407_(i, 1);
               containerItem = pContainer.m_8020_(i);
            }

            if (!itemStack.m_41619_()) {
               if (containerItem.m_41619_()) {
                  pContainer.m_6836_(i, itemStack);
               } else if (ItemStack.m_41746_(containerItem, itemStack) && ItemStack.m_41658_(containerItem, itemStack)) {
                  itemStack.m_41769_(containerItem.m_41613_());
                  pContainer.m_6836_(i, itemStack);
               } else if (!player.m_150109_().m_36054_(itemStack)) {
                  player.m_36176_(itemStack, false);
               }
            }
         }

      }
   }

   public ItemStack m_7648_(Player pPlayer, int pIndex) {
      ItemStack copy = ItemStack.f_41583_;
      Slot slot = (Slot)this.f_38839_.get(pIndex);
      if (slot != null && slot.m_6657_()) {
         ItemStack stack = slot.m_7993_();
         copy = stack.m_41777_();
         if (pIndex == 46) {
            this.access.m_39292_((level, pos) -> {
               stack.m_41720_().m_7836_(stack, level, pPlayer);
            });
            if (!this.m_38903_(stack, 0, 26, true)) {
               return ItemStack.f_41583_;
            }

            slot.m_40234_(stack, copy);
         } else if (pIndex >= 0 && pIndex < 36) {
            if (!this.m_38903_(stack, 36, 46, false)) {
               if (pIndex < 27) {
                  if (!this.m_38903_(stack, 27, 36, false)) {
                     return ItemStack.f_41583_;
                  }
               } else if (!this.m_38903_(stack, 0, 27, false)) {
                  return ItemStack.f_41583_;
               }
            }
         } else if (!this.m_38903_(stack, 0, 36, false)) {
            return ItemStack.f_41583_;
         }

         if (stack.m_41619_()) {
            slot.m_5852_(ItemStack.f_41583_);
         } else {
            slot.m_6654_();
         }

         if (stack.m_41613_() == copy.m_41613_()) {
            return ItemStack.f_41583_;
         }

         slot.m_142406_(pPlayer, stack);
      }

      return copy;
   }

   public boolean m_5882_(ItemStack pStack, Slot pSlot) {
      return pSlot.f_40218_ != this.resultContainer && super.m_5882_(pStack, pSlot);
   }

   public ManasSkill getSkill() {
      return this.skill;
   }

   public Player getPlayer() {
      return this.player;
   }

   public ContainerLevelAccess getAccess() {
      return this.access;
   }
}
