package com.github.manasmods.tensura.enchantment;

import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EquipmentSlot;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.enchantment.EnchantmentCategory;
import net.minecraft.world.item.enchantment.Enchantment.Rarity;

public class EnergyStealEnchantment extends EngravingEnchantment {
   public EnergyStealEnchantment() {
      super(Rarity.VERY_RARE, EnchantmentCategory.WEAPON, EquipmentSlot.MAINHAND);
   }

   public void doAdditionalAttack(ItemStack stack, LivingEntity pAttacker, Entity pTarget, int pLevel, float damage) {
      if (pTarget.f_19802_ < 60) {
         if (pTarget instanceof LivingEntity) {
            LivingEntity target = (LivingEntity)pTarget;
            ItemStack weapon = pAttacker.m_21205_();
            if (pAttacker instanceof Player) {
               Player player = (Player)pAttacker;
               if (player.m_36335_().m_41519_(weapon.m_41720_())) {
                  return;
               }

               player.m_36335_().m_41524_(weapon.m_41720_(), 20);
            }

            if (SkillHelper.reduceEnergy(target, pAttacker, 0.01D * (double)pLevel, true)) {
               SkillHelper.gainMP(pAttacker, TensuraEPCapability.getEP(target) * 0.001D * (double)pLevel, false);
               pAttacker.m_9236_().m_6263_((Player)null, pAttacker.m_20185_(), pAttacker.m_20186_(), pAttacker.m_20189_(), SoundEvents.f_12275_, SoundSource.PLAYERS, 1.0F, 1.0F);
            }

         }
      }
   }
}
