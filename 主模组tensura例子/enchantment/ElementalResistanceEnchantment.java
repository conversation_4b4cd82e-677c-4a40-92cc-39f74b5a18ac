package com.github.manasmods.tensura.enchantment;

import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.registry.enchantment.TensuraEnchantments;
import com.github.manasmods.tensura.registry.items.TensuraArmorItems;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.entity.EquipmentSlot;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.EquipmentSlot.Type;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.enchantment.Enchantment;
import net.minecraft.world.item.enchantment.EnchantmentCategory;
import net.minecraft.world.item.enchantment.Enchantment.Rarity;

public class ElementalResistanceEnchantment extends EngravingEnchantment implements IInherentEngrave {
   public ElementalResistanceEnchantment() {
      super(Rarity.RARE, EnchantmentCategory.WEARABLE, EquipmentSlot.values());
   }

   public int m_6586_() {
      return 2;
   }

   public float getDamageProtection(int pLevel, DamageSource source, LivingEntity wearer, EquipmentSlot slot, float damage) {
      if (slot.m_20743_().equals(Type.HAND)) {
         return 0.0F;
      } else {
         return DamageSourceHelper.isNaturalEffects(source) ? 0.1F * (float)pLevel * damage : 0.0F;
      }
   }

   public static void applyHolyEnchantments(ItemStack toStack) {
      if (toStack.m_204117_(TensuraTags.Items.HOLY_ARMAMENTS_ITEMS) && toStack.getEnchantmentLevel((Enchantment)TensuraEnchantments.ELEMENTAL_RESISTANCE.get()) < 1) {
         toStack.m_41663_((Enchantment)TensuraEnchantments.ELEMENTAL_RESISTANCE.get(), 2);
         toStack.m_41663_((Enchantment)TensuraEnchantments.ELEMENTAL_BOOST.get(), 2);
      }

   }

   public boolean shouldHasFoil(ItemStack stack) {
      if (stack.m_150930_((Item)TensuraArmorItems.ANTI_MAGIC_MASK.get())) {
         return false;
      } else {
         return !stack.m_204117_(TensuraTags.Items.HOLY_ARMAMENTS_ITEMS);
      }
   }
}
