package com.github.manasmods.tensura.enchantment;

import com.github.manasmods.tensura.registry.enchantment.TensuraEnchantments;
import com.github.manasmods.tensura.registry.items.TensuraArmorItems;
import net.minecraft.world.entity.EquipmentSlot;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.enchantment.Enchantment;
import net.minecraft.world.item.enchantment.EnchantmentCategory;
import net.minecraft.world.item.enchantment.Enchantment.Rarity;

public class BreathingSupportEnchantment extends EngravingEnchantment implements IInherentEngrave {
   public BreathingSupportEnchantment() {
      super(Rarity.RARE, EnchantmentCategory.ARMOR_HEAD, EquipmentSlot.values());
   }

   public static void applyAntiMagicMask(ItemStack stack) {
      if (stack.m_150930_((Item)TensuraArmorItems.ANTI_MAGIC_MASK.get()) && stack.getEnchantmentLevel((Enchantment)TensuraEnchantments.BREATHING_SUPPORT.get()) < 1) {
         stack.m_41663_((Enchantment)TensuraEnchantments.ELEMENTAL_RESISTANCE.get(), 1);
         stack.m_41663_((Enchantment)TensuraEnchantments.BREATHING_SUPPORT.get(), 1);
      }

   }

   public boolean shouldHasFoil(ItemStack stack) {
      return !stack.m_150930_((Item)TensuraArmorItems.ANTI_MAGIC_MASK.get());
   }
}
