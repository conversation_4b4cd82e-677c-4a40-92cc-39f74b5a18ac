package com.github.manasmods.tensura.registry.items;

import com.github.manasmods.manascore.api.data.gen.annotation.GenerateItemModels;
import com.github.manasmods.manascore.api.data.gen.annotation.GenerateItemModels.SingleHandheldTextureModel;
import com.github.manasmods.manascore.api.data.gen.annotation.GenerateItemModels.SingleTextureModel;
import com.github.manasmods.tensura.item.TensuraCreativeTab;
import com.github.manasmods.tensura.item.TensuraToolTiers;
import com.github.manasmods.tensura.item.custom.ArmoursaurusGauntletItem;
import com.github.manasmods.tensura.item.custom.ArmoursaurusShieldItem;
import com.github.manasmods.tensura.item.custom.BladeTigerScytheItem;
import com.github.manasmods.tensura.item.custom.CentipedeDaggerItem;
import com.github.manasmods.tensura.item.custom.DeadEndRainbowItem;
import com.github.manasmods.tensura.item.custom.DragonKnuckleItem;
import com.github.manasmods.tensura.item.custom.IceBladeItem;
import com.github.manasmods.tensura.item.custom.InvisibleArrowItem;
import com.github.manasmods.tensura.item.custom.KunaiItem;
import com.github.manasmods.tensura.item.custom.MagisteelKunaiItem;
import com.github.manasmods.tensura.item.custom.MeatCrusherItem;
import com.github.manasmods.tensura.item.custom.MoonlightItem;
import com.github.manasmods.tensura.item.custom.OrbOfDominationItem;
import com.github.manasmods.tensura.item.custom.SissieToothPickaxe;
import com.github.manasmods.tensura.item.custom.SlimeStaffItem;
import com.github.manasmods.tensura.item.custom.SniperPistolItem;
import com.github.manasmods.tensura.item.custom.SpatialBladeItem;
import com.github.manasmods.tensura.item.custom.SpearedFinArrowItem;
import com.github.manasmods.tensura.item.custom.SpiderDaggerItem;
import com.github.manasmods.tensura.item.custom.TempestScaleKnifeItem;
import com.github.manasmods.tensura.item.custom.TempestScaleShieldItem;
import com.github.manasmods.tensura.item.custom.TempestScaleSwordItem;
import com.github.manasmods.tensura.item.custom.VortexSpearItem;
import com.github.manasmods.tensura.item.custom.WebCartridgeItem;
import com.github.manasmods.tensura.item.custom.WebGunItem;
import com.github.manasmods.tensura.item.templates.SimpleAxeItem;
import com.github.manasmods.tensura.item.templates.SimpleBowItem;
import com.github.manasmods.tensura.item.templates.SimpleCrossbowItem;
import com.github.manasmods.tensura.item.templates.SimpleHoeItem;
import com.github.manasmods.tensura.item.templates.SimplePickaxeItem;
import com.github.manasmods.tensura.item.templates.SimpleShieldItem;
import com.github.manasmods.tensura.item.templates.SimpleShovelItem;
import com.github.manasmods.tensura.item.templates.SimpleSwordItem;
import com.github.manasmods.tensura.item.templates.custom.SimpleGreatSwordItem;
import com.github.manasmods.tensura.item.templates.custom.SimpleKatanaItem;
import com.github.manasmods.tensura.item.templates.custom.SimpleKodachiItem;
import com.github.manasmods.tensura.item.templates.custom.SimpleLongSwordItem;
import com.github.manasmods.tensura.item.templates.custom.SimpleOdachiItem;
import com.github.manasmods.tensura.item.templates.custom.SimpleScytheItem;
import com.github.manasmods.tensura.item.templates.custom.SimpleShortSwordItem;
import com.github.manasmods.tensura.item.templates.custom.SimpleSickleItem;
import com.github.manasmods.tensura.item.templates.custom.SimpleSpearItem;
import com.github.manasmods.tensura.item.templates.custom.SimpleTachiItem;
import com.github.manasmods.tensura.item.templates.custom.TensuraLongSword;
import com.github.manasmods.tensura.item.templates.custom.TensuraSword;
import com.github.manasmods.tensura.item.templates.custom.TwoHandedLongSword;
import com.github.manasmods.tensura.item.templates.custom.WarBowItem;
import com.github.manasmods.tensura.registry.blocks.TensuraBlocks;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.Rarity;
import net.minecraft.world.item.Tiers;
import net.minecraft.world.item.Item.Properties;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.level.block.Blocks;
import net.minecraftforge.eventbus.api.IEventBus;
import net.minecraftforge.registries.DeferredRegister;
import net.minecraftforge.registries.ForgeRegistries;
import net.minecraftforge.registries.RegistryObject;

@GenerateItemModels
public class TensuraToolItems {
   private static final DeferredRegister<Item> registry;
   @SingleHandheldTextureModel
   public static final RegistryObject<TensuraSword> GOBLIN_CLUB;
   public static final RegistryObject<TensuraLongSword> KANABO;
   public static final RegistryObject<Item> SHORT_BOW;
   public static final RegistryObject<Item> LONG_BOW;
   public static final RegistryObject<Item> WAR_BOW;
   public static final RegistryObject<Item> SHORT_SPIDER_BOW;
   public static final RegistryObject<Item> SPIDER_BOW;
   public static final RegistryObject<Item> LONG_SPIDER_BOW;
   public static final RegistryObject<Item> WAR_SPIDER_BOW;
   public static final RegistryObject<SimpleCrossbowItem> ANT_CROSSBOW;
   @SingleTextureModel
   public static final RegistryObject<InvisibleArrowItem> INVISIBLE_ARROW;
   @SingleTextureModel
   public static final RegistryObject<SpearedFinArrowItem> SPEARED_FIN_ARROW;
   public static final RegistryObject<KunaiItem> KUNAI;
   public static final RegistryObject<MagisteelKunaiItem> PURE_MAGISTEEL_KUNAI;
   public static final RegistryObject<SimpleShieldItem> AURA_SHIELD;
   public static final RegistryObject<ArmoursaurusShieldItem> ARMOURSAURUS_SHIELD;
   public static final RegistryObject<TempestScaleShieldItem> TEMPEST_SCALE_SHIELD;
   public static final RegistryObject<TwoHandedLongSword> TEMPEST_SCALE_SWORD;
   @SingleHandheldTextureModel
   public static final RegistryObject<TensuraLongSword> TEMPEST_SCALE_KNIFE;
   @SingleHandheldTextureModel
   public static final RegistryObject<CentipedeDaggerItem> CENTIPEDE_DAGGER;
   @SingleHandheldTextureModel
   public static final RegistryObject<SpiderDaggerItem> SPIDER_DAGGER;
   public static final RegistryObject<Item> BEAST_HORN_SPEAR;
   public static final RegistryObject<Item> UNICORN_HORN_SPEAR;
   public static final RegistryObject<Item> BLADE_TIGER_SCYTHE;
   @SingleHandheldTextureModel
   public static final RegistryObject<SimplePickaxeItem> SISSIE_TOOTH_PICKAXE;
   @SingleHandheldTextureModel
   public static final RegistryObject<Item> WOODEN_SHORT_SWORD;
   public static final RegistryObject<Item> WOODEN_LONG_SWORD;
   public static final RegistryObject<Item> WOODEN_GREAT_SWORD;
   @SingleHandheldTextureModel
   public static final RegistryObject<Item> WOODEN_KATANA;
   @SingleHandheldTextureModel
   public static final RegistryObject<Item> WOODEN_KODACHI;
   public static final RegistryObject<Item> WOODEN_TACHI;
   public static final RegistryObject<Item> WOODEN_ODACHI;
   public static final RegistryObject<Item> WOODEN_SPEAR;
   public static final RegistryObject<Item> WOODEN_SCYTHE;
   @SingleHandheldTextureModel
   public static final RegistryObject<SimpleSickleItem> WOODEN_SICKLE;
   @SingleHandheldTextureModel
   public static final RegistryObject<Item> STONE_SHORT_SWORD;
   public static final RegistryObject<Item> STONE_LONG_SWORD;
   public static final RegistryObject<Item> STONE_GREAT_SWORD;
   @SingleHandheldTextureModel
   public static final RegistryObject<Item> STONE_KATANA;
   @SingleHandheldTextureModel
   public static final RegistryObject<Item> STONE_KODACHI;
   public static final RegistryObject<Item> STONE_TACHI;
   public static final RegistryObject<Item> STONE_ODACHI;
   public static final RegistryObject<Item> STONE_SPEAR;
   public static final RegistryObject<Item> STONE_SCYTHE;
   @SingleHandheldTextureModel
   public static final RegistryObject<SimpleSickleItem> STONE_SICKLE;
   @SingleHandheldTextureModel
   public static final RegistryObject<Item> GOLDEN_SHORT_SWORD;
   public static final RegistryObject<Item> GOLDEN_LONG_SWORD;
   public static final RegistryObject<Item> GOLDEN_GREAT_SWORD;
   @SingleHandheldTextureModel
   public static final RegistryObject<Item> GOLDEN_KATANA;
   @SingleHandheldTextureModel
   public static final RegistryObject<Item> GOLDEN_KODACHI;
   public static final RegistryObject<Item> GOLDEN_TACHI;
   public static final RegistryObject<Item> GOLDEN_ODACHI;
   public static final RegistryObject<Item> GOLDEN_SPEAR;
   public static final RegistryObject<Item> GOLDEN_SCYTHE;
   @SingleHandheldTextureModel
   public static final RegistryObject<SimpleSickleItem> GOLDEN_SICKLE;
   @SingleHandheldTextureModel
   public static final RegistryObject<SimpleSwordItem> SILVER_SWORD;
   @SingleHandheldTextureModel
   public static final RegistryObject<Item> SILVER_SHORT_SWORD;
   public static final RegistryObject<Item> SILVER_LONG_SWORD;
   public static final RegistryObject<Item> SILVER_GREAT_SWORD;
   @SingleHandheldTextureModel
   public static final RegistryObject<Item> SILVER_KATANA;
   @SingleHandheldTextureModel
   public static final RegistryObject<Item> SILVER_KODACHI;
   public static final RegistryObject<Item> SILVER_TACHI;
   public static final RegistryObject<Item> SILVER_ODACHI;
   public static final RegistryObject<Item> SILVER_SPEAR;
   public static final RegistryObject<Item> SILVER_SCYTHE;
   @SingleHandheldTextureModel
   public static final RegistryObject<SimplePickaxeItem> SILVER_PICKAXE;
   @SingleHandheldTextureModel
   public static final RegistryObject<SimpleAxeItem> SILVER_AXE;
   @SingleHandheldTextureModel
   public static final RegistryObject<SimpleShovelItem> SILVER_SHOVEL;
   @SingleHandheldTextureModel
   public static final RegistryObject<SimpleHoeItem> SILVER_HOE;
   @SingleHandheldTextureModel
   public static final RegistryObject<SimpleSickleItem> SILVER_SICKLE;
   @SingleHandheldTextureModel
   public static final RegistryObject<Item> IRON_SHORT_SWORD;
   public static final RegistryObject<Item> IRON_LONG_SWORD;
   public static final RegistryObject<Item> IRON_GREAT_SWORD;
   @SingleHandheldTextureModel
   public static final RegistryObject<Item> IRON_KATANA;
   @SingleHandheldTextureModel
   public static final RegistryObject<Item> IRON_KODACHI;
   public static final RegistryObject<Item> IRON_TACHI;
   public static final RegistryObject<Item> IRON_ODACHI;
   public static final RegistryObject<Item> IRON_SPEAR;
   public static final RegistryObject<Item> IRON_SCYTHE;
   @SingleHandheldTextureModel
   public static final RegistryObject<SimpleSickleItem> IRON_SICKLE;
   @SingleHandheldTextureModel
   public static final RegistryObject<Item> DIAMOND_SHORT_SWORD;
   public static final RegistryObject<Item> DIAMOND_LONG_SWORD;
   public static final RegistryObject<Item> DIAMOND_GREAT_SWORD;
   @SingleHandheldTextureModel
   public static final RegistryObject<Item> DIAMOND_KATANA;
   @SingleHandheldTextureModel
   public static final RegistryObject<Item> DIAMOND_KODACHI;
   public static final RegistryObject<Item> DIAMOND_TACHI;
   public static final RegistryObject<Item> DIAMOND_ODACHI;
   public static final RegistryObject<Item> DIAMOND_SPEAR;
   public static final RegistryObject<Item> DIAMOND_SCYTHE;
   @SingleHandheldTextureModel
   public static final RegistryObject<SimpleSickleItem> DIAMOND_SICKLE;
   @SingleHandheldTextureModel
   public static final RegistryObject<SimpleSwordItem> LOW_MAGISTEEL_SWORD;
   @SingleHandheldTextureModel
   public static final RegistryObject<Item> LOW_MAGISTEEL_SHORT_SWORD;
   public static final RegistryObject<Item> LOW_MAGISTEEL_LONG_SWORD;
   public static final RegistryObject<Item> LOW_MAGISTEEL_GREAT_SWORD;
   @SingleHandheldTextureModel
   public static final RegistryObject<Item> LOW_MAGISTEEL_KATANA;
   @SingleHandheldTextureModel
   public static final RegistryObject<Item> LOW_MAGISTEEL_KODACHI;
   public static final RegistryObject<Item> LOW_MAGISTEEL_TACHI;
   public static final RegistryObject<Item> LOW_MAGISTEEL_ODACHI;
   public static final RegistryObject<Item> LOW_MAGISTEEL_SPEAR;
   public static final RegistryObject<Item> LOW_MAGISTEEL_SCYTHE;
   @SingleHandheldTextureModel
   public static final RegistryObject<SimplePickaxeItem> LOW_MAGISTEEL_PICKAXE;
   @SingleHandheldTextureModel
   public static final RegistryObject<SimpleAxeItem> LOW_MAGISTEEL_AXE;
   @SingleHandheldTextureModel
   public static final RegistryObject<SimpleShovelItem> LOW_MAGISTEEL_SHOVEL;
   @SingleHandheldTextureModel
   public static final RegistryObject<SimpleHoeItem> LOW_MAGISTEEL_HOE;
   @SingleHandheldTextureModel
   public static final RegistryObject<SimpleSickleItem> LOW_MAGISTEEL_SICKLE;
   @SingleHandheldTextureModel
   public static final RegistryObject<Item> NETHERITE_SHORT_SWORD;
   public static final RegistryObject<Item> NETHERITE_LONG_SWORD;
   public static final RegistryObject<Item> NETHERITE_GREAT_SWORD;
   @SingleHandheldTextureModel
   public static final RegistryObject<Item> NETHERITE_KATANA;
   @SingleHandheldTextureModel
   public static final RegistryObject<Item> NETHERITE_KODACHI;
   public static final RegistryObject<Item> NETHERITE_TACHI;
   public static final RegistryObject<Item> NETHERITE_ODACHI;
   public static final RegistryObject<Item> NETHERITE_SPEAR;
   public static final RegistryObject<Item> NETHERITE_SCYTHE;
   @SingleHandheldTextureModel
   public static final RegistryObject<SimpleSickleItem> NETHERITE_SICKLE;
   @SingleHandheldTextureModel
   public static final RegistryObject<SimpleSwordItem> HIGH_MAGISTEEL_SWORD;
   @SingleHandheldTextureModel
   public static final RegistryObject<Item> HIGH_MAGISTEEL_SHORT_SWORD;
   public static final RegistryObject<Item> HIGH_MAGISTEEL_LONG_SWORD;
   public static final RegistryObject<Item> HIGH_MAGISTEEL_GREAT_SWORD;
   @SingleHandheldTextureModel
   public static final RegistryObject<Item> HIGH_MAGISTEEL_KATANA;
   @SingleHandheldTextureModel
   public static final RegistryObject<Item> HIGH_MAGISTEEL_KODACHI;
   public static final RegistryObject<Item> HIGH_MAGISTEEL_TACHI;
   public static final RegistryObject<Item> HIGH_MAGISTEEL_ODACHI;
   public static final RegistryObject<Item> HIGH_MAGISTEEL_SPEAR;
   public static final RegistryObject<Item> HIGH_MAGISTEEL_SCYTHE;
   @SingleHandheldTextureModel
   public static final RegistryObject<SimplePickaxeItem> HIGH_MAGISTEEL_PICKAXE;
   @SingleHandheldTextureModel
   public static final RegistryObject<SimpleAxeItem> HIGH_MAGISTEEL_AXE;
   @SingleHandheldTextureModel
   public static final RegistryObject<SimpleShovelItem> HIGH_MAGISTEEL_SHOVEL;
   @SingleHandheldTextureModel
   public static final RegistryObject<SimpleHoeItem> HIGH_MAGISTEEL_HOE;
   @SingleHandheldTextureModel
   public static final RegistryObject<SimpleSickleItem> HIGH_MAGISTEEL_SICKLE;
   @SingleHandheldTextureModel
   public static final RegistryObject<SimpleSwordItem> MITHRIL_SWORD;
   @SingleHandheldTextureModel
   public static final RegistryObject<Item> MITHRIL_SHORT_SWORD;
   public static final RegistryObject<Item> MITHRIL_LONG_SWORD;
   public static final RegistryObject<Item> MITHRIL_GREAT_SWORD;
   @SingleHandheldTextureModel
   public static final RegistryObject<Item> MITHRIL_KATANA;
   @SingleHandheldTextureModel
   public static final RegistryObject<Item> MITHRIL_KODACHI;
   public static final RegistryObject<Item> MITHRIL_TACHI;
   public static final RegistryObject<Item> MITHRIL_ODACHI;
   public static final RegistryObject<Item> MITHRIL_SPEAR;
   public static final RegistryObject<Item> MITHRIL_SCYTHE;
   @SingleHandheldTextureModel
   public static final RegistryObject<SimplePickaxeItem> MITHRIL_PICKAXE;
   @SingleHandheldTextureModel
   public static final RegistryObject<SimpleAxeItem> MITHRIL_AXE;
   @SingleHandheldTextureModel
   public static final RegistryObject<SimpleShovelItem> MITHRIL_SHOVEL;
   @SingleHandheldTextureModel
   public static final RegistryObject<SimpleHoeItem> MITHRIL_HOE;
   @SingleHandheldTextureModel
   public static final RegistryObject<SimpleSickleItem> MITHRIL_SICKLE;
   @SingleHandheldTextureModel
   public static final RegistryObject<SimpleSwordItem> ORICHALCUM_SWORD;
   @SingleHandheldTextureModel
   public static final RegistryObject<Item> ORICHALCUM_SHORT_SWORD;
   public static final RegistryObject<Item> ORICHALCUM_LONG_SWORD;
   public static final RegistryObject<Item> ORICHALCUM_GREAT_SWORD;
   @SingleHandheldTextureModel
   public static final RegistryObject<Item> ORICHALCUM_KATANA;
   @SingleHandheldTextureModel
   public static final RegistryObject<Item> ORICHALCUM_KODACHI;
   public static final RegistryObject<Item> ORICHALCUM_TACHI;
   public static final RegistryObject<Item> ORICHALCUM_ODACHI;
   public static final RegistryObject<Item> ORICHALCUM_SPEAR;
   public static final RegistryObject<Item> ORICHALCUM_SCYTHE;
   @SingleHandheldTextureModel
   public static final RegistryObject<SimplePickaxeItem> ORICHALCUM_PICKAXE;
   @SingleHandheldTextureModel
   public static final RegistryObject<SimpleAxeItem> ORICHALCUM_AXE;
   @SingleHandheldTextureModel
   public static final RegistryObject<SimpleShovelItem> ORICHALCUM_SHOVEL;
   @SingleHandheldTextureModel
   public static final RegistryObject<SimpleHoeItem> ORICHALCUM_HOE;
   @SingleHandheldTextureModel
   public static final RegistryObject<SimpleSickleItem> ORICHALCUM_SICKLE;
   @SingleHandheldTextureModel
   public static final RegistryObject<SimpleSwordItem> PURE_MAGISTEEL_SWORD;
   @SingleHandheldTextureModel
   public static final RegistryObject<Item> PURE_MAGISTEEL_SHORT_SWORD;
   public static final RegistryObject<Item> PURE_MAGISTEEL_LONG_SWORD;
   public static final RegistryObject<Item> PURE_MAGISTEEL_GREAT_SWORD;
   @SingleHandheldTextureModel
   public static final RegistryObject<Item> PURE_MAGISTEEL_KATANA;
   @SingleHandheldTextureModel
   public static final RegistryObject<Item> PURE_MAGISTEEL_KODACHI;
   public static final RegistryObject<Item> PURE_MAGISTEEL_TACHI;
   public static final RegistryObject<Item> PURE_MAGISTEEL_ODACHI;
   public static final RegistryObject<Item> PURE_MAGISTEEL_SPEAR;
   public static final RegistryObject<Item> PURE_MAGISTEEL_SCYTHE;
   @SingleHandheldTextureModel
   public static final RegistryObject<SimplePickaxeItem> PURE_MAGISTEEL_PICKAXE;
   @SingleHandheldTextureModel
   public static final RegistryObject<SimpleAxeItem> PURE_MAGISTEEL_AXE;
   @SingleHandheldTextureModel
   public static final RegistryObject<SimpleShovelItem> PURE_MAGISTEEL_SHOVEL;
   @SingleHandheldTextureModel
   public static final RegistryObject<SimpleHoeItem> PURE_MAGISTEEL_HOE;
   @SingleHandheldTextureModel
   public static final RegistryObject<SimpleSickleItem> PURE_MAGISTEEL_SICKLE;
   @SingleHandheldTextureModel
   public static final RegistryObject<SimpleSwordItem> ADAMANTITE_SWORD;
   @SingleHandheldTextureModel
   public static final RegistryObject<Item> ADAMANTITE_SHORT_SWORD;
   public static final RegistryObject<Item> ADAMANTITE_LONG_SWORD;
   public static final RegistryObject<Item> ADAMANTITE_GREAT_SWORD;
   @SingleHandheldTextureModel
   public static final RegistryObject<Item> ADAMANTITE_KATANA;
   @SingleHandheldTextureModel
   public static final RegistryObject<Item> ADAMANTITE_KODACHI;
   public static final RegistryObject<Item> ADAMANTITE_TACHI;
   public static final RegistryObject<Item> ADAMANTITE_ODACHI;
   public static final RegistryObject<Item> ADAMANTITE_SPEAR;
   public static final RegistryObject<Item> ADAMANTITE_SCYTHE;
   @SingleHandheldTextureModel
   public static final RegistryObject<SimplePickaxeItem> ADAMANTITE_PICKAXE;
   @SingleHandheldTextureModel
   public static final RegistryObject<SimpleAxeItem> ADAMANTITE_AXE;
   @SingleHandheldTextureModel
   public static final RegistryObject<SimpleShovelItem> ADAMANTITE_SHOVEL;
   @SingleHandheldTextureModel
   public static final RegistryObject<SimpleHoeItem> ADAMANTITE_HOE;
   @SingleHandheldTextureModel
   public static final RegistryObject<SimpleSickleItem> ADAMANTITE_SICKLE;
   public static final RegistryObject<SimpleSwordItem> HIHIIROKANE_SWORD;
   public static final RegistryObject<Item> HIHIIROKANE_SHORT_SWORD;
   public static final RegistryObject<Item> HIHIIROKANE_LONG_SWORD;
   public static final RegistryObject<Item> HIHIIROKANE_GREAT_SWORD;
   public static final RegistryObject<Item> HIHIIROKANE_KATANA;
   public static final RegistryObject<Item> HIHIIROKANE_KODACHI;
   public static final RegistryObject<Item> HIHIIROKANE_TACHI;
   public static final RegistryObject<Item> HIHIIROKANE_ODACHI;
   public static final RegistryObject<Item> HIHIIROKANE_SPEAR;
   public static final RegistryObject<Item> HIHIIROKANE_SCYTHE;
   public static final RegistryObject<SimplePickaxeItem> HIHIIROKANE_PICKAXE;
   public static final RegistryObject<SimpleAxeItem> HIHIIROKANE_AXE;
   public static final RegistryObject<SimpleShovelItem> HIHIIROKANE_SHOVEL;
   public static final RegistryObject<SimpleHoeItem> HIHIIROKANE_HOE;
   public static final RegistryObject<SimpleSickleItem> HIHIIROKANE_SICKLE;
   @SingleTextureModel
   public static final RegistryObject<OrbOfDominationItem> ORB_OF_DOMINATION;
   public static final RegistryObject<ArmoursaurusGauntletItem> ARMOURSAURUS_GAUNTLET;
   public static final RegistryObject<DragonKnuckleItem> DRAGON_KNUCKLE;
   public static final RegistryObject<DeadEndRainbowItem> DEAD_END_RAINBOW;
   public static final RegistryObject<IceBladeItem> ICE_BLADE;
   public static final RegistryObject<MeatCrusherItem> MEAT_CRUSHER;
   public static final RegistryObject<MoonlightItem> MOONLIGHT;
   public static final RegistryObject<SpatialBladeItem> SPATIAL_BLADE;
   @SingleHandheldTextureModel
   public static final RegistryObject<Item> SEVERER_BLADE;
   public static final RegistryObject<Item> SNIPER_PISTOL;
   public static final RegistryObject<VortexSpearItem> VORTEX_SPEAR;
   @SingleHandheldTextureModel
   public static final RegistryObject<Item> SLIME_STAFF;
   public static final RegistryObject<WebGunItem> WEB_GUN;
   @SingleHandheldTextureModel
   public static final RegistryObject<Item> COPPER_SHELL;
   @SingleHandheldTextureModel
   public static final RegistryObject<Item> WEB_CARTRIDGE;
   @SingleHandheldTextureModel
   public static final RegistryObject<Item> STICKY_WEB_CARTRIDGE;
   @SingleHandheldTextureModel
   public static final RegistryObject<Item> STICKY_STEEL_WEB_CARTRIDGE;

   public static void init(IEventBus modEventBus) {
      registry.register(modEventBus);
   }

   static {
      registry = DeferredRegister.create(ForgeRegistries.ITEMS, "tensura");
      GOBLIN_CLUB = registry.register("goblin_club", () -> {
         return new TensuraSword(Tiers.WOOD, 2, -2.5F, 0.0D, 0.5D, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41497_(Rarity.RARE));
      });
      KANABO = registry.register("kanabo", () -> {
         return new TensuraLongSword(Tiers.WOOD, 9, -3.2F, 1.0D, 0.0D, 0.5D, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41503_(350).m_41497_(Rarity.EPIC));
      });
      SHORT_BOW = registry.register("short_bow", () -> {
         return new SimpleBowItem((new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41503_(231), 10, 10.0F, 2.0D, 0.5F);
      });
      LONG_BOW = registry.register("long_bow", () -> {
         return new SimpleBowItem((new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41503_(500), 30, 30.0F, 2.5D, 1.2F);
      });
      WAR_BOW = registry.register("war_bow", () -> {
         return new WarBowItem((new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41503_(615), 60, 60.0F, 2.75D, 1.4F);
      });
      SHORT_SPIDER_BOW = registry.register("short_spider_bow", () -> {
         return new SimpleBowItem((new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41503_(578), 15, 15.0F, 2.75D, 0.2F);
      });
      SPIDER_BOW = registry.register("spider_bow", () -> {
         return new SimpleBowItem((new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41503_(980), 25, 25.0F, 2.75D, 0.5F);
      });
      LONG_SPIDER_BOW = registry.register("long_spider_bow", () -> {
         return new SimpleBowItem((new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41503_(1250), 45, 45.0F, 2.75D, 0.7F);
      });
      WAR_SPIDER_BOW = registry.register("war_spider_bow", () -> {
         return new WarBowItem((new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41503_(1538), 80, 80.0F, 3.5D, 1.0F);
      });
      ANT_CROSSBOW = registry.register("ant_crossbow", () -> {
         return new SimpleCrossbowItem((new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41503_(960), 20, 4.0F, 2.0F, 0.6F);
      });
      INVISIBLE_ARROW = registry.register("invisible_arrow", () -> {
         return new InvisibleArrowItem((new Properties()).m_41491_(TensuraCreativeTab.GEAR));
      });
      SPEARED_FIN_ARROW = registry.register("speared_fin_arrow", () -> {
         return new SpearedFinArrowItem((new Properties()).m_41491_(TensuraCreativeTab.GEAR));
      });
      KUNAI = registry.register("kunai", () -> {
         return new KunaiItem(Tiers.IRON, 7.0F, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41503_(-1).m_41487_(16));
      });
      PURE_MAGISTEEL_KUNAI = registry.register("pure_magisteel_kunai", () -> {
         return new MagisteelKunaiItem(TensuraToolTiers.LOW_MAGISTEEL, 10.0F, 1000);
      });
      AURA_SHIELD = registry.register("aura_shield", () -> {
         return new SimpleShieldItem((new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41503_(500));
      });
      ARMOURSAURUS_SHIELD = registry.register("armoursaurus_shield", () -> {
         return new ArmoursaurusShieldItem((new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41503_(2000));
      });
      TEMPEST_SCALE_SHIELD = registry.register("tempest_scale_shield", () -> {
         return new TempestScaleShieldItem((new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41503_(3964).m_41486_());
      });
      TEMPEST_SCALE_SWORD = registry.register("tempest_scale_sword", () -> {
         return new TempestScaleSwordItem((new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41503_(3964));
      });
      TEMPEST_SCALE_KNIFE = registry.register("tempest_scale_knife", () -> {
         return new TempestScaleKnifeItem((new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41503_(3964));
      });
      CENTIPEDE_DAGGER = registry.register("centipede_dagger", CentipedeDaggerItem::new);
      SPIDER_DAGGER = registry.register("spider_dagger", SpiderDaggerItem::new);
      BEAST_HORN_SPEAR = registry.register("beast_horn_spear", () -> {
         return new SimpleSpearItem(Tiers.IRON, 7, -2.6F, 0.1D, 0.0D, 2, -3.0F, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).setNoRepair());
      });
      UNICORN_HORN_SPEAR = registry.register("unicorn_horn_spear", () -> {
         return new SimpleSpearItem(Tiers.IRON, 9, -2.6F, 0.2D, 0.0D, 4, -3.0F, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41499_(500).setNoRepair());
      });
      BLADE_TIGER_SCYTHE = registry.register("blade_tiger_scythe", BladeTigerScytheItem::new);
      SISSIE_TOOTH_PICKAXE = registry.register("sissie_tooth_pickaxe", SissieToothPickaxe::new);
      WOODEN_SHORT_SWORD = registry.register("wooden_short_sword", () -> {
         return new SimpleShortSwordItem(Tiers.WOOD, (new Properties()).m_41491_(TensuraCreativeTab.GEAR));
      });
      WOODEN_LONG_SWORD = registry.register("wooden_long_sword", () -> {
         return new SimpleLongSwordItem(Tiers.WOOD, (new Properties()).m_41491_(TensuraCreativeTab.GEAR));
      });
      WOODEN_GREAT_SWORD = registry.register("wooden_great_sword", () -> {
         return new SimpleGreatSwordItem(Tiers.WOOD, (new Properties()).m_41491_(TensuraCreativeTab.GEAR));
      });
      WOODEN_KATANA = registry.register("wooden_katana", () -> {
         return new SimpleKatanaItem(Tiers.WOOD, (new Properties()).m_41491_(TensuraCreativeTab.GEAR));
      });
      WOODEN_KODACHI = registry.register("wooden_kodachi", () -> {
         return new SimpleKodachiItem(Tiers.WOOD, (new Properties()).m_41491_(TensuraCreativeTab.GEAR));
      });
      WOODEN_TACHI = registry.register("wooden_tachi", () -> {
         return new SimpleTachiItem(Tiers.WOOD, (new Properties()).m_41491_(TensuraCreativeTab.GEAR));
      });
      WOODEN_ODACHI = registry.register("wooden_odachi", () -> {
         return new SimpleOdachiItem(Tiers.WOOD, (new Properties()).m_41491_(TensuraCreativeTab.GEAR));
      });
      WOODEN_SPEAR = registry.register("wooden_spear", () -> {
         return new SimpleSpearItem(Tiers.WOOD, (new Properties()).m_41491_(TensuraCreativeTab.GEAR));
      });
      WOODEN_SCYTHE = registry.register("wooden_scythe", () -> {
         return new SimpleScytheItem(Tiers.WOOD, (new Properties()).m_41491_(TensuraCreativeTab.GEAR));
      });
      WOODEN_SICKLE = registry.register("wooden_sickle", () -> {
         return new SimpleSickleItem(Tiers.WOOD, (new Properties()).m_41491_(TensuraCreativeTab.GEAR));
      });
      STONE_SHORT_SWORD = registry.register("stone_short_sword", () -> {
         return new SimpleShortSwordItem(Tiers.STONE, (new Properties()).m_41491_(TensuraCreativeTab.GEAR));
      });
      STONE_LONG_SWORD = registry.register("stone_long_sword", () -> {
         return new SimpleLongSwordItem(Tiers.STONE, (new Properties()).m_41491_(TensuraCreativeTab.GEAR));
      });
      STONE_GREAT_SWORD = registry.register("stone_great_sword", () -> {
         return new SimpleGreatSwordItem(Tiers.STONE, (new Properties()).m_41491_(TensuraCreativeTab.GEAR));
      });
      STONE_KATANA = registry.register("stone_katana", () -> {
         return new SimpleKatanaItem(Tiers.STONE, (new Properties()).m_41491_(TensuraCreativeTab.GEAR));
      });
      STONE_KODACHI = registry.register("stone_kodachi", () -> {
         return new SimpleKodachiItem(Tiers.STONE, (new Properties()).m_41491_(TensuraCreativeTab.GEAR));
      });
      STONE_TACHI = registry.register("stone_tachi", () -> {
         return new SimpleTachiItem(Tiers.STONE, (new Properties()).m_41491_(TensuraCreativeTab.GEAR));
      });
      STONE_ODACHI = registry.register("stone_odachi", () -> {
         return new SimpleOdachiItem(Tiers.STONE, (new Properties()).m_41491_(TensuraCreativeTab.GEAR));
      });
      STONE_SPEAR = registry.register("stone_spear", () -> {
         return new SimpleSpearItem(Tiers.STONE, (new Properties()).m_41491_(TensuraCreativeTab.GEAR));
      });
      STONE_SCYTHE = registry.register("stone_scythe", () -> {
         return new SimpleScytheItem(Tiers.STONE, (new Properties()).m_41491_(TensuraCreativeTab.GEAR));
      });
      STONE_SICKLE = registry.register("stone_sickle", () -> {
         return new SimpleSickleItem(Tiers.STONE, (new Properties()).m_41491_(TensuraCreativeTab.GEAR));
      });
      GOLDEN_SHORT_SWORD = registry.register("golden_short_sword", () -> {
         return new SimpleShortSwordItem(Tiers.GOLD, (new Properties()).m_41491_(TensuraCreativeTab.GEAR));
      });
      GOLDEN_LONG_SWORD = registry.register("golden_long_sword", () -> {
         return new SimpleLongSwordItem(Tiers.GOLD, (new Properties()).m_41491_(TensuraCreativeTab.GEAR));
      });
      GOLDEN_GREAT_SWORD = registry.register("golden_great_sword", () -> {
         return new SimpleGreatSwordItem(Tiers.GOLD, (new Properties()).m_41491_(TensuraCreativeTab.GEAR));
      });
      GOLDEN_KATANA = registry.register("golden_katana", () -> {
         return new SimpleKatanaItem(Tiers.GOLD, (new Properties()).m_41491_(TensuraCreativeTab.GEAR));
      });
      GOLDEN_KODACHI = registry.register("golden_kodachi", () -> {
         return new SimpleKodachiItem(Tiers.GOLD, (new Properties()).m_41491_(TensuraCreativeTab.GEAR));
      });
      GOLDEN_TACHI = registry.register("golden_tachi", () -> {
         return new SimpleTachiItem(Tiers.GOLD, (new Properties()).m_41491_(TensuraCreativeTab.GEAR));
      });
      GOLDEN_ODACHI = registry.register("golden_odachi", () -> {
         return new SimpleOdachiItem(Tiers.GOLD, (new Properties()).m_41491_(TensuraCreativeTab.GEAR));
      });
      GOLDEN_SPEAR = registry.register("golden_spear", () -> {
         return new SimpleSpearItem(Tiers.GOLD, (new Properties()).m_41491_(TensuraCreativeTab.GEAR));
      });
      GOLDEN_SCYTHE = registry.register("golden_scythe", () -> {
         return new SimpleScytheItem(Tiers.GOLD, (new Properties()).m_41491_(TensuraCreativeTab.GEAR));
      });
      GOLDEN_SICKLE = registry.register("golden_sickle", () -> {
         return new SimpleSickleItem(Tiers.GOLD, (new Properties()).m_41491_(TensuraCreativeTab.GEAR));
      });
      SILVER_SWORD = registry.register("silver_sword", () -> {
         return new SimpleSwordItem(TensuraToolTiers.SILVER, SimpleSwordItem.SwordModifier.NORMAL);
      });
      SILVER_SHORT_SWORD = registry.register("silver_short_sword", () -> {
         return new SimpleShortSwordItem(TensuraToolTiers.SILVER, (new Properties()).m_41491_(TensuraCreativeTab.GEAR));
      });
      SILVER_LONG_SWORD = registry.register("silver_long_sword", () -> {
         return new SimpleLongSwordItem(TensuraToolTiers.SILVER, (new Properties()).m_41491_(TensuraCreativeTab.GEAR));
      });
      SILVER_GREAT_SWORD = registry.register("silver_great_sword", () -> {
         return new SimpleGreatSwordItem(TensuraToolTiers.SILVER, (new Properties()).m_41491_(TensuraCreativeTab.GEAR));
      });
      SILVER_KATANA = registry.register("silver_katana", () -> {
         return new SimpleKatanaItem(TensuraToolTiers.SILVER, (new Properties()).m_41491_(TensuraCreativeTab.GEAR));
      });
      SILVER_KODACHI = registry.register("silver_kodachi", () -> {
         return new SimpleKodachiItem(TensuraToolTiers.SILVER, (new Properties()).m_41491_(TensuraCreativeTab.GEAR));
      });
      SILVER_TACHI = registry.register("silver_tachi", () -> {
         return new SimpleTachiItem(TensuraToolTiers.SILVER, (new Properties()).m_41491_(TensuraCreativeTab.GEAR));
      });
      SILVER_ODACHI = registry.register("silver_odachi", () -> {
         return new SimpleOdachiItem(TensuraToolTiers.SILVER, (new Properties()).m_41491_(TensuraCreativeTab.GEAR));
      });
      SILVER_SPEAR = registry.register("silver_spear", () -> {
         return new SimpleSpearItem(TensuraToolTiers.SILVER, (new Properties()).m_41491_(TensuraCreativeTab.GEAR));
      });
      SILVER_SCYTHE = registry.register("silver_scythe", () -> {
         return new SimpleScytheItem(TensuraToolTiers.SILVER, (new Properties()).m_41491_(TensuraCreativeTab.GEAR));
      });
      SILVER_PICKAXE = registry.register("silver_pickaxe", () -> {
         return new SimplePickaxeItem(TensuraToolTiers.SILVER, (new Properties()).m_41491_(TensuraCreativeTab.GEAR));
      });
      SILVER_AXE = registry.register("silver_axe", () -> {
         return new SimpleAxeItem(TensuraToolTiers.SILVER, SimpleAxeItem.AxeModifier.SILVER);
      });
      SILVER_SHOVEL = registry.register("silver_shovel", () -> {
         return new SimpleShovelItem(TensuraToolTiers.SILVER, (new Properties()).m_41491_(TensuraCreativeTab.GEAR));
      });
      SILVER_HOE = registry.register("silver_hoe", () -> {
         return new SimpleHoeItem(TensuraToolTiers.SILVER, SimpleHoeItem.HoeModifier.SILVER);
      });
      SILVER_SICKLE = registry.register("silver_sickle", () -> {
         return new SimpleSickleItem(TensuraToolTiers.SILVER, (new Properties()).m_41491_(TensuraCreativeTab.GEAR));
      });
      IRON_SHORT_SWORD = registry.register("iron_short_sword", () -> {
         return new SimpleShortSwordItem(Tiers.IRON, (new Properties()).m_41491_(TensuraCreativeTab.GEAR));
      });
      IRON_LONG_SWORD = registry.register("iron_long_sword", () -> {
         return new SimpleLongSwordItem(Tiers.IRON, (new Properties()).m_41491_(TensuraCreativeTab.GEAR));
      });
      IRON_GREAT_SWORD = registry.register("iron_great_sword", () -> {
         return new SimpleGreatSwordItem(Tiers.IRON, (new Properties()).m_41491_(TensuraCreativeTab.GEAR));
      });
      IRON_KATANA = registry.register("iron_katana", () -> {
         return new SimpleKatanaItem(Tiers.IRON, (new Properties()).m_41491_(TensuraCreativeTab.GEAR));
      });
      IRON_KODACHI = registry.register("iron_kodachi", () -> {
         return new SimpleKodachiItem(Tiers.IRON, (new Properties()).m_41491_(TensuraCreativeTab.GEAR));
      });
      IRON_TACHI = registry.register("iron_tachi", () -> {
         return new SimpleTachiItem(Tiers.IRON, (new Properties()).m_41491_(TensuraCreativeTab.GEAR));
      });
      IRON_ODACHI = registry.register("iron_odachi", () -> {
         return new SimpleOdachiItem(Tiers.IRON, (new Properties()).m_41491_(TensuraCreativeTab.GEAR));
      });
      IRON_SPEAR = registry.register("iron_spear", () -> {
         return new SimpleSpearItem(Tiers.IRON, (new Properties()).m_41491_(TensuraCreativeTab.GEAR));
      });
      IRON_SCYTHE = registry.register("iron_scythe", () -> {
         return new SimpleScytheItem(Tiers.IRON, (new Properties()).m_41491_(TensuraCreativeTab.GEAR));
      });
      IRON_SICKLE = registry.register("iron_sickle", () -> {
         return new SimpleSickleItem(Tiers.IRON, (new Properties()).m_41491_(TensuraCreativeTab.GEAR));
      });
      DIAMOND_SHORT_SWORD = registry.register("diamond_short_sword", () -> {
         return new SimpleShortSwordItem(Tiers.DIAMOND, (new Properties()).m_41491_(TensuraCreativeTab.GEAR));
      });
      DIAMOND_LONG_SWORD = registry.register("diamond_long_sword", () -> {
         return new SimpleLongSwordItem(Tiers.DIAMOND, (new Properties()).m_41491_(TensuraCreativeTab.GEAR));
      });
      DIAMOND_GREAT_SWORD = registry.register("diamond_great_sword", () -> {
         return new SimpleGreatSwordItem(Tiers.DIAMOND, (new Properties()).m_41491_(TensuraCreativeTab.GEAR));
      });
      DIAMOND_KATANA = registry.register("diamond_katana", () -> {
         return new SimpleKatanaItem(Tiers.DIAMOND, (new Properties()).m_41491_(TensuraCreativeTab.GEAR));
      });
      DIAMOND_KODACHI = registry.register("diamond_kodachi", () -> {
         return new SimpleKodachiItem(Tiers.DIAMOND, (new Properties()).m_41491_(TensuraCreativeTab.GEAR));
      });
      DIAMOND_TACHI = registry.register("diamond_tachi", () -> {
         return new SimpleTachiItem(Tiers.DIAMOND, (new Properties()).m_41491_(TensuraCreativeTab.GEAR));
      });
      DIAMOND_ODACHI = registry.register("diamond_odachi", () -> {
         return new SimpleOdachiItem(Tiers.DIAMOND, (new Properties()).m_41491_(TensuraCreativeTab.GEAR));
      });
      DIAMOND_SPEAR = registry.register("diamond_spear", () -> {
         return new SimpleSpearItem(Tiers.DIAMOND, (new Properties()).m_41491_(TensuraCreativeTab.GEAR));
      });
      DIAMOND_SCYTHE = registry.register("diamond_scythe", () -> {
         return new SimpleScytheItem(Tiers.DIAMOND, (new Properties()).m_41491_(TensuraCreativeTab.GEAR));
      });
      DIAMOND_SICKLE = registry.register("diamond_sickle", () -> {
         return new SimpleSickleItem(Tiers.DIAMOND, (new Properties()).m_41491_(TensuraCreativeTab.GEAR));
      });
      LOW_MAGISTEEL_SWORD = registry.register("low_magisteel_sword", () -> {
         return new SimpleSwordItem(TensuraToolTiers.LOW_MAGISTEEL, SimpleSwordItem.SwordModifier.NORMAL);
      });
      LOW_MAGISTEEL_SHORT_SWORD = registry.register("low_magisteel_short_sword", () -> {
         return new SimpleShortSwordItem(TensuraToolTiers.LOW_MAGISTEEL, (new Properties()).m_41491_(TensuraCreativeTab.GEAR));
      });
      LOW_MAGISTEEL_LONG_SWORD = registry.register("low_magisteel_long_sword", () -> {
         return new SimpleLongSwordItem(TensuraToolTiers.LOW_MAGISTEEL, (new Properties()).m_41491_(TensuraCreativeTab.GEAR));
      });
      LOW_MAGISTEEL_GREAT_SWORD = registry.register("low_magisteel_great_sword", () -> {
         return new SimpleGreatSwordItem(TensuraToolTiers.LOW_MAGISTEEL, (new Properties()).m_41491_(TensuraCreativeTab.GEAR));
      });
      LOW_MAGISTEEL_KATANA = registry.register("low_magisteel_katana", () -> {
         return new SimpleKatanaItem(TensuraToolTiers.LOW_MAGISTEEL, (new Properties()).m_41491_(TensuraCreativeTab.GEAR));
      });
      LOW_MAGISTEEL_KODACHI = registry.register("low_magisteel_kodachi", () -> {
         return new SimpleKodachiItem(TensuraToolTiers.LOW_MAGISTEEL, (new Properties()).m_41491_(TensuraCreativeTab.GEAR));
      });
      LOW_MAGISTEEL_TACHI = registry.register("low_magisteel_tachi", () -> {
         return new SimpleTachiItem(TensuraToolTiers.LOW_MAGISTEEL, (new Properties()).m_41491_(TensuraCreativeTab.GEAR));
      });
      LOW_MAGISTEEL_ODACHI = registry.register("low_magisteel_odachi", () -> {
         return new SimpleOdachiItem(TensuraToolTiers.LOW_MAGISTEEL, (new Properties()).m_41491_(TensuraCreativeTab.GEAR));
      });
      LOW_MAGISTEEL_SPEAR = registry.register("low_magisteel_spear", () -> {
         return new SimpleSpearItem(TensuraToolTiers.LOW_MAGISTEEL, (new Properties()).m_41491_(TensuraCreativeTab.GEAR));
      });
      LOW_MAGISTEEL_SCYTHE = registry.register("low_magisteel_scythe", () -> {
         return new SimpleScytheItem(TensuraToolTiers.LOW_MAGISTEEL, (new Properties()).m_41491_(TensuraCreativeTab.GEAR));
      });
      LOW_MAGISTEEL_PICKAXE = registry.register("low_magisteel_pickaxe", () -> {
         return new SimplePickaxeItem(TensuraToolTiers.LOW_MAGISTEEL, (new Properties()).m_41491_(TensuraCreativeTab.GEAR));
      });
      LOW_MAGISTEEL_AXE = registry.register("low_magisteel_axe", () -> {
         return new SimpleAxeItem(TensuraToolTiers.LOW_MAGISTEEL, SimpleAxeItem.AxeModifier.LOW_MAGISTEEL);
      });
      LOW_MAGISTEEL_SHOVEL = registry.register("low_magisteel_shovel", () -> {
         return new SimpleShovelItem(TensuraToolTiers.LOW_MAGISTEEL, (new Properties()).m_41491_(TensuraCreativeTab.GEAR));
      });
      LOW_MAGISTEEL_HOE = registry.register("low_magisteel_hoe", () -> {
         return new SimpleHoeItem(TensuraToolTiers.LOW_MAGISTEEL, SimpleHoeItem.HoeModifier.LOW_MAGISTEEL);
      });
      LOW_MAGISTEEL_SICKLE = registry.register("low_magisteel_sickle", () -> {
         return new SimpleSickleItem(TensuraToolTiers.LOW_MAGISTEEL, (new Properties()).m_41491_(TensuraCreativeTab.GEAR));
      });
      NETHERITE_SHORT_SWORD = registry.register("netherite_short_sword", () -> {
         return new SimpleShortSwordItem(Tiers.NETHERITE, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41486_());
      });
      NETHERITE_LONG_SWORD = registry.register("netherite_long_sword", () -> {
         return new SimpleLongSwordItem(Tiers.NETHERITE, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41486_());
      });
      NETHERITE_GREAT_SWORD = registry.register("netherite_great_sword", () -> {
         return new SimpleGreatSwordItem(Tiers.NETHERITE, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41486_());
      });
      NETHERITE_KATANA = registry.register("netherite_katana", () -> {
         return new SimpleKatanaItem(Tiers.NETHERITE, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41486_());
      });
      NETHERITE_KODACHI = registry.register("netherite_kodachi", () -> {
         return new SimpleKodachiItem(Tiers.NETHERITE, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41486_());
      });
      NETHERITE_TACHI = registry.register("netherite_tachi", () -> {
         return new SimpleTachiItem(Tiers.NETHERITE, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41486_());
      });
      NETHERITE_ODACHI = registry.register("netherite_odachi", () -> {
         return new SimpleOdachiItem(Tiers.NETHERITE, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41486_());
      });
      NETHERITE_SPEAR = registry.register("netherite_spear", () -> {
         return new SimpleSpearItem(Tiers.NETHERITE, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41486_());
      });
      NETHERITE_SCYTHE = registry.register("netherite_scythe", () -> {
         return new SimpleScytheItem(Tiers.NETHERITE, (new Properties()).m_41491_(TensuraCreativeTab.GEAR));
      });
      NETHERITE_SICKLE = registry.register("netherite_sickle", () -> {
         return new SimpleSickleItem(Tiers.NETHERITE, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41486_());
      });
      HIGH_MAGISTEEL_SWORD = registry.register("high_magisteel_sword", () -> {
         return new SimpleSwordItem(TensuraToolTiers.HIGH_MAGISTEEL, SimpleSwordItem.SwordModifier.FIRE_RESISTED);
      });
      HIGH_MAGISTEEL_SHORT_SWORD = registry.register("high_magisteel_short_sword", () -> {
         return new SimpleShortSwordItem(TensuraToolTiers.HIGH_MAGISTEEL, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41486_());
      });
      HIGH_MAGISTEEL_LONG_SWORD = registry.register("high_magisteel_long_sword", () -> {
         return new SimpleLongSwordItem(TensuraToolTiers.HIGH_MAGISTEEL, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41486_());
      });
      HIGH_MAGISTEEL_GREAT_SWORD = registry.register("high_magisteel_great_sword", () -> {
         return new SimpleGreatSwordItem(TensuraToolTiers.HIGH_MAGISTEEL, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41486_());
      });
      HIGH_MAGISTEEL_KATANA = registry.register("high_magisteel_katana", () -> {
         return new SimpleKatanaItem(TensuraToolTiers.HIGH_MAGISTEEL, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41486_());
      });
      HIGH_MAGISTEEL_KODACHI = registry.register("high_magisteel_kodachi", () -> {
         return new SimpleKodachiItem(TensuraToolTiers.HIGH_MAGISTEEL, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41486_());
      });
      HIGH_MAGISTEEL_TACHI = registry.register("high_magisteel_tachi", () -> {
         return new SimpleTachiItem(TensuraToolTiers.HIGH_MAGISTEEL, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41486_());
      });
      HIGH_MAGISTEEL_ODACHI = registry.register("high_magisteel_odachi", () -> {
         return new SimpleOdachiItem(TensuraToolTiers.HIGH_MAGISTEEL, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41486_());
      });
      HIGH_MAGISTEEL_SPEAR = registry.register("high_magisteel_spear", () -> {
         return new SimpleSpearItem(TensuraToolTiers.HIGH_MAGISTEEL, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41486_());
      });
      HIGH_MAGISTEEL_SCYTHE = registry.register("high_magisteel_scythe", () -> {
         return new SimpleScytheItem(TensuraToolTiers.HIGH_MAGISTEEL, (new Properties()).m_41491_(TensuraCreativeTab.GEAR));
      });
      HIGH_MAGISTEEL_PICKAXE = registry.register("high_magisteel_pickaxe", () -> {
         return new SimplePickaxeItem(TensuraToolTiers.HIGH_MAGISTEEL, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41486_());
      });
      HIGH_MAGISTEEL_AXE = registry.register("high_magisteel_axe", () -> {
         return new SimpleAxeItem(TensuraToolTiers.HIGH_MAGISTEEL, SimpleAxeItem.AxeModifier.HIGH_MAGISTEEL);
      });
      HIGH_MAGISTEEL_SHOVEL = registry.register("high_magisteel_shovel", () -> {
         return new SimpleShovelItem(TensuraToolTiers.HIGH_MAGISTEEL, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41486_());
      });
      HIGH_MAGISTEEL_HOE = registry.register("high_magisteel_hoe", () -> {
         return new SimpleHoeItem(TensuraToolTiers.HIGH_MAGISTEEL, SimpleHoeItem.HoeModifier.HIGH_MAGISTEEL);
      });
      HIGH_MAGISTEEL_SICKLE = registry.register("high_magisteel_sickle", () -> {
         return new SimpleSickleItem(TensuraToolTiers.HIGH_MAGISTEEL, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41486_());
      });
      MITHRIL_SWORD = registry.register("mithril_sword", () -> {
         return new SimpleSwordItem(TensuraToolTiers.MITHRIL, SimpleSwordItem.SwordModifier.FIRE_RESISTED);
      });
      MITHRIL_SHORT_SWORD = registry.register("mithril_short_sword", () -> {
         return new SimpleShortSwordItem(TensuraToolTiers.MITHRIL, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41486_());
      });
      MITHRIL_LONG_SWORD = registry.register("mithril_long_sword", () -> {
         return new SimpleLongSwordItem(TensuraToolTiers.MITHRIL, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41486_());
      });
      MITHRIL_GREAT_SWORD = registry.register("mithril_great_sword", () -> {
         return new SimpleGreatSwordItem(TensuraToolTiers.MITHRIL, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41486_());
      });
      MITHRIL_KATANA = registry.register("mithril_katana", () -> {
         return new SimpleKatanaItem(TensuraToolTiers.MITHRIL, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41486_());
      });
      MITHRIL_KODACHI = registry.register("mithril_kodachi", () -> {
         return new SimpleKodachiItem(TensuraToolTiers.MITHRIL, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41486_());
      });
      MITHRIL_TACHI = registry.register("mithril_tachi", () -> {
         return new SimpleTachiItem(TensuraToolTiers.MITHRIL, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41486_());
      });
      MITHRIL_ODACHI = registry.register("mithril_odachi", () -> {
         return new SimpleOdachiItem(TensuraToolTiers.MITHRIL, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41486_());
      });
      MITHRIL_SPEAR = registry.register("mithril_spear", () -> {
         return new SimpleSpearItem(TensuraToolTiers.MITHRIL, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41486_());
      });
      MITHRIL_SCYTHE = registry.register("mithril_scythe", () -> {
         return new SimpleScytheItem(TensuraToolTiers.MITHRIL, (new Properties()).m_41491_(TensuraCreativeTab.GEAR));
      });
      MITHRIL_PICKAXE = registry.register("mithril_pickaxe", () -> {
         return new SimplePickaxeItem(TensuraToolTiers.MITHRIL, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41486_());
      });
      MITHRIL_AXE = registry.register("mithril_axe", () -> {
         return new SimpleAxeItem(TensuraToolTiers.MITHRIL, SimpleAxeItem.AxeModifier.MITHRIL);
      });
      MITHRIL_SHOVEL = registry.register("mithril_shovel", () -> {
         return new SimpleShovelItem(TensuraToolTiers.MITHRIL, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41486_());
      });
      MITHRIL_HOE = registry.register("mithril_hoe", () -> {
         return new SimpleHoeItem(TensuraToolTiers.MITHRIL, SimpleHoeItem.HoeModifier.MITHRIL);
      });
      MITHRIL_SICKLE = registry.register("mithril_sickle", () -> {
         return new SimpleSickleItem(TensuraToolTiers.MITHRIL, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41486_());
      });
      ORICHALCUM_SWORD = registry.register("orichalcum_sword", () -> {
         return new SimpleSwordItem(TensuraToolTiers.ORICHALCUM, SimpleSwordItem.SwordModifier.FIRE_RESISTED);
      });
      ORICHALCUM_SHORT_SWORD = registry.register("orichalcum_short_sword", () -> {
         return new SimpleShortSwordItem(TensuraToolTiers.ORICHALCUM, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41486_());
      });
      ORICHALCUM_LONG_SWORD = registry.register("orichalcum_long_sword", () -> {
         return new SimpleLongSwordItem(TensuraToolTiers.ORICHALCUM, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41486_());
      });
      ORICHALCUM_GREAT_SWORD = registry.register("orichalcum_great_sword", () -> {
         return new SimpleGreatSwordItem(TensuraToolTiers.ORICHALCUM, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41486_());
      });
      ORICHALCUM_KATANA = registry.register("orichalcum_katana", () -> {
         return new SimpleKatanaItem(TensuraToolTiers.ORICHALCUM, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41486_());
      });
      ORICHALCUM_KODACHI = registry.register("orichalcum_kodachi", () -> {
         return new SimpleKodachiItem(TensuraToolTiers.ORICHALCUM, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41486_());
      });
      ORICHALCUM_TACHI = registry.register("orichalcum_tachi", () -> {
         return new SimpleTachiItem(TensuraToolTiers.ORICHALCUM, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41486_());
      });
      ORICHALCUM_ODACHI = registry.register("orichalcum_odachi", () -> {
         return new SimpleOdachiItem(TensuraToolTiers.ORICHALCUM, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41486_());
      });
      ORICHALCUM_SPEAR = registry.register("orichalcum_spear", () -> {
         return new SimpleSpearItem(TensuraToolTiers.ORICHALCUM, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41486_());
      });
      ORICHALCUM_SCYTHE = registry.register("orichalcum_scythe", () -> {
         return new SimpleScytheItem(TensuraToolTiers.ORICHALCUM, (new Properties()).m_41491_(TensuraCreativeTab.GEAR));
      });
      ORICHALCUM_PICKAXE = registry.register("orichalcum_pickaxe", () -> {
         return new SimplePickaxeItem(TensuraToolTiers.ORICHALCUM, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41486_());
      });
      ORICHALCUM_AXE = registry.register("orichalcum_axe", () -> {
         return new SimpleAxeItem(TensuraToolTiers.ORICHALCUM, SimpleAxeItem.AxeModifier.ORICHALCUM);
      });
      ORICHALCUM_SHOVEL = registry.register("orichalcum_shovel", () -> {
         return new SimpleShovelItem(TensuraToolTiers.ORICHALCUM, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41486_());
      });
      ORICHALCUM_HOE = registry.register("orichalcum_hoe", () -> {
         return new SimpleHoeItem(TensuraToolTiers.ORICHALCUM, SimpleHoeItem.HoeModifier.ORICHALCUM);
      });
      ORICHALCUM_SICKLE = registry.register("orichalcum_sickle", () -> {
         return new SimpleSickleItem(TensuraToolTiers.ORICHALCUM, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41486_());
      });
      PURE_MAGISTEEL_SWORD = registry.register("pure_magisteel_sword", () -> {
         return new SimpleSwordItem(TensuraToolTiers.PURE_MAGISTEEL, SimpleSwordItem.SwordModifier.FIRE_RESISTED);
      });
      PURE_MAGISTEEL_SHORT_SWORD = registry.register("pure_magisteel_short_sword", () -> {
         return new SimpleShortSwordItem(TensuraToolTiers.PURE_MAGISTEEL, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41486_());
      });
      PURE_MAGISTEEL_LONG_SWORD = registry.register("pure_magisteel_long_sword", () -> {
         return new SimpleLongSwordItem(TensuraToolTiers.PURE_MAGISTEEL, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41486_());
      });
      PURE_MAGISTEEL_GREAT_SWORD = registry.register("pure_magisteel_great_sword", () -> {
         return new SimpleGreatSwordItem(TensuraToolTiers.PURE_MAGISTEEL, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41486_());
      });
      PURE_MAGISTEEL_KATANA = registry.register("pure_magisteel_katana", () -> {
         return new SimpleKatanaItem(TensuraToolTiers.PURE_MAGISTEEL, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41486_());
      });
      PURE_MAGISTEEL_KODACHI = registry.register("pure_magisteel_kodachi", () -> {
         return new SimpleKodachiItem(TensuraToolTiers.PURE_MAGISTEEL, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41486_());
      });
      PURE_MAGISTEEL_TACHI = registry.register("pure_magisteel_tachi", () -> {
         return new SimpleTachiItem(TensuraToolTiers.PURE_MAGISTEEL, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41486_());
      });
      PURE_MAGISTEEL_ODACHI = registry.register("pure_magisteel_odachi", () -> {
         return new SimpleOdachiItem(TensuraToolTiers.PURE_MAGISTEEL, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41486_());
      });
      PURE_MAGISTEEL_SPEAR = registry.register("pure_magisteel_spear", () -> {
         return new SimpleSpearItem(TensuraToolTiers.PURE_MAGISTEEL, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41486_());
      });
      PURE_MAGISTEEL_SCYTHE = registry.register("pure_magisteel_scythe", () -> {
         return new SimpleScytheItem(TensuraToolTiers.PURE_MAGISTEEL, (new Properties()).m_41491_(TensuraCreativeTab.GEAR));
      });
      PURE_MAGISTEEL_PICKAXE = registry.register("pure_magisteel_pickaxe", () -> {
         return new SimplePickaxeItem(TensuraToolTiers.PURE_MAGISTEEL, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41486_());
      });
      PURE_MAGISTEEL_AXE = registry.register("pure_magisteel_axe", () -> {
         return new SimpleAxeItem(TensuraToolTiers.PURE_MAGISTEEL, SimpleAxeItem.AxeModifier.PURE_MAGISTEEL);
      });
      PURE_MAGISTEEL_SHOVEL = registry.register("pure_magisteel_shovel", () -> {
         return new SimpleShovelItem(TensuraToolTiers.PURE_MAGISTEEL, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41486_());
      });
      PURE_MAGISTEEL_HOE = registry.register("pure_magisteel_hoe", () -> {
         return new SimpleHoeItem(TensuraToolTiers.PURE_MAGISTEEL, SimpleHoeItem.HoeModifier.PURE_MAGISTEEL);
      });
      PURE_MAGISTEEL_SICKLE = registry.register("pure_magisteel_sickle", () -> {
         return new SimpleSickleItem(TensuraToolTiers.PURE_MAGISTEEL, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41486_());
      });
      ADAMANTITE_SWORD = registry.register("adamantite_sword", () -> {
         return new SimpleSwordItem(TensuraToolTiers.ADAMANTITE, SimpleSwordItem.SwordModifier.FIRE_RESISTED);
      });
      ADAMANTITE_SHORT_SWORD = registry.register("adamantite_short_sword", () -> {
         return new SimpleShortSwordItem(TensuraToolTiers.ADAMANTITE, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41486_());
      });
      ADAMANTITE_LONG_SWORD = registry.register("adamantite_long_sword", () -> {
         return new SimpleLongSwordItem(TensuraToolTiers.ADAMANTITE, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41486_());
      });
      ADAMANTITE_GREAT_SWORD = registry.register("adamantite_great_sword", () -> {
         return new SimpleGreatSwordItem(TensuraToolTiers.ADAMANTITE, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41486_());
      });
      ADAMANTITE_KATANA = registry.register("adamantite_katana", () -> {
         return new SimpleKatanaItem(TensuraToolTiers.ADAMANTITE, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41486_());
      });
      ADAMANTITE_KODACHI = registry.register("adamantite_kodachi", () -> {
         return new SimpleKodachiItem(TensuraToolTiers.ADAMANTITE, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41486_());
      });
      ADAMANTITE_TACHI = registry.register("adamantite_tachi", () -> {
         return new SimpleTachiItem(TensuraToolTiers.ADAMANTITE, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41486_());
      });
      ADAMANTITE_ODACHI = registry.register("adamantite_odachi", () -> {
         return new SimpleOdachiItem(TensuraToolTiers.ADAMANTITE, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41486_());
      });
      ADAMANTITE_SPEAR = registry.register("adamantite_spear", () -> {
         return new SimpleSpearItem(TensuraToolTiers.ADAMANTITE, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41486_());
      });
      ADAMANTITE_SCYTHE = registry.register("adamantite_scythe", () -> {
         return new SimpleScytheItem(TensuraToolTiers.ADAMANTITE, (new Properties()).m_41491_(TensuraCreativeTab.GEAR));
      });
      ADAMANTITE_PICKAXE = registry.register("adamantite_pickaxe", () -> {
         return new SimplePickaxeItem(TensuraToolTiers.ADAMANTITE, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41486_());
      });
      ADAMANTITE_AXE = registry.register("adamantite_axe", () -> {
         return new SimpleAxeItem(TensuraToolTiers.ADAMANTITE, SimpleAxeItem.AxeModifier.ADAMANTITE);
      });
      ADAMANTITE_SHOVEL = registry.register("adamantite_shovel", () -> {
         return new SimpleShovelItem(TensuraToolTiers.ADAMANTITE, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41486_());
      });
      ADAMANTITE_HOE = registry.register("adamantite_hoe", () -> {
         return new SimpleHoeItem(TensuraToolTiers.ADAMANTITE, SimpleHoeItem.HoeModifier.ADAMANTITE);
      });
      ADAMANTITE_SICKLE = registry.register("adamantite_sickle", () -> {
         return new SimpleSickleItem(TensuraToolTiers.ADAMANTITE, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41486_());
      });
      HIHIIROKANE_SWORD = registry.register("hihiirokane_sword", () -> {
         return new SimpleSwordItem(TensuraToolTiers.HIHIIROKANE, SimpleSwordItem.SwordModifier.FIRE_RESISTED);
      });
      HIHIIROKANE_SHORT_SWORD = registry.register("hihiirokane_short_sword", () -> {
         return new SimpleShortSwordItem(TensuraToolTiers.HIHIIROKANE, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41486_());
      });
      HIHIIROKANE_LONG_SWORD = registry.register("hihiirokane_long_sword", () -> {
         return new SimpleLongSwordItem(TensuraToolTiers.HIHIIROKANE, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41486_());
      });
      HIHIIROKANE_GREAT_SWORD = registry.register("hihiirokane_great_sword", () -> {
         return new SimpleGreatSwordItem(TensuraToolTiers.HIHIIROKANE, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41486_());
      });
      HIHIIROKANE_KATANA = registry.register("hihiirokane_katana", () -> {
         return new SimpleKatanaItem(TensuraToolTiers.HIHIIROKANE, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41486_());
      });
      HIHIIROKANE_KODACHI = registry.register("hihiirokane_kodachi", () -> {
         return new SimpleKodachiItem(TensuraToolTiers.HIHIIROKANE, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41486_());
      });
      HIHIIROKANE_TACHI = registry.register("hihiirokane_tachi", () -> {
         return new SimpleTachiItem(TensuraToolTiers.HIHIIROKANE, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41486_());
      });
      HIHIIROKANE_ODACHI = registry.register("hihiirokane_odachi", () -> {
         return new SimpleOdachiItem(TensuraToolTiers.HIHIIROKANE, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41486_());
      });
      HIHIIROKANE_SPEAR = registry.register("hihiirokane_spear", () -> {
         return new SimpleSpearItem(TensuraToolTiers.HIHIIROKANE, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41486_());
      });
      HIHIIROKANE_SCYTHE = registry.register("hihiirokane_scythe", () -> {
         return new SimpleScytheItem(TensuraToolTiers.HIHIIROKANE, (new Properties()).m_41491_(TensuraCreativeTab.GEAR));
      });
      HIHIIROKANE_PICKAXE = registry.register("hihiirokane_pickaxe", () -> {
         return new SimplePickaxeItem(TensuraToolTiers.HIHIIROKANE, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41486_());
      });
      HIHIIROKANE_AXE = registry.register("hihiirokane_axe", () -> {
         return new SimpleAxeItem(TensuraToolTiers.HIHIIROKANE, SimpleAxeItem.AxeModifier.HIHIIROKANE);
      });
      HIHIIROKANE_SHOVEL = registry.register("hihiirokane_shovel", () -> {
         return new SimpleShovelItem(TensuraToolTiers.HIHIIROKANE, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41486_());
      });
      HIHIIROKANE_HOE = registry.register("hihiirokane_hoe", () -> {
         return new SimpleHoeItem(TensuraToolTiers.HIHIIROKANE, SimpleHoeItem.HoeModifier.HIHIIROKANE);
      });
      HIHIIROKANE_SICKLE = registry.register("hihiirokane_sickle", () -> {
         return new SimpleSickleItem(TensuraToolTiers.HIHIIROKANE, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41486_());
      });
      ORB_OF_DOMINATION = registry.register("orb_of_domination", OrbOfDominationItem::new);
      ARMOURSAURUS_GAUNTLET = registry.register("armoursaurus_gauntlet", ArmoursaurusGauntletItem::new);
      DRAGON_KNUCKLE = registry.register("dragon_knuckle", DragonKnuckleItem::new);
      DEAD_END_RAINBOW = registry.register("dead_end_rainbow", DeadEndRainbowItem::new);
      ICE_BLADE = registry.register("ice_blade", IceBladeItem::new);
      MEAT_CRUSHER = registry.register("meat_crusher", MeatCrusherItem::new);
      MOONLIGHT = registry.register("moonlight", MoonlightItem::new);
      SPATIAL_BLADE = registry.register("spatial_blade", SpatialBladeItem::new);
      SEVERER_BLADE = registry.register("severer_blade", () -> {
         return new Item((new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41487_(64));
      });
      SNIPER_PISTOL = registry.register("sniper_pistol", SniperPistolItem::new);
      VORTEX_SPEAR = registry.register("vortex_spear", VortexSpearItem::new);
      SLIME_STAFF = registry.register("slime_staff", SlimeStaffItem::new);
      WEB_GUN = registry.register("web_gun", WebGunItem::new);
      COPPER_SHELL = registry.register("copper_shell", () -> {
         return new Item((new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41487_(64));
      });
      WEB_CARTRIDGE = registry.register("web_cartridge", () -> {
         return new WebCartridgeItem(100, 100, 0.25D, 0, Blocks.f_50033_);
      });
      STICKY_WEB_CARTRIDGE = registry.register("sticky_web_cartridge", () -> {
         return new WebCartridgeItem(200, 200, 0.25D, 160, (Block)TensuraBlocks.STICKY_COBWEB.get());
      });
      STICKY_STEEL_WEB_CARTRIDGE = registry.register("sticky_steel_web_cartridge", () -> {
         return new WebCartridgeItem(200, 200, 0.25D, 240, (Block)TensuraBlocks.STICKY_STEEL_COBWEB.get());
      });
   }
}
