package com.github.manasmods.tensura.registry.battlewill;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.tensura.ability.battlewill.utility.AirFlightSkill;
import com.github.manasmods.tensura.ability.battlewill.utility.AuraShieldArt;
import com.github.manasmods.tensura.ability.battlewill.utility.BattlewillArt;
import com.github.manasmods.tensura.ability.battlewill.utility.DiamondPathArt;
import com.github.manasmods.tensura.ability.battlewill.utility.FormhideArt;
import com.github.manasmods.tensura.ability.battlewill.utility.HazeArt;
import com.github.manasmods.tensura.ability.battlewill.utility.InstantMoveArt;
import com.github.manasmods.tensura.ability.battlewill.utility.ViolentBreakArt;
import net.minecraftforge.eventbus.api.IEventBus;
import net.minecraftforge.registries.DeferredRegister;
import net.minecraftforge.registries.RegistryObject;

public class UtilityArts {
   private static final DeferredRegister<ManasSkill> registry = DeferredRegister.create(SkillAPI.getSkillRegistryKey(), "tensura");
   public static final RegistryObject<AirFlightSkill> AIR_FLIGHT;
   public static final RegistryObject<AuraShieldArt> AURA_SHIELD;
   public static final RegistryObject<BattlewillArt> BATTLEWILL;
   public static final RegistryObject<DiamondPathArt> DIAMOND_PATH;
   public static final RegistryObject<FormhideArt> FORMHIDE;
   public static final RegistryObject<HazeArt> HAZE;
   public static final RegistryObject<InstantMoveArt> INSTANT_MOVE;
   public static final RegistryObject<ViolentBreakArt> VIOLENT_BREAK;

   public static void init(IEventBus modEventBus) {
      registry.register(modEventBus);
   }

   static {
      AIR_FLIGHT = registry.register("air_flight", AirFlightSkill::new);
      AURA_SHIELD = registry.register("aura_shield", AuraShieldArt::new);
      BATTLEWILL = registry.register("battlewill", BattlewillArt::new);
      DIAMOND_PATH = registry.register("diamond_path", DiamondPathArt::new);
      FORMHIDE = registry.register("formhide", FormhideArt::new);
      HAZE = registry.register("haze", HazeArt::new);
      INSTANT_MOVE = registry.register("instant_move", InstantMoveArt::new);
      VIOLENT_BREAK = registry.register("violent_break", ViolentBreakArt::new);
   }
}
