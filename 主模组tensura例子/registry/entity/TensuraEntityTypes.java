package com.github.manasmods.tensura.registry.entity;

import com.github.manasmods.tensura.entity.AkashEntity;
import com.github.manasmods.tensura.entity.AquaFrogEntity;
import com.github.manasmods.tensura.entity.ArmoursaurusEntity;
import com.github.manasmods.tensura.entity.ArmyWaspEntity;
import com.github.manasmods.tensura.entity.BarghestEntity;
import com.github.manasmods.tensura.entity.BeastGnomeEntity;
import com.github.manasmods.tensura.entity.BlackSpiderEntity;
import com.github.manasmods.tensura.entity.BladeTigerEntity;
import com.github.manasmods.tensura.entity.BulldeerEntity;
import com.github.manasmods.tensura.entity.CharybdisEntity;
import com.github.manasmods.tensura.entity.DirewolfEntity;
import com.github.manasmods.tensura.entity.DragonPeacockEntity;
import com.github.manasmods.tensura.entity.ElementalColossusEntity;
import com.github.manasmods.tensura.entity.FeatheredSerpentEntity;
import com.github.manasmods.tensura.entity.GiantAntEntity;
import com.github.manasmods.tensura.entity.GiantBatEntity;
import com.github.manasmods.tensura.entity.GiantBearEntity;
import com.github.manasmods.tensura.entity.GiantCodEntity;
import com.github.manasmods.tensura.entity.GiantSalmonEntity;
import com.github.manasmods.tensura.entity.GoblinEntity;
import com.github.manasmods.tensura.entity.HellCaterpillarEntity;
import com.github.manasmods.tensura.entity.HellMothEntity;
import com.github.manasmods.tensura.entity.HolyCowEntity;
import com.github.manasmods.tensura.entity.HornedBearEntity;
import com.github.manasmods.tensura.entity.HornedRabbitEntity;
import com.github.manasmods.tensura.entity.HoundDogEntity;
import com.github.manasmods.tensura.entity.HoverLizardEntity;
import com.github.manasmods.tensura.entity.IfritCloneEntity;
import com.github.manasmods.tensura.entity.IfritEntity;
import com.github.manasmods.tensura.entity.KnightSpiderEntity;
import com.github.manasmods.tensura.entity.LandfishEntity;
import com.github.manasmods.tensura.entity.LeechLizardEntity;
import com.github.manasmods.tensura.entity.LizardmanEntity;
import com.github.manasmods.tensura.entity.MegalodonEntity;
import com.github.manasmods.tensura.entity.MetalSlimeEntity;
import com.github.manasmods.tensura.entity.OneEyedOwlEntity;
import com.github.manasmods.tensura.entity.OrcDisasterEntity;
import com.github.manasmods.tensura.entity.OrcEntity;
import com.github.manasmods.tensura.entity.OrcLordEntity;
import com.github.manasmods.tensura.entity.SalamanderEntity;
import com.github.manasmods.tensura.entity.SissieEntity;
import com.github.manasmods.tensura.entity.SlimeEntity;
import com.github.manasmods.tensura.entity.SpearToroEntity;
import com.github.manasmods.tensura.entity.SupermassiveSlimeEntity;
import com.github.manasmods.tensura.entity.SylphideEntity;
import com.github.manasmods.tensura.entity.UndineEntity;
import com.github.manasmods.tensura.entity.UnicornEntity;
import com.github.manasmods.tensura.entity.WarGnomeEntity;
import com.github.manasmods.tensura.entity.WingedCatEntity;
import com.github.manasmods.tensura.entity.human.CloneEntity;
import com.github.manasmods.tensura.entity.human.FalmuthKnightEntity;
import com.github.manasmods.tensura.entity.human.FolgenEntity;
import com.github.manasmods.tensura.entity.human.HinataSakaguchiEntity;
import com.github.manasmods.tensura.entity.human.KiraraMizutaniEntity;
import com.github.manasmods.tensura.entity.human.KyoyaTachibanaEntity;
import com.github.manasmods.tensura.entity.human.MaiFurukiEntity;
import com.github.manasmods.tensura.entity.human.MarkLaurenEntity;
import com.github.manasmods.tensura.entity.human.ShinRyuseiEntity;
import com.github.manasmods.tensura.entity.human.ShinjiTanimuraEntity;
import com.github.manasmods.tensura.entity.human.ShizuEntity;
import com.github.manasmods.tensura.entity.human.ShogoTaguchiEntity;
import com.github.manasmods.tensura.entity.magic.barrier.AcidRainEntity;
import com.github.manasmods.tensura.entity.magic.barrier.BlizzardEntity;
import com.github.manasmods.tensura.entity.magic.barrier.DarkCubeEntity;
import com.github.manasmods.tensura.entity.magic.barrier.DisintegrationEntity;
import com.github.manasmods.tensura.entity.magic.barrier.FlareCircleEntity;
import com.github.manasmods.tensura.entity.magic.barrier.HolyFieldEntity;
import com.github.manasmods.tensura.entity.magic.barrier.MagicEngineBarrierEntity;
import com.github.manasmods.tensura.entity.magic.barrier.MegiddoBubbleEntity;
import com.github.manasmods.tensura.entity.magic.barrier.RangedBarrierEntity;
import com.github.manasmods.tensura.entity.magic.beam.BlackLightningBlastProjectile;
import com.github.manasmods.tensura.entity.magic.beam.BloodRayProjectile;
import com.github.manasmods.tensura.entity.magic.beam.DarknessCannonProjectile;
import com.github.manasmods.tensura.entity.magic.beam.ElectroBlastProjectile;
import com.github.manasmods.tensura.entity.magic.beam.SolarBeamProjectile;
import com.github.manasmods.tensura.entity.magic.beam.SpatialRayProjectile;
import com.github.manasmods.tensura.entity.magic.breath.BlackFlameBreathProjectile;
import com.github.manasmods.tensura.entity.magic.breath.FlameBreathProjectile;
import com.github.manasmods.tensura.entity.magic.breath.GluttonyMistProjectile;
import com.github.manasmods.tensura.entity.magic.breath.ParalysingBreathProjectile;
import com.github.manasmods.tensura.entity.magic.breath.PoisonousBreathProjectile;
import com.github.manasmods.tensura.entity.magic.breath.PredatorMistProjectile;
import com.github.manasmods.tensura.entity.magic.breath.ThunderBreathProjectile;
import com.github.manasmods.tensura.entity.magic.breath.WaterBreathProjectile;
import com.github.manasmods.tensura.entity.magic.breath.WindBreathProjectile;
import com.github.manasmods.tensura.entity.magic.field.GravityField;
import com.github.manasmods.tensura.entity.magic.field.HellFlare;
import com.github.manasmods.tensura.entity.magic.field.Hellfire;
import com.github.manasmods.tensura.entity.magic.field.cloud.BloodMistCloud;
import com.github.manasmods.tensura.entity.magic.lightning.BlackLightningBolt;
import com.github.manasmods.tensura.entity.magic.lightning.LightningBolt;
import com.github.manasmods.tensura.entity.magic.misc.ChaosEaterProjectile;
import com.github.manasmods.tensura.entity.magic.misc.DeathStormTornado;
import com.github.manasmods.tensura.entity.magic.misc.FusionistLandmineEntity;
import com.github.manasmods.tensura.entity.magic.misc.MadOgreOrbsEntity;
import com.github.manasmods.tensura.entity.magic.misc.TempestScaleEntity;
import com.github.manasmods.tensura.entity.magic.misc.ThrownItemProjectile;
import com.github.manasmods.tensura.entity.magic.misc.WarpPortalEntity;
import com.github.manasmods.tensura.entity.magic.projectile.AcidBallProjectile;
import com.github.manasmods.tensura.entity.magic.projectile.AuraBulletProjectile;
import com.github.manasmods.tensura.entity.magic.projectile.AuraSlashProjectile;
import com.github.manasmods.tensura.entity.magic.projectile.BogShotProjectile;
import com.github.manasmods.tensura.entity.magic.projectile.BoulderShotProjectile;
import com.github.manasmods.tensura.entity.magic.projectile.DimensionCutProjectile;
import com.github.manasmods.tensura.entity.magic.projectile.FireBallProjectile;
import com.github.manasmods.tensura.entity.magic.projectile.FireBoltProjectile;
import com.github.manasmods.tensura.entity.magic.projectile.FrostBallProjectile;
import com.github.manasmods.tensura.entity.magic.projectile.GravitySphereProjectile;
import com.github.manasmods.tensura.entity.magic.projectile.IceLanceProjectile;
import com.github.manasmods.tensura.entity.magic.projectile.InvisibleFireBoltProjectile;
import com.github.manasmods.tensura.entity.magic.projectile.LightningLanceProjectile;
import com.github.manasmods.tensura.entity.magic.projectile.LightningSphereProjectile;
import com.github.manasmods.tensura.entity.magic.projectile.MagmaShotProjectile;
import com.github.manasmods.tensura.entity.magic.projectile.MudShotProjectile;
import com.github.manasmods.tensura.entity.magic.projectile.ObsidianShotProjectile;
import com.github.manasmods.tensura.entity.magic.projectile.PlasmaBallProjectile;
import com.github.manasmods.tensura.entity.magic.projectile.PoisonBallProjectile;
import com.github.manasmods.tensura.entity.magic.projectile.PoisonCutterProjectile;
import com.github.manasmods.tensura.entity.magic.projectile.SeveranceCutterProjectile;
import com.github.manasmods.tensura.entity.magic.projectile.SolarGrenadeProjectile;
import com.github.manasmods.tensura.entity.magic.projectile.SpaceCutProjectile;
import com.github.manasmods.tensura.entity.magic.projectile.SpatialArrowProjectile;
import com.github.manasmods.tensura.entity.magic.projectile.SteamBallProjectile;
import com.github.manasmods.tensura.entity.magic.projectile.StoneShotProjectile;
import com.github.manasmods.tensura.entity.magic.projectile.WaterBallProjectile;
import com.github.manasmods.tensura.entity.magic.projectile.WindBladeProjectile;
import com.github.manasmods.tensura.entity.magic.projectile.WindSphereProjectile;
import com.github.manasmods.tensura.entity.magic.skill.BlackFlameBallProjectile;
import com.github.manasmods.tensura.entity.magic.skill.FlameOrbProjectile;
import com.github.manasmods.tensura.entity.magic.skill.FusionistProjectile;
import com.github.manasmods.tensura.entity.magic.skill.HeatSphereProjectile;
import com.github.manasmods.tensura.entity.magic.skill.HellFlareProjectile;
import com.github.manasmods.tensura.entity.magic.skill.ReflectorEchoProjectile;
import com.github.manasmods.tensura.entity.magic.skill.SniperBulletProjectile;
import com.github.manasmods.tensura.entity.magic.skill.SniperGrenadeProjectile;
import com.github.manasmods.tensura.entity.magic.skill.WaterBladeProjectile;
import com.github.manasmods.tensura.entity.magic.spike.EarthSpikeEntity;
import com.github.manasmods.tensura.entity.magic.spike.PillarEntity;
import com.github.manasmods.tensura.entity.multipart.EvilCentipedeBody;
import com.github.manasmods.tensura.entity.multipart.EvilCentipedeEntity;
import com.github.manasmods.tensura.entity.multipart.TempestSerpentBody;
import com.github.manasmods.tensura.entity.multipart.TempestSerpentEntity;
import com.github.manasmods.tensura.entity.projectile.InvisibleArrow;
import com.github.manasmods.tensura.entity.projectile.KunaiProjectile;
import com.github.manasmods.tensura.entity.projectile.LightArrowProjectile;
import com.github.manasmods.tensura.entity.projectile.MonsterSpitProjectile;
import com.github.manasmods.tensura.entity.projectile.PrimedCharybdisCoreEntity;
import com.github.manasmods.tensura.entity.projectile.SevererBladeProjectile;
import com.github.manasmods.tensura.entity.projectile.SpearProjectile;
import com.github.manasmods.tensura.entity.projectile.SpearedFinArrow;
import com.github.manasmods.tensura.entity.projectile.TensuraFallingBlock;
import com.github.manasmods.tensura.entity.projectile.ThrownHealingPotion;
import com.github.manasmods.tensura.entity.projectile.ThrownHolyWater;
import com.github.manasmods.tensura.entity.projectile.UnicornHornProjectile;
import com.github.manasmods.tensura.entity.projectile.WebBulletProjectile;
import com.github.manasmods.tensura.entity.template.TensuraBoatEntity;
import com.github.manasmods.tensura.entity.template.TensuraChestBoatEntity;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.MobCategory;
import net.minecraft.world.entity.EntityType.Builder;
import net.minecraftforge.eventbus.api.IEventBus;
import net.minecraftforge.registries.DeferredRegister;
import net.minecraftforge.registries.ForgeRegistries;
import net.minecraftforge.registries.RegistryObject;

public class TensuraEntityTypes {
   private static final DeferredRegister<EntityType<?>> registry;
   public static final RegistryObject<EntityType<PrimedCharybdisCoreEntity>> CHARYBDIS_CORE;
   public static final RegistryObject<EntityType<TensuraFallingBlock>> FALLING_BLOCK;
   public static final RegistryObject<EntityType<EvilCentipedeEntity>> EVIL_CENTIPEDE;
   public static final RegistryObject<EntityType<EvilCentipedeBody>> EVIL_CENTIPEDE_BODY;
   public static final RegistryObject<EntityType<TempestSerpentEntity>> TEMPEST_SERPENT;
   public static final RegistryObject<EntityType<TempestSerpentBody>> TEMPEST_SERPENT_BODY;
   public static final RegistryObject<EntityType<BlackFlameBreathProjectile>> BLACK_FLAME_BREATH;
   public static final RegistryObject<EntityType<FlameBreathProjectile>> FLAME_BREATH;
   public static final RegistryObject<EntityType<GluttonyMistProjectile>> GLUTTONY_MIST;
   public static final RegistryObject<EntityType<ParalysingBreathProjectile>> PARALYSING_BREATH;
   public static final RegistryObject<EntityType<PoisonousBreathProjectile>> POISONOUS_BREATH;
   public static final RegistryObject<EntityType<PredatorMistProjectile>> PREDATOR_MIST;
   public static final RegistryObject<EntityType<ThunderBreathProjectile>> THUNDER_BREATH;
   public static final RegistryObject<EntityType<WaterBreathProjectile>> WATER_BREATH;
   public static final RegistryObject<EntityType<WindBreathProjectile>> WIND_BREATH;
   public static final RegistryObject<EntityType<AcidRainEntity>> ACID_RAIN;
   public static final RegistryObject<EntityType<RangedBarrierEntity>> BARRIER;
   public static final RegistryObject<EntityType<BlizzardEntity>> BLIZZARD;
   public static final RegistryObject<EntityType<DarkCubeEntity>> DARK_CUBE;
   public static final RegistryObject<EntityType<DisintegrationEntity>> DISINTEGRATION;
   public static final RegistryObject<EntityType<FlareCircleEntity>> FLARE_CIRCLE;
   public static final RegistryObject<EntityType<HolyFieldEntity>> HOLY_FIELD;
   public static final RegistryObject<EntityType<MagicEngineBarrierEntity>> MAGIC_ENGINE_BARRIER;
   public static final RegistryObject<EntityType<MegiddoBubbleEntity>> MEGIDDO_BUBBLE;
   public static final RegistryObject<EntityType<LightningBolt>> LIGHTNING_BOLT;
   public static final RegistryObject<EntityType<BlackLightningBolt>> BLACK_LIGHTNING_BOLT;
   public static final RegistryObject<EntityType<BloodMistCloud>> BLOOD_MIST;
   public static final RegistryObject<EntityType<DeathStormTornado>> DEATH_STORM_TORNADO;
   public static final RegistryObject<EntityType<GravityField>> GRAVITY_FIELD;
   public static final RegistryObject<EntityType<Hellfire>> HELLFIRE;
   public static final RegistryObject<EntityType<HellFlare>> HELL_FLARE;
   public static final RegistryObject<EntityType<FusionistLandmineEntity>> LANDMINE;
   public static final RegistryObject<EntityType<MadOgreOrbsEntity>> MAD_OGRE_ORBS;
   public static final RegistryObject<EntityType<ThrownItemProjectile>> THROWN_ITEM;
   public static final RegistryObject<EntityType<EarthSpikeEntity>> EARTH_SPIKE;
   public static final RegistryObject<EntityType<PillarEntity>> EARTH_PILLAR;
   public static final RegistryObject<EntityType<WarpPortalEntity>> WARP_PORTAL;
   public static final RegistryObject<EntityType<BlackLightningBlastProjectile>> BLACK_LIGHTNING_BLAST;
   public static final RegistryObject<EntityType<BloodRayProjectile>> BLOOD_RAY;
   public static final RegistryObject<EntityType<DarknessCannonProjectile>> DARKNESS_CANNON;
   public static final RegistryObject<EntityType<ElectroBlastProjectile>> ELECTRO_BLAST;
   public static final RegistryObject<EntityType<SolarBeamProjectile>> SOLAR_BEAM;
   public static final RegistryObject<EntityType<SpatialRayProjectile>> SPATIAL_RAY;
   public static final RegistryObject<EntityType<AuraSlashProjectile>> AURA_SLASH;
   public static final RegistryObject<EntityType<AcidBallProjectile>> ACID_BALL;
   public static final RegistryObject<EntityType<AuraBulletProjectile>> AURA_BULLET;
   public static final RegistryObject<EntityType<BlackFlameBallProjectile>> BLACK_FLAME_BALL;
   public static final RegistryObject<EntityType<BogShotProjectile>> BOG_SHOT;
   public static final RegistryObject<EntityType<BoulderShotProjectile>> BOULDER_SHOT;
   public static final RegistryObject<EntityType<ChaosEaterProjectile>> CHAOS_EATER;
   public static final RegistryObject<EntityType<DimensionCutProjectile>> DIMENSION_CUT;
   public static final RegistryObject<EntityType<FireBallProjectile>> FIRE_BALL;
   public static final RegistryObject<EntityType<FireBoltProjectile>> FIRE_BOLT;
   public static final RegistryObject<EntityType<FlameOrbProjectile>> FLAME_ORB;
   public static final RegistryObject<EntityType<FrostBallProjectile>> FROST_BALL;
   public static final RegistryObject<EntityType<FusionistProjectile>> FUSIONIST_PROJECTILE;
   public static final RegistryObject<EntityType<GravitySphereProjectile>> GRAVITY_SPHERE;
   public static final RegistryObject<EntityType<HeatSphereProjectile>> HEAT_SPHERE;
   public static final RegistryObject<EntityType<HellFlareProjectile>> HELL_FLARE_PROJECTILE;
   public static final RegistryObject<EntityType<IceLanceProjectile>> ICE_LANCE;
   public static final RegistryObject<EntityType<InvisibleFireBoltProjectile>> INVISIBLE_FIRE_BOLT;
   public static final RegistryObject<EntityType<LightArrowProjectile>> LIGHT_ARROW;
   public static final RegistryObject<EntityType<LightningLanceProjectile>> LIGHTNING_LANCE;
   public static final RegistryObject<EntityType<LightningSphereProjectile>> LIGHTNING_SPHERE;
   public static final RegistryObject<EntityType<MagmaShotProjectile>> MAGMA_SHOT;
   public static final RegistryObject<EntityType<MudShotProjectile>> MUD_SHOT;
   public static final RegistryObject<EntityType<ObsidianShotProjectile>> OBSIDIAN_SHOT;
   public static final RegistryObject<EntityType<PlasmaBallProjectile>> PLASMA_BALL;
   public static final RegistryObject<EntityType<PoisonBallProjectile>> POISON_BALL;
   public static final RegistryObject<EntityType<PoisonCutterProjectile>> POISON_CUTTER;
   public static final RegistryObject<EntityType<ReflectorEchoProjectile>> REFLECTOR_ECHO;
   public static final RegistryObject<EntityType<SeveranceCutterProjectile>> SEVERANCE_CUTTER;
   public static final RegistryObject<EntityType<SolarGrenadeProjectile>> SOLAR_GRENADE;
   public static final RegistryObject<EntityType<SniperBulletProjectile>> SNIPER_BULLET;
   public static final RegistryObject<EntityType<SniperGrenadeProjectile>> SNIPER_GRENADE;
   public static final RegistryObject<EntityType<SpaceCutProjectile>> SPACE_CUT;
   public static final RegistryObject<EntityType<SpatialArrowProjectile>> SPATIAL_ARROW;
   public static final RegistryObject<EntityType<SteamBallProjectile>> STEAM_BALL;
   public static final RegistryObject<EntityType<StoneShotProjectile>> STONE_SHOT;
   public static final RegistryObject<EntityType<TempestScaleEntity>> TEMPEST_SCALE;
   public static final RegistryObject<EntityType<WaterBallProjectile>> WATER_BALL;
   public static final RegistryObject<EntityType<WaterBladeProjectile>> WATER_BLADE;
   public static final RegistryObject<EntityType<WindBladeProjectile>> WIND_BLADE;
   public static final RegistryObject<EntityType<WindSphereProjectile>> WIND_SPHERE;
   public static final RegistryObject<EntityType<ThrownHealingPotion>> HEALING_POTION;
   public static final RegistryObject<EntityType<ThrownHolyWater>> HOLY_WATER;
   public static final RegistryObject<EntityType<MonsterSpitProjectile>> MONSTER_SPIT;
   public static final RegistryObject<EntityType<SevererBladeProjectile>> SEVERER_BLADE;
   public static final RegistryObject<EntityType<WebBulletProjectile>> WEB_BULLET;
   public static final RegistryObject<EntityType<SpearProjectile>> SPEAR;
   public static final RegistryObject<EntityType<KunaiProjectile>> KUNAI;
   public static final RegistryObject<EntityType<InvisibleArrow>> INVISIBLE_ARROW;
   public static final RegistryObject<EntityType<SpearedFinArrow>> SPEARED_FIN_ARROW;
   public static final RegistryObject<EntityType<UnicornHornProjectile>> UNICORN_HORN;
   public static final RegistryObject<EntityType<TensuraBoatEntity>> BOAT_ENTITY;
   public static final RegistryObject<EntityType<TensuraChestBoatEntity>> CHEST_BOAT_ENTITY;
   public static final RegistryObject<EntityType<CloneEntity>> CLONE_DEFAULT;
   public static final RegistryObject<EntityType<CloneEntity>> CLONE_SLIM;
   public static final RegistryObject<EntityType<IfritCloneEntity>> IFRIT_CLONE;
   public static final RegistryObject<EntityType<FalmuthKnightEntity>> FALMUTH_KNIGHT;
   public static final RegistryObject<EntityType<FolgenEntity>> FOLGEN;
   public static final RegistryObject<EntityType<HinataSakaguchiEntity>> HINATA_SAKAGUCHI;
   public static final RegistryObject<EntityType<KiraraMizutaniEntity>> KIRARA_MIZUTANI;
   public static final RegistryObject<EntityType<KyoyaTachibanaEntity>> KYOYA_TACHIBANA;
   public static final RegistryObject<EntityType<MaiFurukiEntity>> MAI_FURUKI;
   public static final RegistryObject<EntityType<MarkLaurenEntity>> MARK_LAUREN;
   public static final RegistryObject<EntityType<ShinjiTanimuraEntity>> SHINJI_TANIMURA;
   public static final RegistryObject<EntityType<ShinRyuseiEntity>> SHIN_RYUSEI;
   public static final RegistryObject<EntityType<ShizuEntity>> SHIZU;
   public static final RegistryObject<EntityType<ShogoTaguchiEntity>> SHOGO_TAGUCHI;
   public static final RegistryObject<EntityType<AkashEntity>> AKASH;
   public static final RegistryObject<EntityType<AquaFrogEntity>> AQUA_FROG;
   public static final RegistryObject<EntityType<ArmoursaurusEntity>> ARMOURSAURUS;
   public static final RegistryObject<EntityType<ArmyWaspEntity>> ARMY_WASP;
   public static final RegistryObject<EntityType<BarghestEntity>> BARGHEST;
   public static final RegistryObject<EntityType<BeastGnomeEntity>> BEAST_GNOME;
   public static final RegistryObject<EntityType<BlackSpiderEntity>> BLACK_SPIDER;
   public static final RegistryObject<EntityType<BladeTigerEntity>> BLADE_TIGER;
   public static final RegistryObject<EntityType<BulldeerEntity>> BULLDEER;
   public static final RegistryObject<EntityType<CharybdisEntity>> CHARYBDIS;
   public static final RegistryObject<EntityType<DirewolfEntity>> DIREWOLF;
   public static final RegistryObject<EntityType<DragonPeacockEntity>> DRAGON_PEACOCK;
   public static final RegistryObject<EntityType<ElementalColossusEntity>> ELEMENTAL_COLOSSUS;
   public static final RegistryObject<EntityType<FeatheredSerpentEntity>> FEATHERED_SERPENT;
   public static final RegistryObject<EntityType<GiantAntEntity>> GIANT_ANT;
   public static final RegistryObject<EntityType<GiantBatEntity>> GIANT_BAT;
   public static final RegistryObject<EntityType<GiantBearEntity>> GIANT_BEAR;
   public static final RegistryObject<EntityType<GiantCodEntity>> GIANT_COD;
   public static final RegistryObject<EntityType<GiantSalmonEntity>> GIANT_SALMON;
   public static final RegistryObject<EntityType<GoblinEntity>> GOBLIN;
   public static final RegistryObject<EntityType<HellCaterpillarEntity>> HELL_CATERPILLAR;
   public static final RegistryObject<EntityType<HellMothEntity>> HELL_MOTH;
   public static final RegistryObject<EntityType<HolyCowEntity>> HOLY_COW;
   public static final RegistryObject<EntityType<HornedRabbitEntity>> HORNED_RABBIT;
   public static final RegistryObject<EntityType<HoundDogEntity>> HOUND_DOG;
   public static final RegistryObject<EntityType<HoverLizardEntity>> HOVER_LIZARD;
   public static final RegistryObject<EntityType<HornedBearEntity>> HORNED_BEAR;
   public static final RegistryObject<EntityType<IfritEntity>> IFRIT;
   public static final RegistryObject<EntityType<KnightSpiderEntity>> KNIGHT_SPIDER;
   public static final RegistryObject<EntityType<LandfishEntity>> LANDFISH;
   public static final RegistryObject<EntityType<LeechLizardEntity>> LEECH_LIZARD;
   public static final RegistryObject<EntityType<LizardmanEntity>> LIZARDMAN;
   public static final RegistryObject<EntityType<MegalodonEntity>> MEGALODON;
   public static final RegistryObject<EntityType<OneEyedOwlEntity>> ONE_EYED_OWL;
   public static final RegistryObject<EntityType<OrcEntity>> ORC;
   public static final RegistryObject<EntityType<OrcDisasterEntity>> ORC_DISASTER;
   public static final RegistryObject<EntityType<OrcLordEntity>> ORC_LORD;
   public static final RegistryObject<EntityType<SissieEntity>> SISSIE;
   public static final RegistryObject<EntityType<SalamanderEntity>> SALAMANDER;
   public static final RegistryObject<EntityType<SlimeEntity>> SLIME;
   public static final RegistryObject<EntityType<SpearToroEntity>> SPEAR_TORO;
   public static final RegistryObject<EntityType<SupermassiveSlimeEntity>> SUPERMASSIVE_SLIME;
   public static final RegistryObject<EntityType<MetalSlimeEntity>> METAL_SLIME;
   public static final RegistryObject<EntityType<SylphideEntity>> SYLPHIDE;
   public static final RegistryObject<EntityType<UndineEntity>> UNDINE;
   public static final RegistryObject<EntityType<UnicornEntity>> UNICORN;
   public static final RegistryObject<EntityType<WarGnomeEntity>> WAR_GNOME;
   public static final RegistryObject<EntityType<WingedCatEntity>> WINGED_CAT;

   public static void init(IEventBus modEventBus) {
      registry.register(modEventBus);
   }

   static {
      registry = DeferredRegister.create(ForgeRegistries.ENTITY_TYPES, "tensura");
      CHARYBDIS_CORE = registry.register("charybdis_core", () -> {
         return Builder.m_20704_(PrimedCharybdisCoreEntity::new, MobCategory.MISC).m_20719_().m_20699_(0.5F, 0.5F).m_20702_(10).m_20717_(10).m_20712_((new ResourceLocation("tensura", "charybdis_core")).toString());
      });
      FALLING_BLOCK = registry.register("falling_block", () -> {
         return Builder.m_20704_(TensuraFallingBlock::new, MobCategory.MISC).m_20699_(0.98F, 0.98F).m_20702_(10).m_20717_(20).m_20712_((new ResourceLocation("tensura", "falling_block")).toString());
      });
      EVIL_CENTIPEDE = registry.register("evil_centipede", () -> {
         return Builder.m_20704_(EvilCentipedeEntity::new, MobCategory.MONSTER).m_20699_(0.75F, 0.9F).m_20712_((new ResourceLocation("tensura", "evil_centipede")).toString());
      });
      EVIL_CENTIPEDE_BODY = registry.register("evil_centipede_body", () -> {
         return Builder.m_20704_(EvilCentipedeBody::new, MobCategory.MISC).m_20699_(0.65F, 0.9F).m_20719_().setShouldReceiveVelocityUpdates(true).setUpdateInterval(1).m_20712_((new ResourceLocation("tensura", "evil_centipede_body")).toString());
      });
      TEMPEST_SERPENT = registry.register("tempest_serpent", () -> {
         return Builder.m_20704_(TempestSerpentEntity::new, MobCategory.MONSTER).m_20699_(1.1F, 0.9F).m_20712_((new ResourceLocation("tensura", "tempest_serpent")).toString());
      });
      TEMPEST_SERPENT_BODY = registry.register("tempest_serpent_body", () -> {
         return Builder.m_20704_(TempestSerpentBody::new, MobCategory.MISC).m_20699_(1.1F, 0.9F).m_20719_().setShouldReceiveVelocityUpdates(true).setUpdateInterval(1).m_20712_((new ResourceLocation("tensura", "tempest_serpent_body")).toString());
      });
      BLACK_FLAME_BREATH = registry.register("black_flame_breath", () -> {
         return Builder.m_20704_(BlackFlameBreathProjectile::new, MobCategory.MISC).m_20699_(1.0F, 1.0F).m_20702_(64).m_20712_((new ResourceLocation("tensura", "black_flame_breath")).toString());
      });
      FLAME_BREATH = registry.register("flame_breath", () -> {
         return Builder.m_20704_(FlameBreathProjectile::new, MobCategory.MISC).m_20699_(1.0F, 1.0F).m_20702_(64).m_20712_((new ResourceLocation("tensura", "flame_breath")).toString());
      });
      GLUTTONY_MIST = registry.register("gluttony_mist", () -> {
         return Builder.m_20704_(GluttonyMistProjectile::new, MobCategory.MISC).m_20699_(1.0F, 1.0F).m_20702_(64).m_20712_((new ResourceLocation("tensura", "gluttony_mist")).toString());
      });
      PARALYSING_BREATH = registry.register("paralysing_breath", () -> {
         return Builder.m_20704_(ParalysingBreathProjectile::new, MobCategory.MISC).m_20699_(1.0F, 1.0F).m_20702_(64).m_20712_((new ResourceLocation("tensura", "paralysing_breath")).toString());
      });
      POISONOUS_BREATH = registry.register("poisonous_breath", () -> {
         return Builder.m_20704_(PoisonousBreathProjectile::new, MobCategory.MISC).m_20699_(1.0F, 1.0F).m_20702_(64).m_20712_((new ResourceLocation("tensura", "poisonous_breath")).toString());
      });
      PREDATOR_MIST = registry.register("predator_mist", () -> {
         return Builder.m_20704_(PredatorMistProjectile::new, MobCategory.MISC).m_20699_(1.0F, 1.0F).m_20702_(64).m_20712_((new ResourceLocation("tensura", "predator_mist")).toString());
      });
      THUNDER_BREATH = registry.register("thunder_breath", () -> {
         return Builder.m_20704_(ThunderBreathProjectile::new, MobCategory.MISC).m_20699_(1.0F, 1.0F).m_20702_(64).m_20712_((new ResourceLocation("tensura", "thunder_breath")).toString());
      });
      WATER_BREATH = registry.register("water_breath", () -> {
         return Builder.m_20704_(WaterBreathProjectile::new, MobCategory.MISC).m_20699_(1.0F, 1.0F).m_20702_(64).m_20712_((new ResourceLocation("tensura", "water_breath")).toString());
      });
      WIND_BREATH = registry.register("wind_breath", () -> {
         return Builder.m_20704_(WindBreathProjectile::new, MobCategory.MISC).m_20699_(1.0F, 1.0F).m_20702_(64).m_20712_((new ResourceLocation("tensura", "wind_breath")).toString());
      });
      ACID_RAIN = registry.register("acid_rain", () -> {
         return Builder.m_20704_(AcidRainEntity::new, MobCategory.MISC).m_20699_(0.1F, 0.1F).m_20702_(64).m_20717_(Integer.MAX_VALUE).m_20712_((new ResourceLocation("tensura", "acid_rain")).toString());
      });
      BARRIER = registry.register("barrier", () -> {
         return Builder.m_20704_(RangedBarrierEntity::new, MobCategory.MISC).m_20699_(0.1F, 0.1F).m_20702_(64).m_20717_(Integer.MAX_VALUE).m_20712_((new ResourceLocation("tensura", "barrier")).toString());
      });
      BLIZZARD = registry.register("blizzard", () -> {
         return Builder.m_20704_(BlizzardEntity::new, MobCategory.MISC).m_20699_(0.1F, 0.1F).m_20702_(64).m_20717_(Integer.MAX_VALUE).m_20712_((new ResourceLocation("tensura", "blizzard")).toString());
      });
      DARK_CUBE = registry.register("dark_cube", () -> {
         return Builder.m_20704_(DarkCubeEntity::new, MobCategory.MISC).m_20699_(0.1F, 0.1F).m_20702_(64).m_20717_(Integer.MAX_VALUE).m_20712_((new ResourceLocation("tensura", "dark_cube")).toString());
      });
      DISINTEGRATION = registry.register("disintegration", () -> {
         return Builder.m_20704_(DisintegrationEntity::new, MobCategory.MISC).m_20699_(0.1F, 0.1F).m_20702_(64).m_20717_(Integer.MAX_VALUE).m_20719_().m_20712_((new ResourceLocation("tensura", "disintegration")).toString());
      });
      FLARE_CIRCLE = registry.register("flare_circle", () -> {
         return Builder.m_20704_(FlareCircleEntity::new, MobCategory.MISC).m_20699_(0.1F, 0.1F).m_20702_(64).m_20717_(Integer.MAX_VALUE).m_20719_().m_20712_((new ResourceLocation("tensura", "flare_circle")).toString());
      });
      HOLY_FIELD = registry.register("holy_field", () -> {
         return Builder.m_20704_(HolyFieldEntity::new, MobCategory.MISC).m_20699_(0.1F, 0.1F).m_20702_(64).m_20717_(Integer.MAX_VALUE).m_20719_().m_20712_((new ResourceLocation("tensura", "holy_field")).toString());
      });
      MAGIC_ENGINE_BARRIER = registry.register("magic_engine_barrier", () -> {
         return Builder.m_20704_(MagicEngineBarrierEntity::new, MobCategory.MISC).m_20699_(0.1F, 0.1F).m_20702_(64).m_20717_(Integer.MAX_VALUE).m_20712_((new ResourceLocation("tensura", "magic_engine_barrier")).toString());
      });
      MEGIDDO_BUBBLE = registry.register("megiddo_bubble", () -> {
         return Builder.m_20704_(MegiddoBubbleEntity::new, MobCategory.MISC).m_20699_(0.1F, 0.1F).m_20702_(64).m_20717_(Integer.MAX_VALUE).m_20712_((new ResourceLocation("tensura", "megiddo_bubble")).toString());
      });
      LIGHTNING_BOLT = registry.register("lightning_bolt", () -> {
         return Builder.m_20704_((pEntityType, pLevel) -> {
            return new LightningBolt(pLevel);
         }, MobCategory.MISC).m_20716_().m_20699_(0.0F, 0.0F).m_20702_(64).m_20717_(Integer.MAX_VALUE).m_20712_((new ResourceLocation("tensura", "lightning_bolt")).toString());
      });
      BLACK_LIGHTNING_BOLT = registry.register("black_lightning_bolt", () -> {
         return Builder.m_20704_((pEntityType, pLevel) -> {
            return new BlackLightningBolt(pLevel);
         }, MobCategory.MISC).m_20716_().m_20699_(0.0F, 0.0F).m_20702_(64).m_20717_(Integer.MAX_VALUE).m_20712_((new ResourceLocation("tensura", "black_lightning_bolt")).toString());
      });
      BLOOD_MIST = registry.register("blood_mist", () -> {
         return Builder.m_20704_(BloodMistCloud::new, MobCategory.MISC).m_20699_(0.1F, 0.1F).m_20702_(64).m_20712_((new ResourceLocation("tensura", "blood_mist")).toString());
      });
      DEATH_STORM_TORNADO = registry.register("death_storm_tornado", () -> {
         return Builder.m_20704_((pEntityType, pLevel) -> {
            return new DeathStormTornado(pLevel);
         }, MobCategory.MISC).m_20716_().m_20699_(0.0F, 0.0F).m_20702_(64).m_20717_(Integer.MAX_VALUE).m_20712_((new ResourceLocation("tensura", "death_storm_tornado")).toString());
      });
      GRAVITY_FIELD = registry.register("gravity_field", () -> {
         return Builder.m_20704_(GravityField::new, MobCategory.MISC).m_20699_(0.1F, 0.1F).m_20702_(64).m_20712_((new ResourceLocation("tensura", "gravity_field")).toString());
      });
      HELLFIRE = registry.register("hellfire", () -> {
         return Builder.m_20704_(Hellfire::new, MobCategory.MISC).m_20699_(0.1F, 0.1F).m_20702_(64).m_20712_((new ResourceLocation("tensura", "hellfire")).toString());
      });
      HELL_FLARE = registry.register("hell_flare", () -> {
         return Builder.m_20704_(HellFlare::new, MobCategory.MISC).m_20699_(0.1F, 0.1F).m_20702_(64).m_20712_((new ResourceLocation("tensura", "hell_flare")).toString());
      });
      LANDMINE = registry.register("fusionist_landmine", () -> {
         return Builder.m_20704_(FusionistLandmineEntity::new, MobCategory.MISC).m_20699_(1.01F, 1.01F).m_20702_(16).m_20712_((new ResourceLocation("tensura", "fusionist_landmine")).toString());
      });
      MAD_OGRE_ORBS = registry.register("mad_ogre_orbs", () -> {
         return Builder.m_20704_(MadOgreOrbsEntity::new, MobCategory.MISC).m_20699_(0.1F, 0.1F).m_20702_(16).m_20712_((new ResourceLocation("tensura", "mad_ogre_orbs")).toString());
      });
      THROWN_ITEM = registry.register("thrown_item", () -> {
         return Builder.m_20704_(ThrownItemProjectile::new, MobCategory.MISC).m_20699_(0.5F, 0.5F).m_20712_((new ResourceLocation("tensura", "thrown_item")).toString());
      });
      EARTH_SPIKE = registry.register("earth_spike", () -> {
         return Builder.m_20704_(EarthSpikeEntity::new, MobCategory.MISC).m_20699_(1.0F, 1.0F).m_20702_(16).m_20712_((new ResourceLocation("tensura", "earth_spike")).toString());
      });
      EARTH_PILLAR = registry.register("earth_pillar", () -> {
         return Builder.m_20704_(PillarEntity::new, MobCategory.MISC).m_20699_(1.0F, 1.0F).m_20702_(16).m_20712_((new ResourceLocation("tensura", "earth_pillar")).toString());
      });
      WARP_PORTAL = registry.register("warp_portal", () -> {
         return Builder.m_20704_(WarpPortalEntity::new, MobCategory.MISC).m_20699_(0.5F, 0.5F).setCustomClientFactory(WarpPortalEntity::new).m_20719_().m_20712_((new ResourceLocation("tensura", "warp_portal")).toString());
      });
      BLACK_LIGHTNING_BLAST = registry.register("black_lightning_blast", () -> {
         return Builder.m_20704_(BlackLightningBlastProjectile::new, MobCategory.MISC).m_20699_(0.1F, 0.1F).m_20702_(32).m_20712_((new ResourceLocation("tensura", "black_lightning_blast")).toString());
      });
      BLOOD_RAY = registry.register("blood_ray", () -> {
         return Builder.m_20704_(BloodRayProjectile::new, MobCategory.MISC).m_20699_(0.1F, 0.1F).m_20702_(32).m_20712_((new ResourceLocation("tensura", "blood_ray")).toString());
      });
      DARKNESS_CANNON = registry.register("darkness_cannon", () -> {
         return Builder.m_20704_(DarknessCannonProjectile::new, MobCategory.MISC).m_20699_(0.1F, 0.1F).m_20702_(32).m_20712_((new ResourceLocation("tensura", "darkness_cannon")).toString());
      });
      ELECTRO_BLAST = registry.register("electro_blast", () -> {
         return Builder.m_20704_(ElectroBlastProjectile::new, MobCategory.MISC).m_20699_(0.1F, 0.1F).m_20702_(32).m_20712_((new ResourceLocation("tensura", "electro_blast")).toString());
      });
      SOLAR_BEAM = registry.register("solar_beam", () -> {
         return Builder.m_20704_(SolarBeamProjectile::new, MobCategory.MISC).m_20699_(0.1F, 0.1F).m_20702_(32).m_20712_((new ResourceLocation("tensura", "solar_beam")).toString());
      });
      SPATIAL_RAY = registry.register("spatial_ray", () -> {
         return Builder.m_20704_(SpatialRayProjectile::new, MobCategory.MISC).m_20699_(0.1F, 0.1F).m_20702_(32).m_20712_((new ResourceLocation("tensura", "spatial_ray")).toString());
      });
      AURA_SLASH = registry.register("aura_slash", () -> {
         return Builder.m_20704_(AuraSlashProjectile::new, MobCategory.MISC).m_20699_(0.4F, 1.0F).m_20702_(32).m_20712_((new ResourceLocation("tensura", "aura_slash")).toString());
      });
      ACID_BALL = registry.register("acid_ball", () -> {
         return Builder.m_20704_(AcidBallProjectile::new, MobCategory.MISC).m_20699_(0.4F, 0.4F).m_20702_(32).m_20712_((new ResourceLocation("tensura", "acid_ball")).toString());
      });
      AURA_BULLET = registry.register("aura_bullet", () -> {
         return Builder.m_20704_(AuraBulletProjectile::new, MobCategory.MISC).m_20699_(1.0F, 1.0F).m_20702_(32).m_20712_((new ResourceLocation("tensura", "aura_bullet")).toString());
      });
      BLACK_FLAME_BALL = registry.register("black_flame_ball", () -> {
         return Builder.m_20704_(BlackFlameBallProjectile::new, MobCategory.MISC).m_20699_(0.5F, 0.5F).m_20702_(32).m_20712_((new ResourceLocation("tensura", "black_flame_ball")).toString());
      });
      BOG_SHOT = registry.register("bog_shot", () -> {
         return Builder.m_20704_(BogShotProjectile::new, MobCategory.MISC).m_20699_(0.4F, 0.4F).m_20702_(32).m_20712_((new ResourceLocation("tensura", "bog_shot")).toString());
      });
      BOULDER_SHOT = registry.register("boulder_shot", () -> {
         return Builder.m_20704_(BoulderShotProjectile::new, MobCategory.MISC).m_20699_(1.0F, 1.0F).m_20702_(32).m_20712_((new ResourceLocation("tensura", "boulder_shot")).toString());
      });
      CHAOS_EATER = registry.register("chaos_eater", () -> {
         return Builder.m_20704_(ChaosEaterProjectile::new, MobCategory.MISC).m_20699_(1.0F, 1.0F).m_20702_(32).m_20712_((new ResourceLocation("tensura", "chaos_eater")).toString());
      });
      DIMENSION_CUT = registry.register("dimension_cut_projectile", () -> {
         return Builder.m_20704_(DimensionCutProjectile::new, MobCategory.MISC).m_20699_(0.2F, 0.8F).m_20712_((new ResourceLocation("tensura", "dimension_cut_projectile")).toString());
      });
      FIRE_BALL = registry.register("fire_ball", () -> {
         return Builder.m_20704_(FireBallProjectile::new, MobCategory.MISC).m_20699_(0.5F, 0.5F).m_20702_(32).m_20712_((new ResourceLocation("tensura", "fire_ball")).toString());
      });
      FIRE_BOLT = registry.register("fire_bolt", () -> {
         return Builder.m_20704_(FireBoltProjectile::new, MobCategory.MISC).m_20699_(0.5F, 0.5F).m_20702_(32).m_20712_((new ResourceLocation("tensura", "fire_bolt")).toString());
      });
      FLAME_ORB = registry.register("flame_orb", () -> {
         return Builder.m_20704_(FlameOrbProjectile::new, MobCategory.MISC).m_20699_(1.0F, 1.0F).m_20702_(32).m_20712_((new ResourceLocation("tensura", "flame_orb")).toString());
      });
      FROST_BALL = registry.register("frost_ball", () -> {
         return Builder.m_20704_(FrostBallProjectile::new, MobCategory.MISC).m_20699_(0.4F, 0.4F).m_20702_(32).m_20712_((new ResourceLocation("tensura", "frost_ball")).toString());
      });
      FUSIONIST_PROJECTILE = registry.register("fusionist_projectile", () -> {
         return Builder.m_20704_(FusionistProjectile::new, MobCategory.MISC).m_20699_(1.0F, 1.0F).m_20702_(32).m_20712_((new ResourceLocation("tensura", "fusionist_projectile")).toString());
      });
      GRAVITY_SPHERE = registry.register("gravity_sphere", () -> {
         return Builder.m_20704_(GravitySphereProjectile::new, MobCategory.MISC).m_20699_(1.0F, 1.0F).m_20702_(32).m_20712_((new ResourceLocation("tensura", "gravity_sphere")).toString());
      });
      HEAT_SPHERE = registry.register("heat_sphere", () -> {
         return Builder.m_20704_(HeatSphereProjectile::new, MobCategory.MISC).m_20699_(1.0F, 1.0F).m_20702_(32).m_20712_((new ResourceLocation("tensura", "heat_sphere")).toString());
      });
      HELL_FLARE_PROJECTILE = registry.register("hell_flare_projectile", () -> {
         return Builder.m_20704_(HellFlareProjectile::new, MobCategory.MISC).m_20699_(1.0F, 1.0F).m_20702_(32).m_20712_((new ResourceLocation("tensura", "hell_flare_projectile")).toString());
      });
      ICE_LANCE = registry.register("ice_lance", () -> {
         return Builder.m_20704_(IceLanceProjectile::new, MobCategory.MISC).m_20699_(0.5F, 0.5F).m_20702_(32).m_20712_((new ResourceLocation("tensura", "ice_lance")).toString());
      });
      INVISIBLE_FIRE_BOLT = registry.register("invisible_fire_bolt", () -> {
         return Builder.m_20704_(InvisibleFireBoltProjectile::new, MobCategory.MISC).m_20699_(0.5F, 0.5F).m_20702_(32).m_20712_((new ResourceLocation("tensura", "invisible_fire_bolt")).toString());
      });
      LIGHT_ARROW = registry.register("light_arrow", () -> {
         return Builder.m_20704_(LightArrowProjectile::new, MobCategory.MISC).m_20717_(20).m_20699_(0.5F, 0.5F).m_20702_(4).m_20712_((new ResourceLocation("tensura", "light_arrow")).toString());
      });
      LIGHTNING_LANCE = registry.register("lightning_lance", () -> {
         return Builder.m_20704_(LightningLanceProjectile::new, MobCategory.MISC).m_20699_(0.5F, 0.5F).m_20702_(32).m_20712_((new ResourceLocation("tensura", "lightning_lance")).toString());
      });
      LIGHTNING_SPHERE = registry.register("lightning_sphere", () -> {
         return Builder.m_20704_(LightningSphereProjectile::new, MobCategory.MISC).m_20699_(1.0F, 1.0F).m_20702_(32).m_20712_((new ResourceLocation("tensura", "lightning_sphere")).toString());
      });
      MAGMA_SHOT = registry.register("magma_shot", () -> {
         return Builder.m_20704_(MagmaShotProjectile::new, MobCategory.MISC).m_20699_(1.0F, 1.0F).m_20702_(32).m_20712_((new ResourceLocation("tensura", "magma_shot")).toString());
      });
      MUD_SHOT = registry.register("mud_shot", () -> {
         return Builder.m_20704_(MudShotProjectile::new, MobCategory.MISC).m_20699_(0.4F, 0.4F).m_20702_(32).m_20712_((new ResourceLocation("tensura", "mud_shot")).toString());
      });
      OBSIDIAN_SHOT = registry.register("obsidian_shot", () -> {
         return Builder.m_20704_(ObsidianShotProjectile::new, MobCategory.MISC).m_20699_(0.5F, 0.5F).m_20702_(32).m_20712_((new ResourceLocation("tensura", "obsidian_shot")).toString());
      });
      PLASMA_BALL = registry.register("plasmas_ball", () -> {
         return Builder.m_20704_(PlasmaBallProjectile::new, MobCategory.MISC).m_20699_(0.5F, 0.5F).m_20702_(32).m_20712_((new ResourceLocation("tensura", "plasmas_ball")).toString());
      });
      POISON_BALL = registry.register("poison_ball", () -> {
         return Builder.m_20704_(PoisonBallProjectile::new, MobCategory.MISC).m_20699_(0.4F, 0.4F).m_20702_(32).m_20712_((new ResourceLocation("tensura", "poison_ball")).toString());
      });
      POISON_CUTTER = registry.register("poison_cutter", () -> {
         return Builder.m_20704_(PoisonCutterProjectile::new, MobCategory.MISC).m_20699_(0.2F, 0.8F).m_20702_(32).m_20712_((new ResourceLocation("tensura", "poison_cutter")).toString());
      });
      REFLECTOR_ECHO = registry.register("reflector_echo", () -> {
         return Builder.m_20704_(ReflectorEchoProjectile::new, MobCategory.MISC).m_20699_(1.0F, 1.0F).m_20702_(32).m_20712_((new ResourceLocation("tensura", "reflector_echo")).toString());
      });
      SEVERANCE_CUTTER = registry.register("severance_cutter", () -> {
         return Builder.m_20704_(SeveranceCutterProjectile::new, MobCategory.MISC).m_20699_(0.5F, 0.5F).m_20702_(32).m_20712_((new ResourceLocation("tensura", "severance_cutter")).toString());
      });
      SOLAR_GRENADE = registry.register("solar_grenade", () -> {
         return Builder.m_20704_(SolarGrenadeProjectile::new, MobCategory.MISC).m_20699_(1.0F, 1.0F).m_20702_(32).m_20712_((new ResourceLocation("tensura", "solar_grenade")).toString());
      });
      SNIPER_BULLET = registry.register("sniper_bullet", () -> {
         return Builder.m_20704_(SniperBulletProjectile::new, MobCategory.MISC).m_20699_(1.0F, 1.0F).m_20702_(32).m_20712_((new ResourceLocation("tensura", "sniper_bullet")).toString());
      });
      SNIPER_GRENADE = registry.register("sniper_grenade", () -> {
         return Builder.m_20704_(SniperGrenadeProjectile::new, MobCategory.MISC).m_20699_(1.0F, 1.0F).m_20702_(32).m_20712_((new ResourceLocation("tensura", "sniper_grenade")).toString());
      });
      SPACE_CUT = registry.register("space_cut_projectile", () -> {
         return Builder.m_20704_(SpaceCutProjectile::new, MobCategory.MISC).m_20699_(0.2F, 0.8F).m_20712_((new ResourceLocation("tensura", "space_cut_projectile")).toString());
      });
      SPATIAL_ARROW = registry.register("spatial_arrow", () -> {
         return Builder.m_20704_(SpatialArrowProjectile::new, MobCategory.MISC).m_20699_(0.5F, 0.5F).m_20702_(32).m_20712_((new ResourceLocation("tensura", "spatial_arrow")).toString());
      });
      STEAM_BALL = registry.register("steam_ball", () -> {
         return Builder.m_20704_(SteamBallProjectile::new, MobCategory.MISC).m_20699_(0.4F, 0.4F).m_20702_(32).m_20712_((new ResourceLocation("tensura", "steam_ball")).toString());
      });
      STONE_SHOT = registry.register("stone_shot", () -> {
         return Builder.m_20704_(StoneShotProjectile::new, MobCategory.MISC).m_20699_(0.5F, 0.5F).m_20702_(32).m_20712_((new ResourceLocation("tensura", "stone_shot")).toString());
      });
      TEMPEST_SCALE = registry.register("tempest_scale", () -> {
         return Builder.m_20704_(TempestScaleEntity::new, MobCategory.MISC).m_20699_(0.8F, 0.8F).m_20702_(32).m_20712_((new ResourceLocation("tensura", "tempest_scale")).toString());
      });
      WATER_BALL = registry.register("water_ball", () -> {
         return Builder.m_20704_(WaterBallProjectile::new, MobCategory.MISC).m_20699_(0.4F, 0.4F).m_20702_(32).m_20712_((new ResourceLocation("tensura", "water_ball")).toString());
      });
      WATER_BLADE = registry.register("water_blade", () -> {
         return Builder.m_20704_(WaterBladeProjectile::new, MobCategory.MISC).m_20699_(0.2F, 0.8F).m_20702_(32).m_20712_((new ResourceLocation("tensura", "water_blade")).toString());
      });
      WIND_BLADE = registry.register("wind_blade", () -> {
         return Builder.m_20704_(WindBladeProjectile::new, MobCategory.MISC).m_20699_(0.2F, 0.8F).m_20702_(32).m_20712_((new ResourceLocation("tensura", "wind_blade")).toString());
      });
      WIND_SPHERE = registry.register("wind_sphere", () -> {
         return Builder.m_20704_(WindSphereProjectile::new, MobCategory.MISC).m_20699_(1.0F, 1.0F).m_20702_(32).m_20712_((new ResourceLocation("tensura", "wind_sphere")).toString());
      });
      HEALING_POTION = registry.register("healing_potion", () -> {
         return Builder.m_20704_(ThrownHealingPotion::new, MobCategory.MISC).m_20699_(0.3F, 0.3F).m_20719_().m_20712_((new ResourceLocation("tensura", "healing_potion")).toString());
      });
      HOLY_WATER = registry.register("holy_water", () -> {
         return Builder.m_20704_(ThrownHolyWater::new, MobCategory.MISC).m_20699_(0.3F, 0.3F).m_20719_().m_20712_((new ResourceLocation("tensura", "holy_water")).toString());
      });
      MONSTER_SPIT = registry.register("monster_spit", () -> {
         return Builder.m_20704_(MonsterSpitProjectile::new, MobCategory.MISC).m_20699_(1.0F, 1.0F).m_20712_((new ResourceLocation("tensura", "monster_spit")).toString());
      });
      SEVERER_BLADE = registry.register("severer_blade_projectile", () -> {
         return Builder.m_20704_(SevererBladeProjectile::new, MobCategory.MISC).m_20699_(0.5F, 0.5F).m_20712_((new ResourceLocation("tensura", "severer_blade_projectile")).toString());
      });
      WEB_BULLET = registry.register("web_bullet", () -> {
         return Builder.m_20704_(WebBulletProjectile::new, MobCategory.MISC).m_20699_(0.5F, 0.5F).m_20712_((new ResourceLocation("tensura", "web_bullet")).toString());
      });
      SPEAR = registry.register("spear", () -> {
         return Builder.m_20704_(SpearProjectile::new, MobCategory.MISC).m_20699_(0.5F, 0.5F).m_20712_((new ResourceLocation("tensura", "spear")).toString());
      });
      KUNAI = registry.register("kunai", () -> {
         return Builder.m_20704_(KunaiProjectile::new, MobCategory.MISC).m_20699_(0.3F, 0.3F).m_20712_((new ResourceLocation("tensura", "kunai")).toString());
      });
      INVISIBLE_ARROW = registry.register("invisible_arrow", () -> {
         return Builder.m_20704_(InvisibleArrow::new, MobCategory.MISC).m_20699_(0.5F, 0.5F).m_20702_(4).m_20717_(20).m_20712_((new ResourceLocation("tensura", "invisible_arrow")).toString());
      });
      SPEARED_FIN_ARROW = registry.register("speared_fin_arrow", () -> {
         return Builder.m_20704_(SpearedFinArrow::new, MobCategory.MISC).m_20699_(0.5F, 0.5F).m_20702_(4).m_20717_(20).m_20712_((new ResourceLocation("tensura", "speared_fin_arrow")).toString());
      });
      UNICORN_HORN = registry.register("unicorn_horn", () -> {
         return Builder.m_20704_(UnicornHornProjectile::new, MobCategory.MISC).m_20699_(0.5F, 0.5F).m_20702_(4).m_20717_(20).m_20712_((new ResourceLocation("tensura", "unicorn_horn")).toString());
      });
      BOAT_ENTITY = registry.register("tensura_boat", () -> {
         return Builder.m_20704_(TensuraBoatEntity::new, MobCategory.MISC).m_20719_().m_20699_(1.375F, 0.5625F).setCustomClientFactory((spawnEntity, world) -> {
            return new TensuraBoatEntity(world, 0.0D, 0.0D, 0.0D);
         }).m_20712_("tensura_boat");
      });
      CHEST_BOAT_ENTITY = registry.register("tensura_chest_boat", () -> {
         return Builder.m_20704_(TensuraChestBoatEntity::new, MobCategory.MISC).m_20719_().m_20699_(1.375F, 0.5625F).setCustomClientFactory((spawnEntity, world) -> {
            return new TensuraChestBoatEntity(world, 0.0D, 0.0D, 0.0D);
         }).m_20712_("tensura_chest_boat");
      });
      CLONE_DEFAULT = registry.register("clone_default", () -> {
         return Builder.m_20704_(CloneEntity::new, MobCategory.MISC).m_20717_(2).m_20702_(32).m_20699_(0.6F, 1.8F).m_20712_((new ResourceLocation("tensura", "clone_default")).toString());
      });
      CLONE_SLIM = registry.register("clone_slim", () -> {
         return Builder.m_20704_(CloneEntity::new, MobCategory.MISC).m_20717_(2).m_20702_(32).m_20699_(0.6F, 1.8F).m_20712_((new ResourceLocation("tensura", "clone_slim")).toString());
      });
      IFRIT_CLONE = registry.register("ifrit_clone", () -> {
         return Builder.m_20704_(IfritCloneEntity::new, MobCategory.MONSTER).m_20720_().m_20699_(0.8F, 3.0F).m_20712_((new ResourceLocation("tensura", "ifrit_clone")).toString());
      });
      FALMUTH_KNIGHT = registry.register("falmuth_knight", () -> {
         return Builder.m_20704_(FalmuthKnightEntity::new, MobCategory.MONSTER).m_20720_().m_20717_(2).m_20702_(32).m_20699_(0.6F, 1.8F).m_20712_((new ResourceLocation("tensura", "falmuth_knight")).toString());
      });
      FOLGEN = registry.register("folgen", () -> {
         return Builder.m_20704_(FolgenEntity::new, MobCategory.MONSTER).m_20720_().m_20717_(2).m_20702_(32).m_20699_(0.6F, 1.8F).m_20712_((new ResourceLocation("tensura", "folgen")).toString());
      });
      HINATA_SAKAGUCHI = registry.register("hinata_sakaguchi", () -> {
         return Builder.m_20704_(HinataSakaguchiEntity::new, MobCategory.MONSTER).m_20720_().m_20717_(2).m_20702_(32).m_20699_(0.6F, 1.8F).m_20712_((new ResourceLocation("tensura", "hinata_sakaguchi")).toString());
      });
      KIRARA_MIZUTANI = registry.register("kirara_mizutani", () -> {
         return Builder.m_20704_(KiraraMizutaniEntity::new, MobCategory.MONSTER).m_20720_().m_20717_(2).m_20702_(32).m_20699_(0.6F, 1.8F).m_20712_((new ResourceLocation("tensura", "kirara_mizutani")).toString());
      });
      KYOYA_TACHIBANA = registry.register("kyoya_tachibana", () -> {
         return Builder.m_20704_(KyoyaTachibanaEntity::new, MobCategory.MONSTER).m_20720_().m_20717_(2).m_20702_(32).m_20699_(0.6F, 1.8F).m_20712_((new ResourceLocation("tensura", "kyoya_tachibana")).toString());
      });
      MAI_FURUKI = registry.register("mai_furuki", () -> {
         return Builder.m_20704_(MaiFurukiEntity::new, MobCategory.MONSTER).m_20720_().m_20717_(2).m_20702_(32).m_20699_(0.6F, 1.8F).m_20712_((new ResourceLocation("tensura", "mai_furuki")).toString());
      });
      MARK_LAUREN = registry.register("mark_lauren", () -> {
         return Builder.m_20704_(MarkLaurenEntity::new, MobCategory.MONSTER).m_20720_().m_20717_(2).m_20702_(32).m_20699_(0.6F, 1.8F).m_20712_((new ResourceLocation("tensura", "mark_lauren")).toString());
      });
      SHINJI_TANIMURA = registry.register("shinji_tanimura", () -> {
         return Builder.m_20704_(ShinjiTanimuraEntity::new, MobCategory.MONSTER).m_20720_().m_20717_(2).m_20702_(32).m_20699_(0.6F, 1.8F).m_20712_((new ResourceLocation("tensura", "shinji_tanimura")).toString());
      });
      SHIN_RYUSEI = registry.register("shin_ryusei", () -> {
         return Builder.m_20704_(ShinRyuseiEntity::new, MobCategory.MONSTER).m_20720_().m_20717_(2).m_20702_(32).m_20699_(0.6F, 1.8F).m_20712_((new ResourceLocation("tensura", "shin_ryusei")).toString());
      });
      SHIZU = registry.register("shizu", () -> {
         return Builder.m_20704_(ShizuEntity::new, MobCategory.MONSTER).m_20720_().m_20717_(2).m_20702_(32).m_20699_(0.6F, 1.8F).m_20712_((new ResourceLocation("tensura", "shizu")).toString());
      });
      SHOGO_TAGUCHI = registry.register("shogo_taguchi", () -> {
         return Builder.m_20704_(ShogoTaguchiEntity::new, MobCategory.MONSTER).m_20720_().m_20717_(2).m_20702_(32).m_20699_(0.6F, 1.8F).m_20712_((new ResourceLocation("tensura", "shogo_taguchi")).toString());
      });
      AKASH = registry.register("akash", () -> {
         return Builder.m_20704_(AkashEntity::new, MobCategory.MONSTER).m_20720_().m_20699_(0.8F, 1.7F).m_20712_((new ResourceLocation("tensura", "akash")).toString());
      });
      AQUA_FROG = registry.register("aqua_frog", () -> {
         return Builder.m_20704_(AquaFrogEntity::new, MobCategory.MONSTER).m_20720_().m_20699_(1.0F, 1.2F).m_20702_(10).m_20712_((new ResourceLocation("tensura", "aqua_frog")).toString());
      });
      ARMOURSAURUS = registry.register("armoursaurus", () -> {
         return Builder.m_20704_(ArmoursaurusEntity::new, MobCategory.MONSTER).m_20720_().m_20699_(1.5F, 2.0F).m_20712_((new ResourceLocation("tensura", "armoursaurus")).toString());
      });
      ARMY_WASP = registry.register("army_wasp", () -> {
         return Builder.m_20704_(ArmyWaspEntity::new, MobCategory.MONSTER).m_20720_().m_20699_(1.0F, 1.1F).m_20702_(10).m_20712_((new ResourceLocation("tensura", "army_wasp")).toString());
      });
      BARGHEST = registry.register("barghest", () -> {
         return Builder.m_20704_(BarghestEntity::new, MobCategory.MONSTER).m_20699_(0.9F, 1.2F).m_20702_(10).m_20712_((new ResourceLocation("tensura", "barghest")).toString());
      });
      BEAST_GNOME = registry.register("beast_gnome", () -> {
         return Builder.m_20704_(BeastGnomeEntity::new, MobCategory.MONSTER).m_20720_().m_20699_(3.5F, 3.5F).m_20702_(10).m_20712_((new ResourceLocation("tensura", "beast_gnome")).toString());
      });
      BLACK_SPIDER = registry.register("black_spider", () -> {
         return Builder.m_20704_(BlackSpiderEntity::new, MobCategory.MONSTER).m_20720_().m_20699_(3.0F, 2.5F).m_20712_((new ResourceLocation("tensura", "black_spider")).toString());
      });
      BLADE_TIGER = registry.register("blade_tiger", () -> {
         return Builder.m_20704_(BladeTigerEntity::new, MobCategory.MONSTER).m_20720_().m_20699_(1.5F, 2.3F).m_20712_((new ResourceLocation("tensura", "blade_tiger")).toString());
      });
      BULLDEER = registry.register("bulldeer", () -> {
         return Builder.m_20704_(BulldeerEntity::new, MobCategory.MONSTER).m_20699_(0.9F, 1.8F).m_20702_(10).m_20712_((new ResourceLocation("tensura", "bulldeer")).toString());
      });
      CHARYBDIS = registry.register("charybdis", () -> {
         return Builder.m_20704_(CharybdisEntity::new, MobCategory.MONSTER).m_20720_().m_20699_(14.0F, 13.0F).m_20702_(64).m_20717_(Integer.MAX_VALUE).m_20712_((new ResourceLocation("tensura", "charybdis")).toString());
      });
      DIREWOLF = registry.register("direwolf", () -> {
         return Builder.m_20704_(DirewolfEntity::new, MobCategory.MONSTER).m_20720_().m_20699_(0.8F, 1.3F).m_20712_((new ResourceLocation("tensura", "direwolf")).toString());
      });
      DRAGON_PEACOCK = registry.register("dragon_peacock", () -> {
         return Builder.m_20704_(DragonPeacockEntity::new, MobCategory.MONSTER).m_20699_(0.5F, 1.3F).m_20712_((new ResourceLocation("tensura", "dragon_peacock")).toString());
      });
      ELEMENTAL_COLOSSUS = registry.register("elemental_colossus", () -> {
         return Builder.m_20704_(ElementalColossusEntity::new, MobCategory.MISC).m_20699_(2.0F, 4.0F).m_20712_((new ResourceLocation("tensura", "elemental_colossus")).toString());
      });
      FEATHERED_SERPENT = registry.register("feathered_serpent", () -> {
         return Builder.m_20704_(FeatheredSerpentEntity::new, MobCategory.MONSTER).m_20720_().m_20699_(0.9F, 2.5F).m_20702_(10).m_20712_((new ResourceLocation("tensura", "feathered_serpent")).toString());
      });
      GIANT_ANT = registry.register("giant_ant", () -> {
         return Builder.m_20704_(GiantAntEntity::new, MobCategory.MONSTER).m_20720_().m_20699_(3.5F, 3.5F).m_20712_((new ResourceLocation("tensura", "giant_ant")).toString());
      });
      GIANT_BAT = registry.register("giant_bat", () -> {
         return Builder.m_20704_(GiantBatEntity::new, MobCategory.MONSTER).m_20720_().m_20699_(1.2F, 1.65F).setShouldReceiveVelocityUpdates(true).setUpdateInterval(1).m_20712_((new ResourceLocation("tensura", "giant_bat")).toString());
      });
      GIANT_BEAR = registry.register("giant_bear", () -> {
         return Builder.m_20704_(GiantBearEntity::new, MobCategory.CREATURE).m_20699_(2.0F, 3.5F).m_20702_(10).m_20712_((new ResourceLocation("tensura", "giant_bear")).toString());
      });
      GIANT_COD = registry.register("giant_cod", () -> {
         return Builder.m_20704_(GiantCodEntity::new, MobCategory.WATER_CREATURE).m_20699_(1.2F, 1.0F).m_20712_((new ResourceLocation("tensura", "giant_cod")).toString());
      });
      GIANT_SALMON = registry.register("giant_salmon", () -> {
         return Builder.m_20704_(GiantSalmonEntity::new, MobCategory.WATER_CREATURE).m_20699_(1.2F, 1.0F).m_20712_((new ResourceLocation("tensura", "giant_salmon")).toString());
      });
      GOBLIN = registry.register("goblin", () -> {
         return Builder.m_20704_(GoblinEntity::new, MobCategory.MONSTER).m_20720_().m_20699_(0.45F, 1.35F).m_20712_((new ResourceLocation("tensura", "goblin")).toString());
      });
      HELL_CATERPILLAR = registry.register("hell_caterpillar", () -> {
         return Builder.m_20704_(HellCaterpillarEntity::new, MobCategory.MONSTER).m_20720_().m_20699_(0.66F, 0.6F).m_20712_((new ResourceLocation("tensura", "hell_caterpillar")).toString());
      });
      HELL_MOTH = registry.register("hell_moth", () -> {
         return Builder.m_20704_(HellMothEntity::new, MobCategory.MONSTER).m_20720_().m_20699_(2.0F, 1.5F).m_20712_((new ResourceLocation("tensura", "hell_moth")).toString());
      });
      HOLY_COW = registry.register("holy_cow", () -> {
         return Builder.m_20704_(HolyCowEntity::new, MobCategory.CREATURE).m_20699_(0.9F, 1.4F).m_20702_(10).m_20712_((new ResourceLocation("tensura", "holy_cow")).toString());
      });
      HORNED_RABBIT = registry.register("horned_rabbit", () -> {
         return Builder.m_20704_(HornedRabbitEntity::new, MobCategory.MONSTER).m_20699_(0.4F, 0.5F).m_20702_(10).m_20712_((new ResourceLocation("tensura", "horned_rabbit")).toString());
      });
      HOUND_DOG = registry.register("hound_dog", () -> {
         return Builder.m_20704_(HoundDogEntity::new, MobCategory.MONSTER).m_20720_().m_20699_(1.0F, 1.3F).m_20702_(10).m_20712_((new ResourceLocation("tensura", "hound_dog")).toString());
      });
      HOVER_LIZARD = registry.register("hover_lizard", () -> {
         return Builder.m_20704_(HoverLizardEntity::new, MobCategory.MONSTER).m_20720_().m_20699_(1.3964844F, 2.6F).m_20702_(10).m_20712_((new ResourceLocation("tensura", "hover_lizard")).toString());
      });
      HORNED_BEAR = registry.register("horned_bear", () -> {
         return Builder.m_20704_(HornedBearEntity::new, MobCategory.CREATURE).m_20699_(1.5F, 2.0F).m_20702_(10).m_20712_((new ResourceLocation("tensura", "horned_bear")).toString());
      });
      IFRIT = registry.register("ifrit", () -> {
         return Builder.m_20704_(IfritEntity::new, MobCategory.MONSTER).m_20720_().m_20699_(0.8F, 3.0F).m_20712_((new ResourceLocation("tensura", "ifrit")).toString());
      });
      KNIGHT_SPIDER = registry.register("knight_spider", () -> {
         return Builder.m_20704_(KnightSpiderEntity::new, MobCategory.MONSTER).m_20720_().m_20699_(5.0F, 3.75F).m_20712_((new ResourceLocation("tensura", "knight_spider")).toString());
      });
      LANDFISH = registry.register("landfish", () -> {
         return Builder.m_20704_(LandfishEntity::new, MobCategory.MONSTER).m_20699_(1.0F, 1.5F).m_20702_(10).m_20712_((new ResourceLocation("tensura", "landfish")).toString());
      });
      LEECH_LIZARD = registry.register("leech_lizard", () -> {
         return Builder.m_20704_(LeechLizardEntity::new, MobCategory.MONSTER).m_20699_(1.0F, 2.5F).m_20702_(10).m_20712_((new ResourceLocation("tensura", "leech_lizard")).toString());
      });
      LIZARDMAN = registry.register("lizardman", () -> {
         return Builder.m_20704_(LizardmanEntity::new, MobCategory.MONSTER).m_20720_().m_20699_(0.6F, 1.8F).m_20712_((new ResourceLocation("tensura", "lizardman")).toString());
      });
      MEGALODON = registry.register("megalodon", () -> {
         return Builder.m_20704_(MegalodonEntity::new, MobCategory.MONSTER).m_20720_().m_20699_(2.0F, 2.0F).m_20702_(10).m_20712_((new ResourceLocation("tensura", "megalodon")).toString());
      });
      ONE_EYED_OWL = registry.register("one_eyed_owl", () -> {
         return Builder.m_20704_(OneEyedOwlEntity::new, MobCategory.MONSTER).m_20699_(0.3F, 0.7F).m_20712_((new ResourceLocation("tensura", "one_eyed_owl")).toString());
      });
      ORC = registry.register("orc", () -> {
         return Builder.m_20704_(OrcEntity::new, MobCategory.MONSTER).m_20720_().m_20699_(1.2F, 2.5F).m_20712_((new ResourceLocation("tensura", "orc")).toString());
      });
      ORC_DISASTER = registry.register("orc_disaster", () -> {
         return Builder.m_20704_(OrcDisasterEntity::new, MobCategory.MONSTER).m_20720_().m_20699_(1.2F, 4.5F).m_20712_((new ResourceLocation("tensura", "orc_disaster")).toString());
      });
      ORC_LORD = registry.register("orc_lord", () -> {
         return Builder.m_20704_(OrcLordEntity::new, MobCategory.MONSTER).m_20720_().m_20699_(1.2F, 3.0F).m_20712_((new ResourceLocation("tensura", "orc_lord")).toString());
      });
      SISSIE = registry.register("sissie", () -> {
         return Builder.m_20704_(SissieEntity::new, MobCategory.MONSTER).m_20720_().m_20699_(6.0F, 6.0F).m_20702_(10).m_20712_((new ResourceLocation("tensura", "sissie")).toString());
      });
      SALAMANDER = registry.register("salamander", () -> {
         return Builder.m_20704_(SalamanderEntity::new, MobCategory.MONSTER).m_20720_().m_20699_(0.5F, 1.6F).m_20712_((new ResourceLocation("tensura", "salamander")).toString());
      });
      SLIME = registry.register("slime", () -> {
         return Builder.m_20704_(SlimeEntity::new, MobCategory.MONSTER).m_20699_(1.0F, 0.7F).m_20712_((new ResourceLocation("tensura", "slime")).toString());
      });
      SPEAR_TORO = registry.register("spear_toro", () -> {
         return Builder.m_20704_(SpearToroEntity::new, MobCategory.MONSTER).m_20720_().m_20699_(2.5F, 2.5F).m_20702_(10).m_20712_((new ResourceLocation("tensura", "spear_toro")).toString());
      });
      SUPERMASSIVE_SLIME = registry.register("supermassive_slime", () -> {
         return Builder.m_20704_(SupermassiveSlimeEntity::new, MobCategory.MONSTER).m_20699_(1.0F, 0.7F).m_20712_((new ResourceLocation("tensura", "supermassive_slime")).toString());
      });
      METAL_SLIME = registry.register("metal_slime", () -> {
         return Builder.m_20704_(MetalSlimeEntity::new, MobCategory.MONSTER).m_20699_(1.0F, 0.7F).m_20712_((new ResourceLocation("tensura", "metal_slime")).toString());
      });
      SYLPHIDE = registry.register("sylphide", () -> {
         return Builder.m_20704_(SylphideEntity::new, MobCategory.MONSTER).m_20720_().m_20699_(0.8F, 2.0F).m_20712_((new ResourceLocation("tensura", "sylphide")).toString());
      });
      UNDINE = registry.register("undine", () -> {
         return Builder.m_20704_(UndineEntity::new, MobCategory.MONSTER).m_20720_().m_20699_(0.8F, 2.0F).m_20712_((new ResourceLocation("tensura", "undine")).toString());
      });
      UNICORN = registry.register("unicorn", () -> {
         return Builder.m_20704_(UnicornEntity::new, MobCategory.CREATURE).m_20699_(1.3964844F, 1.6F).m_20702_(10).m_20712_((new ResourceLocation("tensura", "unicorn")).toString());
      });
      WAR_GNOME = registry.register("war_gnome", () -> {
         return Builder.m_20704_(WarGnomeEntity::new, MobCategory.MONSTER).m_20720_().m_20699_(1.2F, 4.5F).m_20712_((new ResourceLocation("tensura", "war_gnome")).toString());
      });
      WINGED_CAT = registry.register("winged_cat", () -> {
         return Builder.m_20704_(WingedCatEntity::new, MobCategory.MONSTER).m_20720_().m_20699_(0.9F, 1.0F).m_20702_(10).m_20712_((new ResourceLocation("tensura", "winged_cat")).toString());
      });
   }
}
