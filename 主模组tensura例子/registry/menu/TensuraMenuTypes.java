package com.github.manasmods.tensura.registry.menu;

import com.github.manasmods.tensura.menu.BattlewillSelectionMenu;
import com.github.manasmods.tensura.menu.DegenerateCraftingMenu;
import com.github.manasmods.tensura.menu.DegenerateEnchantmentMenu;
import com.github.manasmods.tensura.menu.EvolutionMenu;
import com.github.manasmods.tensura.menu.KilnMenu;
import com.github.manasmods.tensura.menu.MagicMenu;
import com.github.manasmods.tensura.menu.MagicSelectionMenu;
import com.github.manasmods.tensura.menu.MainMenu;
import com.github.manasmods.tensura.menu.NamingMenu;
import com.github.manasmods.tensura.menu.RaceSelectionMenu;
import com.github.manasmods.tensura.menu.ResearcherEnchantmentMenu;
import com.github.manasmods.tensura.menu.SkillCreatorMenu;
import com.github.manasmods.tensura.menu.SkillMenu;
import com.github.manasmods.tensura.menu.SkillSelectionMenu;
import com.github.manasmods.tensura.menu.SmithingBenchMenu;
import com.github.manasmods.tensura.menu.SpatialMenu;
import net.minecraft.world.inventory.MenuType;
import net.minecraftforge.common.extensions.IForgeMenuType;
import net.minecraftforge.eventbus.api.IEventBus;
import net.minecraftforge.registries.DeferredRegister;
import net.minecraftforge.registries.ForgeRegistries;
import net.minecraftforge.registries.RegistryObject;

public class TensuraMenuTypes {
   private static final DeferredRegister<MenuType<?>> registry;
   public static final RegistryObject<MenuType<KilnMenu>> KILN;
   public static final RegistryObject<MenuType<SmithingBenchMenu>> SMITHING_BENCH;
   public static final RegistryObject<MenuType<RaceSelectionMenu>> RACE_SELECTION;
   public static final RegistryObject<MenuType<SpatialMenu>> SPATIAL_GUI;
   public static final RegistryObject<MenuType<DegenerateCraftingMenu>> DEGENERATE_CRAFTING_MENU;
   public static final RegistryObject<MenuType<DegenerateEnchantmentMenu>> DEGENERATE_ENCHANTMENT_MENU;
   public static final RegistryObject<MenuType<MainMenu>> MAIN_MENU;
   public static final RegistryObject<MenuType<EvolutionMenu>> EVOLUTION_MENU;
   public static final RegistryObject<MenuType<SkillMenu>> SKILL_MENU;
   public static final RegistryObject<MenuType<SkillSelectionMenu>> SKILL_SELECTION_MENU;
   public static final RegistryObject<MenuType<MagicMenu>> MAGIC_MENU;
   public static final RegistryObject<MenuType<MagicSelectionMenu>> MAGIC_SELECTION_MENU;
   public static final RegistryObject<MenuType<BattlewillSelectionMenu>> BATTLEWILL_SELECTION_MENU;
   public static final RegistryObject<MenuType<NamingMenu>> NAMING_MENU;
   public static final RegistryObject<MenuType<ResearcherEnchantmentMenu>> RESEARCHER_ENCHANTMENT_MENU;
   public static final RegistryObject<MenuType<SkillCreatorMenu>> SKILL_CREATOR_MENU;

   public static void init(IEventBus modEventBus) {
      registry.register(modEventBus);
   }

   static {
      registry = DeferredRegister.create(ForgeRegistries.MENU_TYPES, "tensura");
      KILN = registry.register("kiln_menu", () -> {
         return IForgeMenuType.create(KilnMenu::new);
      });
      SMITHING_BENCH = registry.register("smithing_bench_menu", () -> {
         return IForgeMenuType.create(SmithingBenchMenu::new);
      });
      RACE_SELECTION = registry.register("race_selection_menu", () -> {
         return IForgeMenuType.create(RaceSelectionMenu::new);
      });
      SPATIAL_GUI = registry.register("spatial_gui", () -> {
         return IForgeMenuType.create(SpatialMenu::new);
      });
      DEGENERATE_CRAFTING_MENU = registry.register("degenerate_crafting_menu", () -> {
         return IForgeMenuType.create(DegenerateCraftingMenu::new);
      });
      DEGENERATE_ENCHANTMENT_MENU = registry.register("degenerate_enchantment_menu", () -> {
         return IForgeMenuType.create(DegenerateEnchantmentMenu::new);
      });
      MAIN_MENU = registry.register("main_menu", () -> {
         return IForgeMenuType.create(MainMenu::new);
      });
      EVOLUTION_MENU = registry.register("evolution_menu", () -> {
         return IForgeMenuType.create(EvolutionMenu::new);
      });
      SKILL_MENU = registry.register("skill_menu", () -> {
         return IForgeMenuType.create(SkillMenu::new);
      });
      SKILL_SELECTION_MENU = registry.register("skill_selection_menu", () -> {
         return IForgeMenuType.create(SkillSelectionMenu::new);
      });
      MAGIC_MENU = registry.register("magic_menu", () -> {
         return IForgeMenuType.create(MagicMenu::new);
      });
      MAGIC_SELECTION_MENU = registry.register("magic_selection_menu", () -> {
         return IForgeMenuType.create(MagicSelectionMenu::new);
      });
      BATTLEWILL_SELECTION_MENU = registry.register("battlewill_selection_menu", () -> {
         return IForgeMenuType.create(BattlewillSelectionMenu::new);
      });
      NAMING_MENU = registry.register("naming_menu", () -> {
         return IForgeMenuType.create(NamingMenu::new);
      });
      RESEARCHER_ENCHANTMENT_MENU = registry.register("researcher_enchantment_menu", () -> {
         return IForgeMenuType.create(ResearcherEnchantmentMenu::new);
      });
      SKILL_CREATOR_MENU = registry.register("skill_creator_menu", () -> {
         return IForgeMenuType.create(SkillCreatorMenu::new);
      });
   }
}
