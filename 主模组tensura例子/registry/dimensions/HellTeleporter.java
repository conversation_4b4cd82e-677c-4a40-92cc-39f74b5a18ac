package com.github.manasmods.tensura.registry.dimensions;

import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.registry.blocks.TensuraBlocks;
import java.util.Optional;
import java.util.function.Function;
import net.minecraft.core.BlockPos;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.levelgen.Heightmap.Types;
import net.minecraft.world.level.portal.PortalInfo;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.common.util.ITeleporter;
import org.jetbrains.annotations.Nullable;

public class HellTeleporter implements ITeleporter {
   private final boolean setSpawn;

   public HellTeleporter(boolean setSpawn) {
      this.setSpawn = setSpawn;
   }

   public boolean isVanilla() {
      return false;
   }

   @Nullable
   public PortalInfo getPortalInfo(Entity entity, ServerLevel destWorld, Function<ServerLevel, PortalInfo> defaultPortalInfo) {
      entity.f_19802_ = 60;
      ServerPlayer player;
      BlockPos pos;
      int yHeight;
      if (entity instanceof ServerPlayer) {
         player = (ServerPlayer)entity;
         if (destWorld.m_46472_() == TensuraDimensions.HELL) {
            pos = entity.m_20183_();
            if (player.m_8963_() == destWorld.m_46472_()) {
               BlockPos respawnPos = player.m_8961_();
               if (respawnPos != null) {
                  Optional<Vec3> optional = Player.m_36130_(destWorld, respawnPos, player.m_8962_(), player.m_8964_(), true);
                  pos = (BlockPos)optional.map(BlockPos::new).orElse(respawnPos);
               }
            }

            if (!destWorld.m_8055_(pos.m_7494_()).m_60795_() || destWorld.m_8055_(pos.m_7495_()).m_60795_()) {
               yHeight = destWorld.m_6924_(Types.WORLD_SURFACE, pos.m_123341_(), pos.m_123343_());
               if (yHeight < 10) {
                  yHeight = 128;
               }

               pos = pos.m_175288_(yHeight);
               if (destWorld.m_8055_(pos.m_7495_()).m_60795_()) {
                  makeStonePlatform(destWorld, pos);
               }
            }

            if (this.setSpawn) {
               player.m_9158_(destWorld.m_46472_(), pos, player.m_146908_(), true, false);
            }

            return new PortalInfo(new Vec3((double)pos.m_123341_(), (double)pos.m_123342_(), (double)pos.m_123343_()), Vec3.f_82478_, 0.0F, 0.0F);
         }
      }

      if (entity instanceof ServerPlayer) {
         player = (ServerPlayer)entity;
         if (player.m_8963_() == destWorld.m_46472_()) {
            pos = player.m_8961_();
            if (pos != null) {
               Optional<Vec3> optional = Player.m_36130_(destWorld, pos, player.m_8962_(), player.m_8964_(), true);
               if (optional.isPresent()) {
                  pos = new BlockPos((Vec3)optional.get());
               }
            } else {
               pos = destWorld.m_220360_();
            }

            if (!destWorld.m_8055_(pos.m_7494_()).m_60795_()) {
               yHeight = destWorld.m_6924_(Types.WORLD_SURFACE, pos.m_123341_(), pos.m_123343_());
               if (yHeight < 10) {
                  yHeight = 128;
               }

               pos = pos.m_175288_(yHeight);
               if (destWorld.m_8055_(pos.m_7495_()).m_60795_()) {
                  makeSolidSpacePlatform(destWorld, pos);
               }
            }

            if (this.setSpawn) {
               player.m_9158_(destWorld.m_46472_(), pos, player.m_146908_(), true, false);
            }

            return new PortalInfo(new Vec3((double)pos.m_123341_(), (double)pos.m_123342_(), (double)pos.m_123343_()), Vec3.f_82478_, player.m_8962_(), 0.0F);
         }
      }

      BlockPos pos = entity.m_20183_();
      if (!destWorld.m_8055_(pos.m_7494_()).m_60795_()) {
         int yHeight = destWorld.m_6924_(Types.WORLD_SURFACE, pos.m_123341_(), pos.m_123343_());
         if (yHeight < 10) {
            yHeight = 128;
         }

         pos = pos.m_175288_(yHeight);
         if (destWorld.m_8055_(pos.m_7495_()).m_60795_()) {
            makeSolidSpacePlatform(destWorld, pos);
         }
      }

      return new PortalInfo(new Vec3((double)pos.m_123341_(), (double)pos.m_123342_(), (double)pos.m_123343_()), Vec3.f_82478_, entity.m_146908_(), entity.m_146909_());
   }

   public static void makeStonePlatform(ServerLevel pServerLevel, BlockPos pos) {
      int i = pos.m_123341_();
      int j = pos.m_123342_() - 1;
      int k = pos.m_123343_();
      BlockPos.m_121976_(i - 1, j + 1, k - 1, i + 1, j + 3, k + 1).forEach((blockPos) -> {
         pServerLevel.m_46597_(blockPos, Blocks.f_50016_.m_49966_());
      });
      BlockPos.m_121976_(i - 1, j, k - 1, i + 1, j, k + 1).forEach((blockPos) -> {
         pServerLevel.m_46597_(blockPos, Blocks.f_50069_.m_49966_());
      });
   }

   public static void makeSolidSpacePlatform(ServerLevel pServerLevel, BlockPos pos) {
      int i = pos.m_123341_();
      int j = pos.m_123342_() - 1;
      int k = pos.m_123343_();
      BlockPos.m_121976_(i - 1, j + 1, k - 1, i + 1, j + 3, k + 1).forEach((blockPos) -> {
         if (pServerLevel.m_8055_(blockPos).m_204336_(TensuraTags.Blocks.SKILL_BREAK_EASY)) {
            pServerLevel.m_46597_(blockPos, Blocks.f_50016_.m_49966_());
         }

      });
      BlockPos.m_121976_(i - 1, j, k - 1, i + 1, j, k + 1).forEach((blockPos) -> {
         BlockState state = pServerLevel.m_8055_(blockPos);
         if (state.m_60795_() || state.m_204336_(TensuraTags.Blocks.SKILL_BREAK_EASY)) {
            pServerLevel.m_46597_(blockPos, ((Block)TensuraBlocks.SOLID_SPACE.get()).m_49966_());
            pServerLevel.m_186460_(blockPos, (Block)TensuraBlocks.SOLID_SPACE.get(), 1200);
         }

      });
   }
}
