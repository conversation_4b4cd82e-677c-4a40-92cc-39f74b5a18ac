package com.github.manasmods.tensura.registry.dimensions;

import com.github.manasmods.tensura.registry.biome.TensuraBiomes;
import net.minecraft.client.Camera;
import net.minecraft.client.Minecraft;
import net.minecraft.client.multiplayer.ClientLevel;
import net.minecraft.client.player.LocalPlayer;
import net.minecraft.client.renderer.DimensionSpecialEffects;
import net.minecraft.client.renderer.LightTexture;
import net.minecraft.client.renderer.DimensionSpecialEffects.SkyType;
import net.minecraft.core.Holder;
import net.minecraft.world.level.biome.Biome;
import net.minecraft.world.phys.Vec3;
import org.jetbrains.annotations.NotNull;

public class TensuraDimensionEffects {
   public static final DimensionSpecialEffects HELL = new TensuraDimensionEffects.Hell();

   private static class Hell extends DimensionSpecialEffects {
      public Hell() {
         super(128.0F, false, SkyType.NONE, false, false);
      }

      @NotNull
      public Vec3 m_5927_(Vec3 vec3, float v) {
         return vec3;
      }

      public boolean m_5781_(int i, int i1) {
         LocalPlayer player = Minecraft.m_91087_().f_91074_;
         if (player == null) {
            return false;
         } else {
            Holder<Biome> biome = player.f_19853_.m_204166_(player.m_20183_());
            return biome.m_203565_(TensuraBiomes.UNDERWORLD_BARRENS.getKey()) || biome.m_203565_(TensuraBiomes.UNDERWORLD_SPIKES.getKey());
         }
      }

      public boolean tickRain(ClientLevel level, int ticks, Camera camera) {
         return true;
      }

      public boolean renderSnowAndRain(ClientLevel level, int ticks, float partialTick, LightTexture lightTexture, double camX, double camY, double camZ) {
         return true;
      }
   }
}
