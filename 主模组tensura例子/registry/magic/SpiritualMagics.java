package com.github.manasmods.tensura.registry.magic;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.tensura.ability.magic.spiritual.darkness.DarkCubeMagic;
import com.github.manasmods.tensura.ability.magic.spiritual.darkness.DarknessCannonMagic;
import com.github.manasmods.tensura.ability.magic.spiritual.darkness.DarknessMagic;
import com.github.manasmods.tensura.ability.magic.spiritual.darkness.ShadowBindMagic;
import com.github.manasmods.tensura.ability.magic.spiritual.darkness.TrueDarknessMagic;
import com.github.manasmods.tensura.ability.magic.spiritual.earth.EarthJailMagic;
import com.github.manasmods.tensura.ability.magic.spiritual.earth.EarthMagic;
import com.github.manasmods.tensura.ability.magic.spiritual.earth.EarthSpikesMagic;
import com.github.manasmods.tensura.ability.magic.spiritual.earth.EarthStormMagic;
import com.github.manasmods.tensura.ability.magic.spiritual.earth.MagmaSurgeMagic;
import com.github.manasmods.tensura.ability.magic.spiritual.fire.FireBoltMagic;
import com.github.manasmods.tensura.ability.magic.spiritual.fire.FireBreathMagic;
import com.github.manasmods.tensura.ability.magic.spiritual.fire.FireMagic;
import com.github.manasmods.tensura.ability.magic.spiritual.fire.FlareCircleMagic;
import com.github.manasmods.tensura.ability.magic.spiritual.fire.HellfireMagic;
import com.github.manasmods.tensura.ability.magic.spiritual.light.LightMagic;
import com.github.manasmods.tensura.ability.magic.spiritual.light.SolarBeamMagic;
import com.github.manasmods.tensura.ability.magic.spiritual.light.SolarFlareMagic;
import com.github.manasmods.tensura.ability.magic.spiritual.light.SolarRainMagic;
import com.github.manasmods.tensura.ability.magic.spiritual.light.SolarWaveMagic;
import com.github.manasmods.tensura.ability.magic.spiritual.space.GateMagic;
import com.github.manasmods.tensura.ability.magic.spiritual.space.ShrinkMagic;
import com.github.manasmods.tensura.ability.magic.spiritual.space.SpaceMagic;
import com.github.manasmods.tensura.ability.magic.spiritual.space.SwipeMagic;
import com.github.manasmods.tensura.ability.magic.spiritual.space.TeleportMagic;
import com.github.manasmods.tensura.ability.magic.spiritual.water.AcidRainMagic;
import com.github.manasmods.tensura.ability.magic.spiritual.water.BlizzardMagic;
import com.github.manasmods.tensura.ability.magic.spiritual.water.MegiddoMagic;
import com.github.manasmods.tensura.ability.magic.spiritual.water.WaterCutterMagic;
import com.github.manasmods.tensura.ability.magic.spiritual.water.WaterMagic;
import com.github.manasmods.tensura.ability.magic.spiritual.wind.AerialBladeMagic;
import com.github.manasmods.tensura.ability.magic.spiritual.wind.ElectroBlastMagic;
import com.github.manasmods.tensura.ability.magic.spiritual.wind.LightningLanceMagic;
import com.github.manasmods.tensura.ability.magic.spiritual.wind.WindBladeMagic;
import com.github.manasmods.tensura.ability.magic.spiritual.wind.WindMagic;
import com.github.manasmods.tensura.ability.magic.summon.SummonGreaterElementalMagic;
import com.github.manasmods.tensura.ability.magic.summon.SummonHoundDogMagic;
import com.github.manasmods.tensura.ability.magic.summon.SummonMediumElementalMagic;
import net.minecraftforge.eventbus.api.IEventBus;
import net.minecraftforge.registries.DeferredRegister;
import net.minecraftforge.registries.RegistryObject;

public class SpiritualMagics {
   private static final DeferredRegister<ManasSkill> registry = DeferredRegister.create(SkillAPI.getSkillRegistryKey(), "tensura");
   public static final RegistryObject<DarknessMagic> DARKNESS;
   public static final RegistryObject<ShadowBindMagic> SHADOW_BIND;
   public static final RegistryObject<DarkCubeMagic> DARK_CUBE;
   public static final RegistryObject<DarknessCannonMagic> DARKNESS_CANNON;
   public static final RegistryObject<TrueDarknessMagic> TRUE_DARKNESS;
   public static final RegistryObject<EarthMagic> EARTH;
   public static final RegistryObject<EarthSpikesMagic> EARTH_SPIKES;
   public static final RegistryObject<EarthStormMagic> EARTH_STORM;
   public static final RegistryObject<MagmaSurgeMagic> MAGMA_SURGE;
   public static final RegistryObject<EarthJailMagic> EARTH_JAIL;
   public static final RegistryObject<FireMagic> FIRE;
   public static final RegistryObject<FireBreathMagic> FIRE_BREATH;
   public static final RegistryObject<FireBoltMagic> FIRE_BOLT;
   public static final RegistryObject<FlareCircleMagic> FLARE_CIRCLE;
   public static final RegistryObject<HellfireMagic> HELLFIRE;
   public static final RegistryObject<LightMagic> LIGHT;
   public static final RegistryObject<SolarBeamMagic> SOLAR_BEAM;
   public static final RegistryObject<SolarWaveMagic> SOLAR_WAVE;
   public static final RegistryObject<SolarRainMagic> SOLAR_RAIN;
   public static final RegistryObject<SolarFlareMagic> SOLAR_FLARE;
   public static final RegistryObject<SpaceMagic> SPACE;
   public static final RegistryObject<GateMagic> GATE;
   public static final RegistryObject<ShrinkMagic> SHRINK;
   public static final RegistryObject<TeleportMagic> TELEPORT;
   public static final RegistryObject<SwipeMagic> SWIPE;
   public static final RegistryObject<WaterMagic> WATER;
   public static final RegistryObject<WaterCutterMagic> WATER_CUTTER;
   public static final RegistryObject<AcidRainMagic> ACID_RAIN;
   public static final RegistryObject<MegiddoMagic> MEGIDDO;
   public static final RegistryObject<BlizzardMagic> BLIZZARD;
   public static final RegistryObject<WindMagic> WIND;
   public static final RegistryObject<LightningLanceMagic> LIGHTNING_LANCE;
   public static final RegistryObject<WindBladeMagic> WIND_BLADE;
   public static final RegistryObject<ElectroBlastMagic> ELECTRO_BLAST;
   public static final RegistryObject<AerialBladeMagic> AERIAL_BLADE;
   public static final RegistryObject<SummonHoundDogMagic> SUMMON_HOUND_DOG;
   public static final RegistryObject<SummonMediumElementalMagic> SUMMON_MEDIUM_ELEMENTAL;
   public static final RegistryObject<SummonGreaterElementalMagic> SUMMON_GREATER_ELEMENTAL;

   public static void init(IEventBus modEventBus) {
      registry.register(modEventBus);
   }

   static {
      DARKNESS = registry.register("darkness", DarknessMagic::new);
      SHADOW_BIND = registry.register("shadow_bind", ShadowBindMagic::new);
      DARK_CUBE = registry.register("dark_cube", DarkCubeMagic::new);
      DARKNESS_CANNON = registry.register("darkness_cannon", DarknessCannonMagic::new);
      TRUE_DARKNESS = registry.register("true_darkness", TrueDarknessMagic::new);
      EARTH = registry.register("earth", EarthMagic::new);
      EARTH_SPIKES = registry.register("earth_spikes", EarthSpikesMagic::new);
      EARTH_STORM = registry.register("earth_storm", EarthStormMagic::new);
      MAGMA_SURGE = registry.register("magma_surge", MagmaSurgeMagic::new);
      EARTH_JAIL = registry.register("earth_jail", EarthJailMagic::new);
      FIRE = registry.register("fire", FireMagic::new);
      FIRE_BREATH = registry.register("fire_breath", FireBreathMagic::new);
      FIRE_BOLT = registry.register("fire_bolt", FireBoltMagic::new);
      FLARE_CIRCLE = registry.register("flare_circle", FlareCircleMagic::new);
      HELLFIRE = registry.register("hellfire", HellfireMagic::new);
      LIGHT = registry.register("light", LightMagic::new);
      SOLAR_BEAM = registry.register("solar_beam", SolarBeamMagic::new);
      SOLAR_WAVE = registry.register("solar_wave", SolarWaveMagic::new);
      SOLAR_RAIN = registry.register("solar_rain", SolarRainMagic::new);
      SOLAR_FLARE = registry.register("solar_flare", SolarFlareMagic::new);
      SPACE = registry.register("space", SpaceMagic::new);
      GATE = registry.register("gate", GateMagic::new);
      SHRINK = registry.register("shrink", ShrinkMagic::new);
      TELEPORT = registry.register("teleport", TeleportMagic::new);
      SWIPE = registry.register("swipe", SwipeMagic::new);
      WATER = registry.register("water", WaterMagic::new);
      WATER_CUTTER = registry.register("water_cutter", WaterCutterMagic::new);
      ACID_RAIN = registry.register("acid_rain", AcidRainMagic::new);
      MEGIDDO = registry.register("megiddo", MegiddoMagic::new);
      BLIZZARD = registry.register("blizzard", BlizzardMagic::new);
      WIND = registry.register("wind", WindMagic::new);
      LIGHTNING_LANCE = registry.register("lightning_lance", LightningLanceMagic::new);
      WIND_BLADE = registry.register("wind_blade", WindBladeMagic::new);
      ELECTRO_BLAST = registry.register("electro_blast", ElectroBlastMagic::new);
      AERIAL_BLADE = registry.register("aerial_blade", AerialBladeMagic::new);
      SUMMON_HOUND_DOG = registry.register("summon_hound_dog", SummonHoundDogMagic::new);
      SUMMON_MEDIUM_ELEMENTAL = registry.register("summon_medium_elemental", SummonMediumElementalMagic::new);
      SUMMON_GREATER_ELEMENTAL = registry.register("summon_greater_elemental", SummonGreaterElementalMagic::new);
   }
}
