package com.github.manasmods.tensura.registry.blocks;

import com.github.manasmods.manascore.api.data.gen.annotation.GenerateBlockLoot;
import com.github.manasmods.manascore.api.data.gen.annotation.GenerateBlockLoot.SelfDrop;
import com.github.manasmods.manascore.api.data.gen.annotation.GenerateBlockLoot.WithLootTables;
import com.github.manasmods.tensura.block.KilnBlock;
import com.github.manasmods.tensura.block.TensuraStandingSignBlock;
import com.github.manasmods.tensura.block.TensuraWallSignBlock;
import com.github.manasmods.tensura.block.entity.CharybdisCoreBlockEntity;
import com.github.manasmods.tensura.block.entity.KilnBlockEntity;
import com.github.manasmods.tensura.block.entity.MagicEngineBlockEntity;
import com.github.manasmods.tensura.block.entity.OrcDisasterHeadBlockEntity;
import com.github.manasmods.tensura.block.entity.PrayingPathBlockEntity;
import com.github.manasmods.tensura.block.entity.TensuraSignBlockEntity;
import com.github.manasmods.tensura.block.entity.TensuraWoodTypes;
import com.mojang.datafixers.types.Type;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.level.block.SoundType;
import net.minecraft.world.level.block.entity.BlockEntityType;
import net.minecraft.world.level.block.entity.BlockEntityType.Builder;
import net.minecraft.world.level.block.state.BlockBehaviour.Properties;
import net.minecraft.world.level.material.Material;
import net.minecraftforge.eventbus.api.IEventBus;
import net.minecraftforge.registries.DeferredRegister;
import net.minecraftforge.registries.ForgeRegistries;
import net.minecraftforge.registries.RegistryObject;

public class TensuraBlockEntities {
   private static final DeferredRegister<BlockEntityType<?>> registry;
   public static final RegistryObject<BlockEntityType<TensuraSignBlockEntity>> SIGN;
   public static final RegistryObject<BlockEntityType<KilnBlockEntity>> KILN;
   public static final RegistryObject<BlockEntityType<CharybdisCoreBlockEntity>> CHARYBDIS_CORE;
   public static final RegistryObject<BlockEntityType<MagicEngineBlockEntity>> MAGIC_ENGINE;
   public static final RegistryObject<BlockEntityType<OrcDisasterHeadBlockEntity>> ORC_DISASTER_HEAD;
   public static final RegistryObject<BlockEntityType<PrayingPathBlockEntity>> PRAYING_PATH;

   public static void init(IEventBus modEventBus) {
      registry.register(modEventBus);
      TensuraBlockEntities.Blocks.registry.register(modEventBus);
   }

   static {
      registry = DeferredRegister.create(ForgeRegistries.BLOCK_ENTITY_TYPES, "tensura");
      SIGN = registry.register("sign_block_entity", () -> {
         return Builder.m_155273_(TensuraSignBlockEntity::new, new Block[]{(Block)TensuraBlockEntities.Blocks.PALM_WALL_SIGN.get(), (Block)TensuraBlockEntities.Blocks.PALM_STANDING_SIGN.get(), (Block)TensuraBlockEntities.Blocks.SAKURA_WALL_SIGN.get(), (Block)TensuraBlockEntities.Blocks.SAKURA_STANDING_SIGN.get()}).m_58966_((Type)null);
      });
      KILN = registry.register("kiln_block_entity", () -> {
         return Builder.m_155273_(KilnBlockEntity::new, new Block[]{(Block)TensuraBlockEntities.Blocks.KILN.get()}).m_58966_((Type)null);
      });
      CHARYBDIS_CORE = registry.register("charybdis_core_block_entity", () -> {
         return Builder.m_155273_(CharybdisCoreBlockEntity::new, new Block[]{(Block)TensuraBlocks.CHARYBDIS_CORE.get()}).m_58966_((Type)null);
      });
      MAGIC_ENGINE = registry.register("magic_engine_block_entity", () -> {
         return Builder.m_155273_(MagicEngineBlockEntity::new, new Block[]{(Block)TensuraBlocks.BRICK_MAGIC_ENGINE.get(), (Block)TensuraBlocks.STONE_BRICK_MAGIC_ENGINE.get()}).m_58966_((Type)null);
      });
      ORC_DISASTER_HEAD = registry.register("orc_disaster_head", () -> {
         return Builder.m_155273_(OrcDisasterHeadBlockEntity::new, new Block[]{(Block)TensuraBlocks.ORC_DISASTER_HEAD.get()}).m_58966_((Type)null);
      });
      PRAYING_PATH = registry.register("praying_path_block_entity", () -> {
         return Builder.m_155273_(PrayingPathBlockEntity::new, new Block[]{(Block)TensuraBlocks.LABYRINTH_PRAYING_PATH.get()}).m_58966_((Type)null);
      });
   }

   @GenerateBlockLoot
   public static class Blocks {
      @WithLootTables
      private static final DeferredRegister<Block> registry;
      @SelfDrop
      public static final RegistryObject<TensuraStandingSignBlock> PALM_STANDING_SIGN;
      @SelfDrop
      public static final RegistryObject<TensuraWallSignBlock> PALM_WALL_SIGN;
      @SelfDrop
      public static final RegistryObject<TensuraStandingSignBlock> SAKURA_STANDING_SIGN;
      @SelfDrop
      public static final RegistryObject<TensuraWallSignBlock> SAKURA_WALL_SIGN;
      @SelfDrop
      public static final RegistryObject<KilnBlock> KILN;

      static {
         registry = DeferredRegister.create(ForgeRegistries.BLOCKS, "tensura");
         PALM_STANDING_SIGN = registry.register("palm_sign", () -> {
            return new TensuraStandingSignBlock(Properties.m_60939_(Material.f_76320_).m_60978_(1.0F).m_60910_().m_60918_(SoundType.f_56736_), TensuraWoodTypes.PALM);
         });
         PALM_WALL_SIGN = registry.register("palm_wall_sign", () -> {
            return new TensuraWallSignBlock(Properties.m_60939_(Material.f_76320_).m_60978_(1.0F).m_60910_().m_60918_(SoundType.f_56736_), TensuraWoodTypes.PALM);
         });
         SAKURA_STANDING_SIGN = registry.register("sakura_sign", () -> {
            return new TensuraStandingSignBlock(Properties.m_60939_(Material.f_76320_).m_60978_(1.0F).m_60910_().m_60918_(SoundType.f_56736_), TensuraWoodTypes.SAKURA);
         });
         SAKURA_WALL_SIGN = registry.register("sakura_wall_sign", () -> {
            return new TensuraWallSignBlock(Properties.m_60939_(Material.f_76320_).m_60978_(1.0F).m_60910_().m_60918_(SoundType.f_56736_), TensuraWoodTypes.SAKURA);
         });
         KILN = registry.register("kiln", KilnBlock::new);
      }
   }
}
