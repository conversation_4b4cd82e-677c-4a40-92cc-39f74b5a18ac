package com.github.manasmods.tensura.item.custom;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.manascore.api.skills.capability.SkillStorage;
import com.github.manasmods.manascore.api.skills.event.RemoveSkillEvent;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.ability.magic.Magic;
import com.github.manasmods.tensura.ability.skill.resist.ResistSkill;
import com.github.manasmods.tensura.ability.skill.unique.CookSkill;
import com.github.manasmods.tensura.capability.effects.TensuraEffectsCapability;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.capability.race.ITensuraPlayerCapability;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.capability.skill.TensuraSkillCapability;
import com.github.manasmods.tensura.capability.smithing.ISmithingCapability;
import com.github.manasmods.tensura.capability.smithing.SmithingCapability;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.config.TensuraConfig;
import com.github.manasmods.tensura.item.TensuraCreativeTab;
import com.github.manasmods.tensura.menu.RaceSelectionMenu;
import com.github.manasmods.tensura.network.TensuraNetwork;
import com.github.manasmods.tensura.network.play2client.RequestTotemDisplayPacket;
import com.github.manasmods.tensura.race.Race;
import com.github.manasmods.tensura.registry.TensuraStats;
import com.github.manasmods.tensura.registry.dimensions.TensuraDimensions;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.race.TensuraRaces;
import com.github.manasmods.tensura.registry.skill.UniqueSkills;
import com.github.manasmods.tensura.util.TensuraAdvancementsHelper;
import com.github.manasmods.tensura.util.TensuraRarity;
import com.github.manasmods.tensura.world.TensuraGameRules;
import com.github.manasmods.tensura.world.savedata.UniqueSkillSaveData;
import java.awt.Color;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.function.Consumer;
import java.util.stream.Stream;
import net.minecraft.ChatFormatting;
import net.minecraft.core.BlockPos;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.network.FriendlyByteBuf;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.Style;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.stats.ServerStatsCounter;
import net.minecraft.stats.Stat;
import net.minecraft.stats.StatType;
import net.minecraft.stats.Stats;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.InteractionResultHolder;
import net.minecraft.world.SimpleMenuProvider;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.TooltipFlag;
import net.minecraft.world.item.UseAnim;
import net.minecraft.world.item.Item.Properties;
import net.minecraft.world.level.Level;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.network.NetworkHooks;
import net.minecraftforge.network.PacketDistributor;
import net.minecraftforge.registries.ForgeRegistries;
import net.minecraftforge.registries.IForgeRegistry;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

public class ResetScrollItem extends Item {
   private final ResetScrollItem.ResetType resetType;

   public ResetScrollItem(ResetScrollItem.ResetType resetType) {
      super((new Properties()).m_41491_(TensuraCreativeTab.MISCELLANEOUS).m_41497_(TensuraRarity.UNIQUE).m_41487_(1));
      this.resetType = resetType;
   }

   public void m_7373_(ItemStack pStack, @Nullable Level pLevel, List<Component> pTooltipComponents, TooltipFlag pIsAdvanced) {
      pTooltipComponents.add(Component.m_237115_(this.getResetType().tooltip).m_130940_(ChatFormatting.DARK_AQUA));
      if (this.getResetType().equals(ResetScrollItem.ResetType.RESET_SKILL)) {
         pTooltipComponents.add(Component.m_237115_("tooltip.tensura.reset_scroll.skill_warning").m_130940_(ChatFormatting.RED));
      }

   }

   public int m_8105_(ItemStack pStack) {
      return 10000;
   }

   public UseAnim m_6164_(ItemStack pStack) {
      return UseAnim.BOW;
   }

   @NotNull
   public InteractionResultHolder<ItemStack> m_7203_(Level level, Player player, InteractionHand pHand) {
      ItemStack stack = player.m_21120_(pHand);
      if (player.m_36335_().m_41519_(stack.m_41720_()) && !player.m_150110_().f_35937_) {
         return InteractionResultHolder.m_19100_(stack);
      } else {
         player.m_6672_(pHand);
         return InteractionResultHolder.m_19096_(stack);
      }
   }

   public void m_5929_(Level pLevel, LivingEntity pLivingEntity, ItemStack pStack, int pRemainingUseDuration) {
      TensuraParticleHelper.addServerParticlesAroundSelf(pLivingEntity, ParticleTypes.f_123809_);
   }

   public void m_5551_(@NotNull ItemStack pStack, @NotNull Level level, @NotNull LivingEntity entity, int pTimeLeft) {
      if (!level.m_5776_()) {
         int useTicks = this.m_8105_(pStack) - pTimeLeft;
         if (useTicks >= 10) {
            if (entity instanceof ServerPlayer) {
               ServerPlayer player = (ServerPlayer)entity;
               if (level.m_46472_().equals(TensuraDimensions.LABYRINTH) || level.m_46472_().equals(TensuraDimensions.HELL)) {
                  player.m_5661_(Component.m_237110_("tooltip.tensura.reset_scroll.not_safe", new Object[]{pStack.m_41786_()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), false);
                  return;
               }

               if (!player.m_150110_().f_35937_) {
                  if (this.isNotAllowed()) {
                     player.m_5661_(Component.m_237110_("tensura.item.scroll_not_allowed", new Object[]{pStack.m_41786_()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), false);
                     return;
                  }

                  player.m_36335_().m_41524_(pStack.m_41720_(), 2400);
                  pStack.m_41774_(1);
               }

               int penalty;
               if (isFullReset(player)) {
                  if (this.getResetType().equals(ResetScrollItem.ResetType.RESET_ALL)) {
                     TensuraPlayerCapability.increaseResetCounter(player, 1);
                  } else if ((Boolean)TensuraConfig.INSTANCE.skillsConfig.counterPenaltyNonCharScroll.get()) {
                     penalty = level.m_46469_().m_46215_(TensuraGameRules.RESET_INCOMPLETE_PENALTY);
                     if (penalty > 0) {
                        TensuraPlayerCapability.increaseResetCounter(player, penalty * -1);
                     }
                  }
               } else {
                  penalty = level.m_46469_().m_46215_(TensuraGameRules.RESET_INCOMPLETE_PENALTY);
                  if (penalty > 0) {
                     TensuraPlayerCapability.increaseResetCounter(player, penalty * -1);
                  }
               }

               this.getResetType().contextConsumer.accept(player);
               TensuraAdvancementsHelper.grant(player, TensuraAdvancementsHelper.Advancements.REWIND_TIME);
               TensuraNetwork.INSTANCE.send(PacketDistributor.PLAYER.with(() -> {
                  return player;
               }), new RequestTotemDisplayPacket(this.m_7968_()));
               entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12513_, SoundSource.PLAYERS, 1.0F, 1.0F);
               TensuraParticleHelper.addServerParticlesAroundSelf(entity, ParticleTypes.f_123767_, 1.0D);
               TensuraParticleHelper.addServerParticlesAroundSelf(entity, ParticleTypes.f_123767_, 2.0D);
               TensuraParticleHelper.addServerParticlesAroundSelf(entity, ParticleTypes.f_123747_, 1.0D);
            }

         }
      }
   }

   private boolean isNotAllowed() {
      Stream var10000 = ((List)TensuraConfig.INSTANCE.itemsConfig.allowedScrolls.get()).stream().map(ResourceLocation::new);
      IForgeRegistry var10001 = ForgeRegistries.ITEMS;
      Objects.requireNonNull(var10001);
      List<Item> list = var10000.map(var10001::getValue).filter(Objects::nonNull).toList();
      return !list.contains(this);
   }

   public static void resetFlight(Player player) {
      if ((player.m_150110_().f_35935_ || player.m_150110_().f_35936_) && !SkillUtils.canFlyLegit(player)) {
         player.m_150110_().f_35935_ = false;
         player.m_150110_().f_35936_ = false;
         player.m_6885_();
      }

   }

   public static void resetRaceFailsafe(Player player) {
      TensuraPlayerCapability.getFrom(player).ifPresent((cap) -> {
         if (cap.getRace() != null) {
            cap.setRace(player, (Race)TensuraRaces.HUMAN.get(), true);
         }
      });
   }

   public static void resetEverything(ServerPlayer player) {
      SkillStorage storage = SkillAPI.getSkillsFrom(player);
      Iterator iterator = storage.getLearnedSkills().iterator();

      while(iterator.hasNext()) {
         Object var4 = iterator.next();
         if (var4 instanceof TensuraSkillInstance) {
            TensuraSkillInstance instance = (TensuraSkillInstance)var4;
            if (!MinecraftForge.EVENT_BUS.post(new RemoveSkillEvent(instance, player))) {
               iterator.remove();
               if (player.f_19853_.m_7654_() != null) {
                  UniqueSkillSaveData saveData = UniqueSkillSaveData.get(player.f_19853_.m_7654_().m_129783_());
                  if (instance.getSkill().getRegistryName() != null && saveData.hasSkill(instance.getSkill().getRegistryName())) {
                     saveData.removeSkill(instance.getSkill().getRegistryName());
                  }
               }
            }
         }
      }

      storage.syncAll();
      CookSkill.removeCookedHP(player, (ManasSkillInstance)null);
      TensuraPlayerCapability.resetEverything(player);
      TensuraEPCapability.resetEverything(player);
      TensuraSkillCapability.resetEverything(player, true, true);
      TensuraEffectsCapability.resetEverything(player, true, true);
      MinecraftServer server = player.m_20194_();
      if (server != null) {
         ServerStatsCounter stats = server.m_6846_().m_11239_(player);
         stats.m_12850_();
         Iterator var5 = stats.m_12851_().iterator();

         while(var5.hasNext()) {
            Stat<?> stat = (Stat)var5.next();
            stats.m_6085_(player, stat, 0);
         }
      }

      SmithingCapability.getFrom(player).ifPresent(ISmithingCapability::clearSchematics);
      SmithingCapability.sync(player);
      TensuraAdvancementsHelper.revokeAllTensuraAdvancements(player);
      player.m_9158_(Level.f_46428_, (BlockPos)null, 0.0F, false, false);
      resetRaceFailsafe(player);
      if (player.m_9236_().m_46469_().m_46207_(TensuraGameRules.RIMURU_MODE)) {
         RaceSelectionMenu.reincarnateAsRimuru(player);
      } else {
         player.m_20331_(true);
         List<ResourceLocation> races = TensuraPlayerCapability.loadRaces();
         NetworkHooks.openScreen(player, new SimpleMenuProvider(RaceSelectionMenu::new, Component.m_237115_("tensura.race.selection")), (buf) -> {
            buf.writeBoolean(false);
            buf.m_236828_(races, FriendlyByteBuf::m_130085_);
         });
         if (player.m_9236_().m_46469_().m_46207_(TensuraGameRules.SKILL_BEFORE_RACE)) {
            RaceSelectionMenu.grantUniqueSkill(player);
         }
      }

      RaceSelectionMenu.grantLearningResistance(player);
      resetFlight(player);
   }

   public static void resetRace(ServerPlayer player) {
      MinecraftServer server = player.m_20194_();
      if (server != null) {
         ServerStatsCounter stats = server.m_6846_().m_11239_(player);
         stats.m_12850_();
         Iterator var3 = stats.m_12851_().iterator();

         while(var3.hasNext()) {
            Stat<?> stat = (Stat)var3.next();
            stats.m_6085_(player, stat, 0);
         }
      }

      resetRaceFailsafe(player);
      TensuraPlayerCapability.getFrom(player).ifPresent((cap) -> {
         if (cap.getRace() != null) {
            SkillStorage storage = SkillAPI.getSkillsFrom(player);
            Iterator iterator = storage.getLearnedSkills().iterator();

            label37:
            while(true) {
               TensuraSkillInstance instance;
               do {
                  Object patt12668$temp;
                  do {
                     if (!iterator.hasNext()) {
                        storage.syncAll();
                        break label37;
                     }

                     patt12668$temp = iterator.next();
                  } while(!(patt12668$temp instanceof TensuraSkillInstance));

                  instance = (TensuraSkillInstance)patt12668$temp;
               } while(!isIntrinsicSkills(player, cap, cap.getRace(), instance) && !(instance.getSkill() instanceof Magic) && !(instance.getSkill() instanceof ResistSkill));

               if (!MinecraftForge.EVENT_BUS.post(new RemoveSkillEvent(instance, player))) {
                  iterator.remove();
               }
            }
         }

         cap.clearIntrinsicSkills();
         TensuraPlayerCapability.resetEverything(player);
         if (SkillUtils.hasSkill(player, (ManasSkill)UniqueSkills.CHOSEN_ONE.get())) {
            cap.setBlessed(true);
            TensuraPlayerCapability.sync(player);
         }

      });
      CookSkill.removeCookedHP(player, (ManasSkillInstance)null);
      TensuraEPCapability.resetEverything(player);
      TensuraSkillCapability.resetEverything(player, false, true);
      TensuraEffectsCapability.resetEverything(player, true, true);
      player.m_9158_(Level.f_46428_, (BlockPos)null, 0.0F, false, false);
      List<ResourceLocation> races = TensuraPlayerCapability.loadRaces();
      NetworkHooks.openScreen(player, new SimpleMenuProvider(RaceSelectionMenu::new, Component.m_237115_("tensura.race.selection")), (buf) -> {
         buf.writeBoolean(true);
         buf.m_236828_(races, FriendlyByteBuf::m_130085_);
      });
      RaceSelectionMenu.grantLearningResistance(player);
      resetFlight(player);
   }

   public static void resetSkill(ServerPlayer player) {
      resetSkill(player, false);
   }

   public static void resetSkill(ServerPlayer player, boolean coverEP) {
      SkillStorage storage = SkillAPI.getSkillsFrom(player);
      Iterator iterator = storage.getLearnedSkills().iterator();

      while(true) {
         TensuraSkillInstance instance;
         Race race;
         ManasSkill skill;
         do {
            do {
               Object var5;
               do {
                  if (!iterator.hasNext()) {
                     storage.syncAll();
                     TensuraSkillCapability.resetEverything(player, true, false);
                     RaceSelectionMenu.randomUniqueSkill(player, coverEP);
                     RaceSelectionMenu.grantLearningResistance(player);
                     resetFlight(player);
                     TensuraSkillCapability.getFrom(player).ifPresent((cap) -> {
                        cap.setWaterPoint(0.0D);
                        cap.setLavaPoint(0.0D);
                        cap.clearAllWarp();
                     });
                     TensuraSkillCapability.sync(player);
                     return;
                  }

                  var5 = iterator.next();
               } while(!(var5 instanceof TensuraSkillInstance));

               instance = (TensuraSkillInstance)var5;
               skill = instance.getSkill();
            } while(instance.getSkill() instanceof Magic);

            race = TensuraPlayerCapability.getRace(player);
         } while(race != null && race.isIntrinsicSkill(player, skill));

         if (!TensuraPlayerCapability.getIntrinsicList(player).contains(SkillUtils.getSkillId(skill)) && !MinecraftForge.EVENT_BUS.post(new RemoveSkillEvent(instance, player))) {
            iterator.remove();
            if (player.f_19853_.m_7654_() != null) {
               UniqueSkillSaveData saveData = UniqueSkillSaveData.get(player.f_19853_.m_7654_().m_129783_());
               if (skill.getRegistryName() != null && saveData.hasSkill(skill.getRegistryName())) {
                  saveData.removeSkill(skill.getRegistryName());
               }
            }
         }
      }
   }

   public static boolean isIntrinsicSkills(Player player, @Nullable ITensuraPlayerCapability cap, @Nullable Race race, ManasSkillInstance skill) {
      if (skill.isTemporarySkill()) {
         return true;
      } else if (race == null) {
         return false;
      } else if (race.isIntrinsicSkill(player, skill.getSkill())) {
         return true;
      } else {
         return cap != null && cap.getIntrinsicSkills().contains(SkillUtils.getSkillId(skill.getSkill()));
      }
   }

   public static boolean isFullReset(ServerPlayer player) {
      Race race = TensuraPlayerCapability.getRace(player);
      if (race == null) {
         return false;
      } else if (!race.getNextEvolutions(player).isEmpty()) {
         return false;
      } else if (player.m_8951_().m_13015_(((StatType)TensuraStats.BOSS_KILLED.get()).m_12902_((EntityType)TensuraEntityTypes.ORC_DISASTER.get())) <= 0) {
         return false;
      } else if (player.m_8951_().m_13015_(((StatType)TensuraStats.BOSS_KILLED.get()).m_12902_((EntityType)TensuraEntityTypes.IFRIT.get())) <= 0) {
         return false;
      } else if (player.m_8951_().m_13015_(((StatType)TensuraStats.BOSS_KILLED.get()).m_12902_((EntityType)TensuraEntityTypes.CHARYBDIS.get())) <= 0) {
         return false;
      } else if (player.m_8951_().m_13015_(((StatType)TensuraStats.BOSS_KILLED.get()).m_12902_((EntityType)TensuraEntityTypes.ELEMENTAL_COLOSSUS.get())) <= 0) {
         return false;
      } else if (player.m_8951_().m_13015_(Stats.f_12986_.m_12902_((EntityType)TensuraEntityTypes.HINATA_SAKAGUCHI.get())) <= 0) {
         return false;
      } else {
         return TensuraPlayerCapability.isTrueHero(player) || TensuraPlayerCapability.isTrueDemonLord(player);
      }
   }

   public ResetScrollItem.ResetType getResetType() {
      return this.resetType;
   }

   public static enum ResetType {
      RESET_RACE(ResetScrollItem::resetRace, "tooltip.tensura.reset_scroll.race"),
      RESET_SKILL(ResetScrollItem::resetSkill, "tooltip.tensura.reset_scroll.skill"),
      RESET_ALL(ResetScrollItem::resetEverything, "tooltip.tensura.reset_scroll.character");

      private final Consumer<ServerPlayer> contextConsumer;
      private final String tooltip;

      private ResetType(Consumer<ServerPlayer> contextConsumer, String tooltip) {
         this.contextConsumer = contextConsumer;
         this.tooltip = tooltip;
      }

      // $FF: synthetic method
      private static ResetScrollItem.ResetType[] $values() {
         return new ResetScrollItem.ResetType[]{RESET_RACE, RESET_SKILL, RESET_ALL};
      }
   }

   public static enum ResetCounterType {
      COPPER(1, Color.WHITE, new ResourceLocation("tensura", "textures/gui/reset_medals/copper_reset_medal.png")),
      IRON(2, Color.WHITE, new ResourceLocation("tensura", "textures/gui/reset_medals/iron_reset_medal.png")),
      GOLD(3, Color.WHITE, new ResourceLocation("tensura", "textures/gui/reset_medals/gold_reset_medal.png")),
      DIAMOND(4, Color.BLACK, new ResourceLocation("tensura", "textures/gui/reset_medals/diamond_reset_medal.png")),
      MITHRIL(5, Color.WHITE, new ResourceLocation("tensura", "textures/gui/reset_medals/mithril_reset_medal.png")),
      ORICHALCUM(6, Color.WHITE, new ResourceLocation("tensura", "textures/gui/reset_medals/orichalcum_reset_medal.png")),
      MAGISTEEL(7, Color.WHITE, new ResourceLocation("tensura", "textures/gui/reset_medals/magisteel_reset_medal.png")),
      ADAMANTITE(8, Color.WHITE, new ResourceLocation("tensura", "textures/gui/reset_medals/adamantite_reset_medal.png")),
      HIHIIROKANE(9, Color.WHITE, new ResourceLocation("tensura", "textures/gui/reset_medals/hihiirokane_reset_medal.png"));

      private final int level;
      private final Color textColor;
      private final ResourceLocation textureLocation;

      public static ResetScrollItem.ResetCounterType get(int point) {
         if (point >= 64) {
            return HIHIIROKANE;
         } else if (point >= 50) {
            return ADAMANTITE;
         } else if (point >= 40) {
            return MAGISTEEL;
         } else if (point >= 30) {
            return ORICHALCUM;
         } else if (point >= 20) {
            return MITHRIL;
         } else if (point >= 10) {
            return DIAMOND;
         } else if (point >= 5) {
            return GOLD;
         } else {
            return point >= 3 ? IRON : COPPER;
         }
      }

      public int getLevel() {
         return this.level;
      }

      public Color getTextColor() {
         return this.textColor;
      }

      public ResourceLocation getTextureLocation() {
         return this.textureLocation;
      }

      private ResetCounterType(int level, Color textColor, ResourceLocation textureLocation) {
         this.level = level;
         this.textColor = textColor;
         this.textureLocation = textureLocation;
      }

      // $FF: synthetic method
      private static ResetScrollItem.ResetCounterType[] $values() {
         return new ResetScrollItem.ResetCounterType[]{COPPER, IRON, GOLD, DIAMOND, MITHRIL, ORICHALCUM, MAGISTEEL, ADAMANTITE, HIHIIROKANE};
      }
   }
}
