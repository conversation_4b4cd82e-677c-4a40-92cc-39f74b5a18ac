package com.github.manasmods.tensura.item.custom;

import com.github.manasmods.tensura.api.item.dispensing.TensuraDispenseBehaviors;
import com.github.manasmods.tensura.entity.projectile.SpearedFinArrow;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.projectile.AbstractArrow;
import net.minecraft.world.item.ArrowItem;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Item.Properties;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.DispenserBlock;

public class SpearedFinArrowItem extends ArrowItem {
   public SpearedFinArrowItem(Properties pProperties) {
      super(pProperties);
      DispenserBlock.m_52672_(this, TensuraDispenseBehaviors.DISPENSE_SPEARED_FIN_ARROW_BEHAVIOR);
   }

   public AbstractArrow m_6394_(Level pLevel, ItemStack pStack, LivingEntity pShooter) {
      return new SpearedFinArrow(pLevel, pShooter);
   }
}
