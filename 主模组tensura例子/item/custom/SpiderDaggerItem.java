package com.github.manasmods.tensura.item.custom;

import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.item.TensuraCreativeTab;
import com.github.manasmods.tensura.item.TensuraToolTiers;
import com.github.manasmods.tensura.item.templates.custom.SimpleShortSwordItem;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.items.TensuraMobDropItems;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Item.Properties;

public class SpiderDaggerItem extends SimpleShortSwordItem {
   public SpiderDaggerItem() {
      super(TensuraToolTiers.LOW_MAGISTEEL, 1, -2.0F, -1.0D, 0.1D, 0.0D, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41499_(150));
   }

   public boolean m_7579_(ItemStack pStack, LivingEntity pTarget, LivingEntity pAttacker) {
      boolean canAttack = super.m_7579_(pStack, pTarget, pAttacker);
      if (canAttack) {
         SkillHelper.checkThenAddEffectSource(pTarget, pAttacker, (MobEffect)TensuraMobEffects.FATAL_POISON.get(), 100, 0, true, true, true);
      }

      return canAttack;
   }

   public boolean m_6832_(ItemStack pToRepair, ItemStack pRepair) {
      return pRepair.m_150930_((Item)TensuraMobDropItems.SPIDER_FANG.get());
   }
}
