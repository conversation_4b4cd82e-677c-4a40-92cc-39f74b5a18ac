package com.github.manasmods.tensura.item.custom;

import com.github.manasmods.tensura.block.BlackFireBlock;
import com.github.manasmods.tensura.block.HolyFireBlock;
import java.util.List;
import java.util.function.Supplier;
import net.minecraft.ChatFormatting;
import net.minecraft.core.BlockPos;
import net.minecraft.network.chat.Component;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.util.RandomSource;
import net.minecraft.world.InteractionResult;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.TooltipFlag;
import net.minecraft.world.item.Item.Properties;
import net.minecraft.world.item.context.UseOnContext;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.BaseFireBlock;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.level.block.CampfireBlock;
import net.minecraft.world.level.block.CandleBlock;
import net.minecraft.world.level.block.CandleCakeBlock;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.block.state.properties.BlockStateProperties;
import net.minecraft.world.level.gameevent.GameEvent;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

public class TensuraFireChargeItem extends Item {
   public final Supplier<Block> fire;

   public TensuraFireChargeItem(Supplier<Block> fire, Properties pProperties) {
      super(pProperties);
      this.fire = fire;
   }

   public void m_7373_(@NotNull ItemStack pStack, @Nullable Level pLevel, @NotNull List<Component> pTooltipComponents, @NotNull TooltipFlag pIsAdvanced) {
      pTooltipComponents.add(Component.m_237115_("tooltip.tensura.creative_only").m_130940_(ChatFormatting.RED));
   }

   public InteractionResult m_6225_(UseOnContext pContext) {
      Level level = pContext.m_43725_();
      BlockPos blockpos = pContext.m_8083_();
      BlockState blockstate = level.m_8055_(blockpos);
      boolean flag = false;
      if (!CampfireBlock.m_51321_(blockstate) && !CandleBlock.m_152845_(blockstate) && !CandleCakeBlock.m_152910_(blockstate)) {
         blockpos = blockpos.m_121945_(pContext.m_43719_());
         Block fireBlock = (Block)this.fire.get();
         boolean canBePlaced = fireBlock instanceof BlackFireBlock ? BlackFireBlock.canBePlacedAt(level, blockpos) : (fireBlock instanceof HolyFireBlock ? HolyFireBlock.canBePlacedAt(level, blockpos) : BaseFireBlock.m_49255_(level, blockpos, pContext.m_8125_()));
         if (canBePlaced) {
            this.playSound(level, blockpos);
            level.m_46597_(blockpos, fireBlock.m_49966_());
            level.m_142346_(pContext.m_43723_(), GameEvent.f_157797_, blockpos);
            flag = true;
         }
      } else {
         this.playSound(level, blockpos);
         level.m_46597_(blockpos, (BlockState)blockstate.m_61124_(BlockStateProperties.f_61443_, Boolean.TRUE));
         level.m_142346_(pContext.m_43723_(), GameEvent.f_157792_, blockpos);
         flag = true;
      }

      if (flag) {
         pContext.m_43722_().m_41774_(1);
         return InteractionResult.m_19078_(level.f_46443_);
      } else {
         return InteractionResult.FAIL;
      }
   }

   private void playSound(Level pLevel, BlockPos pPos) {
      RandomSource randomsource = pLevel.m_213780_();
      pLevel.m_5594_((Player)null, pPos, SoundEvents.f_11874_, SoundSource.BLOCKS, 1.0F, (randomsource.m_188501_() - randomsource.m_188501_()) * 0.2F + 1.0F);
   }
}
