package com.github.manasmods.tensura.item.custom;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.capability.skill.TensuraSkillCapability;
import com.github.manasmods.tensura.effect.template.MobEffectHelper;
import com.github.manasmods.tensura.entity.magic.skill.SniperBulletProjectile;
import com.github.manasmods.tensura.item.TensuraCreativeTab;
import com.github.manasmods.tensura.item.TensuraToolTiers;
import com.github.manasmods.tensura.registry.skill.UniqueSkills;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import com.mojang.math.Vector3f;
import java.util.List;
import java.util.Optional;
import java.util.Random;
import net.minecraft.ChatFormatting;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.chat.Component;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.stats.Stats;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.InteractionResultHolder;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.HumanoidArm;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Rarity;
import net.minecraft.world.item.TieredItem;
import net.minecraft.world.item.TooltipFlag;
import net.minecraft.world.item.UseAnim;
import net.minecraft.world.item.Vanishable;
import net.minecraft.world.item.Item.Properties;
import net.minecraft.world.level.Level;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

public class SniperPistolItem extends TieredItem implements Vanishable {
   private final Random random = new Random();

   public SniperPistolItem() {
      super(TensuraToolTiers.PURE_MAGISTEEL, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41497_(Rarity.RARE));
   }

   public void m_7373_(ItemStack pStack, @Nullable Level pLevel, List<Component> pTooltipComponents, TooltipFlag pIsAdvanced) {
      super.m_7373_(pStack, pLevel, pTooltipComponents, pIsAdvanced);
      if (pStack.m_41784_().m_128471_("magic")) {
         pTooltipComponents.add(Component.m_237115_("tooltip.tensura.sniper_pistol.tooltip.mode_magic"));
      } else {
         pTooltipComponents.add(Component.m_237115_("tooltip.tensura.sniper_pistol.tooltip.mode_physical"));
      }

      pTooltipComponents.add(Component.m_237115_("tooltip.tensura.sniper_pistol.tooltip.mode").m_130940_(ChatFormatting.GRAY));
   }

   @NotNull
   public UseAnim m_6164_(ItemStack pStack) {
      return UseAnim.BOW;
   }

   public int m_8105_(ItemStack pStack) {
      return 10000;
   }

   @NotNull
   public InteractionResultHolder<ItemStack> m_7203_(Level level, Player player, InteractionHand hand) {
      ItemStack stack = player.m_21120_(hand);
      if (this.shouldVanish(player)) {
         stack.m_41622_(stack.m_41776_(), player, (pPlayer) -> {
            pPlayer.m_21190_(pPlayer.m_7655_());
         });
         return InteractionResultHolder.m_19100_(stack);
      } else if (player.m_6144_()) {
         CompoundTag tag = stack.m_41784_();
         tag.m_128379_("magic", !tag.m_128471_("magic"));
         player.m_21011_(hand, true);
         return InteractionResultHolder.m_19092_(stack, level.m_5776_());
      } else {
         player.m_6672_(hand);
         return InteractionResultHolder.m_19092_(stack, level.m_5776_());
      }
   }

   public void m_5551_(@NotNull ItemStack pStack, @NotNull Level level, @NotNull LivingEntity entity, int pTimeLeft) {
      if (entity instanceof Player) {
         Player player = (Player)entity;
         InteractionHand hand = entity.m_7655_();
         ItemStack var7 = entity.m_21120_(hand);
         CompoundTag tag = var7.m_41784_();
         if (!level.m_5776_() && !player.m_36335_().m_41519_(this)) {
            if (tag.m_128471_("magic")) {
               if (SkillHelper.outOfMagicule(player, 100.0D)) {
                  return;
               }
            } else if (SkillHelper.outOfAura(player, 100.0D)) {
               return;
            }

            level.m_6263_((Player)null, player.m_20185_(), player.m_20186_(), player.m_20189_(), SoundEvents.f_11705_, SoundSource.PLAYERS, 0.5F, 0.4F + this.random.nextFloat() * 0.4F + 0.8F);
            boolean left = hand == InteractionHand.OFF_HAND && player.m_5737_() == HumanoidArm.RIGHT || hand == InteractionHand.MAIN_HAND && player.m_5737_() == HumanoidArm.LEFT;
            SniperBulletProjectile bullet = new SniperBulletProjectile(level, player, !left);
            bullet.setDamage(30.0F);
            ManasSkillInstance sniper = this.getSniperSkill(player);
            bullet.setSkill(sniper);
            boolean mastered = sniper != null && sniper.isMastered(player);
            if (tag.m_128471_("magic")) {
               bullet.setDamageSource(TensuraDamageSources.shot(bullet, player).m_19389_());
               bullet.setMpCost(100.0D);
               bullet.setDamage(mastered ? 300.0F : 100.0F);
               if (!mastered) {
                  player.m_36335_().m_41524_(this, 60);
               }
            } else if (!mastered) {
               player.m_36335_().m_41524_(this, 10);
            }

            if (!this.nonMastery(bullet, player)) {
               Vector3f vector3f = new Vector3f(player.m_20252_(2.0F));
               bullet.m_6686_((double)vector3f.m_122239_(), (double)vector3f.m_122260_(), (double)vector3f.m_122269_(), 3.0F, 0.0F);
            }

            level.m_7967_(bullet);
            player.m_36246_(Stats.f_12982_.m_12902_(this));
            if (sniper != null) {
               sniper.addMasteryPoint(player);
            }
         }

      }
   }

   public boolean onEntitySwing(ItemStack itemstack, LivingEntity pEntity) {
      if (this.shouldVanish(pEntity)) {
         itemstack.m_41622_(itemstack.m_41776_(), pEntity, (entity) -> {
            entity.m_21190_(pEntity.m_7655_());
         });
         return false;
      } else {
         return super.onEntitySwing(itemstack, pEntity);
      }
   }

   private boolean shouldVanish(LivingEntity pEntity) {
      Optional<ManasSkillInstance> optional = SkillAPI.getSkillsFrom(pEntity).getSkill((ManasSkill)UniqueSkills.SNIPER.get());
      if (optional.isPresent() && ((ManasSkillInstance)optional.get()).getSkill().canInteractSkill((ManasSkillInstance)optional.get(), pEntity)) {
         return false;
      } else if (pEntity instanceof Player) {
         Player pPlayer = (Player)pEntity;
         return !pPlayer.m_7500_();
      } else {
         return true;
      }
   }

   private boolean nonMastery(SniperBulletProjectile bullet, LivingEntity owner) {
      if (owner instanceof Player) {
         Player pPlayer = (Player)owner;
         if (MobEffectHelper.noTeleportation(owner)) {
            return false;
         } else if (!TensuraSkillCapability.isSkillInSlot(owner, (ManasSkill)UniqueSkills.SNIPER.get())) {
            return false;
         } else {
            ManasSkillInstance sniper = this.getSniperSkill(pPlayer);
            if (sniper != null && sniper.isMastered(owner)) {
               Entity entity = SkillHelper.getTargetingEntity(pPlayer, 50.0D, 0.0D, false, false);
               if (entity != null && entity.m_6084_()) {
                  bullet.setTarget(entity);
                  bullet.shootFromBehind(entity, 2.0F, 0.0F);
                  return true;
               } else {
                  return false;
               }
            } else {
               return false;
            }
         }
      } else {
         return false;
      }
   }

   @Nullable
   private ManasSkillInstance getSniperSkill(Player player) {
      Optional<ManasSkillInstance> optional = SkillAPI.getSkillsFrom(player).getSkill((ManasSkill)UniqueSkills.SNIPER.get());
      return (ManasSkillInstance)optional.orElse((Object)null);
   }
}
