package com.github.manasmods.tensura.item.custom;

import com.github.manasmods.tensura.item.TensuraCreativeTab;
import com.github.manasmods.tensura.item.TensuraToolTiers;
import com.github.manasmods.tensura.item.client.MoonlightItemRenderer;
import com.github.manasmods.tensura.item.templates.custom.TensuraLongSword;
import com.github.manasmods.tensura.util.TensuraRarity;
import java.util.function.Consumer;
import net.minecraft.client.renderer.BlockEntityWithoutLevelRenderer;
import net.minecraft.world.item.Item.Properties;
import net.minecraftforge.client.extensions.common.IClientItemExtensions;
import software.bernie.geckolib3.core.IAnimatable;
import software.bernie.geckolib3.core.manager.AnimationData;
import software.bernie.geckolib3.core.manager.AnimationFactory;
import software.bernie.geckolib3.util.GeckoLibUtil;

public class MoonlightItem extends TensuraLongSword implements IAnimatable {
   private final AnimationFactory factory = GeckoLibUtil.createFactory(this);

   public MoonlightItem() {
      super(TensuraToolTiers.MITHRIL, 27, -2.2F, 1.0D, 50.0D, 0.0D, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41486_().m_41497_(TensuraRarity.UNIQUE));
   }

   public void initializeClient(Consumer<IClientItemExtensions> consumer) {
      consumer.accept(new IClientItemExtensions() {
         private final MoonlightItemRenderer renderer = new MoonlightItemRenderer();

         public BlockEntityWithoutLevelRenderer getCustomRenderer() {
            return this.renderer;
         }
      });
   }

   public void registerControllers(AnimationData data) {
   }

   public AnimationFactory getFactory() {
      return this.factory;
   }
}
