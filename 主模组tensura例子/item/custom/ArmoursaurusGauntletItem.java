package com.github.manasmods.tensura.item.custom;

import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.item.TensuraToolTiers;
import com.github.manasmods.tensura.item.client.ArmoursaurusGauntletItemRenderer;
import com.github.manasmods.tensura.util.TensuraRarity;
import com.google.common.collect.ImmutableMultimap;
import com.google.common.collect.Multimap;
import java.util.UUID;
import java.util.function.Consumer;
import net.minecraft.client.renderer.BlockEntityWithoutLevelRenderer;
import net.minecraft.core.BlockPos;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.InteractionResultHolder;
import net.minecraft.world.entity.EquipmentSlot;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.ai.attributes.AttributeModifier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.attributes.AttributeModifier.Operation;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.TieredItem;
import net.minecraft.world.item.UseAnim;
import net.minecraft.world.item.Item.Properties;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraftforge.client.extensions.common.IClientItemExtensions;
import net.minecraftforge.common.TierSortingRegistry;
import net.minecraftforge.common.ToolAction;
import net.minecraftforge.common.ToolActions;
import software.bernie.geckolib3.core.IAnimatable;
import software.bernie.geckolib3.core.PlayState;
import software.bernie.geckolib3.core.builder.AnimationBuilder;
import software.bernie.geckolib3.core.builder.ILoopType.EDefaultLoopTypes;
import software.bernie.geckolib3.core.controller.AnimationController;
import software.bernie.geckolib3.core.event.predicate.AnimationEvent;
import software.bernie.geckolib3.core.manager.AnimationData;
import software.bernie.geckolib3.core.manager.AnimationFactory;
import software.bernie.geckolib3.util.GeckoLibUtil;

public class ArmoursaurusGauntletItem extends TieredItem implements IAnimatable {
   protected static final UUID KNOCKBACK_UUID = UUID.fromString("e4b4c1e6-9f47-11ee-8c90-0242ac120002");
   private final AnimationFactory factory = GeckoLibUtil.createFactory(this);

   public ArmoursaurusGauntletItem() {
      super(TensuraToolTiers.HIGH_MAGISTEEL, (new Properties()).m_41497_(TensuraRarity.UNIQUE).m_41503_(100));
   }

   public Multimap<Attribute, AttributeModifier> getAttributeModifiers(EquipmentSlot pEquipmentSlot, ItemStack stack) {
      return pEquipmentSlot != EquipmentSlot.MAINHAND ? ImmutableMultimap.of() : ImmutableMultimap.builder().put(Attributes.f_22281_, new AttributeModifier(f_41374_, "Weapon modifier", 7.0D, Operation.ADDITION)).put(Attributes.f_22283_, new AttributeModifier(f_41375_, "Weapon modifier", -2.799999952316284D, Operation.ADDITION)).put(Attributes.f_22282_, new AttributeModifier(KNOCKBACK_UUID, "Weapon modifier", 2.0D, Operation.ADDITION)).build();
   }

   public float m_8102_(ItemStack pStack, BlockState pState) {
      return pState.m_204336_(TensuraTags.Blocks.DIGGABLE_BY_MONSTER) ? 18.0F : 9.0F;
   }

   public boolean canPerformAction(ItemStack stack, ToolAction toolAction) {
      if (ToolActions.SHOVEL_DIG.equals(toolAction)) {
         return true;
      } else if (ToolActions.AXE_DIG.equals(toolAction)) {
         return true;
      } else if (ToolActions.PICKAXE_DIG.equals(toolAction)) {
         return true;
      } else if (ToolActions.HOE_DIG.equals(toolAction)) {
         return true;
      } else {
         return ToolActions.SWORD_DIG.equals(toolAction) ? true : ToolActions.SHIELD_BLOCK.equals(toolAction);
      }
   }

   public UseAnim m_6164_(ItemStack pStack) {
      return UseAnim.BLOCK;
   }

   public int m_8105_(ItemStack pStack) {
      return 72000;
   }

   public InteractionResultHolder<ItemStack> m_7203_(Level pLevel, Player pPlayer, InteractionHand pHand) {
      pPlayer.m_6672_(pHand);
      return InteractionResultHolder.m_19096_(pPlayer.m_21120_(pHand));
   }

   public boolean isCorrectToolForDrops(ItemStack stack, BlockState state) {
      return TierSortingRegistry.isCorrectTierForDrops(this.m_43314_(), state);
   }

   public boolean m_6813_(ItemStack pStack, Level pLevel, BlockState pState, BlockPos pPos, LivingEntity pEntityLiving) {
      if (!pLevel.f_46443_ && pState.m_60800_(pLevel, pPos) != 0.0F) {
         pStack.m_41622_(1, pEntityLiving, (entity) -> {
            entity.m_21166_(EquipmentSlot.MAINHAND);
         });
      }

      return true;
   }

   public boolean m_7579_(ItemStack pStack, LivingEntity pTarget, LivingEntity pAttacker) {
      pStack.m_41622_(1, pAttacker, (entity) -> {
         entity.m_21166_(EquipmentSlot.MAINHAND);
      });
      return true;
   }

   public void initializeClient(Consumer<IClientItemExtensions> consumer) {
      consumer.accept(new IClientItemExtensions() {
         private final ArmoursaurusGauntletItemRenderer renderer = new ArmoursaurusGauntletItemRenderer();

         public BlockEntityWithoutLevelRenderer getCustomRenderer() {
            return this.renderer;
         }
      });
   }

   public void registerControllers(AnimationData data) {
      data.addAnimationController(new AnimationController(this, "controller", 3.0F, this::predicate));
   }

   public <P extends Item & IAnimatable> PlayState predicate(AnimationEvent<P> event) {
      event.getController().setAnimation((new AnimationBuilder()).addAnimation("idle", EDefaultLoopTypes.LOOP));
      return PlayState.CONTINUE;
   }

   public AnimationFactory getFactory() {
      return this.factory;
   }
}
