package com.github.manasmods.tensura.item.custom;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.entity.magic.misc.ChaosEaterProjectile;
import com.github.manasmods.tensura.item.TensuraCreativeTab;
import com.github.manasmods.tensura.item.TensuraToolTiers;
import com.github.manasmods.tensura.item.templates.custom.TwoHandedLongSword;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.enchantment.TensuraEnchantments;
import com.github.manasmods.tensura.registry.skill.UniqueSkills;
import com.github.manasmods.tensura.util.TensuraRarity;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import com.github.manasmods.tensura.world.TensuraGameRules;
import java.util.Iterator;
import java.util.List;
import net.minecraft.core.BlockPos;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.util.Mth;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.InteractionResultHolder;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.UseAnim;
import net.minecraft.world.item.Item.Properties;
import net.minecraft.world.item.enchantment.Enchantment;
import net.minecraft.world.level.Level;
import net.minecraft.world.phys.AABB;
import net.minecraft.world.phys.Vec3;
import org.jetbrains.annotations.NotNull;

public class MeatCrusherItem extends TwoHandedLongSword {
   public MeatCrusherItem() {
      super(TensuraToolTiers.HIGH_MAGISTEEL, 9, -3.0F, 0.5D, 0.0D, 0.0D, 7, -3.2F, 50.0D, 25.0D, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41486_().m_41497_(TensuraRarity.UNIQUE));
   }

   public UseAnim m_6164_(ItemStack pStack) {
      return UseAnim.SPEAR;
   }

   public int m_8105_(ItemStack pStack) {
      return 10000;
   }

   @NotNull
   public InteractionResultHolder<ItemStack> m_7203_(Level level, Player player, InteractionHand pHand) {
      ItemStack stack = player.m_21120_(pHand);
      if (player.m_36335_().m_41519_(stack.m_41720_())) {
         return InteractionResultHolder.m_19100_(stack);
      } else if (stack.getEnchantmentLevel((Enchantment)TensuraEnchantments.SOUL_EATER.get()) > 0 && SkillUtils.isSkillToggled(player, (ManasSkill)UniqueSkills.STARVED.get())) {
         this.summonChaosEater(player, 4, 1.0F);
         player.m_21011_(player.m_7655_(), true);
         if (this.isStarvedMastered(player, stack)) {
            player.m_36335_().m_41524_(stack.m_41720_(), 100);
         } else {
            player.m_36335_().m_41524_(stack.m_41720_(), 40);
         }

         level.m_6263_((Player)null, player.m_20185_(), player.m_20186_(), player.m_20189_(), SoundEvents.f_11862_, SoundSource.PLAYERS, 1.0F, 1.0F);
         return InteractionResultHolder.m_19096_(stack);
      } else {
         player.m_6672_(pHand);
         return InteractionResultHolder.m_19096_(stack);
      }
   }

   public void m_5551_(@NotNull ItemStack pStack, @NotNull Level level, @NotNull LivingEntity entity, int pTimeLeft) {
      int useTicks = this.m_8105_(pStack) - pTimeLeft;
      if (useTicks >= 7) {
         if (entity instanceof Player) {
            Player player = (Player)entity;
            player.m_36335_().m_41524_(pStack.m_41720_(), 40);
            if (!player.m_150110_().f_35937_) {
               pStack.m_41622_(10, player, (pPlayer) -> {
                  pPlayer.m_21190_(pPlayer.m_7655_());
               });
            }
         }

         entity.m_21011_(entity.m_7655_(), true);
         Vec3 target = entity.m_20182_().m_82549_(entity.m_20154_().m_82490_(2.0D));
         Vec3 source = entity.m_20182_().m_82520_(0.0D, (double)entity.m_20192_(), 0.0D);
         Vec3 sourceToTarget = target.m_82546_(source);
         Vec3 normalizes = sourceToTarget.m_82541_();
         level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_215778_, SoundSource.PLAYERS, 1.0F, 1.0F);

         for(int particleIndex = 1; particleIndex < Mth.m_14107_(sourceToTarget.m_82553_()); ++particleIndex) {
            Vec3 particlePos = source.m_82549_(normalizes.m_82490_((double)particleIndex));
            TensuraParticleHelper.spawnParticlesLikeServer(level, ParticleTypes.f_123813_, particlePos.f_82479_, particlePos.f_82480_, particlePos.f_82481_, 1, 0.0D, 0.0D, 0.0D, 0.0D, false);
            if (TensuraGameRules.canSkillGrief(level)) {
               SkillHelper.launchBlock(entity, particlePos, 2, 1, 0.3F, 0.2F, (blockState) -> {
                  return entity.m_217043_().m_188503_(2) != 1 ? false : blockState.m_204336_(TensuraTags.Blocks.EARTH_MANIPULATING);
               }, (pos) -> {
                  return !pos.equals(entity.m_20097_()) && !pos.equals(entity.m_20097_().m_7495_());
               });
            }

            AABB aabb = (new AABB(new BlockPos(particlePos.f_82479_, particlePos.f_82480_, particlePos.f_82481_))).m_82400_(3.0D);
            List<LivingEntity> list = level.m_6443_(LivingEntity.class, aabb, (entityData) -> {
               return !entityData.m_7306_(entity);
            });
            if (!list.isEmpty()) {
               Iterator var14 = list.iterator();

               while(var14.hasNext()) {
                  LivingEntity living = (LivingEntity)var14.next();
                  DamageSource damageSource = DamageSourceHelper.addSkillAndCost(DamageSource.m_19370_(entity), 200.0D, (ManasSkillInstance)null);
                  if (living.m_6469_(damageSource, (float)entity.m_21133_(Attributes.f_22281_))) {
                     SkillHelper.checkThenAddEffectSource(living, entity, (MobEffect)TensuraMobEffects.CORROSION.get(), 60, 1);
                     TensuraParticleHelper.spawnServerGroundSlamParticle(living, 10, 2.0F);
                     living.m_20184_().m_82520_(0.0D, 0.3D, 0.0D);
                  }
               }
            }
         }

      }
   }

   private boolean isStarvedMastered(Player player, ItemStack stack) {
      if (player.m_150110_().f_35937_) {
         return true;
      } else {
         stack.m_41622_(10, player, (pPlayer) -> {
            pPlayer.m_21190_(pPlayer.m_7655_());
         });
         return SkillUtils.isSkillMastered(player, (ManasSkill)UniqueSkills.STARVED.get());
      }
   }

   private void summonChaosEater(LivingEntity entity, int amount, float distance) {
      int rot = 360 / amount;

      for(int i = 0; i < amount; ++i) {
         Vec3 offset = (new Vec3(0.0D, (double)distance, 0.0D)).m_82535_(((float)(rot * i) - (float)rot / 2.0F) * 0.017453292F).m_82496_(-entity.m_146909_() * 0.017453292F).m_82524_(-entity.m_146908_() * 0.017453292F);
         Vec3 offPos = entity.m_146892_().m_82549_(offset);
         ChaosEaterProjectile chaosEater = new ChaosEaterProjectile(entity.m_9236_(), entity);
         chaosEater.m_146884_(offPos);
         chaosEater.setUpStartPos(amount, i, distance);
         LivingEntity target = SkillHelper.getTargetingEntity(entity, 20.0D, false);
         if (entity.m_6144_()) {
            List<LivingEntity> list = this.getTargetList(entity);
            if (!list.isEmpty()) {
               target = (LivingEntity)list.get(entity.m_217043_().m_188503_(list.size()));
            }
         }

         chaosEater.setTarget(target);
         chaosEater.setSpeed(1.0F);
         chaosEater.shootFromRot(entity.m_20154_());
         chaosEater.setLife(300);
         chaosEater.setDamage((float)entity.m_21133_(Attributes.f_22281_));
         chaosEater.setMobEffect(new MobEffectInstance((MobEffect)TensuraMobEffects.CORROSION.get(), 200, 1, false, false, false));
         chaosEater.setEffectRange(1.5F);
         chaosEater.setMpCost(100.0D);
         chaosEater.setSkill(SkillUtils.getSkillOrNull(entity, (ManasSkill)UniqueSkills.STARVED.get()));
         entity.m_9236_().m_7967_(chaosEater);
      }

   }

   private List<LivingEntity> getTargetList(LivingEntity owner) {
      AABB box = owner.m_20191_().m_82400_(20.0D);
      return owner.m_9236_().m_6443_(LivingEntity.class, box, (entity) -> {
         return this.shouldAttack(entity, owner);
      });
   }

   protected boolean shouldAttack(LivingEntity entity, LivingEntity owner) {
      if (!entity.m_7307_(owner) && !owner.m_7307_(entity)) {
         boolean var10000;
         if (entity instanceof Player) {
            Player player = (Player)entity;
            if (player.m_7500_() || player.m_5833_()) {
               var10000 = false;
               return var10000;
            }
         }

         var10000 = true;
         return var10000;
      } else {
         return false;
      }
   }
}
