package com.github.manasmods.tensura.item;

import com.github.manasmods.tensura.registry.items.TensuraMaterialItems;
import com.github.manasmods.tensura.registry.items.TensuraMobDropItems;
import java.util.function.Supplier;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.util.LazyLoadedValue;
import net.minecraft.world.entity.EquipmentSlot;
import net.minecraft.world.item.ArmorMaterial;
import net.minecraft.world.item.Items;
import net.minecraft.world.item.crafting.Ingredient;
import net.minecraft.world.level.ItemLike;

public enum TensuraArmourMaterials implements ArmorMaterial {
   SILVER("silver", 15, new int[]{2, 5, 6, 2}, 20, SoundEvents.f_11677_, 0.0F, 0.0F, () -> {
      return Ingredient.m_43929_(new ItemLike[]{(ItemLike)TensuraMaterialItems.SILVER_INGOT.get()});
   }),
   LOW_MAGISTEEL("low_magisteel", 35, new int[]{3, 6, 8, 3}, 25, SoundEvents.f_11677_, 2.5F, 0.1F, () -> {
      return Ingredient.m_43929_(new ItemLike[]{(ItemLike)TensuraMaterialItems.LOW_MAGISTEEL_INGOT.get()});
   }),
   HIGH_MAGISTEEL("high_magisteel", 40, new int[]{4, 7, 9, 4}, 30, SoundEvents.f_11677_, 4.0F, 0.2F, () -> {
      return Ingredient.m_43929_(new ItemLike[]{(ItemLike)TensuraMaterialItems.HIGH_MAGISTEEL_INGOT.get()});
   }),
   MITHRIL("mithril", 45, new int[]{6, 9, 11, 6}, 35, SoundEvents.f_11677_, 5.0F, 0.3F, () -> {
      return Ingredient.m_43929_(new ItemLike[]{(ItemLike)TensuraMaterialItems.MITHRIL_INGOT.get()});
   }),
   ORICHALCUM("orichalcum", 50, new int[]{6, 9, 11, 6}, 40, SoundEvents.f_11677_, 6.0F, 0.4F, () -> {
      return Ingredient.m_43929_(new ItemLike[]{(ItemLike)TensuraMaterialItems.ORICHALCUM_INGOT.get()});
   }),
   PURE_MAGISTEEL("pure_magisteel", 60, new int[]{8, 11, 13, 8}, 40, SoundEvents.f_11677_, 7.0F, 0.6F, () -> {
      return Ingredient.m_43929_(new ItemLike[]{(ItemLike)TensuraMaterialItems.PURE_MAGISTEEL_INGOT.get()});
   }),
   ADAMANTITE("adamantite", 70, new int[]{10, 13, 15, 10}, 45, SoundEvents.f_11677_, 8.0F, 0.8F, () -> {
      return Ingredient.m_43929_(new ItemLike[]{(ItemLike)TensuraMaterialItems.ADAMANTITE_INGOT.get()});
   }),
   HIHIIROKANE("hihiirokane", 80, new int[]{12, 15, 17, 12}, 50, SoundEvents.f_11677_, 10.0F, 1.0F, () -> {
      return Ingredient.m_43929_(new ItemLike[]{(ItemLike)TensuraMaterialItems.HIHIIROKANE_INGOT.get()});
   }),
   CHARYBDIS_SCALEMAIL("charybdis_scalemail", 65, new int[]{9, 12, 14, 9}, 38, SoundEvents.f_11672_, 7.5F, 0.7F, () -> {
      return Ingredient.m_43929_(new ItemLike[]{(ItemLike)TensuraMobDropItems.CHARYBDIS_SCALE.get()});
   }),
   ARMOURSAURUS("armoursaurus", 40, new int[]{5, 8, 10, 5}, 20, SoundEvents.f_11673_, 4.0F, 0.5F, () -> {
      return Ingredient.m_43929_(new ItemLike[]{Items.f_41852_});
   }),
   ARMOURSAURUS_SCALEMAIL("armoursaurus_scalemail", 38, new int[]{4, 7, 9, 4}, 20, SoundEvents.f_11672_, 3.0F, 0.4F, () -> {
      return Ingredient.m_43929_(new ItemLike[]{(ItemLike)TensuraMobDropItems.ARMOURSAURUS_SCALE.get(), (ItemLike)TensuraMobDropItems.ARMOURSAURUS_SHELL.get()});
   }),
   SERPENT_SCALEMAIL("serpent_scalemail", 37, new int[]{3, 6, 8, 3}, 15, SoundEvents.f_11672_, 2.0F, 0.3F, () -> {
      return Ingredient.m_43929_(new ItemLike[]{(ItemLike)TensuraMobDropItems.SERPENT_SCALE.get()});
   }),
   KNIGHT_SPIDER_CARAPACE("knight_spider_carapace", 37, new int[]{2, 5, 7, 2}, 15, SoundEvents.f_11677_, 2.0F, 0.2F, () -> {
      return Ingredient.m_43929_(new ItemLike[]{(ItemLike)TensuraMobDropItems.KNIGHT_SPIDER_CARAPACE.get()});
   }),
   ANT_CARAPACE("ant_carapace", 34, new int[]{2, 5, 7, 2}, 15, SoundEvents.f_11677_, 2.0F, 0.1F, () -> {
      return Ingredient.m_43929_(new ItemLike[]{(ItemLike)TensuraMobDropItems.GIANT_ANT_CARAPACE.get()});
   }),
   MONSTER_LEATHER_D("monster_leather_d", 11, new int[]{1, 3, 5, 2}, 15, SoundEvents.f_11678_, 0.5F, 0.1F, () -> {
      return Ingredient.m_43929_(new ItemLike[]{(ItemLike)TensuraMobDropItems.MONSTER_LEATHER_D.get()});
   }),
   MONSTER_LEATHER_C("monster_leather_c", 33, new int[]{2, 5, 6, 2}, 18, SoundEvents.f_11678_, 1.0F, 0.2F, () -> {
      return Ingredient.m_43929_(new ItemLike[]{(ItemLike)TensuraMobDropItems.MONSTER_LEATHER_C.get()});
   }),
   MONSTER_LEATHER_B("monster_leather_b", 35, new int[]{3, 6, 8, 3}, 20, SoundEvents.f_11678_, 2.0F, 0.3F, () -> {
      return Ingredient.m_43929_(new ItemLike[]{(ItemLike)TensuraMobDropItems.MONSTER_LEATHER_B.get()});
   }),
   MONSTER_LEATHER_A("monster_leather_a", 36, new int[]{4, 7, 9, 4}, 25, SoundEvents.f_11678_, 4.0F, 0.4F, () -> {
      return Ingredient.m_43929_(new ItemLike[]{(ItemLike)TensuraMobDropItems.MONSTER_LEATHER_A.get()});
   }),
   MONSTER_LEATHER_SPECIAL_A("monster_leather_special_a", 75, new int[]{9, 12, 14, 9}, 45, SoundEvents.f_11678_, 7.5F, 0.8F, () -> {
      return Ingredient.m_43929_(new ItemLike[]{(ItemLike)TensuraMobDropItems.MONSTER_LEATHER_SPECIAL_A.get()});
   }),
   WINGED_SHOES("winged_shoes", 33, new int[]{3, 0, 0, 0}, 20, SoundEvents.f_11678_, 1.0F, 0.0F, () -> {
      return Ingredient.m_43929_(new ItemLike[]{(ItemLike)TensuraMobDropItems.MONSTER_LEATHER_C.get(), (ItemLike)TensuraMobDropItems.DRAGON_PEACOCK_FEATHER.get()});
   }),
   ANTI_MAGIC_MASK("anti_magic_mask", 60, new int[]{1, 1, 1, 8}, 35, SoundEvents.f_11677_, 6.0F, 0.5F, () -> {
      return Ingredient.m_43929_(new ItemLike[]{Items.f_42461_});
   }),
   DARK("dark", 35, new int[]{4, 7, 9, 4}, 20, SoundEvents.f_11678_, 4.0F, 0.0F, () -> {
      return Ingredient.m_43929_(new ItemLike[]{(ItemLike)TensuraMobDropItems.MONSTER_LEATHER_B.get(), (ItemLike)TensuraMobDropItems.MONSTER_LEATHER_A.get(), (ItemLike)TensuraMobDropItems.MONSTER_LEATHER_SPECIAL_A.get()});
   }),
   CRAZY_PIERROT_MASK("crazy_pierrot_mask", 15, new int[]{1, 1, 1, 4}, 10, SoundEvents.f_11677_, 0.0F, 0.0F, () -> {
      return Ingredient.m_43929_(new ItemLike[]{Items.f_42461_});
   }),
   ANGRY_PIERROT_MASK("angry_pierrot_mask", 15, new int[]{1, 1, 1, 4}, 10, SoundEvents.f_11677_, 0.0F, 0.0F, () -> {
      return Ingredient.m_43929_(new ItemLike[]{Items.f_42461_});
   }),
   WONDER_PIERROT_MASK("wonder_pierrot_mask", 15, new int[]{1, 1, 1, 4}, 10, SoundEvents.f_11677_, 0.0F, 0.0F, () -> {
      return Ingredient.m_43929_(new ItemLike[]{Items.f_42461_});
   }),
   TEARY_PIERROT_MASK("teary_pierrot_mask", 15, new int[]{1, 1, 1, 4}, 10, SoundEvents.f_11677_, 0.0F, 0.0F, () -> {
      return Ingredient.m_43929_(new ItemLike[]{Items.f_42461_});
   });

   private static final int[] HEALTH_PER_SLOT = new int[]{13, 15, 16, 11};
   private final String name;
   private final int durabilityMultiplier;
   private final int[] slotProtections;
   private final int enchantmentValue;
   private final SoundEvent sound;
   private final float toughness;
   private final float knockbackResistance;
   private final LazyLoadedValue<Ingredient> repairIngredient;

   private TensuraArmourMaterials(String pName, int pDurabilityMultiplier, int[] pSlotProtections, int pEnchantmentValue, SoundEvent pSound, float pToughness, float pKnockbackResistance, Supplier<Ingredient> pRepairIngredient) {
      this.name = pName;
      this.durabilityMultiplier = pDurabilityMultiplier;
      this.slotProtections = pSlotProtections;
      this.enchantmentValue = pEnchantmentValue;
      this.sound = pSound;
      this.toughness = pToughness;
      this.knockbackResistance = pKnockbackResistance;
      this.repairIngredient = new LazyLoadedValue(pRepairIngredient);
   }

   public int m_7366_(EquipmentSlot pSlot) {
      return HEALTH_PER_SLOT[pSlot.m_20749_()] * this.durabilityMultiplier;
   }

   public int m_7365_(EquipmentSlot pSlot) {
      return this.slotProtections[pSlot.m_20749_()];
   }

   public int m_6646_() {
      return this.enchantmentValue;
   }

   public SoundEvent m_7344_() {
      return this.sound;
   }

   public Ingredient m_6230_() {
      return (Ingredient)this.repairIngredient.m_13971_();
   }

   public String m_6082_() {
      return "tensura:" + this.name;
   }

   public float m_6651_() {
      return this.toughness;
   }

   public float m_6649_() {
      return this.knockbackResistance;
   }

   // $FF: synthetic method
   private static TensuraArmourMaterials[] $values() {
      return new TensuraArmourMaterials[]{SILVER, LOW_MAGISTEEL, HIGH_MAGISTEEL, MITHRIL, ORICHALCUM, PURE_MAGISTEEL, ADAMANTITE, HIHIIROKANE, CHARYBDIS_SCALEMAIL, ARMOURSAURUS, ARMOURSAURUS_SCALEMAIL, SERPENT_SCALEMAIL, KNIGHT_SPIDER_CARAPACE, ANT_CARAPACE, MONSTER_LEATHER_D, MONSTER_LEATHER_C, MONSTER_LEATHER_B, MONSTER_LEATHER_A, MONSTER_LEATHER_SPECIAL_A, WINGED_SHOES, ANTI_MAGIC_MASK, DARK, CRAZY_PIERROT_MASK, ANGRY_PIERROT_MASK, WONDER_PIERROT_MASK, TEARY_PIERROT_MASK};
   }
}
