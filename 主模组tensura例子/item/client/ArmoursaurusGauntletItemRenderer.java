package com.github.manasmods.tensura.item.client;

import com.github.manasmods.tensura.item.custom.ArmoursaurusGauntletItem;
import com.mojang.blaze3d.vertex.PoseStack;
import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.client.renderer.block.model.ItemTransforms.TransformType;
import net.minecraft.world.item.ItemStack;
import software.bernie.geckolib3.renderers.geo.GeoItemRenderer;

public class ArmoursaurusGauntletItemRenderer extends GeoItemRenderer<ArmoursaurusGauntletItem> {
   public ArmoursaurusGauntletItemRenderer() {
      super(new ArmoursaurusGauntletItemModel());
   }

   public void m_108829_(ItemStack itemStack, TransformType transformType, PoseStack matrixStack, MultiBufferSource bufferIn, int combinedLightIn, int combinedOverlayIn) {
      if (!this.modelProvider.getAnimationProcessor().getModelRendererList().isEmpty()) {
         if (transformType != TransformType.THIRD_PERSON_LEFT_HAND && transformType != TransformType.FIRST_PERSON_LEFT_HAND) {
            this.modelProvider.getBone("all").setHidden(false);
            this.modelProvider.getBone("allFlip").setHidden(true);
         } else {
            this.modelProvider.getBone("all").setHidden(true);
            this.modelProvider.getBone("allFlip").setHidden(false);
         }
      }

      super.m_108829_(itemStack, transformType, matrixStack, bufferIn, combinedLightIn, combinedOverlayIn);
   }
}
