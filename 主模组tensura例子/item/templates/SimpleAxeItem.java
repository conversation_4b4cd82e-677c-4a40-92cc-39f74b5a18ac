package com.github.manasmods.tensura.item.templates;

import com.github.manasmods.tensura.item.TensuraCreativeTab;
import net.minecraft.world.item.AxeItem;
import net.minecraft.world.item.Tier;
import net.minecraft.world.item.Item.Properties;

public class SimpleAxeItem extends AxeItem {
   public SimpleAxeItem(Tier pTier, float pAttackDamageModifier, float pAttackSpeedModifier, Properties properties) {
      super(pTier, pAttackDamageModifier, pAttackSpeedModifier, properties);
   }

   public SimpleAxeItem(Tier pTier, SimpleAxeItem.AxeModifier axeModifier) {
      this(pTier, axeModifier.getAttackDamageModifier(), axeModifier.getAttackSpeedModifier(), axeModifier.getProperties());
   }

   public static enum AxeModifier {
      WOOD(6.0F, -3.2F, (new Properties()).m_41491_(TensuraCreativeTab.GEAR)),
      FLINT(6.0F, -3.2F, (new Properties()).m_41491_(TensuraCreativeTab.GEAR)),
      STONE(7.0F, -3.2F, (new Properties()).m_41491_(TensuraCreativeTab.GEAR)),
      IRON(6.0F, -3.1F, (new Properties()).m_41491_(TensuraCreativeTab.GEAR)),
      SILVER(6.0F, -3.1F, (new Properties()).m_41491_(TensuraCreativeTab.GEAR)),
      GOLD(6.0F, -3.0F, (new Properties()).m_41491_(TensuraCreativeTab.GEAR)),
      DIAMOND(5.0F, -3.0F, (new Properties()).m_41491_(TensuraCreativeTab.GEAR)),
      LOW_MAGISTEEL(5.0F, -3.0F, (new Properties()).m_41491_(TensuraCreativeTab.GEAR)),
      NETHERITE(5.0F, -3.0F, (new Properties()).m_41491_(TensuraCreativeTab.GEAR)),
      HIGH_MAGISTEEL(5.0F, -2.9F, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41486_()),
      MITHRIL(5.0F, -2.9F, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41486_()),
      ORICHALCUM(5.0F, -2.9F, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41486_()),
      PURE_MAGISTEEL(5.0F, -2.8F, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41486_()),
      ADAMANTITE(5.0F, -2.8F, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41486_()),
      HIHIIROKANE(5.0F, -2.7F, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41486_());

      private final float attackDamageModifier;
      private final float attackSpeedModifier;
      private final Properties properties;

      private AxeModifier(float attackDamageModifier, float attackSpeedModifier, Properties properties) {
         this.attackDamageModifier = attackDamageModifier;
         this.attackSpeedModifier = attackSpeedModifier;
         this.properties = properties;
      }

      public float getAttackDamageModifier() {
         return this.attackDamageModifier;
      }

      public float getAttackSpeedModifier() {
         return this.attackSpeedModifier;
      }

      public Properties getProperties() {
         return this.properties;
      }

      // $FF: synthetic method
      private static SimpleAxeItem.AxeModifier[] $values() {
         return new SimpleAxeItem.AxeModifier[]{WOOD, FLINT, STONE, IRON, SILVER, GOLD, DIAMOND, LOW_MAGISTEEL, NETHERITE, HIGH_MAGISTEEL, MITHRIL, ORICHALCUM, PURE_MAGISTEEL, ADAMANTITE, HIHIIROKANE};
      }
   }
}
