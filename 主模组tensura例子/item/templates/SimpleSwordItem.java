package com.github.manasmods.tensura.item.templates;

import com.github.manasmods.tensura.item.TensuraCreativeTab;
import net.minecraft.world.item.SwordItem;
import net.minecraft.world.item.Tier;
import net.minecraft.world.item.Item.Properties;

public class SimpleSwordItem extends SwordItem {
   public SimpleSwordItem(Tier pTier, int pAttackDamageModifier, float pAttackSpeedModifier, Properties properties) {
      super(pTier, pAttackDamageModifier, pAttackSpeedModifier, properties);
   }

   public SimpleSwordItem(Tier pTier, SimpleSwordItem.SwordModifier swordModifier) {
      this(pTier, swordModifier.getAttackDamageModifier(), swordModifier.getAttackSpeedModifier(), swordModifier.getProperties());
   }

   public static enum SwordModifier {
      NORMAL(3, -2.4F, (new Properties()).m_41491_(TensuraCreativeTab.GEAR)),
      FIRE_RESISTED(3, -2.4F, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41486_());

      private final int attackDamageModifier;
      private final float attackSpeedModifier;
      private final Properties properties;

      private SwordModifier(int attackDamageModifier, float attackSpeedModifier, Properties properties) {
         this.attackDamageModifier = attackDamageModifier;
         this.attackSpeedModifier = attackSpeedModifier;
         this.properties = properties;
      }

      public int getAttackDamageModifier() {
         return this.attackDamageModifier;
      }

      public float getAttackSpeedModifier() {
         return this.attackSpeedModifier;
      }

      public Properties getProperties() {
         return this.properties;
      }

      // $FF: synthetic method
      private static SimpleSwordItem.SwordModifier[] $values() {
         return new SimpleSwordItem.SwordModifier[]{NORMAL, FIRE_RESISTED};
      }
   }
}
