package com.github.manasmods.tensura.item.templates.custom;

import com.github.manasmods.manascore.attribute.ManasCoreAttributes;
import com.google.common.collect.ImmutableMultimap;
import com.google.common.collect.Multimap;
import java.util.UUID;
import net.minecraft.world.entity.EquipmentSlot;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.ai.attributes.AttributeModifier;
import net.minecraft.world.entity.ai.attributes.AttributeModifier.Operation;
import net.minecraft.world.item.SwordItem;
import net.minecraft.world.item.Tier;
import net.minecraft.world.item.Item.Properties;
import org.jetbrains.annotations.NotNull;

public class TensuraSword extends SwordItem {
   protected static final UUID BASE_CRIT_CHANCE_UUID = UUID.fromString("6595a9a4-a3b0-11ed-a8fc-0242ac120002");
   protected static final UUID BASE_CRIT_MULTIPLIER_UUID = UUID.fromString("e731da6e-a3b0-11ed-a8fc-0242ac120002");
   protected static final UUID BASE_SWEEP_CHANCE_UUID = UUID.fromString("a20fa56e-3bf0-4ab7-8042-2eec78563b80");
   private final double critChance;
   private final double critDamageMultiplier;
   private double sweepChance;

   public TensuraSword(Tier pTier, int pAttackDamageModifier, float pAttackSpeedModifier, Properties pProperties) {
      this(pTier, pAttackDamageModifier, pAttackSpeedModifier, 0.0D, 0.0D, 0.0D, pProperties);
   }

   public TensuraSword(Tier pTier, int pAttackDamageModifier, float pAttackSpeedModifier, double critChance, double critDamageMultiplier, Properties pProperties) {
      this(pTier, pAttackDamageModifier, pAttackSpeedModifier, critChance, critDamageMultiplier, 0.0D, pProperties);
   }

   public TensuraSword(Tier pTier, int pAttackDamageModifier, float pAttackSpeedModifier, double critChance, double critDamageMultiplier, double sweepChance, Properties pProperties) {
      super(pTier, pAttackDamageModifier, pAttackSpeedModifier, pProperties);
      this.critChance = critChance;
      this.critDamageMultiplier = critDamageMultiplier;
      this.sweepChance = sweepChance;
   }

   @NotNull
   public Multimap<Attribute, AttributeModifier> m_7167_(@NotNull EquipmentSlot pEquipmentSlot) {
      return pEquipmentSlot != EquipmentSlot.MAINHAND ? ImmutableMultimap.of() : ImmutableMultimap.builder().putAll(super.m_7167_(pEquipmentSlot)).put((Attribute)ManasCoreAttributes.CRIT_CHANCE.get(), new AttributeModifier(BASE_CRIT_CHANCE_UUID, "Weapon modifier", this.critChance, Operation.ADDITION)).put((Attribute)ManasCoreAttributes.CRIT_MULTIPLIER.get(), new AttributeModifier(BASE_CRIT_MULTIPLIER_UUID, "Weapon modifier", this.critDamageMultiplier, Operation.ADDITION)).put((Attribute)ManasCoreAttributes.SWEEP_CHANCE.get(), new AttributeModifier(BASE_SWEEP_CHANCE_UUID, "Weapon modifier", this.sweepChance, Operation.ADDITION)).build();
   }

   public double getCritChance() {
      return this.critChance;
   }

   public double getCritDamageMultiplier() {
      return this.critDamageMultiplier;
   }

   public double getSweepChance() {
      return this.sweepChance;
   }
}
