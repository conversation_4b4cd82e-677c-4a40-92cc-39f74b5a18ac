package com.github.manasmods.tensura.item.templates.custom;

import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Tier;
import net.minecraft.world.item.Item.Properties;
import net.minecraft.world.item.enchantment.Enchantment;
import net.minecraft.world.item.enchantment.Enchantments;
import net.minecraftforge.common.ToolAction;
import net.minecraftforge.common.ToolActions;
import org.jetbrains.annotations.NotNull;

public class SimpleKodachiItem extends TensuraLongSword {
   public SimpleKodachiItem(Tier pTier, Properties properties) {
      super(pTier, 1, -2.0F, -0.75D, 20.0D, 0.5D, properties);
   }

   public boolean canApplyAtEnchantingTable(ItemStack stack, Enchantment enchantment) {
      return enchantment.equals(Enchantments.f_44983_) ? false : enchantment.f_44672_.m_7454_(stack.m_41720_());
   }

   public boolean canPerformAction(@NotNull ItemStack stack, @NotNull ToolAction toolAction) {
      if (ToolActions.SWORD_SWEEP.equals(toolAction)) {
         return false;
      } else {
         return ToolActions.SHEARS_CARVE.equals(toolAction) ? true : ToolActions.DEFAULT_SWORD_ACTIONS.contains(toolAction);
      }
   }
}
