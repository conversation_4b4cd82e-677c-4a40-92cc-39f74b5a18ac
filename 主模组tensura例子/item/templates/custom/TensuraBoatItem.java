package com.github.manasmods.tensura.item.templates.custom;

import com.github.manasmods.tensura.entity.template.TensuraBoatEntity;
import java.util.Iterator;
import java.util.List;
import java.util.function.Predicate;
import net.minecraft.stats.Stats;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.InteractionResultHolder;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntitySelector;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Item.Properties;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.ClipContext.Fluid;
import net.minecraft.world.phys.AABB;
import net.minecraft.world.phys.HitResult;
import net.minecraft.world.phys.Vec3;
import net.minecraft.world.phys.HitResult.Type;

public class TensuraBoatItem extends Item {
   private static final Predicate<Entity> ENTITY_PREDICATE;
   private final TensuraBoatEntity.Type type;

   public TensuraBoatItem(Properties properties, TensuraBoatEntity.Type typeIn) {
      super(properties);
      this.type = typeIn;
   }

   public InteractionResultHolder<ItemStack> m_7203_(Level worldIn, Player playerIn, InteractionHand handIn) {
      ItemStack itemstack = playerIn.m_21120_(handIn);
      HitResult rayTraceResult = m_41435_(worldIn, playerIn, Fluid.ANY);
      if (rayTraceResult.m_6662_() == Type.MISS) {
         return InteractionResultHolder.m_19098_(itemstack);
      } else {
         Vec3 vec3d = playerIn.m_20299_(1.0F);
         double d0 = 5.0D;
         List<Entity> list = worldIn.m_6249_(playerIn, playerIn.m_20191_().m_82369_(vec3d.m_82490_(d0)).m_82400_(1.0D), ENTITY_PREDICATE);
         if (!list.isEmpty()) {
            Vec3 vec3d1 = playerIn.m_20299_(1.0F);
            Iterator var11 = list.iterator();

            while(var11.hasNext()) {
               Entity entity = (Entity)var11.next();
               AABB axisAlignedBb = entity.m_20191_().m_82400_((double)entity.m_6143_());
               if (axisAlignedBb.m_82390_(vec3d1)) {
                  return InteractionResultHolder.m_19098_(itemstack);
               }
            }
         }

         if (rayTraceResult.m_6662_() == Type.BLOCK) {
            TensuraBoatEntity boatEntity = new TensuraBoatEntity(worldIn, rayTraceResult.m_82450_().f_82479_, rayTraceResult.m_82450_().f_82480_, rayTraceResult.m_82450_().f_82481_);
            boatEntity.setBoatType(this.type);
            boatEntity.m_146922_(playerIn.m_146908_());
            if (!worldIn.m_45756_(boatEntity, boatEntity.m_20191_().m_82400_(-0.1D))) {
               return InteractionResultHolder.m_19100_(itemstack);
            } else {
               if (!worldIn.f_46443_) {
                  worldIn.m_7967_(boatEntity);
               }

               if (!playerIn.m_150110_().f_35937_) {
                  itemstack.m_41774_(1);
               }

               playerIn.m_36246_(Stats.f_12982_.m_12902_(this));
               return InteractionResultHolder.m_19090_(itemstack);
            }
         } else {
            return InteractionResultHolder.m_19098_(itemstack);
         }
      }
   }

   static {
      ENTITY_PREDICATE = EntitySelector.f_20408_.and(Entity::m_5829_);
   }
}
