package com.github.manasmods.tensura.item.templates.custom;

import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.registry.blocks.TensuraBlocks;
import com.github.manasmods.tensura.registry.items.TensuraMaterialItems;
import java.util.Objects;
import net.minecraft.core.BlockPos;
import net.minecraft.world.InteractionResult;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.item.ItemEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.DiggerItem;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Tier;
import net.minecraft.world.item.Item.Properties;
import net.minecraft.world.item.context.UseOnContext;
import net.minecraft.world.level.ItemLike;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.block.state.BlockState;

public class SimpleSickleItem extends DiggerItem {
   public SimpleSickleItem(Tier pTier, int pAttackDamageModifier, float pAttackSpeedModifier, Properties pProperties) {
      super((float)pAttackDamageModifier, pAttackSpeedModifier, pTier, TensuraTags.Blocks.MINEABLE_WITH_SICKLE, pProperties);
   }

   public SimpleSickleItem(Tier pTier, Properties properties) {
      this(pTier, 2, -1.5F, properties);
   }

   public InteractionResult m_6225_(UseOnContext pContext) {
      if (!pContext.m_43725_().m_5776_()) {
         Level level = pContext.m_43725_();
         BlockPos positionClicked = pContext.m_8083_();
         BlockState blockClicked = level.m_8055_(positionClicked);
         if (blockClicked.m_204336_(TensuraTags.Blocks.MINEABLE_WITH_SICKLE)) {
            ItemEntity entityItem = new ItemEntity(level, (double)positionClicked.m_123341_(), (double)positionClicked.m_123342_(), (double)positionClicked.m_123343_(), new ItemStack((ItemLike)TensuraMaterialItems.THATCH.get(), this.drop(blockClicked.m_60734_())));
            level.m_46961_(positionClicked, false);
            level.m_7967_(entityItem);
            pContext.m_43722_().m_41622_(1, (Player)Objects.requireNonNull(pContext.m_43723_()), (p) -> {
               p.m_21190_(pContext.m_43724_());
            });
         }
      }

      return InteractionResult.SUCCESS;
   }

   public boolean m_6813_(ItemStack pStack, Level pLevel, BlockState pState, BlockPos pPos, LivingEntity pEntityLiving) {
      if (!pLevel.m_5776_()) {
         if (!pState.m_204336_(TensuraTags.Blocks.MINEABLE_WITH_SICKLE)) {
            return super.m_6813_(pStack, pLevel, pState, pPos, pEntityLiving);
         }

         ItemEntity entityItem = new ItemEntity(pLevel, (double)pPos.m_123341_(), (double)pPos.m_123342_(), (double)pPos.m_123343_(), new ItemStack((ItemLike)TensuraMaterialItems.THATCH.get(), this.drop(pState.m_60734_())));
         pLevel.m_46961_(pPos, false);
         pLevel.m_7967_(entityItem);
         if (pEntityLiving instanceof Player) {
            Player player = (Player)pEntityLiving;
            pStack.m_41622_(1, (Player)Objects.requireNonNull(player), (p) -> {
               p.m_21190_(player.m_7655_());
            });
         }
      }

      return true;
   }

   private int drop(Block block) {
      if (block != Blocks.f_50034_ && block != Blocks.f_50037_) {
         if (block != Blocks.f_50359_ && block != Blocks.f_50038_) {
            if (block == TensuraBlocks.THATCH_BLOCK.get()) {
               return 9;
            } else if (block == TensuraBlocks.THATCH_SLAB.get()) {
               return 3;
            } else {
               return block == TensuraBlocks.THATCH_STAIRS.get() ? 6 : 1;
            }
         } else {
            return 2;
         }
      } else {
         return 1;
      }
   }
}
