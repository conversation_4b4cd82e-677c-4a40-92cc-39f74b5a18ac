package com.github.manasmods.tensura.item.templates.custom;

import com.github.manasmods.tensura.item.templates.SimpleBowItem;
import com.google.common.collect.ImmutableMultimap;
import com.google.common.collect.Multimap;
import java.util.UUID;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.InteractionResultHolder;
import net.minecraft.world.entity.EquipmentSlot;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.ai.attributes.AttributeModifier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.attributes.AttributeModifier.Operation;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Item.Properties;
import net.minecraft.world.item.enchantment.Enchantment;
import net.minecraft.world.item.enchantment.Enchantments;
import net.minecraft.world.level.Level;
import net.minecraftforge.event.ForgeEventFactory;

public class WarBowItem extends SimpleBowItem {
   protected static final UUID TOTAL_MOVEMENT_SPEED_MODIFIER = UUID.fromString("b72c70e6-aa35-11ed-afa1-0242ac120002");

   public WarBowItem(Properties pProperties, int pRange, float pChargeTicks, double pBaseDamage, float pInaccuracy) {
      super(pProperties, pRange, pChargeTicks, pBaseDamage, pInaccuracy);
   }

   public Multimap<Attribute, AttributeModifier> getAttributeModifiers(EquipmentSlot slot, ItemStack pStack) {
      return (Multimap)(pStack.m_41783_() == null || !pStack.m_41783_().m_128471_("pulling") || !slot.equals(EquipmentSlot.MAINHAND) && !slot.equals(EquipmentSlot.OFFHAND) ? super.getAttributeModifiers(slot, pStack) : ImmutableMultimap.builder().put(Attributes.f_22279_, new AttributeModifier(TOTAL_MOVEMENT_SPEED_MODIFIER, "Total Movement Speed Modifier", -1.0D, Operation.MULTIPLY_TOTAL)).build());
   }

   public InteractionResultHolder<ItemStack> m_7203_(Level pLevel, Player pPlayer, InteractionHand pHand) {
      ItemStack itemstack = pPlayer.m_21120_(pHand);
      boolean flag = !pPlayer.m_6298_(itemstack).m_41619_();
      InteractionResultHolder<ItemStack> ret = ForgeEventFactory.onArrowNock(itemstack, pLevel, pPlayer, pHand, flag);
      if (ret != null) {
         return ret;
      } else if (!pPlayer.m_150110_().f_35937_ && !flag) {
         return InteractionResultHolder.m_19100_(itemstack);
      } else {
         if (!pPlayer.m_20159_() && itemstack.m_41783_() != null) {
            itemstack.m_41783_().m_128347_("oldSpeed", pPlayer.m_21133_(Attributes.f_22279_));
            itemstack.m_41783_().m_128379_("pulling", true);
         }

         pPlayer.m_6672_(pHand);
         return InteractionResultHolder.m_19096_(itemstack);
      }
   }

   public boolean canApplyAtEnchantingTable(ItemStack stack, Enchantment enchantment) {
      return enchantment.equals(Enchantments.f_44961_) ? true : enchantment.f_44672_.m_7454_(stack.m_41720_());
   }
}
