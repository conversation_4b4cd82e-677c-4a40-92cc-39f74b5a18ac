package com.github.manasmods.tensura.item.templates.custom;

import net.minecraft.core.NonNullList;
import net.minecraft.world.item.CreativeModeTab;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Tier;
import net.minecraft.world.item.Item.Properties;

public class SimpleScytheItem extends TwoHandedLongSword {
   public SimpleScytheItem(Tier pTier, Properties properties) {
      super(pTier, 5, -3.2F, 2.0D, 0.0D, 0.0D, 4, -3.4F, 100.0D, 50.0D, properties);
   }

   public SimpleScytheItem(Tier pTier, int pAttackDamageModifier, float pAttackSpeedModifier, double attackRangeModifier, double critChance, double critDamageMultiplier, int oneHandedAttackDamageModifier, float oneHandedAttackSpeedModifier, double sweepChance, double oneHandedSweepChance, Properties pProperties) {
      super(pTier, pAttackDamageModifier, pAttackSpeedModifier, attackRangeModifier, critChance, critDamageMultiplier, oneHandedAttackDamageModifier, oneHandedAttackSpeedModifier, attackRangeModifier, sweepChance, oneHandedSweepChance, pProperties);
   }

   public void m_6787_(CreativeModeTab pCategory, NonNullList<ItemStack> pItems) {
      if (this.m_220152_(pCategory)) {
         ItemStack itemStack = new ItemStack(this);
         TwoHandedLongSword.setTwoHandedTag(itemStack, true);
         pItems.add(itemStack);
      }
   }
}
