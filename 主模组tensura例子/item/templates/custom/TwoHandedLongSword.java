package com.github.manasmods.tensura.item.templates.custom;

import com.github.manasmods.manascore.attribute.ManasCoreAttributes;
import com.google.common.collect.ImmutableMultimap;
import com.google.common.collect.Multimap;
import java.util.List;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.chat.Component;
import net.minecraft.world.entity.EquipmentSlot;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.ai.attributes.AttributeModifier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.attributes.AttributeModifier.Operation;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Tier;
import net.minecraft.world.item.TooltipFlag;
import net.minecraft.world.item.Item.Properties;
import net.minecraft.world.level.Level;
import net.minecraftforge.common.ForgeMod;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

public class TwoHandedLongSword extends TensuraLongSword {
   private final double twoHandedAttackRange;
   private final int oneHandedAttackDamageModifier;
   private final float oneHandedAttackSpeedModifier;
   private final double oneHandedAttackRangeModifier;
   private final double oneHandedCritChance;
   private final double oneHandedCritDamageMultiplier;
   private double oneHandedSweepChance;

   public TwoHandedLongSword(Tier pTier, int pAttackDamageModifier, float pAttackSpeedModifier, double attackRangeModifier, double critChance, double critDamageMultiplier, double sweepChance, double oneHandedSweepChance, Properties pProperties) {
      this(pTier, pAttackDamageModifier, pAttackSpeedModifier, attackRangeModifier, critChance, critDamageMultiplier, pAttackDamageModifier, sweepChance, oneHandedSweepChance, pProperties);
   }

   public TwoHandedLongSword(Tier pTier, int pAttackDamageModifier, float pAttackSpeedModifier, double attackRangeModifier, double critChance, double critDamageMultiplier, int oneHandedAttackDamageModifier, double sweepChance, double oneHandedSweepChance, Properties pProperties) {
      this(pTier, pAttackDamageModifier, pAttackSpeedModifier, attackRangeModifier, critChance, critDamageMultiplier, oneHandedAttackDamageModifier, pAttackSpeedModifier, sweepChance, oneHandedSweepChance, pProperties);
   }

   public TwoHandedLongSword(Tier pTier, int pAttackDamageModifier, float pAttackSpeedModifier, double attackRangeModifier, double critChance, double critDamageMultiplier, int oneHandedAttackDamageModifier, float oneHandedAttackSpeedModifier, double sweepChance, double oneHandedSweepChance, Properties pProperties) {
      this(pTier, pAttackDamageModifier, pAttackSpeedModifier, attackRangeModifier, critChance, critDamageMultiplier, oneHandedAttackDamageModifier, oneHandedAttackSpeedModifier, attackRangeModifier, sweepChance, oneHandedSweepChance, pProperties);
   }

   public TwoHandedLongSword(Tier pTier, int pAttackDamageModifier, float pAttackSpeedModifier, double attackRangeModifier, double critChance, double critDamageMultiplier, int oneHandedAttackDamageModifier, float oneHandedAttackSpeedModifier, double oneHandedAttackRangeModifier, double sweepChance, double oneHandedSweepChance, Properties pProperties) {
      this(pTier, pAttackDamageModifier, pAttackSpeedModifier, attackRangeModifier, critChance, critDamageMultiplier, oneHandedAttackDamageModifier, oneHandedAttackSpeedModifier, oneHandedAttackRangeModifier, critChance, sweepChance, oneHandedSweepChance, pProperties);
   }

   public TwoHandedLongSword(Tier pTier, int pAttackDamageModifier, float pAttackSpeedModifier, double attackRangeModifier, double critChance, double critDamageMultiplier, int oneHandedAttackDamageModifier, float oneHandedAttackSpeedModifier, double oneHandedAttackRangeModifier, double oneHandedCritChance, double sweepChance, double oneHandedSweepChance, Properties pProperties) {
      this(pTier, pAttackDamageModifier, pAttackSpeedModifier, attackRangeModifier, critChance, critDamageMultiplier, oneHandedAttackDamageModifier, oneHandedAttackSpeedModifier, oneHandedAttackRangeModifier, oneHandedCritChance, critDamageMultiplier, sweepChance, oneHandedSweepChance, pProperties);
   }

   public TwoHandedLongSword(Tier pTier, int pAttackDamageModifier, float pAttackSpeedModifier, double attackRangeModifier, double critChance, double critDamageMultiplier, int oneHandedAttackDamageModifier, float oneHandedAttackSpeedModifier, double oneHandedAttackRangeModifier, double oneHandedCritChance, double oneHandedCritDamageMultiplier, double sweepChance, double oneHandedSweepChance, Properties pProperties) {
      super(pTier, pAttackDamageModifier, pAttackSpeedModifier, attackRangeModifier, critChance, critDamageMultiplier, sweepChance, pProperties);
      this.twoHandedAttackRange = attackRangeModifier;
      this.oneHandedAttackDamageModifier = (int)(this.m_43314_().m_6631_() + (float)oneHandedAttackDamageModifier);
      this.oneHandedAttackSpeedModifier = oneHandedAttackSpeedModifier;
      this.oneHandedAttackRangeModifier = oneHandedAttackRangeModifier;
      this.oneHandedCritChance = oneHandedCritChance;
      this.oneHandedCritDamageMultiplier = oneHandedCritDamageMultiplier;
      this.oneHandedSweepChance = oneHandedSweepChance;
   }

   public Multimap<Attribute, AttributeModifier> getAttributeModifiers(EquipmentSlot slot, ItemStack stack) {
      if (slot != EquipmentSlot.MAINHAND) {
         return ImmutableMultimap.of();
      } else {
         return (Multimap)(isTwoHanded(stack) ? super.getAttributeModifiers(slot, stack) : ImmutableMultimap.builder().put(Attributes.f_22281_, new AttributeModifier(f_41374_, "Weapon modifier", (double)this.oneHandedAttackDamageModifier, Operation.ADDITION)).put(Attributes.f_22283_, new AttributeModifier(f_41375_, "Weapon modifier", (double)this.oneHandedAttackSpeedModifier, Operation.ADDITION)).put((Attribute)ForgeMod.ATTACK_RANGE.get(), new AttributeModifier(BASE_ATTACK_RANGE_UUID, "Weapon modifier", this.oneHandedAttackRangeModifier, Operation.ADDITION)).put((Attribute)ManasCoreAttributes.CRIT_CHANCE.get(), new AttributeModifier(BASE_CRIT_CHANCE_UUID, "Weapon modifier", this.oneHandedCritChance, Operation.ADDITION)).put((Attribute)ManasCoreAttributes.CRIT_MULTIPLIER.get(), new AttributeModifier(BASE_CRIT_MULTIPLIER_UUID, "Weapon modifier", this.oneHandedCritDamageMultiplier, Operation.ADDITION)).put((Attribute)ManasCoreAttributes.SWEEP_CHANCE.get(), new AttributeModifier(BASE_SWEEP_CHANCE_UUID, "Weapon modifier", this.oneHandedSweepChance, Operation.ADDITION)).build());
      }
   }

   public ItemStack m_7968_() {
      ItemStack defaultStack = super.m_7968_();
      setTwoHandedTag(defaultStack, true);
      return defaultStack;
   }

   public static void setTwoHandedTag(ItemStack stack, boolean value) {
      CompoundTag tag = stack.m_41784_();
      tag.m_128379_("isTwoHanded", value);
      stack.m_41751_(tag);
   }

   public static boolean isTwoHanded(ItemStack stack) {
      if (!(stack.m_41720_() instanceof TwoHandedLongSword)) {
         return false;
      } else if (!stack.m_41782_()) {
         return false;
      } else {
         CompoundTag tag = stack.m_41783_();
         if (tag == null) {
            return false;
         } else {
            return !tag.m_128441_("isTwoHanded") ? false : tag.m_128471_("isTwoHanded");
         }
      }
   }

   public void m_7373_(@NotNull ItemStack pStack, @Nullable Level pLevel, @NotNull List<Component> pTooltipComponents, @NotNull TooltipFlag pIsAdvanced) {
      pTooltipComponents.add(Component.m_237115_("tooltip.tensura.long_sword.tooltip"));
      super.m_7373_(pStack, pLevel, pTooltipComponents, pIsAdvanced);
   }

   public double getTwoHandedAttackRange() {
      return this.twoHandedAttackRange;
   }

   public int getOneHandedAttackDamageModifier() {
      return this.oneHandedAttackDamageModifier;
   }

   public float getOneHandedAttackSpeedModifier() {
      return this.oneHandedAttackSpeedModifier;
   }

   public double getOneHandedAttackRangeModifier() {
      return this.oneHandedAttackRangeModifier;
   }

   public double getOneHandedCritChance() {
      return this.oneHandedCritChance;
   }

   public double getOneHandedCritDamageMultiplier() {
      return this.oneHandedCritDamageMultiplier;
   }

   public double getOneHandedSweepChance() {
      return this.oneHandedSweepChance;
   }
}
