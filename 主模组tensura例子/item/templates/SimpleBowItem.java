package com.github.manasmods.tensura.item.templates;

import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.stats.Stats;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.InteractionResultHolder;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.entity.projectile.AbstractArrow;
import net.minecraft.world.entity.projectile.AbstractArrow.Pickup;
import net.minecraft.world.item.ArrowItem;
import net.minecraft.world.item.BowItem;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Items;
import net.minecraft.world.item.Item.Properties;
import net.minecraft.world.item.enchantment.Enchantments;
import net.minecraft.world.level.Level;
import net.minecraftforge.event.ForgeEventFactory;

public class SimpleBowItem extends BowItem {
   protected final float chargeTicks;
   protected final float inaccuracy;
   protected final double baseDamage;
   protected final int projectileRange;

   public SimpleBowItem(Properties pProperties, int pRange, float pChargeTicks, double pBaseDamage, float pInaccuracy) {
      super(pProperties);
      this.projectileRange = pRange;
      this.baseDamage = pBaseDamage;
      this.inaccuracy = pInaccuracy;
      this.chargeTicks = pChargeTicks;
   }

   public float getPowerForChargeTime(int pCharge) {
      float f = (float)pCharge / 20.0F;
      f = (f * f + f * 2.0F) / 3.0F;
      if (f > this.chargeTicks / 20.0F) {
         f = this.chargeTicks / 20.0F;
      }

      return f;
   }

   public int m_8105_(ItemStack pStack) {
      return 3600 * (int)this.getChargeTicks();
   }

   public int m_6615_() {
      return this.projectileRange;
   }

   public void m_5551_(ItemStack pStack, Level pLevel, LivingEntity pEntityLiving, int pTimeLeft) {
      if (pEntityLiving instanceof Player) {
         Player player = (Player)pEntityLiving;
         if (pStack.m_41783_() != null && pStack.m_41783_().m_128471_("pulling")) {
            pStack.m_41783_().m_128379_("pulling", false);
         }

         boolean infiniteArrow = player.m_150110_().f_35937_ || pStack.getEnchantmentLevel(Enchantments.f_44952_) > 0;
         ItemStack itemStack = player.m_6298_(pStack);
         int useTicks = this.m_8105_(pStack) - pTimeLeft;
         useTicks = ForgeEventFactory.onArrowLoose(pStack, pLevel, player, useTicks, !itemStack.m_41619_() || infiniteArrow);
         if (useTicks < 0) {
            return;
         }

         if (itemStack.m_41619_() && !infiniteArrow) {
            return;
         }

         if (itemStack.m_41619_()) {
            itemStack = new ItemStack(Items.f_42412_);
         }

         float chargedPower = this.getPowerForChargeTime(useTicks);
         if ((double)chargedPower < 0.1D) {
            return;
         }

         boolean infiniteAmmo = player.m_150110_().f_35937_ || itemStack.m_41720_() instanceof ArrowItem && ((ArrowItem)itemStack.m_41720_()).isInfinite(itemStack, pStack, player);
         if (!pLevel.f_46443_) {
            ArrowItem arrowitem = (ArrowItem)(itemStack.m_41720_() instanceof ArrowItem ? itemStack.m_41720_() : Items.f_42412_);
            AbstractArrow abstractArrow = arrowitem.m_6394_(pLevel, itemStack, player);
            abstractArrow = this.customArrow(abstractArrow);
            abstractArrow.m_37251_(player, player.m_146909_(), player.m_146908_(), 0.0F, chargedPower * 3.0F, this.inaccuracy);
            if (chargedPower == this.chargeTicks / 20.0F) {
               abstractArrow.m_36762_(true);
            }

            abstractArrow.m_36781_(this.baseDamage);
            this.applyBowEnchantments(abstractArrow, pStack);
            pStack.m_41622_(1, player, (player1) -> {
               player1.m_21190_(player.m_7655_());
            });
            if (infiniteAmmo || player.m_150110_().f_35937_ && (itemStack.m_150930_(Items.f_42737_) || itemStack.m_150930_(Items.f_42738_))) {
               abstractArrow.f_36705_ = Pickup.CREATIVE_ONLY;
            }

            pLevel.m_7967_(abstractArrow);
         }

         pLevel.m_6263_((Player)null, player.m_20185_(), player.m_20186_(), player.m_20189_(), SoundEvents.f_11687_, SoundSource.PLAYERS, 1.0F, 1.0F / (pLevel.m_213780_().m_188501_() * 0.4F + 1.2F) + chargedPower * 0.5F);
         if (!infiniteAmmo && !player.m_150110_().f_35937_) {
            itemStack.m_41774_(1);
            if (itemStack.m_41619_()) {
               player.m_150109_().m_36057_(itemStack);
            }
         }

         player.m_36246_(Stats.f_12982_.m_12902_(this));
      }

   }

   public void applyBowEnchantments(AbstractArrow abstractArrow, ItemStack pStack) {
      int j = pStack.getEnchantmentLevel(Enchantments.f_44988_);
      if (j > 0) {
         abstractArrow.m_36781_(abstractArrow.m_36789_() + (double)j * 0.5D + 0.5D);
      }

      int k = pStack.getEnchantmentLevel(Enchantments.f_44989_);
      if (k > 0) {
         abstractArrow.m_36735_(k);
      }

      int l = pStack.getEnchantmentLevel(Enchantments.f_44961_);
      if (l > 0) {
         abstractArrow.m_36767_((byte)l);
      }

      if (pStack.getEnchantmentLevel(Enchantments.f_44990_) > 0) {
         abstractArrow.m_20254_(100);
      }

   }

   public InteractionResultHolder<ItemStack> m_7203_(Level pLevel, Player pPlayer, InteractionHand pHand) {
      ItemStack itemstack = pPlayer.m_21120_(pHand);
      boolean flag = !pPlayer.m_6298_(itemstack).m_41619_();
      InteractionResultHolder<ItemStack> ret = ForgeEventFactory.onArrowNock(itemstack, pLevel, pPlayer, pHand, flag);
      if (ret != null) {
         return ret;
      } else if (!pPlayer.m_150110_().f_35937_ && !flag) {
         return InteractionResultHolder.m_19100_(itemstack);
      } else {
         pPlayer.m_6672_(pHand);
         return InteractionResultHolder.m_19096_(itemstack);
      }
   }

   public float getChargeTicks() {
      return this.chargeTicks;
   }

   public double getBaseDamage() {
      return this.baseDamage;
   }
}
