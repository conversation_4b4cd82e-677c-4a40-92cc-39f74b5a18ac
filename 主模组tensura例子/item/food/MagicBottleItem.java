package com.github.manasmods.tensura.item.food;

import com.github.manasmods.tensura.registry.items.TensuraConsumableItems;
import java.util.List;
import net.minecraft.core.BlockPos;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.tags.FluidTags;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.InteractionResultHolder;
import net.minecraft.world.entity.AreaEffectCloud;
import net.minecraft.world.entity.boss.enderdragon.EnderDragon;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.BottleItem;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Items;
import net.minecraft.world.item.Item.Properties;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.ClipContext.Fluid;
import net.minecraft.world.level.gameevent.GameEvent;
import net.minecraft.world.phys.BlockHitResult;
import net.minecraft.world.phys.HitResult.Type;

public class MagicBottleItem extends BottleItem {
   public MagicBottleItem(Properties properties) {
      super(properties);
   }

   public InteractionResultHolder<ItemStack> m_7203_(Level pLevel, Player pPlayer, InteractionHand pHand) {
      List<AreaEffectCloud> list = pLevel.m_6443_(AreaEffectCloud.class, pPlayer.m_20191_().m_82400_(2.0D), (cloud) -> {
         return cloud != null && cloud.m_6084_() && cloud.m_19749_() instanceof EnderDragon;
      });
      ItemStack itemstack = pPlayer.m_21120_(pHand);
      if (!list.isEmpty()) {
         AreaEffectCloud areaeffectcloud = (AreaEffectCloud)list.get(0);
         areaeffectcloud.m_19712_(areaeffectcloud.m_19743_() - 0.5F);
         pLevel.m_6263_((Player)null, pPlayer.m_20185_(), pPlayer.m_20186_(), pPlayer.m_20189_(), SoundEvents.f_11771_, SoundSource.NEUTRAL, 1.0F, 1.0F);
         pLevel.m_220400_(pPlayer, GameEvent.f_157816_, pPlayer.m_20182_());
         return InteractionResultHolder.m_19092_(this.m_40651_(itemstack, pPlayer, new ItemStack(Items.f_42735_)), pLevel.m_5776_());
      } else {
         BlockHitResult result = m_41435_(pLevel, pPlayer, Fluid.SOURCE_ONLY);
         if (result.m_6662_() == Type.BLOCK) {
            BlockPos blockpos = result.m_82425_();
            if (!pLevel.m_7966_(pPlayer, blockpos)) {
               return InteractionResultHolder.m_19098_(itemstack);
            }

            if (pLevel.m_6425_(blockpos).m_205070_(FluidTags.f_13131_)) {
               pLevel.m_6263_(pPlayer, pPlayer.m_20185_(), pPlayer.m_20186_(), pPlayer.m_20189_(), SoundEvents.f_11770_, SoundSource.NEUTRAL, 1.0F, 1.0F);
               pLevel.m_142346_(pPlayer, GameEvent.f_157816_, blockpos);
               return InteractionResultHolder.m_19092_(this.m_40651_(itemstack, pPlayer, ((Item)TensuraConsumableItems.WATER_MAGIC_BOTTLE.get()).m_7968_()), pLevel.m_5776_());
            }
         }

         return InteractionResultHolder.m_19098_(itemstack);
      }
   }
}
