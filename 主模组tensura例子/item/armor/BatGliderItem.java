package com.github.manasmods.tensura.item.armor;

import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.item.TensuraCreativeTab;
import com.github.manasmods.tensura.race.Race;
import com.github.manasmods.tensura.registry.items.TensuraMobDropItems;
import javax.annotation.Nullable;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.stats.Stats;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.InteractionResultHolder;
import net.minecraft.world.entity.EquipmentSlot;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.ArmorItem;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Wearable;
import net.minecraft.world.item.Item.Properties;
import net.minecraft.world.item.enchantment.Enchantment;
import net.minecraft.world.item.enchantment.EnchantmentCategory;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.DispenserBlock;
import net.minecraft.world.level.gameevent.GameEvent;

public class BatGliderItem extends Item implements Wearable {
   public BatGliderItem() {
      super((new Properties()).m_41491_(TensuraCreativeTab.ARMOUR).m_41503_(500));
      DispenserBlock.m_52672_(this, ArmorItem.f_40376_);
   }

   public EquipmentSlot getEquipmentSlot(ItemStack stack) {
      return EquipmentSlot.CHEST;
   }

   public boolean m_6832_(ItemStack pToRepair, ItemStack pRepair) {
      return pRepair.m_150930_((Item)TensuraMobDropItems.GIANT_BAT_WING.get());
   }

   public boolean canApplyAtEnchantingTable(ItemStack stack, Enchantment enchantment) {
      if (enchantment.f_44672_.equals(EnchantmentCategory.ARMOR)) {
         return true;
      } else {
         return enchantment.f_44672_.equals(EnchantmentCategory.ARMOR_CHEST) ? true : enchantment.f_44672_.m_7454_(stack.m_41720_());
      }
   }

   public int getEnchantmentValue(ItemStack stack) {
      return 20;
   }

   public InteractionResultHolder<ItemStack> m_7203_(Level pLevel, Player pPlayer, InteractionHand pHand) {
      ItemStack stack = pPlayer.m_21120_(pHand);
      ItemStack chest = pPlayer.m_6844_(EquipmentSlot.CHEST);
      if (chest.m_41619_()) {
         pPlayer.m_8061_(EquipmentSlot.CHEST, stack.m_41777_());
         if (!pLevel.m_5776_()) {
            pPlayer.m_36246_(Stats.f_12982_.m_12902_(this));
         }

         stack.m_41764_(0);
         return InteractionResultHolder.m_19092_(stack, pLevel.m_5776_());
      } else {
         return InteractionResultHolder.m_19100_(stack);
      }
   }

   public boolean canElytraFly(ItemStack stack, LivingEntity entity) {
      return stack.m_41773_() < stack.m_41776_() - 1;
   }

   public boolean elytraFlightTick(ItemStack stack, LivingEntity entity, int flightTicks) {
      if (!entity.f_19853_.f_46443_) {
         int nextFlightTick = flightTicks + 1;
         if (nextFlightTick % 10 == 0) {
            if (nextFlightTick % 20 == 0) {
               Race race = TensuraPlayerCapability.getRace(entity);
               if (race == null || !race.canFly()) {
                  stack.m_41622_(1, entity, (e) -> {
                     e.m_21166_(EquipmentSlot.CHEST);
                  });
               }
            }

            entity.m_146850_(GameEvent.f_223705_);
         }
      }

      return true;
   }

   @Nullable
   public SoundEvent m_142602_() {
      return SoundEvents.f_11674_;
   }
}
