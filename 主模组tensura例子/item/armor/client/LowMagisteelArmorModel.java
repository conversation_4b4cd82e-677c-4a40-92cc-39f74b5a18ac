package com.github.manasmods.tensura.item.armor.client;

import com.github.manasmods.tensura.item.armor.LowMagisteelArmorItem;
import net.minecraft.resources.ResourceLocation;
import software.bernie.geckolib3.model.AnimatedGeoModel;

public class LowMagisteelArmorModel extends AnimatedGeoModel<LowMagisteelArmorItem> {
   public ResourceLocation getModelResource(LowMagisteelArmorItem object) {
      return new ResourceLocation("tensura", "geo/armor/low_magisteel_armor.geo.json");
   }

   public ResourceLocation getTextureResource(LowMagisteelArmorItem object) {
      return new ResourceLocation("tensura", "textures/models/armor/low_magisteel_layer_0.png");
   }

   public ResourceLocation getAnimationResource(LowMagisteelArmorItem item) {
      return new ResourceLocation("tensura", "animations/armor.animation.json");
   }
}
