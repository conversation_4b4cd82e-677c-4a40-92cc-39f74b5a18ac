package com.github.manasmods.tensura.item.armor.client;

import com.github.manasmods.tensura.item.armor.HolyArmamentsArmorItem;
import net.minecraft.resources.ResourceLocation;
import software.bernie.geckolib3.model.AnimatedGeoModel;

public class HolyArmamentsArmorModel extends AnimatedGeoModel<HolyArmamentsArmorItem> {
   public ResourceLocation getModelResource(HolyArmamentsArmorItem object) {
      return new ResourceLocation("tensura", "geo/armor/holy_armaments_armor.geo.json");
   }

   public ResourceLocation getTextureResource(HolyArmamentsArmorItem object) {
      return new ResourceLocation("tensura", "textures/models/armor/holy_armaments_layer_0.png");
   }

   public ResourceLocation getAnimationResource(HolyArmamentsArmorItem item) {
      return new ResourceLocation("tensura", "animations/armor.animation.json");
   }
}
