package com.github.manasmods.tensura.item.armor.client;

import com.github.manasmods.tensura.item.armor.CrazyPierrotMaskItem;
import net.minecraft.resources.ResourceLocation;
import software.bernie.geckolib3.model.AnimatedGeoModel;

public class CrazyPierrotMaskModel extends AnimatedGeoModel<CrazyPierrotMaskItem> {
   public ResourceLocation getModelResource(CrazyPierrotMaskItem object) {
      return new ResourceLocation("tensura", "geo/armor/crazy_pierrot_mask.geo.json");
   }

   public ResourceLocation getTextureResource(CrazyPierrotMaskItem object) {
      return new ResourceLocation("tensura", "textures/models/armor/crazy_pierrot_mask.png");
   }

   public ResourceLocation getAnimationResource(CrazyPierrotMaskItem maskItem) {
      return null;
   }
}
