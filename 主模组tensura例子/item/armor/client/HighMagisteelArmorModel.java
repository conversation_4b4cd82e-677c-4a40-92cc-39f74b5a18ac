package com.github.manasmods.tensura.item.armor.client;

import com.github.manasmods.tensura.item.armor.HighMagisteelArmorItem;
import net.minecraft.resources.ResourceLocation;
import software.bernie.geckolib3.model.AnimatedGeoModel;

public class HighMagisteelArmorModel extends AnimatedGeoModel<HighMagisteelArmorItem> {
   public ResourceLocation getModelResource(HighMagisteelArmorItem object) {
      return new ResourceLocation("tensura", "geo/armor/high_magisteel_armor.geo.json");
   }

   public ResourceLocation getTextureResource(HighMagisteelArmorItem object) {
      return new ResourceLocation("tensura", "textures/models/armor/high_magisteel_layer_0.png");
   }

   public ResourceLocation getAnimationResource(HighMagisteelArmorItem item) {
      return new ResourceLocation("tensura", "animations/armor.animation.json");
   }
}
