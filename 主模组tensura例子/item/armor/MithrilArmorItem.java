package com.github.manasmods.tensura.item.armor;

import com.github.manasmods.tensura.item.TensuraArmourMaterials;
import com.github.manasmods.tensura.item.TensuraCreativeTab;
import net.minecraft.world.entity.EquipmentSlot;
import net.minecraft.world.item.Item.Properties;
import software.bernie.geckolib3.core.IAnimatable;
import software.bernie.geckolib3.core.manager.AnimationData;
import software.bernie.geckolib3.core.manager.AnimationFactory;
import software.bernie.geckolib3.item.GeoArmorItem;
import software.bernie.geckolib3.util.GeckoLibUtil;

public class MithrilArmorItem extends GeoArmorItem implements IAnimatable {
   private final AnimationFactory factory = GeckoLibUtil.createFactory(this);

   public MithrilArmorItem(EquipmentSlot equipmentSlot) {
      super(TensuraArmourMaterials.MITHRIL, equipmentSlot, (new Properties()).m_41491_(TensuraCreativeTab.ARMOUR).m_41486_());
   }

   public void registerControllers(AnimationData data) {
   }

   public AnimationFactory getFactory() {
      return this.factory;
   }
}
