package com.github.manasmods.tensura.api.entity.ai;

import com.github.manasmods.tensura.capability.effects.TensuraEffectsCapability;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import java.util.EnumSet;
import javax.annotation.Nullable;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.Mob;
import net.minecraft.world.entity.PathfinderMob;
import net.minecraft.world.entity.ai.goal.Goal;
import net.minecraft.world.entity.ai.goal.Goal.Flag;
import net.minecraft.world.entity.ai.navigation.PathNavigation;
import net.minecraft.world.entity.ai.util.DefaultRandomPos;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.pathfinder.Path;
import net.minecraft.world.phys.Vec3;

public class AvoidFearedEntityGoal extends Goal {
   protected final PathfinderMob mob;
   private final double walkSpeedModifier;
   private final double sprintSpeedModifier;
   @Nullable
   protected Player toAvoid;
   protected final float maxDist;
   @Nullable
   protected Path path;
   protected final PathNavigation pathNav;

   public AvoidFearedEntityGoal(PathfinderMob pMob, float pMaxDistance, double pWalkSpeedModifier, double pSprintSpeedModifier) {
      this.mob = pMob;
      this.maxDist = pMaxDistance;
      this.walkSpeedModifier = pWalkSpeedModifier;
      this.sprintSpeedModifier = pSprintSpeedModifier;
      this.pathNav = pMob.m_21573_();
      this.m_7021_(EnumSet.of(Flag.MOVE));
   }

   public static boolean shouldAvoid(Mob mob) {
      if (mob.m_21023_((MobEffect)TensuraMobEffects.RAMPAGE.get())) {
         return false;
      } else {
         MobEffectInstance instance = mob.m_21124_((MobEffect)TensuraMobEffects.FEAR.get());
         return instance != null && instance.m_19564_() >= 4;
      }
   }

   public boolean m_8036_() {
      if (!shouldAvoid(this.mob)) {
         return false;
      } else {
         this.toAvoid = TensuraEffectsCapability.getEffectSource(this.mob, (MobEffect)TensuraMobEffects.FEAR.get());
         if (this.toAvoid == null) {
            return false;
         } else {
            Vec3 posAway = DefaultRandomPos.m_148407_(this.mob, 20, 7, this.toAvoid.m_20182_());
            if (posAway == null) {
               return false;
            } else if (this.toAvoid.m_20275_(posAway.f_82479_, posAway.f_82480_, posAway.f_82481_) < this.toAvoid.m_20280_(this.mob)) {
               return false;
            } else {
               this.path = this.pathNav.m_26524_(posAway.f_82479_, posAway.f_82480_, posAway.f_82481_, 0);
               return this.path != null;
            }
         }
      }
   }

   public boolean m_8045_() {
      return !this.pathNav.m_26571_();
   }

   public void m_8056_() {
      this.pathNav.m_26536_(this.path, this.walkSpeedModifier);
   }

   public void m_8041_() {
      this.toAvoid = null;
   }

   public void m_8037_() {
      if (this.toAvoid != null) {
         if (this.mob.m_20280_(this.toAvoid) < 100.0D) {
            this.mob.m_21573_().m_26517_(this.sprintSpeedModifier);
         } else {
            this.mob.m_21573_().m_26517_(this.walkSpeedModifier);
         }

      }
   }
}
