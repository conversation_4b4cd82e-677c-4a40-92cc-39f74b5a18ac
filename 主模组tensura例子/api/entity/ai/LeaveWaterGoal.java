package com.github.manasmods.tensura.api.entity.ai;

import com.github.manasmods.tensura.api.entity.subclass.ISemiAquatic;
import java.util.EnumSet;
import java.util.Iterator;
import net.minecraft.core.BlockPos;
import net.minecraft.tags.FluidTags;
import net.minecraft.util.Mth;
import net.minecraft.world.entity.PathfinderMob;
import net.minecraft.world.entity.ai.goal.Goal;
import net.minecraft.world.entity.ai.goal.Goal.Flag;
import net.minecraft.world.entity.ai.util.LandRandomPos;
import net.minecraft.world.phys.Vec3;

public class LeaveWaterGoal extends Goal {
   private final PathfinderMob mob;
   private BlockPos targetPos;

   public LeaveWaterGoal(PathfinderMob mob) {
      this.mob = mob;
      this.m_7021_(EnumSet.of(Flag.MOVE, Flag.LOOK));
   }

   public boolean m_8036_() {
      if (this.mob.f_19853_.m_6425_(this.mob.m_20183_()).m_205070_(FluidTags.f_13131_) && (this.mob.m_5448_() != null || this.mob.m_217043_().m_188503_(30) == 0) && this.mob instanceof ISemiAquatic && ((ISemiAquatic)this.mob).shouldLeaveWater()) {
         this.targetPos = this.generateTarget();
         return this.targetPos != null;
      } else {
         return false;
      }
   }

   public void m_8056_() {
      if (this.targetPos != null) {
         this.mob.m_21573_().m_26519_((double)this.targetPos.m_123341_(), (double)this.targetPos.m_123342_(), (double)this.targetPos.m_123343_(), 1.2D);
      }
   }

   public void m_8037_() {
      if (this.targetPos != null) {
         this.mob.m_21573_().m_26519_((double)this.targetPos.m_123341_(), (double)this.targetPos.m_123342_(), (double)this.targetPos.m_123343_(), 1.2D);
      }

      if (this.mob.f_19862_ && this.mob.m_20069_()) {
         float f1 = (float)((double)this.mob.m_146908_() * 3.141592653589793D / 180.0D);
         this.mob.m_20256_(this.mob.m_20184_().m_82520_((double)(-Mth.m_14031_(f1) * 0.2F), 0.1D, (double)(Mth.m_14089_(f1) * 0.2F)));
      }

   }

   public boolean m_8045_() {
      if (this.mob instanceof ISemiAquatic && !((ISemiAquatic)this.mob).shouldLeaveWater()) {
         this.mob.m_21573_().m_26573_();
         return false;
      } else {
         return !this.mob.m_21573_().m_26571_() && this.targetPos != null && !this.mob.f_19853_.m_6425_(this.targetPos).m_205070_(FluidTags.f_13131_);
      }
   }

   public BlockPos generateTarget() {
      Vec3 vector3d = LandRandomPos.m_148488_(this.mob, 23, 7);

      for(int tries = 0; vector3d != null && tries < 8; ++tries) {
         boolean waterDetected = false;
         Iterator var4 = BlockPos.m_121976_(Mth.m_14107_(vector3d.f_82479_ - 2.0D), Mth.m_14107_(vector3d.f_82480_ - 1.0D), Mth.m_14107_(vector3d.f_82481_ - 2.0D), Mth.m_14107_(vector3d.f_82479_ + 2.0D), Mth.m_14107_(vector3d.f_82480_), Mth.m_14107_(vector3d.f_82481_ + 2.0D)).iterator();

         while(var4.hasNext()) {
            BlockPos blockpos1 = (BlockPos)var4.next();
            if (this.mob.f_19853_.m_6425_(blockpos1).m_205070_(FluidTags.f_13131_)) {
               waterDetected = true;
               break;
            }
         }

         if (!waterDetected) {
            return new BlockPos(vector3d);
         }

         vector3d = LandRandomPos.m_148488_(this.mob, 23, 7);
      }

      return null;
   }
}
