package com.github.manasmods.tensura.api.entity.ai;

import javax.annotation.Nullable;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.Mob;
import net.minecraft.world.entity.ai.goal.Goal;

public class BasicSpellGoal extends Goal {
   protected final Mob entity;
   protected int castWarmupTime;
   protected int castingInterval;
   protected int attackWarmupDelay;
   protected int nextAttackTickCount;

   public BasicSpellGoal(Mob creature, int castWarmUpTime, int castingInterval) {
      this.entity = creature;
      this.castWarmupTime = castWarmUpTime;
      this.castingInterval = castingInterval;
   }

   public boolean m_8036_() {
      if (!this.entity.m_6084_()) {
         return false;
      } else {
         LivingEntity livingentity = this.entity.m_5448_();
         if ((livingentity == null || !livingentity.m_6084_()) && !this.noTargetActivation()) {
            return false;
         } else {
            return this.entity.f_19797_ >= this.nextAttackTickCount;
         }
      }
   }

   public boolean m_8045_() {
      LivingEntity livingentity = this.entity.m_5448_();
      if ((livingentity == null || !livingentity.m_6084_()) && !this.noTargetActivation()) {
         return false;
      } else {
         return this.attackWarmupDelay > 0;
      }
   }

   public void m_8056_() {
      this.attackWarmupDelay = this.m_183277_(this.castWarmupTime);
      this.nextAttackTickCount = this.entity.f_19797_ + this.getCastingInterval();
      SoundEvent sound = this.getSpellPrepareSound();
      if (sound != null) {
         this.entity.m_5496_(sound, 1.0F, 1.0F);
      }

   }

   public void m_8037_() {
      --this.attackWarmupDelay;
      if (this.attackWarmupDelay == 0) {
         this.performSpellCasting();
         SoundEvent sound = this.getCastingSoundEvent();
         if (sound != null) {
            this.entity.m_5496_(sound, 1.0F, 1.0F);
         }
      }

   }

   protected void performSpellCasting() {
   }

   protected boolean noTargetActivation() {
      return false;
   }

   protected int getCastingInterval() {
      return this.castingInterval;
   }

   @Nullable
   protected SoundEvent getSpellPrepareSound() {
      return null;
   }

   @Nullable
   protected SoundEvent getCastingSoundEvent() {
      return null;
   }
}
