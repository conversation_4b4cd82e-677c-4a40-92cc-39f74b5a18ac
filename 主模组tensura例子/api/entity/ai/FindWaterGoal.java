package com.github.manasmods.tensura.api.entity.ai;

import com.github.manasmods.tensura.api.entity.subclass.ISemiAquatic;
import com.github.manasmods.tensura.entity.LandfishEntity;
import java.util.EnumSet;
import net.minecraft.core.BlockPos;
import net.minecraft.tags.FluidTags;
import net.minecraft.util.RandomSource;
import net.minecraft.world.entity.PathfinderMob;
import net.minecraft.world.entity.ai.goal.Goal;
import net.minecraft.world.entity.ai.goal.Goal.Flag;

public class FindWaterGoal extends Goal {
   private final PathfinderMob mob;
   private BlockPos targetPos;

   public FindWaterGoal(PathfinderMob mob) {
      this.mob = mob;
      this.m_7021_(EnumSet.of(Flag.MOVE, Flag.LOOK));
   }

   public boolean m_8036_() {
      if (!this.mob.m_20096_()) {
         return false;
      } else if (this.mob.f_19853_.m_6425_(this.mob.m_20183_()).m_205070_(FluidTags.f_13131_)) {
         return false;
      } else {
         PathfinderMob var2 = this.mob;
         if (var2 instanceof ISemiAquatic) {
            ISemiAquatic semiAquatic = (ISemiAquatic)var2;
            if (semiAquatic.shouldEnterWater() && (this.mob.m_5448_() != null || this.mob.m_217043_().m_188503_(30) == 0)) {
               this.targetPos = this.generateTarget();
               return this.targetPos != null;
            }
         }

         return false;
      }
   }

   public void m_8056_() {
      if (this.targetPos != null) {
         double speed = 1.2D;
         PathfinderMob var4 = this.mob;
         if (var4 instanceof LandfishEntity) {
            LandfishEntity landfishEntity = (LandfishEntity)var4;
            if (landfishEntity.getMoistnessLevel() <= 300) {
               speed = 2.0D;
            }
         }

         this.mob.m_21573_().m_26519_((double)this.targetPos.m_123341_(), (double)this.targetPos.m_123342_(), (double)this.targetPos.m_123343_(), speed);
      }

   }

   public void m_8037_() {
      if (this.targetPos != null) {
         double speed = 1.2D;
         PathfinderMob var4 = this.mob;
         if (var4 instanceof LandfishEntity) {
            LandfishEntity landfishEntity = (LandfishEntity)var4;
            if (landfishEntity.getMoistnessLevel() <= 300) {
               speed = 2.0D;
            }
         }

         this.mob.m_21573_().m_26519_((double)this.targetPos.m_123341_(), (double)this.targetPos.m_123342_(), (double)this.targetPos.m_123343_(), speed);
      }
   }

   public boolean m_8045_() {
      PathfinderMob var2 = this.mob;
      if (var2 instanceof ISemiAquatic) {
         ISemiAquatic semiAquatic = (ISemiAquatic)var2;
         if (!semiAquatic.shouldEnterWater()) {
            this.mob.m_21573_().m_26573_();
            return false;
         }
      }

      return !this.mob.m_21573_().m_26571_() && this.targetPos != null && !this.mob.f_19853_.m_6425_(this.mob.m_20183_()).m_205070_(FluidTags.f_13131_);
   }

   public BlockPos generateTarget() {
      BlockPos blockpos = null;
      RandomSource random = this.mob.m_217043_();
      int range = this.mob instanceof ISemiAquatic ? ((ISemiAquatic)this.mob).getWaterSearchRange() : 14;

      for(int i = 0; i < 15; ++i) {
         BlockPos blockPos;
         for(blockPos = this.mob.m_20183_().m_7918_(random.m_188503_(range) - range / 2, 3, random.m_188503_(range) - range / 2); this.mob.f_19853_.m_46859_(blockPos) && blockPos.m_123342_() > 1; blockPos = blockPos.m_7495_()) {
         }

         if (this.mob.f_19853_.m_6425_(blockPos).m_205070_(FluidTags.f_13131_)) {
            blockpos = blockPos;
         }
      }

      return blockpos;
   }
}
