package com.github.manasmods.tensura.api.entity.ai;

import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.entity.template.TensuraHorseEntity;
import java.util.EnumSet;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.Mob;
import net.minecraft.world.entity.TamableAnimal;
import net.minecraft.world.entity.ai.goal.Goal.Flag;
import net.minecraft.world.entity.ai.goal.target.TargetGoal;
import net.minecraft.world.entity.ai.targeting.TargetingConditions;

public class TensuraOwnerHurtGoal extends TargetGoal {
   private final Mob entity;
   private LivingEntity ownerLastHurt;
   private int timestamp;

   public TensuraOwnerHurtGoal(Mob mob) {
      super(mob, false);
      this.entity = mob;
      this.m_7021_(EnumSet.of(Flag.TARGET));
   }

   public boolean m_8036_() {
      if (this.entity instanceof TamableAnimal) {
         return false;
      } else if (this.entity instanceof TensuraHorseEntity) {
         return false;
      } else {
         LivingEntity owner = SkillHelper.getSubordinateOwner(this.entity);
         if (owner == null) {
            return false;
         } else {
            this.ownerLastHurt = owner.m_21214_();
            int i = owner.m_21215_();
            if (i == this.timestamp) {
               return false;
            } else if (!this.m_26150_(this.ownerLastHurt, TargetingConditions.f_26872_)) {
               return false;
            } else {
               return this.ownerLastHurt != null && !this.entity.m_7307_(this.ownerLastHurt);
            }
         }
      }
   }

   public void m_8056_() {
      this.f_26135_.m_6710_(this.ownerLastHurt);
      LivingEntity owner = SkillHelper.getSubordinateOwner(this.entity);
      if (owner != null) {
         this.timestamp = owner.m_21215_();
      }

      super.m_8056_();
   }
}
