package com.github.manasmods.tensura.api.entity.ai;

import javax.annotation.Nullable;
import net.minecraft.world.entity.PathfinderMob;
import net.minecraft.world.entity.ai.goal.RandomStrollGoal;
import net.minecraft.world.entity.ai.util.LandRandomPos;
import net.minecraft.world.phys.Vec3;

public class BetterWanderAroundGoal extends RandomStrollGoal {
   protected final float probability;
   protected final int xzRange;
   protected final int yRange;

   public BetterWanderAroundGoal(PathfinderMob mob) {
      this(mob, 120, 1.0D, 0.001F, 15, 7);
   }

   public BetterWanderAroundGoal(PathfinderMob mob, double speed) {
      this(mob, 120, speed, 0.001F, 15, 7);
   }

   public BetterWanderAroundGoal(PathfinderMob mob, double speed, int xzRange, int yRange) {
      this(mob, 120, speed, 0.001F, xzRange, yRange);
   }

   public BetterWanderAroundGoal(PathfinderMob mob, int interval, double speed, int xzRange, int yRange) {
      this(mob, interval, speed, 0.001F, xzRange, yRange);
   }

   public BetterWanderAroundGoal(PathfinderMob creature, int interval, double speed, float probability, int xzRange, int yRange) {
      super(creature, speed, interval);
      this.probability = probability;
      this.xzRange = xzRange;
      this.yRange = yRange;
   }

   public boolean m_8036_() {
      if (this.f_25725_.m_20160_()) {
         return false;
      } else {
         if (!this.f_25731_) {
            if (this.f_25725_.m_21216_() >= 100) {
               return false;
            }

            if (this.f_25725_.m_217043_().m_188503_(this.f_25730_) != 0) {
               return false;
            }
         }

         Vec3 position = this.m_7037_();
         if (position == null) {
            return false;
         } else {
            this.f_25726_ = position.f_82479_;
            this.f_25727_ = position.f_82480_;
            this.f_25728_ = position.f_82481_;
            this.f_25731_ = false;
            return true;
         }
      }
   }

   @Nullable
   protected Vec3 m_7037_() {
      if (this.f_25725_.m_20072_()) {
         Vec3 vector3d = LandRandomPos.m_148488_(this.f_25725_, this.xzRange, this.yRange);
         return vector3d == null ? super.m_7037_() : vector3d;
      } else {
         return this.f_25725_.m_217043_().m_188501_() >= this.probability ? LandRandomPos.m_148488_(this.f_25725_, this.xzRange, this.yRange) : super.m_7037_();
      }
   }
}
