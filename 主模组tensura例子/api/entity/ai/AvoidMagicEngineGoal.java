package com.github.manasmods.tensura.api.entity.ai;

import com.github.manasmods.tensura.entity.magic.barrier.MagicEngineBarrierEntity;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import java.util.EnumSet;
import java.util.Iterator;
import java.util.List;
import javax.annotation.Nullable;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.Mob;
import net.minecraft.world.entity.NeutralMob;
import net.minecraft.world.entity.PathfinderMob;
import net.minecraft.world.entity.ai.goal.Goal;
import net.minecraft.world.entity.ai.goal.Goal.Flag;
import net.minecraft.world.entity.ai.navigation.PathNavigation;
import net.minecraft.world.entity.ai.util.DefaultRandomPos;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.pathfinder.Path;
import net.minecraft.world.phys.AABB;
import net.minecraft.world.phys.Vec3;

public class AvoidMagicEngineGoal extends Goal {
   protected final PathfinderMob mob;
   private final double walkSpeedModifier;
   private final double sprintSpeedModifier;
   @Nullable
   protected MagicEngineBarrierEntity toAvoid;
   protected final float maxDist;
   @Nullable
   protected Path path;
   protected final PathNavigation pathNav;

   public AvoidMagicEngineGoal(PathfinderMob pMob, float pMaxDistance, double pWalkSpeedModifier, double pSprintSpeedModifier) {
      this.mob = pMob;
      this.maxDist = pMaxDistance;
      this.walkSpeedModifier = pWalkSpeedModifier;
      this.sprintSpeedModifier = pSprintSpeedModifier;
      this.pathNav = pMob.m_21573_();
      this.m_7021_(EnumSet.of(Flag.MOVE));
   }

   public static boolean shouldAvoid(Mob mob) {
      if (mob.m_21023_((MobEffect)TensuraMobEffects.RAMPAGE.get())) {
         return false;
      } else {
         LivingEntity target = mob.m_5448_();
         if (target != null) {
            if (mob instanceof NeutralMob) {
               NeutralMob neutral = (NeutralMob)mob;
               if (neutral.m_21674_(target)) {
                  return false;
               }
            }

            return target != mob.m_21188_() && target != mob.m_21214_();
         } else {
            return true;
         }
      }
   }

   public boolean m_8036_() {
      if (!shouldAvoid(this.mob)) {
         return false;
      } else {
         this.toAvoid = (MagicEngineBarrierEntity)getNearestEntity(this.mob.f_19853_, MagicEngineBarrierEntity.class, this.mob.m_20191_().m_82377_((double)this.maxDist, (double)this.maxDist, (double)this.maxDist), this.mob.m_20185_(), this.mob.m_20186_(), this.mob.m_20189_());
         if (this.toAvoid == null) {
            return false;
         } else if (this.toAvoid.m_20270_(this.mob) < 25.0F) {
            return false;
         } else {
            Vec3 posAway = DefaultRandomPos.m_148407_(this.mob, 20, 7, this.toAvoid.m_20182_());
            if (posAway == null) {
               return false;
            } else if (this.toAvoid.m_20275_(posAway.f_82479_, posAway.f_82480_, posAway.f_82481_) < this.toAvoid.m_20280_(this.mob)) {
               return false;
            } else {
               this.path = this.pathNav.m_26524_(posAway.f_82479_, posAway.f_82480_, posAway.f_82481_, 0);
               return this.path != null;
            }
         }
      }
   }

   public boolean m_8045_() {
      return !this.pathNav.m_26571_();
   }

   public void m_8056_() {
      this.pathNav.m_26536_(this.path, this.walkSpeedModifier);
   }

   public void m_8041_() {
      this.toAvoid = null;
   }

   public void m_8037_() {
      if (this.toAvoid != null) {
         if (this.mob.m_20280_(this.toAvoid) < 25.0D) {
            this.mob.m_21573_().m_26517_(this.sprintSpeedModifier);
         } else {
            this.mob.m_21573_().m_26517_(this.walkSpeedModifier);
         }

         this.mob.m_6710_((LivingEntity)null);
      }
   }

   public static <T extends Entity> T getNearestEntity(Level level, Class<T> pEntityClass, AABB aabb, double pX, double pY, double pZ) {
      return getNearestEntity(level.m_45976_(pEntityClass, aabb), pX, pY, pZ);
   }

   @Nullable
   public static <T extends Entity> T getNearestEntity(List<? extends T> pEntities, double pX, double pY, double pZ) {
      double d0 = -1.0D;
      T t = null;
      Iterator var10 = pEntities.iterator();

      while(true) {
         Entity t1;
         double d1;
         do {
            if (!var10.hasNext()) {
               return t;
            }

            t1 = (Entity)var10.next();
            d1 = t1.m_20275_(pX, pY, pZ);
         } while(d0 != -1.0D && !(d1 < d0));

         d0 = d1;
         t = t1;
      }
   }
}
