package com.github.manasmods.tensura.api.entity.ai;

import com.github.manasmods.tensura.api.entity.subclass.DynamicMeleeAttackAction;
import java.util.Comparator;
import java.util.EnumSet;
import java.util.List;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.PathfinderMob;
import net.minecraft.world.entity.ai.goal.Goal;
import net.minecraft.world.entity.ai.goal.Goal.Flag;

public class DynamicMeleeAttackGoal extends Goal {
   private final PathfinderMob pathfinderMob;
   private final List<DynamicMeleeAttackAction> actions;
   private int timeToRecalcPath;
   public boolean shouldMoveToTarget = true;
   private float defaultSpeed = 1.0F;

   public DynamicMeleeAttackGoal(PathfinderMob pathfinderMob, List<DynamicMeleeAttackAction> actions) {
      this.pathfinderMob = pathfinderMob;
      this.actions = actions;
      this.m_7021_(EnumSet.of(Flag.TARGET));
   }

   public void m_8056_() {
      this.timeToRecalcPath = 0;
      this.defaultSpeed = this.pathfinderMob.m_6113_();
   }

   public void m_8041_() {
      this.timeToRecalcPath = 0;
      this.pathfinderMob.m_7910_(this.defaultSpeed);
   }

   public boolean m_8036_() {
      return this.pathfinderMob.m_5448_() != null && this.pathfinderMob.m_5448_().m_6084_();
   }

   public boolean m_8045_() {
      if (this.pathfinderMob.m_5448_() == null) {
         return false;
      } else if (!this.pathfinderMob.m_5448_().m_6084_()) {
         return false;
      } else if (!this.pathfinderMob.m_5448_().m_5789_()) {
         return false;
      } else {
         return this.pathfinderMob.m_5448_().m_20183_().m_123331_(this.pathfinderMob.m_20183_()) <= 1024.0D;
      }
   }

   protected List<DynamicMeleeAttackAction> getActions() {
      return this.actions;
   }

   protected boolean shouldMoveToTarget() {
      return this.shouldMoveToTarget;
   }

   public void m_8037_() {
      if (this.timeToRecalcPath-- <= 0) {
         this.timeToRecalcPath = this.m_183277_(10);
         LivingEntity target = this.pathfinderMob.m_5448_();
         if (target != null) {
            float speed = (Float)this.getActions().stream().map((action) -> {
               return action.execute(this.pathfinderMob, target, this);
            }).max(Comparator.naturalOrder()).orElse(1.0F);
            if (this.shouldMoveToTarget()) {
               this.moveTo(target, (double)speed);
            }
         }
      }
   }

   protected void moveTo(LivingEntity target, double speed) {
      this.pathfinderMob.m_21573_().m_5624_(target, speed);
   }
}
