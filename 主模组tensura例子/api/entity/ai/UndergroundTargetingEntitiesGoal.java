package com.github.manasmods.tensura.api.entity.ai;

import java.util.function.Predicate;
import javax.annotation.Nullable;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.TamableAnimal;
import net.minecraft.world.entity.ai.goal.target.NonTameRandomTargetGoal;
import net.minecraft.world.entity.ai.targeting.TargetingConditions;
import net.minecraft.world.phys.AABB;
import org.jetbrains.annotations.NotNull;

public class UndergroundTargetingEntitiesGoal<T extends LivingEntity> extends NonTameRandomTargetGoal<T> {
   protected final float yDistance;

   public UndergroundTargetingEntitiesGoal(TamableAnimal mob, Class<T> pClass, boolean mustSee, float yDistance, @Nullable Predicate<LivingEntity> pTargetPredicate) {
      super(mob, pClass, mustSee, pTargetPredicate);
      this.f_26051_ = TargetingConditions.m_148352_().m_26883_(this.m_7623_()).m_148355_().m_26893_().m_26888_(pTargetPredicate);
      this.yDistance = yDistance;
   }

   @NotNull
   protected AABB m_7255_(double pTargetDistance) {
      return this.f_26135_.m_20191_().m_82377_(pTargetDistance, (double)this.yDistance, pTargetDistance);
   }
}
