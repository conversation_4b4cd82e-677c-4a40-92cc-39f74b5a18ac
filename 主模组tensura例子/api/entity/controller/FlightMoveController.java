package com.github.manasmods.tensura.api.entity.controller;

import net.minecraft.util.Mth;
import net.minecraft.world.entity.Mob;
import net.minecraft.world.entity.ai.control.MoveControl;
import net.minecraft.world.entity.ai.control.MoveControl.Operation;
import net.minecraft.world.phys.Vec3;

public class FlightMoveController extends MoveControl {
   private final Mob entity;
   private final float speedGeneral;
   private final boolean shouldLookAtTarget;
   private final boolean needsYSupport;

   public FlightMoveController(Mob mob, float speedGeneral, boolean shouldLookAtTarget, boolean needsYSupport) {
      super(mob);
      this.entity = mob;
      this.shouldLookAtTarget = shouldLookAtTarget;
      this.speedGeneral = speedGeneral;
      this.needsYSupport = needsYSupport;
   }

   public FlightMoveController(Mob mob, float speedGeneral, boolean shouldLookAtTarget) {
      this(mob, speedGeneral, shouldLookAtTarget, false);
   }

   public void m_8126_() {
      if (this.f_24981_ == Operation.MOVE_TO) {
         Vec3 vector3d = new Vec3(this.f_24975_ - this.entity.m_20185_(), this.f_24976_ - this.entity.m_20186_(), this.f_24977_ - this.entity.m_20189_());
         double d0 = vector3d.m_82553_();
         if (d0 < this.entity.m_20191_().m_82309_()) {
            this.f_24981_ = Operation.WAIT;
            this.entity.m_20256_(this.entity.m_20184_().m_82490_(0.5D));
         } else {
            this.entity.m_20256_(this.entity.m_20184_().m_82549_(vector3d.m_82490_(this.f_24978_ * (double)this.speedGeneral * 0.05D / d0)));
            double d2;
            if (this.needsYSupport) {
               d2 = this.f_24976_ - this.entity.m_20186_();
               this.entity.m_20256_(this.entity.m_20184_().m_82520_(0.0D, (double)this.entity.m_6113_() * (double)this.speedGeneral * Mth.m_14008_(d2, -1.0D, 1.0D) * 0.6000000238418579D, 0.0D));
            }

            if (this.entity.m_5448_() != null && this.shouldLookAtTarget) {
               d2 = this.entity.m_5448_().m_20185_() - this.entity.m_20185_();
               double d1 = this.entity.m_5448_().m_20189_() - this.entity.m_20189_();
               this.entity.m_146922_(-((float)Mth.m_14136_(d2, d1)) * 57.295776F);
            } else {
               Vec3 vector3d1 = this.entity.m_20184_();
               this.entity.m_146922_(-((float)Mth.m_14136_(vector3d1.f_82479_, vector3d1.f_82481_)) * 57.295776F);
            }

            this.entity.f_20883_ = this.entity.m_146908_();
         }
      } else if (this.f_24981_ == Operation.STRAFE) {
         this.f_24981_ = Operation.WAIT;
      }

   }
}
