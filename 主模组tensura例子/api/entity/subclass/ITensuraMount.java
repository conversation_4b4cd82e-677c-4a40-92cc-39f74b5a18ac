package com.github.manasmods.tensura.api.entity.subclass;

import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;

public interface ITensuraMount {
   void mountAbility(Player var1);

   default boolean hasScrollAbility() {
      return false;
   }

   default void mountScrollAbility(Player rider, double scrollChange) {
   }

   default void descending(Entity mount, LivingEntity rider) {
      mount.m_20256_(mount.m_20184_().m_82520_(0.0D, -0.07D, 0.0D));
   }
}
