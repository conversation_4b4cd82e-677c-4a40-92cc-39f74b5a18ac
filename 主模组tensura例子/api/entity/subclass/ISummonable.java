package com.github.manasmods.tensura.api.entity.subclass;

import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import java.util.UUID;
import javax.annotation.Nullable;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.Mob;
import net.minecraft.world.entity.player.Player;

public interface ISummonable extends IRanking {
   default boolean canBeNamed() {
      return this.getSummoningTick() == -1;
   }

   default int getSummoningTick() {
      return 0;
   }

   default void setSummoningTick(int tick) {
   }

   @Nullable
   default UUID getSummonerUUID() {
      return null;
   }

   default void setSummonerUUID(@Nullable UUID pUuid) {
   }

   default void summoningTicking(Mob entity, double mpCost) {
      if (!entity.m_21525_() && entity.m_6084_()) {
         int tick = this.getSummoningTick();
         if (tick > 0) {
            this.setSummoningTick(tick - 1);
            if (this.getSummoningTick() <= 0) {
               entity.m_6469_(TensuraDamageSources.OUT_OF_ENERGY, entity.m_21233_());
            } else if (tick % 20 == 0) {
               LivingEntity var6 = SkillHelper.getSubordinateOwner(entity);
               if (var6 instanceof Player) {
                  Player owner = (Player)var6;
                  if (SkillHelper.outOfMagicule(owner, mpCost) || !owner.m_6084_()) {
                     entity.m_6469_(TensuraDamageSources.OUT_OF_ENERGY, entity.m_21233_());
                  }

               }
            }
         }
      }
   }
}
