package com.github.manasmods.tensura.api.entity.subclass;

import net.minecraft.core.BlockPos;
import net.minecraft.core.Direction;
import net.minecraft.core.BlockPos.MutableBlockPos;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.gameevent.GameEvent;
import net.minecraft.world.level.gameevent.GameEvent.Context;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.event.ForgeEventFactory;
import net.minecraftforge.event.entity.EntityTeleportEvent.EnderEntity;
import org.jetbrains.annotations.Nullable;

public interface ITeleportation {
   default void teleportTowards(LivingEntity entity, Entity target, double distance) {
      Vec3 vec3 = (new Vec3(entity.m_20185_() - target.m_20185_(), entity.m_20227_(0.5D) - target.m_20188_(), entity.m_20189_() - target.m_20189_())).m_82541_();
      double tpDistance = (double)target.m_20270_(entity) - distance;
      double d1 = entity.m_20185_() + (entity.m_217043_().m_188500_() - 0.5D) * 8.0D - vec3.f_82479_ * tpDistance;
      double d2 = entity.m_20186_() + (double)(entity.m_217043_().m_188503_(16) - 8) - vec3.f_82480_ * tpDistance;
      double d3 = entity.m_20189_() + (entity.m_217043_().m_188500_() - 0.5D) * 8.0D - vec3.f_82481_ * tpDistance;
      this.teleport(entity, d1, d2, d3, target.m_20186_());
   }

   default boolean shouldCountMotionBlock() {
      return true;
   }

   @Nullable
   default SoundEvent getTeleportSound() {
      return SoundEvents.f_11852_;
   }

   default void teleport(LivingEntity entity, double pX, double pY, double pZ) {
      this.teleport(entity, pX, pY, pZ, (double)entity.f_19853_.m_141937_());
   }

   default void teleport(LivingEntity entity, double pX, double pY, double pZ, double minY) {
      MutableBlockPos pos = new MutableBlockPos(pX, pY, pZ);
      if (this.shouldCountMotionBlock()) {
         while(pos.m_123342_() > entity.f_19853_.m_141937_() && !entity.f_19853_.m_8055_(pos).m_60767_().m_76334_()) {
            pos.m_122173_(Direction.DOWN);
         }
      } else if ((double)pos.m_123342_() > minY) {
         while((double)pos.m_123342_() > minY && !entity.f_19853_.m_8055_(pos).m_60767_().m_76334_()) {
            pos.m_122173_(Direction.DOWN);
         }
      }

      BlockState state = entity.f_19853_.m_8055_(pos);
      if ((!this.shouldCountMotionBlock() || state.m_60767_().m_76334_()) && state.m_60819_().m_76178_()) {
         EnderEntity event = ForgeEventFactory.onEnderTeleport(entity, pX, pY, pZ);
         if (event.isCanceled()) {
            return;
         }

         Vec3 vec3 = entity.m_20182_();
         if (this.randomTeleport(entity, event.getTargetX(), event.getTargetY(), event.getTargetZ(), true)) {
            entity.f_19853_.m_214171_(GameEvent.f_238175_, vec3, Context.m_223717_(entity));
            if (!entity.m_20067_() && this.getTeleportSound() != null) {
               entity.f_19853_.m_6263_((Player)null, entity.f_19854_, entity.f_19855_, entity.f_19856_, this.getTeleportSound(), entity.m_5720_(), 1.0F, 1.0F);
               entity.m_5496_(this.getTeleportSound(), 1.0F, 1.0F);
            }
         }
      }

   }

   default boolean randomTeleport(LivingEntity entity, double pX, double pY, double pZ, boolean pBroadcastTeleport) {
      double d0 = entity.m_20185_();
      double d1 = entity.m_20186_();
      double d2 = entity.m_20189_();
      double d3 = pY;
      boolean flag = false;
      BlockPos blockpos = new BlockPos(pX, pY, pZ);
      Level level = entity.f_19853_;
      if (level.m_46805_(blockpos)) {
         boolean flag1 = !this.shouldCountMotionBlock();

         while(!flag1 && blockpos.m_123342_() > level.m_141937_()) {
            BlockPos blockpos1 = blockpos.m_7495_();
            BlockState blockstate = level.m_8055_(blockpos1);
            if (blockstate.m_60767_().m_76334_()) {
               flag1 = true;
            } else {
               --d3;
               blockpos = blockpos1;
            }
         }

         if (flag1) {
            entity.m_6021_(pX, d3, pZ);
            if (level.m_45786_(entity) && !level.m_46855_(entity.m_20191_())) {
               flag = true;
            }
         }
      }

      if (!flag) {
         entity.m_6021_(d0, d1, d2);
         return false;
      } else {
         if (pBroadcastTeleport) {
            level.m_7605_(entity, (byte)46);
         }

         return true;
      }
   }
}
