package com.github.manasmods.tensura.api.entity.subclass;

import com.github.manasmods.tensura.entity.template.TensuraTamableEntity;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.PathfinderMob;
import net.minecraft.world.entity.TamableAnimal;

public interface IJumpingEntity {
   int getJumpDelay();

   void setJumpAnimation(boolean var1);

   SoundEvent getJumpSound();

   float getJumpSoundVolume();

   default boolean setWantedTarget(PathfinderMob mob, double targetDistance) {
      LivingEntity livingentity = mob.m_5448_();
      if (livingentity != null && mob.m_20280_(livingentity) > targetDistance && livingentity.m_6084_()) {
         mob.m_21566_().m_6849_(livingentity.m_20185_(), livingentity.m_20186_(), livingentity.m_20189_(), mob.m_21566_().m_24999_());
         return true;
      } else {
         return false;
      }
   }

   default boolean setWantedOwner(TamableAnimal tamableAnimal, double ownerDistance) {
      LivingEntity owner = tamableAnimal.m_21826_();
      if (tamableAnimal.m_21827_()) {
         return false;
      } else {
         if (tamableAnimal instanceof TensuraTamableEntity) {
            TensuraTamableEntity entity = (TensuraTamableEntity)tamableAnimal;
            if (entity.isWandering()) {
               return false;
            }
         }

         if (owner != null && tamableAnimal.m_20280_(owner) > ownerDistance) {
            tamableAnimal.m_21566_().m_6849_(owner.m_20185_(), owner.m_20186_(), owner.m_20189_(), tamableAnimal.m_21566_().m_24999_());
            return true;
         } else {
            return false;
         }
      }
   }
}
