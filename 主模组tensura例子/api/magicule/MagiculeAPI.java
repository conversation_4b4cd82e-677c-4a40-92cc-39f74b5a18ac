package com.github.manasmods.tensura.api.magicule;

import com.github.manasmods.tensura.capability.magicule.MagiculeChunkCapability;
import com.github.manasmods.tensura.capability.magicule.MagiculeChunkCapabilityImpl;
import com.mojang.datafixers.util.Pair;
import net.minecraft.core.BlockPos;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.level.Level;

public final class MagiculeAPI {
   public static double getMagicule(Level level, BlockPos pos) {
      MagiculeChunkCapability cap = MagiculeChunkCapabilityImpl.get(level.m_46745_(pos));
      return cap.getMagicule(pos);
   }

   public static double getMagicule(LivingEntity entity) {
      return getMagicule(entity.f_19853_, entity.m_20183_());
   }

   public static double getMaxMagicule(Level level, BlockPos pos) {
      MagiculeChunkCapability cap = MagiculeChunkCapabilityImpl.get(level.m_46745_(pos));
      return cap.getMaxMagicule(pos);
   }

   public static Pair<Double, Double> getMagiculePair(Level level, BlockPos pos) {
      MagiculeChunkCapability cap = MagiculeChunkCapabilityImpl.get(level.m_46745_(pos));
      return Pair.of(cap.getMagicule(pos), cap.getMaxMagicule(pos));
   }

   public static Pair<Double, Double> getMagiculePair(LivingEntity entity) {
      return getMagiculePair(entity.f_19853_, entity.m_20183_());
   }

   public static double getMaxMagicule(LivingEntity entity) {
      return getMaxMagicule(entity.f_19853_, entity.m_20183_());
   }

   public static boolean consumeMagicule(Level level, BlockPos pos, double amount) {
      MagiculeChunkCapability cap = MagiculeChunkCapabilityImpl.get(level.m_46745_(pos));
      return cap.consumeMagicule(pos, amount);
   }

   public static boolean consumeMagicule(LivingEntity entity, double amount) {
      return consumeMagicule(entity.f_19853_, entity.m_20183_(), amount);
   }

   public static double getMagiculeRegenerationRate(Level level, BlockPos pos) {
      MagiculeChunkCapability cap = MagiculeChunkCapabilityImpl.get(level.m_46745_(pos));
      return cap.getRegenerationRate();
   }

   public static double getMagiculeRegenerationRate(LivingEntity entity) {
      return getMagiculeRegenerationRate(entity.f_19853_, entity.m_20183_());
   }

   private MagiculeAPI() {
      throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
   }
}
