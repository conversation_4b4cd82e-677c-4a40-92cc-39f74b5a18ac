package com.github.manasmods.tensura.api.item.dispensing;

import com.github.manasmods.tensura.entity.projectile.InvisibleArrow;
import com.github.manasmods.tensura.entity.projectile.KunaiProjectile;
import com.github.manasmods.tensura.entity.projectile.SpearProjectile;
import com.github.manasmods.tensura.entity.projectile.SpearedFinArrow;
import com.github.manasmods.tensura.entity.projectile.ThrownHolyWater;
import com.github.manasmods.tensura.entity.projectile.UnicornHornProjectile;
import com.github.manasmods.tensura.entity.projectile.WebBulletProjectile;
import net.minecraft.Util;
import net.minecraft.core.Position;
import net.minecraft.core.dispenser.AbstractProjectileDispenseBehavior;
import net.minecraft.core.dispenser.DispenseItemBehavior;
import net.minecraft.world.entity.projectile.Projectile;
import net.minecraft.world.entity.projectile.AbstractArrow.Pickup;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.enchantment.Enchantments;
import net.minecraft.world.level.Level;
import org.jetbrains.annotations.NotNull;

public class TensuraDispenseBehaviors {
   public static final DispenseItemBehavior DISPENSE_INVISIBLE_ARROW_BEHAVIOR = new AbstractProjectileDispenseBehavior() {
      @NotNull
      protected Projectile m_6895_(@NotNull Level pLevel, Position position, @NotNull ItemStack pStack) {
         InvisibleArrow arrow = new InvisibleArrow(pLevel, position.m_7096_(), position.m_7098_(), position.m_7094_());
         arrow.f_36705_ = Pickup.ALLOWED;
         return arrow;
      }
   };
   public static final DispenseItemBehavior DISPENSE_SPEARED_FIN_ARROW_BEHAVIOR = new AbstractProjectileDispenseBehavior() {
      @NotNull
      protected Projectile m_6895_(@NotNull Level pLevel, Position position, @NotNull ItemStack pStack) {
         SpearedFinArrow arrow = new SpearedFinArrow(pLevel, position.m_7096_(), position.m_7098_(), position.m_7094_());
         arrow.f_36705_ = Pickup.ALLOWED;
         return arrow;
      }
   };
   public static final DispenseItemBehavior DISPENSE_HOLY_WATER_BEHAVIOR = new AbstractProjectileDispenseBehavior() {
      @NotNull
      protected Projectile m_6895_(@NotNull Level level, Position position, @NotNull ItemStack pStack) {
         return (Projectile)Util.m_137469_(new ThrownHolyWater(level, position.m_7096_(), position.m_7098_(), position.m_7094_()), (holyWater) -> {
            holyWater.m_37446_(pStack);
         });
      }

      protected float m_7101_() {
         return super.m_7101_() * 0.5F;
      }

      protected float m_7104_() {
         return super.m_7104_() * 1.25F;
      }
   };
   public static final DispenseItemBehavior DISPENSE_KUNAI_BEHAVIOR = new MultishotDispenseBehavior() {
      @NotNull
      protected Projectile m_6895_(@NotNull Level pLevel, Position position, @NotNull ItemStack pStack) {
         KunaiProjectile kunai = new KunaiProjectile(pLevel, position.m_7096_(), position.m_7098_(), position.m_7094_());
         kunai.f_36705_ = Pickup.ALLOWED;
         kunai.setSourceItem(pStack.m_41777_());
         kunai.getSourceItem().m_41764_(1);
         return kunai;
      }

      protected Projectile getMultishotProjectile(Level pLevel, Position position, ItemStack pStack) {
         if (pStack.getEnchantmentLevel(Enchantments.f_44959_) <= 0) {
            return null;
         } else {
            KunaiProjectile kunai = new KunaiProjectile(pLevel, position.m_7096_(), position.m_7098_(), position.m_7094_());
            kunai.f_36705_ = Pickup.DISALLOWED;
            kunai.setSourceItem(pStack.m_41777_());
            kunai.getSourceItem().m_41764_(1);
            return kunai;
         }
      }
   };
   public static final DispenseItemBehavior DISPENSE_MAGISTEEL_KUNAI_BEHAVIOR = new MultishotDispenseBehavior() {
      @NotNull
      protected Projectile m_6895_(@NotNull Level pLevel, Position position, @NotNull ItemStack pStack) {
         KunaiProjectile kunai = new KunaiProjectile(pLevel, position.m_7096_(), position.m_7098_(), position.m_7094_());
         kunai.f_36705_ = Pickup.DISALLOWED;
         pStack.m_41721_(pStack.m_41773_() + 10);
         kunai.setSourceItem(pStack.m_41777_());
         return kunai;
      }

      protected Projectile getMultishotProjectile(Level pLevel, Position position, ItemStack pStack) {
         if (pStack.getEnchantmentLevel(Enchantments.f_44959_) <= 0) {
            return null;
         } else {
            KunaiProjectile kunai = new KunaiProjectile(pLevel, position.m_7096_(), position.m_7098_(), position.m_7094_());
            kunai.f_36705_ = Pickup.DISALLOWED;
            kunai.setSourceItem(pStack.m_41777_());
            return kunai;
         }
      }

      protected boolean getUnlimited() {
         return true;
      }
   };
   public static final DispenseItemBehavior DISPENSE_SPEAR_BEHAVIOR = new AbstractProjectileDispenseBehavior() {
      @NotNull
      protected Projectile m_6895_(@NotNull Level pLevel, Position position, @NotNull ItemStack pStack) {
         SpearProjectile spear = new SpearProjectile(pLevel, position.m_7096_(), position.m_7098_(), position.m_7094_());
         spear.f_36705_ = Pickup.ALLOWED;
         pStack.m_41721_(pStack.m_41773_() + 10);
         spear.setSourceItem(pStack.m_41777_());
         return spear;
      }
   };
   public static final DispenseItemBehavior DISPENSE_UNICORN_HORN_BEHAVIOR = new AbstractProjectileDispenseBehavior() {
      @NotNull
      protected Projectile m_6895_(@NotNull Level pLevel, Position position, @NotNull ItemStack pStack) {
         UnicornHornProjectile arrow = new UnicornHornProjectile(pLevel, position.m_7096_(), position.m_7098_(), position.m_7094_());
         arrow.f_36705_ = Pickup.ALLOWED;
         return arrow;
      }
   };
   public static final DispenseItemBehavior DISPENSE_WEB_CARTRIDGE_BEHAVIOR = new AbstractProjectileDispenseBehavior() {
      @NotNull
      protected Projectile m_6895_(@NotNull Level pLevel, Position position, @NotNull ItemStack pStack) {
         WebBulletProjectile bullet = new WebBulletProjectile(pLevel, position.m_7096_(), position.m_7098_(), position.m_7094_());
         bullet.setAmmo(pStack.m_41777_());
         return bullet;
      }
   };
}
