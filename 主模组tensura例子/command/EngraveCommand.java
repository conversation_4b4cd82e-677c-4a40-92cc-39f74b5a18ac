package com.github.manasmods.tensura.command;

import com.github.manasmods.tensura.enchantment.EngravingEnchantment;
import com.mojang.brigadier.arguments.IntegerArgumentType;
import com.mojang.brigadier.builder.LiteralArgumentBuilder;
import com.mojang.brigadier.builder.RequiredArgumentBuilder;
import com.mojang.brigadier.exceptions.CommandSyntaxException;
import com.mojang.brigadier.exceptions.DynamicCommandExceptionType;
import com.mojang.brigadier.exceptions.SimpleCommandExceptionType;
import com.mojang.brigadier.suggestion.SuggestionProvider;
import java.util.Collection;
import java.util.Iterator;
import net.minecraft.commands.CommandSourceStack;
import net.minecraft.commands.Commands;
import net.minecraft.commands.SharedSuggestionProvider;
import net.minecraft.commands.arguments.EntityArgument;
import net.minecraft.commands.arguments.ItemEnchantmentArgument;
import net.minecraft.core.Registry;
import net.minecraft.network.chat.Component;
import net.minecraft.resources.ResourceKey;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.enchantment.Enchantment;
import net.minecraftforge.event.RegisterCommandsEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod.EventBusSubscriber;
import net.minecraftforge.fml.common.Mod.EventBusSubscriber.Bus;

@EventBusSubscriber(
   modid = "tensura",
   bus = Bus.FORGE
)
public class EngraveCommand {
   private static final DynamicCommandExceptionType ERROR_NOT_LIVING_ENTITY = new DynamicCommandExceptionType((o) -> {
      return Component.m_237115_("commands.enchant.failed.entity");
   });
   private static final DynamicCommandExceptionType ERROR_NO_ITEM = new DynamicCommandExceptionType((translatable) -> {
      return Component.m_237115_("commands.enchant.failed.itemless");
   });
   private static final SimpleCommandExceptionType ERROR_NOTHING_HAPPENED = new SimpleCommandExceptionType(Component.m_237115_("commands.enchant.failed"));
   public static final SuggestionProvider<CommandSourceStack> ENGRAVING_ENCHANTMENTS = (context, builder) -> {
      return SharedSuggestionProvider.m_82957_(Registry.f_122825_.m_6579_().stream().filter((enchantment) -> {
         return enchantment.getValue() instanceof EngravingEnchantment;
      }).map((skill) -> {
         return ((ResourceKey)skill.getKey()).m_135782_();
      }), builder);
   };

   @SubscribeEvent
   public static void register(RegisterCommandsEvent e) {
      e.getDispatcher().register((LiteralArgumentBuilder)((LiteralArgumentBuilder)Commands.m_82127_("engrave").requires((stack) -> {
         return stack.m_6761_(2);
      })).then(Commands.m_82129_("targets", EntityArgument.m_91460_()).then(((RequiredArgumentBuilder)Commands.m_82129_("enchantment", ItemEnchantmentArgument.m_95260_()).suggests(ENGRAVING_ENCHANTMENTS).executes((context) -> {
         return engrave((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), ItemEnchantmentArgument.m_95263_(context, "enchantment"), 1);
      })).then(Commands.m_82129_("level", IntegerArgumentType.integer(0)).executes((context) -> {
         return engrave((CommandSourceStack)context.getSource(), EntityArgument.m_91461_(context, "targets"), ItemEnchantmentArgument.m_95263_(context, "enchantment"), IntegerArgumentType.getInteger(context, "level"));
      })))));
   }

   private static int engrave(CommandSourceStack pSource, Collection<? extends Entity> pTargets, Enchantment enchantment, int pLevel) throws CommandSyntaxException {
      int i = 0;
      Iterator var5 = pTargets.iterator();

      while(var5.hasNext()) {
         Entity entity = (Entity)var5.next();
         if (entity instanceof LivingEntity) {
            LivingEntity livingentity = (LivingEntity)entity;
            ItemStack itemstack = livingentity.m_21205_();
            if (!itemstack.m_41619_()) {
               EngravingEnchantment.engrave(itemstack, enchantment, pLevel);
               ++i;
            } else if (pTargets.size() == 1) {
               throw ERROR_NO_ITEM.create(livingentity.m_7755_().getString());
            }
         } else if (pTargets.size() == 1) {
            throw ERROR_NOT_LIVING_ENTITY.create(entity.m_7755_().getString());
         }
      }

      if (i == 0) {
         throw ERROR_NOTHING_HAPPENED.create();
      } else {
         if (pTargets.size() == 1) {
            pSource.m_81354_(Component.m_237110_("commands.enchant.success.single", new Object[]{enchantment.m_44700_(pLevel), ((Entity)pTargets.iterator().next()).m_5446_()}), true);
         } else {
            pSource.m_81354_(Component.m_237110_("commands.enchant.success.multiple", new Object[]{enchantment.m_44700_(pLevel), pTargets.size()}), true);
         }

         return i;
      }
   }
}
