package com.github.manasmods.tensura.effect;

import com.github.manasmods.tensura.race.RaceHelper;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import javax.annotation.Nullable;
import net.minecraft.world.effect.InstantenousMobEffect;
import net.minecraft.world.effect.MobEffectCategory;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;

public class HolyDamageEffect extends InstantenousMobEffect {
   public HolyDamageEffect(MobEffectCategory pCategory, int pColor) {
      super(pCategory, pColor);
   }

   public void m_6742_(LivingEntity entity, int pAmplifier) {
      if (RaceHelper.isAffectedByHolyCoat(entity)) {
         int damage = 2 * (pAmplifier + 1);
         if (RaceHelper.isUndead(entity)) {
            damage *= 2;
         }

         entity.m_6469_(TensuraDamageSources.HOLY_DAMAGE, (float)damage);
      }

   }

   public void m_19461_(@Nullable Entity pSource, @Nullable Entity pIndirectSource, LivingEntity entity, int pAmplifier, double pHealth) {
      if (RaceHelper.isAffectedByHolyCoat(entity)) {
         int damage = 2 * (pAmplifier + 1);
         if (RaceHelper.isUndead(entity)) {
            damage *= 2;
         }

         if (pSource == null) {
            entity.m_6469_(TensuraDamageSources.HOLY_DAMAGE, (float)damage);
         } else {
            entity.m_6469_(TensuraDamageSources.holyDamage(pSource, pIndirectSource), (float)damage);
         }
      }

   }
}
