package com.github.manasmods.tensura.effect.skill;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.manascore.api.skills.capability.SkillStorage;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.effect.template.DamageAction;
import com.github.manasmods.tensura.effect.template.SkillMobEffect;
import com.github.manasmods.tensura.enchantment.SlottingEnchantment;
import com.github.manasmods.tensura.registry.skill.ExtraSkills;
import com.github.manasmods.tensura.util.damage.TensuraDamageSource;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import com.github.manasmods.tensura.util.damage.TensuraEntityDamageSource;
import java.util.Optional;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffectCategory;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.ItemStack;

public class MagicAuraEffect extends SkillMobEffect implements DamageAction {
   public MagicAuraEffect(MobEffectCategory pCategory, int pColor) {
      super(pCategory, pColor);
   }

   public void onPlayerAttack(Player attacker, Entity entity) {
      SkillStorage storage = SkillAPI.getSkillsFrom(attacker);
      Optional<ManasSkillInstance> optional = storage.getSkill((ManasSkill)ExtraSkills.MAGIC_AURA.get());
      if (!optional.isEmpty()) {
         ManasSkillInstance instance = (ManasSkillInstance)optional.get();
         ItemStack stack = attacker.m_21205_();
         if (SlottingEnchantment.getContentWeight(stack) <= 0) {
            float amount = SkillHelper.noCritAttackDamage(attacker, entity) * 1.5F;
            entity.m_6469_(this.auraSource(instance, attacker), amount);
            entity.f_19802_ = 0;
         }
      }
   }

   public boolean m_6584_(int duration, int amplifier) {
      return false;
   }

   private DamageSource auraSource(ManasSkillInstance instance, LivingEntity attacker) {
      if (instance.getMode() == 1) {
         return TensuraDamageSources.genericMagic(attacker);
      } else if (instance.getMode() == 2) {
         return TensuraDamageSources.holyDamage(attacker);
      } else {
         String var10000;
         switch(instance.getMode()) {
         case 3:
            var10000 = "tensura.earth_attack";
            break;
         case 4:
            var10000 = "tensura.fire_attack";
            break;
         case 5:
            var10000 = "tensura.space_attack";
            break;
         case 6:
            var10000 = "tensura.water_attack";
            break;
         default:
            var10000 = "tensura.wind_attack";
         }

         String element = var10000;
         TensuraDamageSource damageSource = (new TensuraEntityDamageSource(element, attacker)).setSkill(instance).setMpCost(1000.0D).setNotTensuraMagic();
         return instance.getMode() == 4 ? damageSource.m_19380_().m_19389_().m_19383_() : damageSource.m_19380_().m_19389_();
      }
   }
}
