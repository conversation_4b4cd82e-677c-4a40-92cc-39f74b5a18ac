package com.github.manasmods.tensura.effect.skill;

import com.github.manasmods.tensura.effect.template.Concealment;
import com.github.manasmods.tensura.effect.template.SkillMobEffect;
import net.minecraft.world.effect.MobEffectCategory;
import net.minecraft.world.entity.LivingEntity;

public class FalsifierEffect extends SkillMobEffect implements Concealment {
   public FalsifierEffect(MobEffectCategory pCategory, int pColor) {
      super(pCategory, pColor);
   }

   public void m_6742_(LivingEntity pLivingEntity, int pAmplifier) {
      this.concealing(pLivingEntity, 40.0D, pAmplifier + 2);
   }

   public boolean m_6584_(int duration, int amplifier) {
      return duration % 5 == 0;
   }
}
