package com.github.manasmods.tensura.effect.skill;

import com.github.manasmods.tensura.effect.template.SkillMobEffect;
import net.minecraft.world.effect.MobEffectCategory;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.attributes.AttributeModifier.Operation;
import net.minecraftforge.common.ForgeMod;

public class FateChangeEffect extends SkillMobEffect {
   protected static final String TUNER = "2097503e-7be9-11ee-b962-0242ac120002";

   public FateChangeEffect(MobEffectCategory pCategory, int pColor) {
      super(pCategory, pColor);
      this.m_19472_(Attributes.f_22281_, "2097503e-7be9-11ee-b962-0242ac120002", 30.0D, Operation.ADDITION);
      this.m_19472_(Attributes.f_22283_, "2097503e-7be9-11ee-b962-0242ac120002", 0.2D, Operation.ADDITION);
      this.m_19472_(Attributes.f_22279_, "2097503e-7be9-11ee-b962-0242ac120002", 0.04D, Operation.ADDITION);
      this.m_19472_((Attribute)ForgeMod.SWIM_SPEED.get(), "2097503e-7be9-11ee-b962-0242ac120002", 1.0D, Operation.ADDITION);
   }

   public boolean m_6584_(int duration, int amplifier) {
      return false;
   }
}
