package com.github.manasmods.tensura.effect.skill;

import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.effect.template.MagicElementalEffect;
import com.github.manasmods.tensura.registry.particle.TensuraParticles;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import net.minecraft.core.particles.ParticleOptions;
import net.minecraft.world.effect.MobEffectCategory;
import net.minecraft.world.entity.LivingEntity;
import net.minecraftforge.event.entity.living.LivingHurtEvent;

public class MagicFlameEffect extends MagicElementalEffect {
   public MagicFlameEffect(MobEffectCategory pCategory, int pColor) {
      super(pCategory, pColor);
   }

   protected void doVisualEffect(LivingEntity entity) {
      TensuraParticleHelper.addParticlesAroundSelf(entity, (ParticleOptions)TensuraParticles.RED_FIRE.get());
      TensuraParticleHelper.addParticlesAroundSelf(entity, (ParticleOptions)TensuraParticles.RED_FIRE.get());
      TensuraParticleHelper.addParticlesAroundSelf(entity, (ParticleOptions)TensuraParticles.RED_FIRE.get());
   }

   public void onDamagingEntity(LivingEntity attacker, LivingHurtEvent e) {
      e.getEntity().m_20254_(10);
      if (DamageSourceHelper.isFireDamage(e.getSource())) {
         e.setAmount(e.getAmount() * 2.0F);
      }

   }
}
