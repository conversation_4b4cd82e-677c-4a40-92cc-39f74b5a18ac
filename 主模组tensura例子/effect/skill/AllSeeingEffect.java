package com.github.manasmods.tensura.effect.skill;

import com.github.manasmods.manascore.attribute.ManasCoreAttributes;
import com.github.manasmods.tensura.effect.template.SkillMobEffect;
import net.minecraft.world.effect.MobEffectCategory;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.attributes.AttributeModifier.Operation;
import net.minecraftforge.common.ForgeMod;

public class AllSeeingEffect extends SkillMobEffect {
   protected static final String All_SEEING = "14986d2c-7af9-11ee-b962-0242ac120002";

   public AllSeeingEffect(MobEffectCategory pCategory, int pColor) {
      super(pCategory, pColor);
      this.m_19472_(Attributes.f_22283_, "14986d2c-7af9-11ee-b962-0242ac120002", 0.1D, Operation.ADDITION);
      this.m_19472_(Attributes.f_22279_, "14986d2c-7af9-11ee-b962-0242ac120002", 0.02D, Operation.ADDITION);
      this.m_19472_((Attribute)ForgeMod.SWIM_SPEED.get(), "14986d2c-7af9-11ee-b962-0242ac120002", 0.5D, Operation.ADDITION);
      this.m_19472_((Attribute)ManasCoreAttributes.MINING_SPEED_MULTIPLIER.get(), "14986d2c-7af9-11ee-b962-0242ac120002", 0.2D, Operation.ADDITION);
   }

   public boolean m_6584_(int duration, int amplifier) {
      return false;
   }
}
