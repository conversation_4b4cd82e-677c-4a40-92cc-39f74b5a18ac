package com.github.manasmods.tensura.effect.skill.debuff;

import com.github.manasmods.tensura.effect.template.TensuraMobEffect;
import java.util.Collections;
import java.util.List;
import net.minecraft.world.effect.MobEffectCategory;
import net.minecraft.world.item.ItemStack;

public class SpatialBlockadeEffect extends TensuraMobEffect {
   public SpatialBlockadeEffect(MobEffectCategory pCategory, int pColor) {
      super(pCategory, pColor);
   }

   public boolean m_6584_(int pDuration, int pAmplifier) {
      return false;
   }

   public List<ItemStack> getCurativeItems() {
      return Collections.emptyList();
   }
}
