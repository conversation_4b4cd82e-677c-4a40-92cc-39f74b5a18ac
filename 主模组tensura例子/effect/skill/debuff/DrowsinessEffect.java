package com.github.manasmods.tensura.effect.skill.debuff;

import com.github.manasmods.tensura.capability.effects.TensuraEffectsCapability;
import com.github.manasmods.tensura.effect.template.DamageAction;
import com.github.manasmods.tensura.effect.template.TensuraMobEffect;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import net.minecraft.world.effect.MobEffectCategory;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.attributes.AttributeModifier.Operation;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.ItemStack;
import net.minecraftforge.common.ForgeMod;
import net.minecraftforge.event.entity.living.LivingHurtEvent;

public class DrowsinessEffect extends TensuraMobEffect implements DamageAction {
   protected static final String DROWSINESS = "b3e4bd8c-667b-11ee-8c99-0242ac120002";

   public DrowsinessEffect(MobEffectCategory pCategory, int pColor) {
      super(pCategory, pColor);
      this.m_19472_(Attributes.f_22279_, "b3e4bd8c-667b-11ee-8c99-0242ac120002", -0.25D, Operation.MULTIPLY_TOTAL);
      this.m_19472_(Attributes.f_22281_, "b3e4bd8c-667b-11ee-8c99-0242ac120002", -4.0D, Operation.ADDITION);
      this.m_19472_(Attributes.f_22283_, "b3e4bd8c-667b-11ee-8c99-0242ac120002", -0.10000000149011612D, Operation.MULTIPLY_TOTAL);
      this.m_19472_(Attributes.f_22288_, "b3e4bd8c-667b-11ee-8c99-0242ac120002", -0.25D, Operation.MULTIPLY_TOTAL);
      this.m_19472_((Attribute)ForgeMod.SWIM_SPEED.get(), "b3e4bd8c-667b-11ee-8c99-0242ac120002", -0.25D, Operation.MULTIPLY_TOTAL);
   }

   public void m_6742_(LivingEntity entity, int pAmplifier) {
      float damage = entity.m_21223_();
      Player source = TensuraEffectsCapability.getEffectSource(entity, this);
      if (source == null) {
         entity.m_6469_(TensuraDamageSources.DROWSY_DEATH, damage);
      } else {
         entity.m_6469_(TensuraDamageSources.drowsyDeath(source), damage);
      }

   }

   public void onBeingDamaged(LivingHurtEvent e) {
      float effectLevel = (float)(((MobEffectInstance)Objects.requireNonNull(e.getEntity().m_21124_(this))).m_19564_() + 1);
      e.setAmount(e.getAmount() * (1.0F + 0.2F * effectLevel));
   }

   public boolean m_6584_(int pDuration, int pAmplifier) {
      return pAmplifier >= 4;
   }

   public List<ItemStack> getCurativeItems() {
      return Collections.emptyList();
   }
}
