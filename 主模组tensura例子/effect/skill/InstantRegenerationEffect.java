package com.github.manasmods.tensura.effect.skill;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.ability.skill.extra.InfiniteRegenerationSkill;
import com.github.manasmods.tensura.capability.effects.TensuraEffectsCapability;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.effect.template.MobEffectHelper;
import com.github.manasmods.tensura.effect.template.SkillMobEffect;
import com.github.manasmods.tensura.registry.attribute.TensuraAttributeRegistry;
import com.github.manasmods.tensura.registry.skill.ExtraSkills;
import com.github.manasmods.tensura.registry.skill.UniqueSkills;
import com.github.manasmods.tensura.util.damage.TensuraDamageSource;
import java.util.Optional;
import net.minecraft.ChatFormatting;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.Style;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffectCategory;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.player.Player;

public class InstantRegenerationEffect extends SkillMobEffect {
   public InstantRegenerationEffect(MobEffectCategory pCategory, int pColor) {
      super(pCategory, pColor);
   }

   public void m_6742_(LivingEntity entity, int pAmplifier) {
      double maxHealth = (double)entity.m_21233_() - TensuraEffectsCapability.getSeverance(entity);
      if (!(maxHealth <= 0.0D)) {
         boolean failed = false;
         float lackedHealth = (float)maxHealth - entity.m_21223_();
         if (lackedHealth > 0.0F && !MobEffectHelper.shouldCancelHeal(entity)) {
            if (entity instanceof Player) {
               Player player = (Player)entity;
               float cost = pAmplifier == 0 && SkillUtils.isSkillToggled(entity, (ManasSkill)UniqueSkills.SURVIVOR.get()) ? 20.0F : 100.0F;
               Skill toggledSkill = pAmplifier > 0 ? (Skill)ExtraSkills.INFINITE_REGENERATION.get() : (Skill)ExtraSkills.ULTRASPEED_REGENERATION.get();
               Optional<ManasSkillInstance> instance = SkillAPI.getSkillsFrom(entity).getSkill(toggledSkill);
               if (instance.isPresent() && ((ManasSkillInstance)instance.get()).isMastered(entity)) {
                  cost = 60.0F;
               }

               double lackedMagicule = SkillHelper.outOfMagiculeStillConsume(player, (double)((int)(lackedHealth * cost)));
               if (lackedMagicule > 0.0D) {
                  lackedHealth = (float)((double)lackedHealth - lackedMagicule / (double)cost);
                  if (instance.isPresent() && ((ManasSkillInstance)instance.get()).isToggled()) {
                     ((ManasSkillInstance)instance.get()).setToggled(false);
                     SkillAPI.getSkillsFrom(entity).syncChanges();
                     failed = true;
                     player.m_21195_(this);
                     player.m_5661_(Component.m_237110_("tensura.skill.lack_magicule.toggled_off", new Object[]{toggledSkill.getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), false);
                  }
               }
            }

            entity.m_5634_(lackedHealth);
         }

         if (!failed) {
            this.healSHP(entity, pAmplifier);
         }

      }
   }

   private void healSHP(LivingEntity entity, int pAmplifier) {
      if (pAmplifier >= 1) {
         double SHP = TensuraEPCapability.getSpiritualHealth(entity);
         double maxSHP = entity.m_21133_((Attribute)TensuraAttributeRegistry.MAX_SPIRITUAL_HEALTH.get());
         double lackedSHP = maxSHP - SHP;
         if (!(lackedSHP <= 0.0D)) {
            if (lackedSHP > 0.0D) {
               if (entity instanceof Player) {
                  Player player = (Player)entity;
                  float cost = 120.0F;
                  Optional<ManasSkillInstance> instance = SkillAPI.getSkillsFrom(entity).getSkill((ManasSkill)ExtraSkills.INFINITE_REGENERATION.get());
                  if (instance.isPresent() && ((ManasSkillInstance)instance.get()).isMastered(entity)) {
                     cost = 80.0F;
                  }

                  double lackedMagicule = SkillHelper.outOfMagiculeStillConsume(player, (double)((int)(lackedSHP * (double)cost)));
                  if (lackedMagicule > 0.0D) {
                     lackedSHP -= lackedMagicule / (double)cost;
                     if (instance.isPresent() && ((ManasSkillInstance)instance.get()).isToggled()) {
                        ((ManasSkillInstance)instance.get()).setToggled(false);
                        SkillAPI.getSkillsFrom(entity).syncChanges();
                        player.m_21195_(this);
                        player.m_5661_(Component.m_237110_("tensura.skill.lack_magicule.toggled_off", new Object[]{((InfiniteRegenerationSkill)ExtraSkills.INFINITE_REGENERATION.get()).getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), false);
                     }
                  }
               }

               TensuraEPCapability.setSpiritualHealth(entity, Math.min(SHP + lackedSHP, maxSHP));
            }

         }
      }
   }

   public boolean m_6584_(int pDuration, int pAmplifier) {
      return pDuration % 10 == 0;
   }

   public static boolean canStopDeath(DamageSource source, LivingEntity entity) {
      if (source.m_19378_()) {
         return false;
      } else {
         if (source instanceof TensuraDamageSource) {
            TensuraDamageSource damageSource = (TensuraDamageSource)source;
            if ((double)damageSource.getIgnoreBarrier() >= 1.75D) {
               return false;
            }
         }

         return (double)entity.m_21233_() > TensuraEffectsCapability.getSeverance(entity);
      }
   }
}
