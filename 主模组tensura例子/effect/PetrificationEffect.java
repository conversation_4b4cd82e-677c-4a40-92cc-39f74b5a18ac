package com.github.manasmods.tensura.effect;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.capability.effects.TensuraEffectsCapability;
import com.github.manasmods.tensura.effect.template.TensuraMobEffect;
import com.github.manasmods.tensura.registry.skill.ResistanceSkills;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import java.util.Collections;
import java.util.List;
import net.minecraft.world.effect.MobEffectCategory;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.attributes.AttributeModifier.Operation;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.ItemStack;

public class PetrificationEffect extends TensuraMobEffect {
   protected static final String PETRIFICATION = "046fedcd-f652-4e11-a50f-f6864eae0fb3";

   public PetrificationEffect(MobEffectCategory pCategory, int pColor) {
      super(pCategory, pColor);
      this.m_19472_(Attributes.f_22279_, "046fedcd-f652-4e11-a50f-f6864eae0fb3", -0.25D, Operation.MULTIPLY_TOTAL);
   }

   public void m_6742_(LivingEntity entity, int pAmplifier) {
      if (SkillUtils.isSkillToggled(entity, (ManasSkill)ResistanceSkills.ABNORMAL_CONDITION_RESISTANCE.get())) {
         pAmplifier -= 2;
         if (pAmplifier < 3) {
            return;
         }
      }

      float damage = entity.m_21223_();
      Player source = TensuraEffectsCapability.getEffectSource(entity, this);
      if (source == null) {
         entity.m_6469_(TensuraDamageSources.PETRIFICATION, damage);
      } else {
         entity.m_6469_(TensuraDamageSources.petrification(source), damage);
      }

   }

   public boolean m_6584_(int pDuration, int pAmplifier) {
      return pAmplifier >= 3;
   }

   public List<ItemStack> getCurativeItems() {
      return Collections.emptyList();
   }
}
