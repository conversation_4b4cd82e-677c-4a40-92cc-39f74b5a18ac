package com.github.manasmods.tensura.effect;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.capability.effects.ITensuraEffectsCapability;
import com.github.manasmods.tensura.capability.effects.TensuraEffectsCapability;
import com.github.manasmods.tensura.effect.template.DamageAction;
import com.github.manasmods.tensura.effect.template.TensuraMobEffect;
import com.github.manasmods.tensura.handler.CapabilityHandler;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.items.TensuraConsumableItems;
import com.github.manasmods.tensura.registry.skill.ResistanceSkills;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import java.util.ArrayList;
import java.util.List;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectCategory;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.ai.attributes.AttributeMap;
import net.minecraft.world.entity.ai.attributes.AttributeModifier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.attributes.AttributeModifier.Operation;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.level.ItemLike;
import net.minecraftforge.event.entity.living.LivingHurtEvent;

public final class InfectionEffect extends TensuraMobEffect implements DamageAction {
   private static final String INFECTION_SPEED_UUID = "6ebbd768-c4d7-11ed-afa1-0242ac120002";
   private static final String INFECTION_ATTACK_DAMAGE_UUID = "6ebbdb14-c4d7-11ed-afa1-0242ac120002";

   public InfectionEffect(MobEffectCategory pCategory, int pColor) {
      super(pCategory, pColor);
   }

   public boolean m_6584_(int pDuration, int pAmplifier) {
      return pDuration % 60 == 0;
   }

   public double m_7048_(int pAmplifier, AttributeModifier pModifier) {
      return pModifier.m_22218_() * (double)pAmplifier;
   }

   public void setInfectionAge(LivingEntity pLivingEntity, double d) {
      TensuraEffectsCapability.getFrom(pLivingEntity).ifPresent((cap) -> {
         cap.setInfectionAge(d);
         TensuraEffectsCapability.sync(pLivingEntity);
      });
   }

   public double getInfectionAge(LivingEntity pLivingEntity) {
      ITensuraEffectsCapability effectsCapability = (ITensuraEffectsCapability)CapabilityHandler.getCapability(pLivingEntity, TensuraEffectsCapability.CAPABILITY);
      return effectsCapability == null ? 0.0D : effectsCapability.getInfectionAge();
   }

   public void m_6385_(LivingEntity pLivingEntity, AttributeMap pAttributeMap, int pAmplifier) {
      if (pAmplifier == 0) {
         this.setInfectionAge(pLivingEntity, 0.0D);
      }

      if (pAmplifier >= 1) {
         this.m_19472_(Attributes.f_22279_, "6ebbd768-c4d7-11ed-afa1-0242ac120002", -0.15000000596046448D, Operation.MULTIPLY_TOTAL);
         this.m_19472_(Attributes.f_22281_, "6ebbdb14-c4d7-11ed-afa1-0242ac120002", -2.0D, Operation.ADDITION);
      }

      super.m_6385_(pLivingEntity, pAttributeMap, pAmplifier);
   }

   public void m_6742_(LivingEntity entity, int pAmplifier) {
      if (!(entity.m_21223_() <= 0.0F)) {
         if (SkillUtils.isSkillToggled(entity, (ManasSkill)ResistanceSkills.ABNORMAL_CONDITION_RESISTANCE.get())) {
            pAmplifier -= 2;
            if (pAmplifier < 0) {
               return;
            }
         }

         if (pAmplifier >= 4) {
            this.dealInfectionDamage(entity, entity.m_21223_());
         } else if (pAmplifier >= 1) {
            this.dealInfectionDamage(entity, 2.0F * (float)pAmplifier);
         }

         this.increaseInfection(entity, pAmplifier);
      }
   }

   public void dealInfectionDamage(LivingEntity pLivingEntity, float damage) {
      Player source = TensuraEffectsCapability.getEffectSource(pLivingEntity, this);
      if (source == null) {
         pLivingEntity.m_6469_(TensuraDamageSources.INFECTION, damage);
      } else {
         pLivingEntity.m_6469_(TensuraDamageSources.infection(source), damage);
      }

   }

   private void increaseInfection(LivingEntity entity, int pAmplifier) {
      int maxInfectionAge = 10;
      if (pAmplifier == 1) {
         maxInfectionAge = 5;
      }

      if (pAmplifier > 1) {
         maxInfectionAge = 3;
      }

      if (this.getInfectionAge(entity) < (double)maxInfectionAge) {
         this.setInfectionAge(entity, this.getInfectionAge(entity) + 1.0D);
         if (pAmplifier > 1) {
            entity.m_7292_(new MobEffectInstance(MobEffects.f_19604_, 200, 0, false, false, false));
            if (pAmplifier > 2) {
               entity.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.FRAGILITY.get(), 200, pAmplifier - 2, false, false, false));
            }
         }
      } else {
         this.setInfectionAge(entity, 0.0D);
         Player source = TensuraEffectsCapability.getEffectSource(entity, this);
         entity.m_21195_(this);
         SkillHelper.checkThenAddEffectSource(entity, source, (MobEffect)TensuraMobEffects.INFECTION.get(), 360, pAmplifier + 1, true, false, true, true);
      }

   }

   public List<ItemStack> getCurativeItems() {
      ArrayList<ItemStack> itemStacks = new ArrayList();
      itemStacks.add(new ItemStack((ItemLike)TensuraConsumableItems.HOLY_MILK.get()));
      itemStacks.add(new ItemStack((ItemLike)TensuraConsumableItems.HOLY_MILK_BUCKET.get()));
      return itemStacks;
   }

   public void onDamagingEntity(LivingEntity attacker, LivingHurtEvent e) {
      if (!e.getSource().m_19387_() && e.getSource().m_7640_() == attacker) {
         LivingEntity entity = e.getEntity();
         boolean infectingAttacker = entity.m_21023_(this) && !attacker.m_21023_(this);
         boolean infectingAttacked = !entity.m_21023_(this) && attacker.m_21023_(this);
         Player source;
         if (infectingAttacker) {
            source = TensuraEffectsCapability.getEffectSource(entity, this);
            SkillHelper.checkThenAddEffectSource(attacker, source, this, 900, 0, false, false, true);
         } else if (infectingAttacked) {
            source = TensuraEffectsCapability.getEffectSource(attacker, this);
            SkillHelper.checkThenAddEffectSource(entity, source, this, 900, 0, false, false, true);
         }

      }
   }
}
