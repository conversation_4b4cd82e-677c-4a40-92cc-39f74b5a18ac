package com.github.manasmods.tensura.effect;

import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.capability.effects.TensuraEffectsCapability;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.effect.template.DamageAction;
import com.github.manasmods.tensura.effect.template.TensuraMobEffect;
import com.github.manasmods.tensura.registry.attribute.TensuraAttributeRegistry;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;
import java.util.Map.Entry;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectCategory;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.Mob;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.ai.attributes.AttributeInstance;
import net.minecraft.world.entity.ai.attributes.AttributeMap;
import net.minecraft.world.entity.ai.attributes.AttributeModifier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.attributes.AttributeModifier.Operation;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.ItemStack;
import net.minecraftforge.common.ForgeMod;
import net.minecraftforge.event.entity.living.LivingHurtEvent;

public class RampageEffect extends TensuraMobEffect implements DamageAction {
   protected static final String BLESS = "f3c3e634-6bfe-11ee-b962-0242ac120002";

   public RampageEffect(MobEffectCategory pCategory, int pColor) {
      super(pCategory, pColor);
      this.m_19472_(Attributes.f_22284_, "f3c3e634-6bfe-11ee-b962-0242ac120002", 10.0D, Operation.ADDITION);
      this.m_19472_(Attributes.f_22281_, "f3c3e634-6bfe-11ee-b962-0242ac120002", 60.0D, Operation.ADDITION);
      this.m_19472_(Attributes.f_22283_, "f3c3e634-6bfe-11ee-b962-0242ac120002", 0.3D, Operation.ADDITION);
      this.m_19472_(Attributes.f_22279_, "f3c3e634-6bfe-11ee-b962-0242ac120002", 0.1D, Operation.ADDITION);
      this.m_19472_(Attributes.f_22278_, "f3c3e634-6bfe-11ee-b962-0242ac120002", 0.2D, Operation.ADDITION);
      this.m_19472_((Attribute)ForgeMod.SWIM_SPEED.get(), "f3c3e634-6bfe-11ee-b962-0242ac120002", 1.0D, Operation.ADDITION);
   }

   public void m_6385_(LivingEntity pLivingEntity, AttributeMap pAttributeMap, int pAmplifier) {
      Player source = TensuraEffectsCapability.getEffectSource(pLivingEntity, this);
      if (pLivingEntity.equals(source)) {
         super.m_6385_(pLivingEntity, pAttributeMap, pAmplifier);
      } else if (source != null) {
         Iterator var5 = this.m_19485_().entrySet().iterator();

         while(var5.hasNext()) {
            Entry<Attribute, AttributeModifier> entry = (Entry)var5.next();
            AttributeInstance instance = pAttributeMap.m_22146_((Attribute)entry.getKey());
            if (instance != null && instance.m_22099_() != Attributes.f_22284_ && instance.m_22099_() != Attributes.f_22278_) {
               AttributeModifier attributemodifier = (AttributeModifier)entry.getValue();
               instance.m_22130_(attributemodifier);
               double amount = this.m_7048_(pAmplifier, attributemodifier);
               if (instance.m_22099_() == Attributes.f_22281_) {
                  amount /= 20.0D;
               }

               instance.m_22125_(new AttributeModifier(attributemodifier.m_22209_(), this.m_19481_() + " " + pAmplifier, amount, attributemodifier.m_22217_()));
            }
         }
      }

   }

   public void m_6742_(LivingEntity pLivingEntity, int pAmplifier) {
      if (pLivingEntity instanceof Mob) {
         Mob mob = (Mob)pLivingEntity;
         if (mob.m_5448_() != null) {
            return;
         }

         if (mob.m_21023_((MobEffect)TensuraMobEffects.INSPIRATION.get())) {
            return;
         }

         List<LivingEntity> list = mob.m_9236_().m_6443_(LivingEntity.class, mob.m_20191_().m_82400_(15.0D), (targetx) -> {
            return this.targetFilter(mob, targetx, pAmplifier);
         });
         if (list.isEmpty()) {
            return;
         }

         Iterator var9 = list.iterator();
         if (var9.hasNext()) {
            LivingEntity target = (LivingEntity)var9.next();
            mob.m_6710_(target);
         }
      } else if (pLivingEntity instanceof Player) {
         Player player = (Player)pLivingEntity;
         MobEffectInstance instance = player.m_21124_(this);
         if (instance == null || instance.m_19557_() > 200) {
            return;
         }

         if (pLivingEntity.m_21223_() > 1.0F) {
            player.m_6469_(TensuraDamageSources.INSANITY, Math.min(player.m_21233_() * 0.1F, player.m_21223_() - 1.0F));
         }

         if (TensuraEPCapability.getSpiritualHealth(player) > 1.0D) {
            double maxSHP = player.m_21133_((Attribute)TensuraAttributeRegistry.MAX_SPIRITUAL_HEALTH.get());
            DamageSourceHelper.directSpiritualHurt(player, player, (float)Math.min(maxSHP * 0.10000000149011612D, TensuraEPCapability.getSpiritualHealth(player) - 1.0D));
         }
      }

   }

   public void onDamagingEntity(LivingEntity attacker, LivingHurtEvent e) {
      MobEffectInstance instance = attacker.m_21124_(this);
      if (instance != null) {
         int duration = Math.max(instance.m_19557_() + 60, 600);
         Player source = TensuraEffectsCapability.getEffectSource(attacker, this);
         SkillHelper.checkThenAddEffectSource(attacker, source, this, duration, instance.m_19564_(), instance.m_19571_(), instance.m_19572_(), instance.m_19575_(), true);
      }
   }

   public boolean m_6584_(int pDuration, int pAmplifier) {
      return pDuration % 20 == 0;
   }

   public List<ItemStack> getCurativeItems() {
      return Collections.emptyList();
   }

   private boolean targetFilter(Mob mob, LivingEntity target, int level) {
      if (mob == target) {
         return false;
      } else if (!target.m_6084_()) {
         return false;
      } else {
         if (target instanceof Player) {
            Player player = (Player)target;
            if (player.m_7500_() || player.m_5833_()) {
               return false;
            }
         }

         return !target.m_7307_(mob) || level >= 1;
      }
   }
}
