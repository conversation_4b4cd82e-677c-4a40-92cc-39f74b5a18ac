package com.github.manasmods.tensura.effect.battlewill;

import com.github.manasmods.tensura.effect.template.SkillMobEffect;
import net.minecraft.world.effect.MobEffectCategory;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.attributes.AttributeModifier.Operation;
import net.minecraftforge.common.ForgeMod;

public class OgreGuillotineEffect extends SkillMobEffect {
   protected static final String GUILLOTINE = "35c92512-99db-11ee-b9d1-0242ac120002";

   public OgreGuillotineEffect(MobEffectCategory pCategory, int pColor) {
      super(pCategory, pColor);
      this.m_19472_(Attributes.f_22281_, "35c92512-99db-11ee-b9d1-0242ac120002", 0.5D, Operation.MULTIPLY_TOTAL);
      this.m_19472_((Attribute)ForgeMod.ATTACK_RANGE.get(), "35c92512-99db-11ee-b9d1-0242ac120002", 0.5D, Operation.MULTIPLY_BASE);
   }
}
