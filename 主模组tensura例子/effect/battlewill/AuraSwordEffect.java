package com.github.manasmods.tensura.effect.battlewill;

import com.github.manasmods.tensura.effect.template.DamageAction;
import com.github.manasmods.tensura.effect.template.SkillMobEffect;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import net.minecraft.world.effect.MobEffectCategory;
import net.minecraft.world.entity.LivingEntity;
import net.minecraftforge.event.entity.living.LivingHurtEvent;

public class AuraSwordEffect extends SkillMobEffect implements DamageAction {
   public AuraSwordEffect(MobEffectCategory pCategory, int pColor) {
      super(pCategory, pColor);
   }

   public void onDamagingEntity(LivingEntity attacker, LivingHurtEvent e) {
      if (!e.getSource().m_19387_() && e.getSource().m_7640_() == attacker) {
         e.setAmount(e.getAmount() + DamageSourceHelper.getMainWeaponDamage(attacker, e.getEntity()));
      }
   }
}
