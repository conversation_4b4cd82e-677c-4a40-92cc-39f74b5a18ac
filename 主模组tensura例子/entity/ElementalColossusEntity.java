package com.github.manasmods.tensura.entity;

import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.api.entity.ai.LeapWithStrengthGoal;
import com.github.manasmods.tensura.api.entity.ai.WanderingFollowOwnerGoal;
import com.github.manasmods.tensura.api.entity.subclass.IGiantMob;
import com.github.manasmods.tensura.api.entity.subclass.ITensuraMount;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.entity.template.TensuraTamableEntity;
import com.github.manasmods.tensura.registry.dimensions.TensuraDimensions;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.items.TensuraMaterialItems;
import com.github.manasmods.tensura.registry.items.TensuraMobDropItems;
import com.github.manasmods.tensura.registry.sound.TensuraSoundEvents;
import com.github.manasmods.tensura.util.TensuraAdvancementsHelper;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import com.github.manasmods.tensura.world.savedata.LabyrinthSaveData;
import java.util.Iterator;
import java.util.List;
import javax.annotation.Nullable;
import net.minecraft.core.BlockPos;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.chat.Component;
import net.minecraft.network.syncher.EntityDataAccessor;
import net.minecraft.network.syncher.EntityDataSerializers;
import net.minecraft.network.syncher.SynchedEntityData;
import net.minecraft.server.level.ServerBossEvent;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.util.Mth;
import net.minecraft.world.DifficultyInstance;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.InteractionResult;
import net.minecraft.world.BossEvent.BossBarColor;
import net.minecraft.world.BossEvent.BossBarOverlay;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.world.entity.AgeableMob;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityDimensions;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.Mob;
import net.minecraft.world.entity.MobSpawnType;
import net.minecraft.world.entity.PlayerRideableJumping;
import net.minecraft.world.entity.Pose;
import net.minecraft.world.entity.SpawnGroupData;
import net.minecraft.world.entity.Entity.RemovalReason;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.ai.attributes.AttributeSupplier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.goal.FloatGoal;
import net.minecraft.world.entity.ai.goal.LookAtPlayerGoal;
import net.minecraft.world.entity.ai.goal.MeleeAttackGoal;
import net.minecraft.world.entity.ai.goal.MoveToBlockGoal;
import net.minecraft.world.entity.ai.goal.SitWhenOrderedToGoal;
import net.minecraft.world.entity.ai.goal.target.NearestAttackableTargetGoal;
import net.minecraft.world.entity.ai.goal.target.NonTameRandomTargetGoal;
import net.minecraft.world.entity.ai.goal.target.ResetUniversalAngerTargetGoal;
import net.minecraft.world.entity.animal.Animal;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.entity.projectile.Projectile;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.level.ExplosionDamageCalculator;
import net.minecraft.world.level.GameRules;
import net.minecraft.world.level.ItemLike;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.LevelReader;
import net.minecraft.world.level.ServerLevelAccessor;
import net.minecraft.world.level.Explosion.BlockInteraction;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.phys.AABB;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.common.ForgeHooks;
import net.minecraftforge.common.ForgeMod;
import software.bernie.geckolib3.core.AnimationState;
import software.bernie.geckolib3.core.IAnimatable;
import software.bernie.geckolib3.core.PlayState;
import software.bernie.geckolib3.core.builder.AnimationBuilder;
import software.bernie.geckolib3.core.builder.ILoopType.EDefaultLoopTypes;
import software.bernie.geckolib3.core.controller.AnimationController;
import software.bernie.geckolib3.core.event.predicate.AnimationEvent;
import software.bernie.geckolib3.core.manager.AnimationData;
import software.bernie.geckolib3.core.manager.AnimationFactory;
import software.bernie.geckolib3.util.GeckoLibUtil;

public class ElementalColossusEntity extends TensuraTamableEntity implements IAnimatable, IGiantMob, ITensuraMount, PlayerRideableJumping {
   private static final EntityDataAccessor<Integer> MISC_ANIMATION;
   private static final EntityDataAccessor<Integer> LEAP_PHASE;
   private final ServerBossEvent bossEvent;
   public int leapingTicks;
   public int miscAnimationTicks;
   protected float playerJumpPendingScale;
   protected boolean playerJumping;
   private final AnimationFactory factory;

   public ElementalColossusEntity(EntityType<? extends ElementalColossusEntity> type, Level level) {
      super(type, level);
      this.bossEvent = (ServerBossEvent)(new ServerBossEvent(this.m_5446_(), BossBarColor.PINK, BossBarOverlay.NOTCHED_20)).m_7005_(true);
      this.leapingTicks = 0;
      this.miscAnimationTicks = 0;
      this.factory = GeckoLibUtil.createFactory(this);
      this.f_19793_ = 3.0F;
      this.f_21364_ = 100;
      this.f_21365_ = new ElementalColossusEntity.ColossusLookControl();
      this.f_21342_ = new ElementalColossusEntity.ColossusMoveControl();
   }

   public ElementalColossusEntity(ServerLevel level, Vec3 pos, MobSpawnType spawnType) {
      super((EntityType)TensuraEntityTypes.ELEMENTAL_COLOSSUS.get(), level);
      this.bossEvent = (ServerBossEvent)(new ServerBossEvent(this.m_5446_(), BossBarColor.PINK, BossBarOverlay.NOTCHED_20)).m_7005_(true);
      this.leapingTicks = 0;
      this.miscAnimationTicks = 0;
      this.factory = GeckoLibUtil.createFactory(this);
      this.m_146884_(pos);
      this.m_6518_(level, level.m_6436_(this.m_20183_()), spawnType, (SpawnGroupData)null, (CompoundTag)null);
   }

   public static AttributeSupplier setAttributes() {
      return Mob.m_21552_().m_22268_(Attributes.f_22284_, 40.0D).m_22268_(Attributes.f_22276_, 600.0D).m_22268_(Attributes.f_22277_, 64.0D).m_22268_(Attributes.f_22281_, 50.0D).m_22268_(Attributes.f_22282_, 2.0D).m_22268_(Attributes.f_22279_, 0.25D).m_22268_(Attributes.f_22278_, 1.0D).m_22268_(Attributes.f_22288_, 2.5D).m_22268_((Attribute)ForgeMod.SWIM_SPEED.get(), 1.0D).m_22268_((Attribute)ForgeMod.ATTACK_RANGE.get(), 2.0D).m_22265_();
   }

   protected void m_8099_() {
      this.f_21345_.m_25352_(0, new FloatGoal(this));
      this.f_21345_.m_25352_(1, new SitWhenOrderedToGoal(this));
      this.f_21345_.m_25352_(2, new ElementalColossusEntity.ColossusSleepGoal(this, 1.0D));
      this.f_21345_.m_25352_(3, new ElementalColossusEntity.LeapJumpGoal(this));
      this.f_21345_.m_25352_(4, new WanderingFollowOwnerGoal(this, 1.2D, 20.0F, 5.0F, false));
      this.f_21345_.m_25352_(5, new TensuraTamableEntity.WanderAroundPosGoal(this) {
         public boolean m_8036_() {
            return !ElementalColossusEntity.this.m_21824_() ? false : super.m_8036_();
         }
      });
      this.f_21345_.m_25352_(6, new LookAtPlayerGoal(this, Player.class, 10.0F));
      this.f_21346_.m_25352_(1, new TensuraTamableEntity.TensuraOwnerHurtByTargetGoal(this));
      this.f_21346_.m_25352_(2, new TensuraTamableEntity.TensuraOwnerHurtTargetGoal(this));
      this.f_21346_.m_25352_(3, new ElementalColossusEntity.ColossusAttackGoal(this, 2.0D, false));
      this.f_21346_.m_25352_(4, (new TensuraTamableEntity.TensuraHurtByTargetGoal(this) {
         public boolean m_8045_() {
            LivingEntity target = this.f_26135_.m_5448_();
            if (target == null) {
               target = this.f_26137_;
            }

            return LabyrinthSaveData.isEntityPassedColossus(target) ? false : super.m_8045_();
         }
      }).m_26044_(new Class[0]));
      this.f_21346_.m_25352_(5, new NearestAttackableTargetGoal(this, Player.class, 10, true, false, this::m_21674_));
      this.f_21346_.m_25352_(5, new NonTameRandomTargetGoal<LivingEntity>(this, LivingEntity.class, true, this::shouldAttack) {
         public void m_8056_() {
            super.m_8056_();
            ElementalColossusEntity colossus = ElementalColossusEntity.this;
            Level var3 = colossus.f_19853_;
            if (var3 instanceof ServerLevel) {
               ServerLevel serverLevel = (ServerLevel)var3;
               colossus.m_21666_(serverLevel, false);
            }

         }
      });
      this.f_21346_.m_25352_(8, new ResetUniversalAngerTargetGoal(this, true));
   }

   public boolean shouldAttack(LivingEntity entity) {
      if (this.m_21826_() != null) {
         if (entity instanceof Mob) {
            Mob mob = (Mob)entity;
            return mob.m_5448_() == this.m_21826_();
         } else {
            return this.m_21826_().m_21214_() == entity || this.m_21826_().m_21188_() == entity;
         }
      } else {
         if (entity instanceof Player) {
            Player player = (Player)entity;
            if (player.m_7500_() || player.m_5833_()) {
               return false;
            }
         }

         return !LabyrinthSaveData.isEntityPassedColossus(entity);
      }
   }

   protected void m_8097_() {
      super.m_8097_();
      this.f_19804_.m_135372_(MISC_ANIMATION, 0);
      this.f_19804_.m_135372_(LEAP_PHASE, 0);
   }

   public void m_7380_(CompoundTag compound) {
      super.m_7380_(compound);
      compound.m_128405_("MiscAnimation", this.getMiscAnimation());
      compound.m_128405_("LeapPhase", this.getLeapPhase());
      if (this.m_8077_()) {
         this.bossEvent.m_6456_(this.m_5446_());
      }

   }

   public void m_7378_(CompoundTag compound) {
      super.m_7378_(compound);
      this.f_19804_.m_135381_(MISC_ANIMATION, compound.m_128451_("MiscAnimation"));
      this.setLeapPhase(compound.m_128451_("LeapPhase"));
   }

   public int getMiscAnimation() {
      return (Integer)this.f_19804_.m_135370_(MISC_ANIMATION);
   }

   public void setMiscAnimation(int animation) {
      this.f_19804_.m_135381_(MISC_ANIMATION, animation);
   }

   public int getLeapPhase() {
      return (Integer)this.f_19804_.m_135370_(LEAP_PHASE);
   }

   public void setLeapPhase(int phase) {
      this.f_19804_.m_135381_(LEAP_PHASE, phase);
   }

   public void setSleeping(boolean sleeping) {
      this.f_19804_.m_135381_(SLEEPING, sleeping);
      this.m_6210_();
      if (!sleeping) {
         this.setMiscAnimation(6);
      } else {
         this.m_21662_();
         this.m_146922_(180.0F);
         this.f_19859_ = this.m_146908_();
         this.m_146926_(0.0F);
         this.m_19915_(180.0F, 0.0F);
         this.f_20883_ = this.m_146908_();
         this.f_20885_ = this.f_20883_;
      }

   }

   public void m_7350_(EntityDataAccessor<?> pKey) {
      if (f_21798_.equals(pKey)) {
         this.m_6210_();
      }

      super.m_7350_(pKey);
   }

   public EntityDimensions m_6972_(Pose pPose) {
      EntityDimensions entitydimensions = super.m_6972_(pPose);
      if (this.m_5803_()) {
         return entitydimensions.m_20390_(1.0F, 1.5F);
      } else {
         return !this.m_21827_() && !this.m_21825_() ? entitydimensions : entitydimensions.m_20390_(1.0F, 0.75F);
      }
   }

   protected boolean m_8028_() {
      return false;
   }

   public boolean m_6785_(double pDistanceToClosestPlayer) {
      return false;
   }

   protected boolean removeWhenNoAction() {
      return false;
   }

   public boolean m_6783_(double pDistance) {
      return super.m_6783_(pDistance) || pDistance < 1024.0D;
   }

   public boolean m_5957_() {
      return false;
   }

   public boolean m_7848_(Animal pOtherAnimal) {
      return false;
   }

   public ElementalColossusEntity getBreedOffspring(ServerLevel pLevel, AgeableMob pOtherParent) {
      return null;
   }

   public boolean m_7327_(Entity pEntity) {
      boolean flag = super.m_7327_(pEntity);
      if (flag && this.getMiscAnimation() == 0) {
         this.setMiscAnimation(1);
      }

      return flag;
   }

   public boolean canSleep() {
      return true;
   }

   protected void m_7625_(DamageSource pDamageSource, boolean pAttackedRecently) {
      if (this.getSpawnType() != MobSpawnType.TRIGGERED) {
         super.m_7625_(pDamageSource, pAttackedRecently);
      }
   }

   public void m_6593_(@Nullable Component pName) {
      super.m_6593_(pName);
      this.bossEvent.m_6456_(this.m_5446_());
   }

   public void m_6457_(ServerPlayer pPlayer) {
      super.m_6457_(pPlayer);
      if (!this.m_21824_()) {
         this.bossEvent.m_6543_(pPlayer);
      }
   }

   public void m_6452_(ServerPlayer pPlayer) {
      super.m_6452_(pPlayer);
      this.bossEvent.m_6539_(pPlayer);
   }

   protected void m_8024_() {
      super.m_8024_();
      this.bossEvent.m_142711_(this.m_21223_() / this.m_21233_());
   }

   public void m_8119_() {
      super.m_8119_();
      this.targetingMovementHelper();
      if (this.getLeapPhase() != 0) {
         ++this.leapingTicks;
         if (this.getLeapPhase() == 1 && this.leapingTicks >= 20) {
            this.setLeapPhase(2);
            this.leapingTicks = 0;
         } else if (this.getLeapPhase() == 2 && (this.m_20096_() || this.m_20072_())) {
            this.setLeapPhase(0);
            this.leapingTicks = 0;
         } else if (this.getLeapPhase() == 3 && this.leapingTicks >= 10) {
            this.setLeapPhase(0);
            this.leapingTicks = 0;
         }
      }

      LivingEntity target;
      if (this.getMiscAnimation() != 0) {
         ++this.miscAnimationTicks;
         if (!this.m_6084_()) {
            return;
         }

         if (this.getMiscAnimation() == 1 && this.miscAnimationTicks == 10) {
            target = this.m_5448_();
            if (target != null) {
               this.m_7327_(target);
               this.m_9236_().m_6263_((Player)null, this.m_20185_(), this.m_20186_(), this.m_20189_(), SoundEvents.f_12057_, SoundSource.NEUTRAL, 1.0F, 1.0F);
            }
         } else if (this.getMiscAnimation() == 2 && this.miscAnimationTicks >= 10 && this.miscAnimationTicks <= 20) {
            this.spinAttack();
            this.m_9236_().m_6263_((Player)null, this.m_20185_(), this.m_20186_(), this.m_20189_(), SoundEvents.f_12317_, SoundSource.NEUTRAL, 1.0F, 1.0F);
         } else if (this.getMiscAnimation() == 3 && this.miscAnimationTicks == 10) {
            target = this.m_5448_();
            if (target != null) {
               target.m_6469_(DamageSource.m_19370_(this), (float)(this.m_21133_(Attributes.f_22281_) * 0.5D));
               if (target.m_21223_() >= 1.0F && target.m_20270_(this) < 5.0F) {
                  target.m_7998_(this, true);
               }

               this.m_9236_().m_6263_((Player)null, this.m_20185_(), this.m_20186_(), this.m_20189_(), SoundEvents.f_12057_, SoundSource.NEUTRAL, 1.0F, 1.0F);
            }
         } else if (this.getMiscAnimation() == 4 && this.miscAnimationTicks >= 5 && this.isHoldingTargets()) {
            Iterator var1 = this.m_20197_().iterator();

            while(var1.hasNext()) {
               Entity target = (Entity)var1.next();
               if (target != this.getControllingPassenger()) {
                  target.m_19877_();
                  Vec3 throwVec = this.m_20252_(10.0F).m_82490_(10.0D);
                  LivingEntity var6 = this.m_21826_();
                  if (var6 instanceof Player) {
                     Player owner = (Player)var6;
                     var6 = this.getControllingPassenger();
                     if (var6 instanceof Player) {
                        Player controller = (Player)var6;
                        if (controller.equals(owner)) {
                           throwVec = owner.m_20252_(10.0F).m_82490_(7.0D);
                        }
                     }
                  }

                  target.m_20256_(target.m_20184_().m_82520_(throwVec.m_7096_(), 1.0D + throwVec.m_7098_(), throwVec.m_7094_()));
                  target.m_6469_(DamageSource.m_19370_(this), (float)(this.m_21133_(Attributes.f_22281_) * 2.0D));
                  target.f_19864_ = true;
               }
            }

            this.m_9236_().m_6263_((Player)null, this.m_20185_(), this.m_20186_(), this.m_20189_(), SoundEvents.f_12317_, SoundSource.NEUTRAL, 1.0F, 1.0F);
         }

         if (this.miscAnimationTicks >= this.getAnimationTick(this.getMiscAnimation())) {
            this.setMiscAnimation(0);
            this.miscAnimationTicks = 0;
         }
      }

      if (!this.f_19853_.m_5776_()) {
         target = this.getControllingPassenger();
         if (!this.m_21824_() || target != null && this.m_21830_(target)) {
            this.breakBlocks(this, 1.0F, false);
         }

      }
   }

   public void mountAbility(Player rider) {
      if (this.getLeapPhase() == 0 || this.getMiscAnimation() == 0) {
         if (this.isHoldingTargets()) {
            this.setMiscAnimation(4);
         } else {
            LivingEntity target = SkillHelper.getTargetingEntity(rider, 8.0D, false);
            if (target != null && target.m_20270_(this) <= 5.0F) {
               this.m_6710_((LivingEntity)null);
               this.setMiscAnimation(3);
               target.m_7998_(this, true);
               this.m_9236_().m_6263_((Player)null, this.m_20185_(), this.m_20186_(), this.m_20189_(), SoundEvents.f_12057_, SoundSource.NEUTRAL, 1.0F, 1.0F);
            } else {
               this.setMiscAnimation(2);
            }
         }

      }
   }

   private int getAnimationTick(int miscAnimation) {
      byte var10000;
      switch(miscAnimation) {
      case 2:
         var10000 = 25;
         break;
      case 3:
      default:
         var10000 = 20;
         break;
      case 4:
         var10000 = 15;
         break;
      case 5:
         var10000 = 40;
      }

      return var10000;
   }

   public void spinAttack() {
      AABB aabb = this.m_20191_().m_82400_(4.0D);
      List<LivingEntity> livingEntityList = this.f_19853_.m_6443_(LivingEntity.class, aabb, (entity) -> {
         return !entity.m_7307_(this) && entity != this.m_21826_() && !entity.equals(this);
      });
      if (!livingEntityList.isEmpty()) {
         Iterator var3 = livingEntityList.iterator();

         while(var3.hasNext()) {
            LivingEntity target = (LivingEntity)var3.next();
            double damageMultiplier = 1.5D;
            if (target.m_6469_(DamageSource.m_19370_(this), (float)(this.m_21133_(Attributes.f_22281_) * damageMultiplier))) {
               if (target.m_21223_() >= 1.0F) {
                  SkillHelper.knockBack(this, target, 2.0F);
               }
            } else if (target.m_21223_() >= 1.0F) {
               SkillHelper.knockBack(this, target, 1.5F);
            }
         }

      }
   }

   protected void sleepHandler() {
      if (!this.m_9236_().m_5776_()) {
         if (this.m_5803_()) {
            if (this.m_5448_() != null || this.m_20160_() || this.m_20159_() || this.shouldFollowOwner()) {
               this.setSleeping(false);
            }

            if (this.f_19797_ % 20 == 0) {
               this.m_5634_(30.0F);
            }
         } else if (this.m_21827_() && this.f_19797_ % 20 == 0) {
            this.m_5634_(30.0F);
         } else if (this.f_19797_ % 40 == 0) {
            this.m_5634_(10.0F);
         }

      }
   }

   public void m_8107_() {
      super.m_8107_();
      if (this.getMiscAnimation() == 6) {
         this.f_20899_ = false;
         this.f_20900_ = 0.0F;
         this.f_20902_ = 0.0F;
      }

   }

   public InteractionResult m_6071_(Player player, InteractionHand hand) {
      ItemStack itemstack = player.m_21120_(hand);
      InteractionResult eating = this.handleEating(player, hand, itemstack);
      if (eating.m_19077_()) {
         return eating;
      } else if (!this.f_19853_.f_46443_) {
         if (this.m_21824_() && this.m_21830_(player)) {
            if (player.m_36341_()) {
               this.commanding(player);
            } else if (player.m_146895_() == null) {
               this.m_21839_(false);
               this.setWandering(false);
               player.m_7998_(this, true);
            }

            return InteractionResult.SUCCESS;
         } else {
            return super.m_6071_(player, hand);
         }
      } else {
         boolean flag = this.m_21830_(player) || this.m_21824_();
         return flag ? InteractionResult.CONSUME : InteractionResult.PASS;
      }
   }

   public InteractionResult handleEating(Player pPlayer, InteractionHand hand, ItemStack itemstack) {
      if (this.m_6898_(itemstack) && this.m_21223_() < this.m_21233_()) {
         if (itemstack.m_150930_((Item)TensuraMaterialItems.LOW_MAGISTEEL_INGOT.get())) {
            this.m_5634_(100.0F);
         } else if (itemstack.m_150930_((Item)TensuraMaterialItems.HIGH_MAGISTEEL_INGOT.get())) {
            this.m_5634_(300.0F);
         } else if (itemstack.m_150930_((Item)TensuraMaterialItems.PURE_MAGISTEEL_INGOT.get())) {
            this.m_5634_(500.0F);
         }

         if (!pPlayer.m_7500_()) {
            itemstack.m_41774_(1);
         }

         this.m_8035_();
         this.m_9236_().m_6269_((Player)null, this, SoundEvents.f_12009_, SoundSource.NEUTRAL, 1.0F, 1.0F);
         return InteractionResult.SUCCESS;
      } else {
         return InteractionResult.PASS;
      }
   }

   public boolean m_6898_(ItemStack pStack) {
      if (pStack.m_150930_((Item)TensuraMaterialItems.LOW_MAGISTEEL_INGOT.get())) {
         return true;
      } else {
         return pStack.m_150930_((Item)TensuraMaterialItems.HIGH_MAGISTEEL_INGOT.get()) ? true : pStack.m_150930_((Item)TensuraMaterialItems.PURE_MAGISTEEL_INGOT.get());
      }
   }

   public boolean m_7132_() {
      return this.m_20160_();
   }

   public double getCustomJump() {
      return this.m_21133_(Attributes.f_22288_);
   }

   public boolean m_6146_() {
      return true;
   }

   @Nullable
   public LivingEntity getControllingPassenger() {
      Iterator var1 = this.m_20197_().iterator();

      while(var1.hasNext()) {
         Entity passenger = (Entity)var1.next();
         if (passenger instanceof Player) {
            Player player = (Player)passenger;
            if (this.m_21830_(player)) {
               return player;
            }
         }
      }

      return null;
   }

   protected boolean isHoldingTargets() {
      if (!this.m_20197_().isEmpty() && this.getControllingPassenger() == null) {
         return true;
      } else {
         return this.m_20197_().size() >= 2 && this.getControllingPassenger() != null;
      }
   }

   public void m_7332_(Entity passenger) {
      if (this.m_20363_(passenger)) {
         passenger.m_183634_();
         float radius = 0.4F;
         float angle = 0.017453292F * this.f_20883_;
         double extraX = (double)(radius * Mth.m_14031_((float)(3.141592653589793D + (double)angle)));
         double extraZ = (double)(radius * Mth.m_14089_(angle));
         double yOffset = this.m_20186_() + this.m_6048_() + passenger.m_6049_();
         if (passenger == this.getControllingPassenger()) {
            passenger.m_6034_(this.m_20185_() + extraX, yOffset + 0.3D, this.m_20189_() + extraZ);
         } else {
            float yaw = this.m_146908_() * 0.017453292F;
            float f3 = Mth.m_14031_((float)((double)yaw + Math.toRadians(145.0D)));
            float f = -Mth.m_14089_((float)((double)yaw + Math.toRadians(145.0D)));
            passenger.m_6034_(this.m_20185_() + extraX * 1.5D + (double)(f3 * 2.0F), yOffset - 3.0D, this.m_20189_() + extraZ * 1.5D + (double)(f * 2.0F));
         }

      }
   }

   public void m_7888_(int pJumpPower) {
      if (pJumpPower >= 90) {
         this.playerJumpPendingScale = 1.0F;
      } else {
         if (pJumpPower < 0) {
            pJumpPower = 0;
         }

         this.playerJumpPendingScale = 0.4F + 0.4F * (float)pJumpPower / 90.0F;
      }

   }

   public void m_7199_(int pJumpPower) {
      if (this.m_20096_()) {
         this.setLeapPhase(1);
         this.playJumpSound();
      }
   }

   public void m_8012_() {
   }

   public void m_7023_(Vec3 pTravelVector) {
      if (this.m_6084_()) {
         LivingEntity livingentity = this.getControllingPassenger();
         if (this.m_20160_() && livingentity != null) {
            this.m_146922_(livingentity.m_146908_());
            this.f_19859_ = this.m_146908_();
            this.m_146926_(livingentity.m_146909_() * 0.5F);
            this.m_19915_(this.m_146908_(), this.m_146909_());
            this.f_20883_ = this.m_146908_();
            this.f_20885_ = this.f_20883_;
            float f = livingentity.f_20900_ * 0.5F;
            float f1 = livingentity.f_20902_;
            if (f1 <= 0.0F) {
               f1 *= 0.25F;
            }

            if (this.playerJumpPendingScale > 0.0F && !this.isPlayerJumping() && this.f_19861_) {
               double d0 = this.getCustomJump() * (double)this.playerJumpPendingScale * (double)this.m_20098_();
               double d1 = d0 + this.m_182332_();
               Vec3 vec3 = this.m_20184_();
               this.m_20334_(vec3.f_82479_, d1, vec3.f_82481_);
               this.setPlayerJumping(true);
               this.f_20899_ = true;
               this.f_19812_ = true;
               ForgeHooks.onLivingJump(this);
               if (f1 > 0.0F) {
                  float f2 = Mth.m_14031_(this.m_146908_() * 0.017453292F);
                  float f3 = Mth.m_14089_(this.m_146908_() * 0.017453292F);
                  this.m_20256_(this.m_20184_().m_82520_((double)(-0.4F * f2 * this.playerJumpPendingScale), 0.0D, (double)(0.4F * f3 * this.playerJumpPendingScale)));
               }

               this.playerJumpPendingScale = 0.0F;
            }

            this.f_20887_ = this.m_6113_() * 0.1F;
            if (this.m_6109_()) {
               float speed = (float)this.m_21133_(Attributes.f_22279_);
               if (livingentity.m_20142_()) {
                  speed = (float)((double)speed * 1.5D);
               }

               this.m_7910_(speed);
               super.m_7023_(new Vec3((double)f, pTravelVector.f_82480_, (double)f1));
            } else if (livingentity instanceof Player) {
               this.m_20256_(Vec3.f_82478_);
            }

            if (this.f_19861_) {
               this.playerJumpPendingScale = 0.0F;
               this.setPlayerJumping(false);
               this.f_20899_ = false;
            }

            this.m_146872_();
         } else {
            this.f_20887_ = 0.02F;
            super.m_7023_(pTravelVector);
         }
      }

   }

   public boolean m_6673_(DamageSource source) {
      if (source.m_7640_() instanceof Projectile && DamageSourceHelper.isPierce(source)) {
         return true;
      } else {
         return source == DamageSource.f_19310_ || source == DamageSource.f_19314_ || source == DamageSource.f_19325_ || source == DamageSource.f_19309_ || super.m_6673_(source);
      }
   }

   public boolean m_7301_(MobEffectInstance instance) {
      if (instance.m_19544_() == MobEffects.f_19614_) {
         return false;
      } else if (instance.m_19544_() == TensuraMobEffects.FATAL_POISON.get()) {
         return false;
      } else if (instance.m_19544_() == TensuraMobEffects.PARALYSIS.get()) {
         return false;
      } else if (instance.m_19544_() == TensuraMobEffects.INFECTION.get()) {
         return false;
      } else {
         return instance.m_19544_() == TensuraMobEffects.BURDEN.get() ? false : super.m_7301_(instance);
      }
   }

   public void m_6710_(@Nullable LivingEntity pTarget) {
      super.m_6710_(pTarget);
      if (pTarget != null && this.m_6084_() && !this.m_21824_()) {
         LabyrinthSaveData.removePassedEntity(pTarget, true);
      }

   }

   public boolean m_6469_(DamageSource pSource, float pAmount) {
      if (this.m_6673_(pSource)) {
         return false;
      } else {
         if (this.m_5803_() || this.getMiscAnimation() == 6) {
            pAmount *= 0.1F;
         }

         if (DamageSourceHelper.isNaturalEffects(pSource)) {
            pAmount *= 0.2F;
         }

         boolean hurt = super.m_6469_(pSource, pAmount);
         if (hurt && this.m_6084_() && !this.m_21824_()) {
            Entity var5 = pSource.m_7639_();
            if (var5 instanceof LivingEntity) {
               LivingEntity target = (LivingEntity)var5;
               LabyrinthSaveData.removePassedEntity(target, true);
            }
         }

         return hurt;
      }
   }

   public boolean m_142535_(float pFallDistance, float pMultiplier, DamageSource pSource) {
      if (this.getLeapPhase() == 2) {
         this.setLeapPhase(3);
         AABB aabb = AABB.m_165882_(this.m_20182_(), 10.0D, 10.0D, 10.0D);
         List<LivingEntity> list = this.m_9236_().m_6443_(LivingEntity.class, aabb, (entity) -> {
            return !entity.m_7307_(this) && entity.m_6084_() && entity != this.m_21826_() && entity != this;
         });
         Iterator var6 = list.iterator();

         while(var6.hasNext()) {
            LivingEntity target = (LivingEntity)var6.next();
            target.m_6469_(DamageSource.m_19370_(this), (float)this.m_21133_(Attributes.f_22281_) * 2.0F);
            if (target.m_20270_(this) <= 10.0F) {
               SkillHelper.knockBack(this, target, 2.0F);
            }
         }

         if (this.f_19853_.m_46469_().m_46207_(GameRules.f_46132_)) {
            SkillHelper.launchBlock(this, this.m_20182_(), 5, 1, 0.5F, 0.3F, (blockState) -> {
               return this.m_217043_().m_188503_(3) != 1 ? false : blockState.m_204336_(TensuraTags.Blocks.EARTH_DOMINATING);
            }, (blockPos) -> {
               return true;
            });
         }

         this.f_19853_.m_7703_(this, DamageSource.m_19370_(this), (ExplosionDamageCalculator)null, (double)this.m_20097_().m_123341_(), (double)this.m_20097_().m_123342_(), (double)this.m_20097_().m_123343_(), 3.0F, false, BlockInteraction.NONE);
         return false;
      } else {
         this.m_21229_();
         return true;
      }
   }

   public boolean m_214076_(ServerLevel pLevel, LivingEntity pEntity) {
      boolean wasKilled = super.m_214076_(pLevel, pEntity);
      if (!this.m_21824_()) {
         this.markAsPassedAndTeleport(pEntity, true, false);
      }

      return wasKilled;
   }

   public void markAsPassedAndTeleport(LivingEntity target, boolean teleport, boolean won) {
      if (won) {
         LivingEntity owner = SkillHelper.getSubordinateOwner(target);
         if (owner != null) {
            if (target instanceof Player) {
               this.markAsPassedAndTeleport(owner, teleport, won);
            } else {
               target = owner;
            }
         }
      }

      LabyrinthSaveData.addPassedEntity(target, won);
      this.m_21662_();
      if (teleport) {
         if (target instanceof ServerPlayer) {
            ServerPlayer serverPlayer = (ServerPlayer)target;
            TensuraAdvancementsHelper.grant(serverPlayer, TensuraAdvancementsHelper.Advancements.JUST_A_TEST);
         }

         if (this.f_19853_.m_7654_() != null) {
            ServerLevel serverLevel = this.f_19853_.m_7654_().m_129783_();
            target.m_19877_();
            target.m_20219_(LabyrinthSaveData.get(serverLevel).getPassedEntrance());
            target.m_20256_(Vec3.f_82478_);
            target.f_19864_ = true;
         }

      }
   }

   public void m_142687_(RemovalReason pReason) {
      super.m_142687_(pReason);
      if (pReason == RemovalReason.DISCARDED || pReason == RemovalReason.CHANGED_DIMENSION) {
         if (!this.m_21824_() && this.f_19853_.m_46472_() == TensuraDimensions.LABYRINTH && !this.m_21824_() && this.f_19853_.m_7654_() != null && LabyrinthSaveData.get(this.f_19853_.m_7654_().m_129783_()).isHavingColossus()) {
            LabyrinthSaveData.get(this.f_19853_.m_7654_().m_129783_()).setHavingColossus(false);
         }

      }
   }

   protected void m_6153_() {
      if (++this.f_20919_ >= 40) {
         this.m_142687_(RemovalReason.KILLED);
         this.m_5496_(SoundEvents.f_11913_, 1.0F, 1.0F);
         if (!this.m_21824_() && this.f_19853_.m_46472_() == TensuraDimensions.LABYRINTH && !this.m_21824_() && this.f_19853_.m_7654_() != null && LabyrinthSaveData.get(this.f_19853_.m_7654_().m_129783_()).isHavingColossus()) {
            LabyrinthSaveData.get(this.f_19853_.m_7654_().m_129783_()).setHavingColossus(false);
         }

         TensuraParticleHelper.addServerParticlesAroundSelf(this, ParticleTypes.f_123759_, 3.0D);
         TensuraParticleHelper.addServerParticlesAroundSelf(this, ParticleTypes.f_123796_, 3.0D);
         TensuraParticleHelper.addServerParticlesAroundSelf(this, ParticleTypes.f_123759_, 2.0D);
         TensuraParticleHelper.addServerParticlesAroundSelf(this, ParticleTypes.f_123796_, 1.0D);
         TensuraParticleHelper.addServerParticlesAroundSelf(this, ParticleTypes.f_123759_);
         TensuraParticleHelper.addServerParticlesAroundSelf(this, ParticleTypes.f_123796_);
      }

   }

   public void m_6667_(DamageSource pDamageSource) {
      super.m_6667_(pDamageSource);
      if (this.m_20089_() == Pose.DYING) {
         this.setMiscAnimation(5);
      }

   }

   protected void m_7472_(DamageSource pSource, int pLooting, boolean pRecentlyHit) {
      super.m_7472_(pSource, pLooting, pRecentlyHit);
      if (!((double)this.f_19796_.m_188501_() > 0.1D)) {
         this.m_19998_((ItemLike)TensuraMobDropItems.ELEMENTAL_ESSENCE.get());
      }
   }

   @Nullable
   public SpawnGroupData m_6518_(ServerLevelAccessor pLevel, DifficultyInstance pDifficulty, MobSpawnType pReason, @Nullable SpawnGroupData pSpawnData, @Nullable CompoundTag pDataTag) {
      this.setSleeping(true);
      return super.m_6518_(pLevel, pDifficulty, pReason, pSpawnData, pDataTag);
   }

   protected SoundEvent m_7515_() {
      return SoundEvents.f_12010_;
   }

   protected SoundEvent m_7975_(DamageSource source) {
      return SoundEvents.f_12008_;
   }

   protected SoundEvent m_5592_() {
      return SoundEvents.f_12059_;
   }

   protected void playJumpSound() {
      this.m_5496_((SoundEvent)TensuraSoundEvents.SMALL_JUMP_IMPACT.get(), 0.4F, 1.0F);
   }

   private <E extends IAnimatable> PlayState predicate(AnimationEvent<E> event) {
      if (!this.m_21825_() && !this.m_5803_()) {
         if (this.getLeapPhase() == 2) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.elemental_colossus.jump_fall", EDefaultLoopTypes.LOOP));
         } else if (event.isMoving()) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.elemental_colossus.walk", EDefaultLoopTypes.LOOP));
         } else {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.elemental_colossus.idle", EDefaultLoopTypes.LOOP));
         }
      } else {
         event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.elemental_colossus.idle_deactivated", EDefaultLoopTypes.LOOP));
      }

      return PlayState.CONTINUE;
   }

   private <E extends IAnimatable> PlayState miscPredicate(AnimationEvent<E> event) {
      if (event.getController().getAnimationState().equals(AnimationState.Stopped)) {
         event.getController().markNeedsReload();
         if (this.getMiscAnimation() == 1) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.elemental_colossus.punch", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 2) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.elemental_colossus.spin", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 3) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.elemental_colossus.grab", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 4) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.elemental_colossus.throw", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 5) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.elemental_colossus.death", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 6) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.elemental_colossus.activating", EDefaultLoopTypes.PLAY_ONCE));
         }
      }

      return PlayState.CONTINUE;
   }

   private <E extends IAnimatable> PlayState leapPredicate(AnimationEvent<E> event) {
      if (event.getController().getAnimationState().equals(AnimationState.Stopped)) {
         event.getController().markNeedsReload();
         if (this.getLeapPhase() == 1) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.elemental_colossus.jump", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getLeapPhase() == 3) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.elemental_colossus.slam_fall", EDefaultLoopTypes.PLAY_ONCE));
         }
      }

      return PlayState.CONTINUE;
   }

   public void registerControllers(AnimationData data) {
      data.addAnimationController(new AnimationController(this, "controller", 0.0F, this::predicate));
      data.addAnimationController(new AnimationController(this, "miscController", 0.0F, this::miscPredicate));
      data.addAnimationController(new AnimationController(this, "leapController", 0.0F, this::leapPredicate));
   }

   public void setPlayerJumping(boolean playerJumping) {
      this.playerJumping = playerJumping;
   }

   public boolean isPlayerJumping() {
      return this.playerJumping;
   }

   public AnimationFactory getFactory() {
      return this.factory;
   }

   static {
      MISC_ANIMATION = SynchedEntityData.m_135353_(ElementalColossusEntity.class, EntityDataSerializers.f_135028_);
      LEAP_PHASE = SynchedEntityData.m_135353_(ElementalColossusEntity.class, EntityDataSerializers.f_135028_);
   }

   public class ColossusLookControl extends TensuraTamableEntity.SleepLookControl {
      public ColossusLookControl() {
         super();
      }

      public void m_8128_() {
         if (ElementalColossusEntity.this.getMiscAnimation() != 6) {
            if (!ElementalColossusEntity.this.m_21827_()) {
               super.m_8128_();
            }
         }
      }
   }

   public class ColossusMoveControl extends TensuraTamableEntity.SleepMoveControl {
      public ColossusMoveControl() {
         super();
      }

      public void m_8126_() {
         if (ElementalColossusEntity.this.getMiscAnimation() != 6) {
            super.m_8126_();
         }
      }
   }

   static class ColossusSleepGoal extends MoveToBlockGoal {
      private final ElementalColossusEntity colossus;

      ColossusSleepGoal(ElementalColossusEntity colossus, double pSpeedModifier) {
         super(colossus, pSpeedModifier, 64);
         this.colossus = colossus;
      }

      public boolean m_8036_() {
         if (!super.m_8036_()) {
            return false;
         } else if (!this.colossus.m_21827_() && !this.colossus.m_5803_()) {
            return this.colossus.m_5448_() == null;
         } else {
            return false;
         }
      }

      public boolean m_8045_() {
         return super.m_8045_() && !this.colossus.m_5803_() && this.colossus.m_5448_() == null;
      }

      public void m_8037_() {
         if (this.colossus.m_5448_() == null) {
            BlockPos blockpos = this.colossus.m_20183_();
            Level level = this.colossus.f_19853_;
            if (this.m_25625_()) {
               level.m_5594_((Player)null, blockpos, SoundEvents.f_12010_, SoundSource.BLOCKS, 0.3F, 0.9F + level.f_46441_.m_188501_() * 0.2F);
               level.m_46796_(2001, blockpos, Block.m_49956_(level.m_8055_(blockpos.m_7495_())));
               this.colossus.setSleeping(true);
            }

            super.m_8037_();
         }
      }

      protected boolean m_25626_() {
         if (this.f_25598_.f_19853_.m_7654_() == null) {
            return false;
         } else {
            BlockPos pos = new BlockPos(LabyrinthSaveData.get(this.f_25598_.f_19853_.m_7654_().m_129783_()).getColossusPos());
            if (!this.f_25598_.m_21444_(pos)) {
               return false;
            } else {
               this.f_25602_ = pos;
               return true;
            }
         }
      }

      protected BlockPos m_6669_() {
         return new BlockPos(this.f_25602_.m_123341_() - 1, this.f_25602_.m_123342_(), this.f_25602_.m_123343_() - 1);
      }

      public double m_8052_() {
         return 3.0D;
      }

      protected boolean m_6465_(LevelReader pLevel, BlockPos pPos) {
         return true;
      }
   }

   static class LeapJumpGoal extends LeapWithStrengthGoal {
      private final ElementalColossusEntity colossus;

      public LeapJumpGoal(ElementalColossusEntity colossus) {
         super(colossus, 1.0F, 3.0F, 15.0D, 128.0D, 10);
         this.colossus = colossus;
      }

      public boolean m_8036_() {
         if (!super.m_8036_()) {
            return false;
         } else {
            return this.colossus.getMiscAnimation() != 6;
         }
      }

      public void m_8056_() {
         this.colossus.setLeapPhase(1);
         super.m_8056_();
      }

      public boolean m_8045_() {
         if (!this.colossus.m_20096_()) {
            return false;
         } else if (this.colossus.getMiscAnimation() != 0) {
            return false;
         } else if (LabyrinthSaveData.isEntityPassedColossus(this.colossus.m_5448_())) {
            this.colossus.m_21661_();
            return false;
         } else {
            return true;
         }
      }
   }

   static class ColossusAttackGoal extends MeleeAttackGoal {
      private final ElementalColossusEntity colossus;

      public ColossusAttackGoal(ElementalColossusEntity colossus, double pSpeedModifier, boolean pFollowingTargetEvenIfNotSeen) {
         super(colossus, pSpeedModifier, pFollowingTargetEvenIfNotSeen);
         this.colossus = colossus;
      }

      public boolean m_8036_() {
         if (this.colossus.m_5803_()) {
            return false;
         } else if (this.colossus.m_21827_()) {
            return false;
         } else if (this.colossus.getLeapPhase() != 0) {
            return false;
         } else {
            return LabyrinthSaveData.isEntityPassedColossus(this.colossus.m_5448_()) ? false : super.m_8036_();
         }
      }

      public boolean m_8045_() {
         if (this.colossus.m_5803_()) {
            return false;
         } else if (this.colossus.m_21827_()) {
            return false;
         } else if (this.colossus.getLeapPhase() != 0) {
            return false;
         } else {
            return LabyrinthSaveData.isEntityPassedColossus(this.colossus.m_5448_()) ? false : super.m_8045_();
         }
      }

      public void m_8037_() {
         if (this.colossus.getMiscAnimation() == 0) {
            super.m_8037_();
         }

      }

      protected void m_6739_(LivingEntity pEnemy, double pDistToEnemySqr) {
         double distance = this.m_6639_(pEnemy);
         if (this.colossus.getMiscAnimation() == 0) {
            int randomAttack = this.randomAttack(distance);
            double var10000;
            switch(randomAttack) {
            case 2:
               var10000 = distance + 9.0D;
               break;
            case 3:
               var10000 = distance + 16.0D;
               break;
            default:
               var10000 = distance;
            }

            double attackRange = var10000;
            if (pDistToEnemySqr <= attackRange && this.m_25564_()) {
               this.m_25563_();
               this.colossus.setMiscAnimation(randomAttack);
            }
         }

      }

      protected int randomAttack(double distance) {
         if (this.colossus.isHoldingTargets()) {
            return 4;
         } else if (this.colossus.f_19796_.m_188503_(10) == 2 && distance <= 25.0D) {
            return 3;
         } else {
            return (double)this.colossus.f_19796_.m_188501_() <= 0.3D ? 2 : 1;
         }
      }

      protected double m_6639_(LivingEntity pAttackTarget) {
         return (double)(this.f_25540_.m_20205_() * this.f_25540_.m_20205_() * 3.0F + pAttackTarget.m_20205_()) + this.f_25540_.m_21133_((Attribute)ForgeMod.ATTACK_RANGE.get());
      }
   }
}
