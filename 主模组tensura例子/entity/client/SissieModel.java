package com.github.manasmods.tensura.entity.client;

import com.github.manasmods.tensura.entity.SissieEntity;
import net.minecraft.resources.ResourceLocation;
import software.bernie.geckolib3.core.event.predicate.AnimationEvent;
import software.bernie.geckolib3.core.processor.IBone;
import software.bernie.geckolib3.model.AnimatedGeoModel;

public class SissieModel extends AnimatedGeoModel<SissieEntity> {
   public ResourceLocation getModelResource(SissieEntity object) {
      return new ResourceLocation("tensura", "geo/sissie.geo.json");
   }

   public ResourceLocation getTextureResource(SissieEntity instance) {
      return new ResourceLocation("tensura", "textures/entity/sissie/sissie.png");
   }

   public ResourceLocation getAnimationResource(SissieEntity entity) {
      return new ResourceLocation("tensura", "animations/sissie.animation.json");
   }

   public void setCustomAnimations(SissieEntity fish, int instanceId, AnimationEvent customPredicate) {
      super.setCustomAnimations(fish, instanceId, customPredicate);
      IBone chest = this.getAnimationProcessor().getBone("Chest");
      if (fish.isChested() == chest.isHidden()) {
         chest.setHidden(!fish.isChested());
      }

      IBone saddle = this.getAnimationProcessor().getBone("Saddle");
      if (fish.isSaddled() == saddle.isHidden()) {
         saddle.setHidden(!fish.isSaddled());
      }

   }
}
