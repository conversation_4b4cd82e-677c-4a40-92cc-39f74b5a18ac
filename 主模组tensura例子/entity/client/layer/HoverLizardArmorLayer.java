package com.github.manasmods.tensura.entity.client.layer;

import com.github.manasmods.tensura.entity.HoverLizardEntity;
import com.mojang.blaze3d.vertex.PoseStack;
import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.client.renderer.RenderType;
import net.minecraft.client.renderer.texture.OverlayTexture;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.world.item.DyeableHorseArmorItem;
import net.minecraft.world.item.HorseArmorItem;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Items;
import software.bernie.geckolib3.renderers.geo.GeoLayerRenderer;
import software.bernie.geckolib3.renderers.geo.IGeoRenderer;

public class HoverLizardArmorLayer extends GeoLayerRenderer<HoverLizardEntity> {
   private static final ResourceLocation MODEL = new ResourceLocation("tensura", "geo/hover_lizard.geo.json");

   public HoverLizardArmorLayer(IGeoRenderer<HoverLizardEntity> entityRendererIn) {
      super(entityRendererIn);
   }

   public ResourceLocation getTextureResource(ItemStack itemStack) {
      if (itemStack.m_150930_(Items.f_42653_)) {
         return new ResourceLocation("tensura", "textures/entity/hover_lizard/diamond_armor.png");
      } else if (itemStack.m_150930_(Items.f_42652_)) {
         return new ResourceLocation("tensura", "textures/entity/hover_lizard/golden_armor.png");
      } else {
         return itemStack.m_150930_(Items.f_42651_) ? new ResourceLocation("tensura", "textures/entity/hover_lizard/iron_armor.png") : new ResourceLocation("tensura", "textures/entity/hover_lizard/leather_armor.png");
      }
   }

   public void render(PoseStack matrixStackIn, MultiBufferSource bufferIn, int packedLightIn, HoverLizardEntity lizard, float limbSwing, float limbSwingAmount, float partialTicks, float ageInTicks, float netHeadYaw, float headPitch) {
      ItemStack itemstack = lizard.getArmor();
      Item var13 = itemstack.m_41720_();
      if (var13 instanceof HorseArmorItem) {
         HorseArmorItem horsearmoritem = (HorseArmorItem)var13;
         matrixStackIn.m_85836_();
         RenderType cameo = RenderType.m_110473_(this.getTextureResource(itemstack));
         float red;
         float green;
         float blue;
         if (horsearmoritem instanceof DyeableHorseArmorItem) {
            int i = ((DyeableHorseArmorItem)horsearmoritem).m_41121_(itemstack);
            red = (float)(i >> 16 & 255) / 255.0F;
            green = (float)(i >> 8 & 255) / 255.0F;
            blue = (float)(i & 255) / 255.0F;
         } else {
            red = 1.0F;
            green = 1.0F;
            blue = 1.0F;
         }

         this.getRenderer().render(this.getEntityModel().getModel(MODEL), lizard, partialTicks, cameo, matrixStackIn, bufferIn, bufferIn.m_6299_(cameo), packedLightIn, OverlayTexture.f_118083_, red, green, blue, 1.0F);
         matrixStackIn.m_85849_();
      }

   }
}
