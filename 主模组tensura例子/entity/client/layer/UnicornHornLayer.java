package com.github.manasmods.tensura.entity.client.layer;

import com.github.manasmods.tensura.entity.UnicornEntity;
import com.github.manasmods.tensura.entity.client.UnicornModel;
import com.mojang.blaze3d.vertex.PoseStack;
import com.mojang.blaze3d.vertex.VertexConsumer;
import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.client.renderer.entity.LivingEntityRenderer;
import net.minecraft.client.renderer.entity.RenderLayerParent;
import net.minecraft.client.renderer.entity.layers.RenderLayer;
import net.minecraft.resources.ResourceLocation;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.api.distmarker.OnlyIn;

@OnlyIn(Dist.CLIENT)
public class UnicornHornLayer<T extends UnicornEntity> extends RenderLayer<T, UnicornModel<T>> {
   private static final ResourceLocation HORN_TEXTURE = new ResourceLocation("tensura", "textures/entity/unicorn/unicorn_horn.png");

   public UnicornHornLayer(RenderLayerParent<T, UnicornModel<T>> parent) {
      super(parent);
   }

   public void render(PoseStack poseStack, MultiBufferSource multiBufferSource, int packedLight, T entity, float limbSwing, float limbSwingAmount, float partialTicks, float ageInTicks, float netHeadYaw, float headPitch) {
      if (!entity.m_6162_()) {
         int packedOverlay = LivingEntityRenderer.m_115338_(entity, 0.0F);
         VertexConsumer vertexConsumer = multiBufferSource.m_6299_(((UnicornModel)this.m_117386_()).m_103119_(HORN_TEXTURE));
         ((UnicornModel)this.m_117386_()).renderHorn(entity, poseStack, vertexConsumer, packedLight, packedOverlay, partialTicks);
      }

   }
}
