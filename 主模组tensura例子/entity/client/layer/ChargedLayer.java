package com.github.manasmods.tensura.entity.client.layer;

import com.github.manasmods.tensura.ability.skill.extra.SpatialDominationSkill;
import com.github.manasmods.tensura.capability.effects.TensuraEffectsCapability;
import com.github.manasmods.tensura.entity.SlimeEntity;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.google.common.collect.ImmutableList;
import com.mojang.blaze3d.vertex.PoseStack;
import com.mojang.blaze3d.vertex.VertexConsumer;
import javax.annotation.Nullable;
import net.minecraft.client.Minecraft;
import net.minecraft.client.model.HumanoidModel;
import net.minecraft.client.model.geom.ModelLayerLocation;
import net.minecraft.client.model.geom.ModelPart;
import net.minecraft.client.model.geom.builders.CubeDeformation;
import net.minecraft.client.model.geom.builders.LayerDefinition;
import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.client.renderer.RenderType;
import net.minecraft.client.renderer.entity.RenderLayerParent;
import net.minecraft.client.renderer.entity.layers.RenderLayer;
import net.minecraft.client.renderer.texture.OverlayTexture;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.entity.LivingEntity;
import software.bernie.geckolib3.core.IAnimatable;
import software.bernie.geckolib3.geo.render.built.GeoModel;
import software.bernie.geckolib3.model.AnimatedGeoModel;
import software.bernie.geckolib3.model.provider.GeoModelProvider;
import software.bernie.geckolib3.renderers.geo.GeoLayerRenderer;
import software.bernie.geckolib3.renderers.geo.IGeoRenderer;

public class ChargedLayer {
   public static final LayerDefinition CHARGED_LAYER = LayerDefinition.m_171565_(HumanoidModel.m_170681_(new CubeDeformation(0.3F), 0.0F), 64, 64);
   public static final LayerDefinition CHARGED_ARM_LAYER = LayerDefinition.m_171565_(HumanoidModel.m_170681_(new CubeDeformation(0.5F), 0.0F), 64, 64);

   private static RenderType getRenderType(ResourceLocation texture, float f) {
      return RenderType.m_110436_(texture, f * 0.02F % 1.0F, f * 0.01F % 1.0F);
   }

   @Nullable
   private static ResourceLocation getChargingTexture(LivingEntity entity) {
      if (TensuraEffectsCapability.hasSyncedEffect(entity, (MobEffect)TensuraMobEffects.LUST_EMBRACEMENT.get())) {
         return new ResourceLocation("tensura", "textures/models/layer/lust_embracement.png");
      } else if (SpatialDominationSkill.hasFaultField(entity)) {
         return new ResourceLocation("tensura", "textures/models/layer/riptide.png");
      } else if (TensuraEffectsCapability.hasSyncedEffect(entity, (MobEffect)TensuraMobEffects.MAD_OGRE.get())) {
         return new ResourceLocation("tensura", "textures/models/layer/mad_ogre.png");
      } else if (TensuraEffectsCapability.hasSyncedEffect(entity, (MobEffect)TensuraMobEffects.DRAGON_MODE.get())) {
         return new ResourceLocation("tensura", "textures/models/layer/enchanted_yellow.png");
      } else if (TensuraEffectsCapability.hasSyncedEffect(entity, (MobEffect)TensuraMobEffects.BEAST_TRANSFORMATION.get())) {
         return new ResourceLocation("tensura", "textures/models/layer/enchanted_blue.png");
      } else if (TensuraEffectsCapability.hasSyncedEffect(entity, (MobEffect)TensuraMobEffects.OGRE_BERSERKER.get())) {
         return new ResourceLocation("tensura", "textures/models/layer/enchanted.png");
      } else if (TensuraEffectsCapability.hasSyncedEffect(entity, (MobEffect)TensuraMobEffects.MAGIC_LIGHT.get())) {
         return new ResourceLocation("tensura", "textures/models/layer/magic_light.png");
      } else if (TensuraEffectsCapability.hasSyncedEffect(entity, (MobEffect)TensuraMobEffects.MAGIC_DARKNESS.get())) {
         return new ResourceLocation("tensura", "textures/models/layer/magic_darkness.png");
      } else if (TensuraEffectsCapability.hasSyncedEffect(entity, (MobEffect)TensuraMobEffects.MAGIC_EARTH.get())) {
         return new ResourceLocation("tensura", "textures/models/layer/magic_earth.png");
      } else if (TensuraEffectsCapability.hasSyncedEffect(entity, (MobEffect)TensuraMobEffects.MAGIC_FLAME.get())) {
         return new ResourceLocation("tensura", "textures/models/layer/magic_flame.png");
      } else if (TensuraEffectsCapability.hasSyncedEffect(entity, (MobEffect)TensuraMobEffects.MAGIC_SPACE.get())) {
         return new ResourceLocation("tensura", "textures/models/layer/magic_space.png");
      } else if (TensuraEffectsCapability.hasSyncedEffect(entity, (MobEffect)TensuraMobEffects.MAGIC_WATER.get())) {
         return new ResourceLocation("tensura", "textures/models/layer/magic_water.png");
      } else if (TensuraEffectsCapability.hasSyncedEffect(entity, (MobEffect)TensuraMobEffects.MAGIC_WIND.get())) {
         return new ResourceLocation("tensura", "textures/models/layer/magic_wind.png");
      } else {
         return TensuraEffectsCapability.hasSyncedEffect(entity, (MobEffect)TensuraMobEffects.OPPRESSION.get()) ? new ResourceLocation("tensura", "textures/models/layer/oppression.png") : null;
      }
   }

   public static class HumanoidArm<T extends LivingEntity> extends RenderLayer<T, HumanoidModel<T>> {
      public static ModelLayerLocation ENERGY_LAYER = new ModelLayerLocation(new ResourceLocation("tensura", "energy_arm_layer"), "main");
      private final HumanoidModel<T> model;

      public HumanoidArm(RenderLayerParent pRenderer) {
         super(pRenderer);
         this.model = new HumanoidModel<T>(Minecraft.m_91087_().m_167973_().m_171103_(ENERGY_LAYER)) {
            // $FF: synthetic field
            final ChargedLayer.HumanoidArm this$0;

            {
               super(pRoot);
               this.this$0 = this$0;
            }

            protected Iterable<ModelPart> m_5607_() {
               return ImmutableList.of();
            }

            protected Iterable<ModelPart> m_5608_() {
               return ImmutableList.of(this.f_102811_, this.f_102812_);
            }
         };
      }

      public void render(PoseStack pMatrixStack, MultiBufferSource pBuffer, int pPackedLight, T entity, float pLimbSwing, float pLimbSwingAmount, float partialTicks, float pAgeInTicks, float pNetHeadYaw, float pHeadPitch) {
         ResourceLocation chargingTexture = this.getChargingTexture(entity);
         if (chargingTexture != null) {
            float f = (float)entity.f_19797_ + partialTicks;
            HumanoidModel<T> model = this.model();
            model.m_6839_(entity, pLimbSwing, pLimbSwingAmount, partialTicks);
            ((HumanoidModel)this.m_117386_()).m_102872_(model);
            VertexConsumer vertexconsumer = pBuffer.m_6299_(ChargedLayer.getRenderType(chargingTexture, f));
            model.m_6973_(entity, pLimbSwing, pLimbSwingAmount, pAgeInTicks, pNetHeadYaw, pHeadPitch);
            model.m_7695_(pMatrixStack, vertexconsumer, pPackedLight, OverlayTexture.f_118083_, 0.8F, 0.8F, 0.8F, 1.0F);
         }
      }

      protected HumanoidModel<T> model() {
         return this.model;
      }

      @Nullable
      private ResourceLocation getChargingTexture(T entity) {
         if (TensuraEffectsCapability.hasSyncedEffect(entity, (MobEffect)TensuraMobEffects.SEVERANCE_BLADE.get())) {
            return new ResourceLocation("tensura", "textures/models/layer/severance.png");
         } else if (TensuraEffectsCapability.hasSyncedEffect(entity, (MobEffect)TensuraMobEffects.HAKI_COAT.get())) {
            return new ResourceLocation("tensura", "textures/models/layer/haki_coat.png");
         } else if (TensuraEffectsCapability.hasSyncedEffect(entity, (MobEffect)TensuraMobEffects.MAGIC_AURA.get())) {
            return new ResourceLocation("tensura", "textures/models/layer/magic_aura.png");
         } else if (TensuraEffectsCapability.hasSyncedEffect(entity, (MobEffect)TensuraMobEffects.OGRE_GUILLOTINE.get())) {
            return new ResourceLocation("tensura", "textures/models/layer/guillotine.png");
         } else {
            return TensuraEffectsCapability.hasSyncedEffect(entity, (MobEffect)TensuraMobEffects.AURA_SWORD.get()) ? new ResourceLocation("tensura", "textures/models/layer/aura.png") : null;
         }
      }
   }

   public static class Geo<P extends LivingEntity & IAnimatable> extends GeoLayerRenderer<P> {
      public Geo(IGeoRenderer entityRendererIn) {
         super(entityRendererIn);
      }

      public void render(PoseStack matrixStackIn, MultiBufferSource bufferIn, int packedLightIn, P entity, float limbSwing, float limbSwingAmount, float partialTicks, float ageInTicks, float netHeadYaw, float headPitch) {
         ResourceLocation chargingTexture = ChargedLayer.getChargingTexture(entity);
         if (chargingTexture != null) {
            float f = (float)entity.f_19797_ + partialTicks;
            RenderType renderType = ChargedLayer.getRenderType(chargingTexture, f);
            VertexConsumer vertexconsumer = bufferIn.m_6299_(renderType);
            matrixStackIn.m_85836_();
            GeoModelProvider var16 = this.getEntityModel();
            if (var16 instanceof AnimatedGeoModel) {
               AnimatedGeoModel<P> model = (AnimatedGeoModel)var16;
               GeoModel geoModel = model.getModel(model.getModelResource(entity));
               geoModel.getBone("body").ifPresent((rootBone) -> {
                  rootBone.childBones.forEach((bone) -> {
                     bone.setHidden(rootBone.isHidden());
                     bone.setScale(1.1F, 1.1F, 1.1F);
                  });
               });
               if (entity instanceof SlimeEntity) {
                  SlimeEntity slime = (SlimeEntity)entity;
                  float scale = 1.0F / (0.175F * (float)slime.getSize());
                  matrixStackIn.m_85841_(scale, scale, scale);
               }

               this.getRenderer().render(geoModel, entity, partialTicks, renderType, matrixStackIn, bufferIn, vertexconsumer, packedLightIn, OverlayTexture.f_118083_, 0.5F, 0.5F, 0.5F, 1.0F);
            }

            matrixStackIn.m_85849_();
         }
      }
   }

   public static class Humanoid<T extends LivingEntity> extends RenderLayer<T, HumanoidModel<T>> {
      public static ModelLayerLocation ENERGY_LAYER = new ModelLayerLocation(new ResourceLocation("tensura", "energy_layer"), "main");
      private final HumanoidModel<T> model;

      public Humanoid(RenderLayerParent pRenderer) {
         super(pRenderer);
         this.model = new HumanoidModel(Minecraft.m_91087_().m_167973_().m_171103_(ENERGY_LAYER));
      }

      public void render(PoseStack pMatrixStack, MultiBufferSource pBuffer, int pPackedLight, T entity, float pLimbSwing, float pLimbSwingAmount, float partialTicks, float pAgeInTicks, float pNetHeadYaw, float pHeadPitch) {
         ResourceLocation chargingTexture = ChargedLayer.getChargingTexture(entity);
         if (chargingTexture != null) {
            float f = (float)entity.f_19797_ + partialTicks;
            HumanoidModel<T> model = this.model();
            model.m_6839_(entity, pLimbSwing, pLimbSwingAmount, partialTicks);
            ((HumanoidModel)this.m_117386_()).m_102872_(model);
            VertexConsumer vertexconsumer = pBuffer.m_6299_(ChargedLayer.getRenderType(chargingTexture, f));
            model.m_6973_(entity, pLimbSwing, pLimbSwingAmount, pAgeInTicks, pNetHeadYaw, pHeadPitch);
            model.m_7695_(pMatrixStack, vertexconsumer, pPackedLight, OverlayTexture.f_118083_, 0.8F, 0.8F, 0.8F, 1.0F);
         }
      }

      protected HumanoidModel<T> model() {
         return this.model;
      }
   }
}
