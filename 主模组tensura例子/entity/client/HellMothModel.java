package com.github.manasmods.tensura.entity.client;

import com.github.manasmods.tensura.entity.HellMothEntity;
import com.github.manasmods.tensura.entity.variant.MothVariant;
import net.minecraft.resources.ResourceLocation;
import software.bernie.geckolib3.core.event.predicate.AnimationEvent;
import software.bernie.geckolib3.core.processor.IBone;
import software.bernie.geckolib3.model.AnimatedGeoModel;
import software.bernie.geckolib3.model.provider.data.EntityModelData;

public class HellMothModel extends AnimatedGeoModel<HellMothEntity> {
   public ResourceLocation getModelResource(HellMothEntity object) {
      return new ResourceLocation("tensura", "geo/hell_moth.geo.json");
   }

   public ResourceLocation getTextureResource(HellMothEntity instance) {
      return instance.m_7770_() != null && instance.m_7770_().getString().equalsIgnoreCase("Mothra") ? new ResourceLocation("tensura", "textures/entity/hell_moth_line/mothra.png") : (ResourceLocation)MothVariant.LOCATION_BY_VARIANT.get(instance.getVariant());
   }

   public ResourceLocation getAnimationResource(HellMothEntity moth) {
      return new ResourceLocation("tensura", "animations/hell_moth.animation.json");
   }

   public void setCustomAnimations(HellMothEntity moth, int instanceId, AnimationEvent customPredicate) {
      super.setCustomAnimations(moth, instanceId, customPredicate);
      EntityModelData extraData = (EntityModelData)customPredicate.getExtraDataOfType(EntityModelData.class).get(0);
      IBone head = this.getAnimationProcessor().getBone("head");
      if (head != null) {
         head.setRotationX(extraData.headPitch * 0.017453292F);
         head.setRotationY(extraData.netHeadYaw * 0.017453292F);
      }

   }
}
