package com.github.manasmods.tensura.entity.client;

import com.github.manasmods.tensura.entity.OrcDisasterEntity;
import net.minecraft.resources.ResourceLocation;
import software.bernie.geckolib3.core.event.predicate.AnimationEvent;
import software.bernie.geckolib3.core.processor.IBone;
import software.bernie.geckolib3.model.AnimatedGeoModel;
import software.bernie.geckolib3.model.provider.data.EntityModelData;

public class OrcDisasterModel extends AnimatedGeoModel<OrcDisasterEntity> {
   public ResourceLocation getModelResource(OrcDisasterEntity object) {
      return new ResourceLocation("tensura", "geo/orc_disaster.geo.json");
   }

   public ResourceLocation getTextureResource(OrcDisasterEntity instance) {
      return new ResourceLocation("tensura", "textures/entity/orc/orc_disaster.png");
   }

   public ResourceLocation getAnimationResource(OrcDisasterEntity bear) {
      return new ResourceLocation("tensura", "animations/orc_disaster.animation.json");
   }

   public void setCustomAnimations(OrcDisasterEntity bear, int instanceId, AnimationEvent customPredicate) {
      super.setCustomAnimations(bear, instanceId, customPredicate);
      EntityModelData extraData = (EntityModelData)customPredicate.getExtraDataOfType(EntityModelData.class).get(0);
      IBone head = this.getAnimationProcessor().getBone("Head");
      if (head != null) {
         head.setRotationX(extraData.headPitch * 3.1415927F / 180.0F);
         head.setRotationY(extraData.netHeadYaw * 3.1415927F / 180.0F);
      }

   }
}
