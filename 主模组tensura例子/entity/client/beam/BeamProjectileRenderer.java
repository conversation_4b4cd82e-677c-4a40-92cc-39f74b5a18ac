package com.github.manasmods.tensura.entity.client.beam;

import com.github.manasmods.tensura.entity.magic.beam.BeamProjectile;
import com.mojang.blaze3d.vertex.PoseStack;
import com.mojang.blaze3d.vertex.VertexConsumer;
import com.mojang.blaze3d.vertex.PoseStack.Pose;
import com.mojang.math.Matrix3f;
import com.mojang.math.Matrix4f;
import com.mojang.math.Vector3f;
import java.awt.Color;
import java.util.Arrays;
import java.util.Iterator;
import java.util.Map.Entry;
import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.client.renderer.RenderType;
import net.minecraft.client.renderer.blockentity.BeaconRenderer;
import net.minecraft.client.renderer.entity.EntityRenderer;
import net.minecraft.client.renderer.entity.EntityRendererProvider.Context;
import net.minecraft.client.renderer.texture.OverlayTexture;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.util.Mth;
import net.minecraft.world.phys.Vec3;
import org.jetbrains.annotations.NotNull;

public class BeamProjectileRenderer extends EntityRenderer<BeamProjectile> {
   public BeamProjectileRenderer(Context pContext) {
      super(pContext);
   }

   @NotNull
   public ResourceLocation getTextureLocation(BeamProjectile instance) {
      ResourceLocation[] resourceLocations = instance.getTextureLocation();
      return resourceLocations == null ? BeaconRenderer.f_112102_ : (ResourceLocation)Arrays.stream(resourceLocations).toList().get(instance.f_19797_ % resourceLocations.length);
   }

   public void render(BeamProjectile beam, float pEntityYaw, float pPartialTick, @NotNull PoseStack pPoseStack, @NotNull MultiBufferSource pBuffer, int pPackedLight) {
      double collidePosX = Mth.m_14139_((double)pPartialTick, beam.prevCollidePosX, beam.collidePosX);
      double collidePosY = Mth.m_14139_((double)pPartialTick, beam.prevCollidePosY, beam.collidePosY);
      double collidePosZ = Mth.m_14139_((double)pPartialTick, beam.prevCollidePosZ, beam.collidePosZ);
      Vec3 collisionPos = new Vec3(collidePosX, collidePosY, collidePosZ);
      double posX = Mth.m_14139_((double)pPartialTick, beam.f_19854_, beam.m_20185_());
      double posY = Mth.m_14139_((double)pPartialTick, beam.f_19855_, beam.m_20186_());
      double posZ = Mth.m_14139_((double)pPartialTick, beam.f_19856_, beam.m_20189_());
      Vec3 startPos = new Vec3(posX, posY, posZ);
      pPoseStack.m_85836_();
      pPoseStack.m_85837_(0.0D, (double)(beam.m_20206_() / 2.0F), 0.0D);
      beam.startParticles(startPos);
      Vec3 offSetToTarget = collisionPos.m_82546_(startPos);

      for(int i = 1; i < Mth.m_14107_(offSetToTarget.m_82553_()) - 1; ++i) {
         beam.rayParticles(startPos.m_82549_(offSetToTarget.m_82541_().m_82490_((double)i)), i);
      }

      this.renderRays(beam, startPos, collisionPos, pPartialTick, pPoseStack, pBuffer, pPackedLight);
      beam.hitParticles(collidePosX, collidePosY, collidePosZ);
      pPoseStack.m_85849_();
   }

   public void renderRays(BeamProjectile beam, Vec3 startPos, Vec3 collidePos, float pPartialTick, @NotNull PoseStack pPoseStack, @NotNull MultiBufferSource pBuffer, int pPackedLight) {
      Iterator var8 = beam.beamColorAndSize.entrySet().iterator();

      while(var8.hasNext()) {
         Entry<Color, Float> entry = (Entry)var8.next();
         this.renderRay(beam, startPos, collidePos, pPartialTick, pPoseStack, pBuffer, pPackedLight, (Float)entry.getValue() * beam.getVisualSize(), ((Color)entry.getKey()).getRed(), ((Color)entry.getKey()).getGreen(), ((Color)entry.getKey()).getBlue(), ((Color)entry.getKey()).getAlpha());
      }

   }

   public void renderRay(BeamProjectile beam, Vec3 start, Vec3 end, float partialTick, PoseStack poseStack, MultiBufferSource bufferSource, int packedLight, float size, int red, int green, int blue, int alpha) {
      float gloveHalfWidth = beam.m_20205_() / 2.0F;
      float xDist = (float)(end.m_7096_() - start.m_7096_());
      float yDist = (float)(end.m_7098_() - start.m_7098_() - (double)gloveHalfWidth);
      float zDist = (float)(end.m_7094_() - start.m_7094_());
      float f = Mth.m_14116_(xDist * xDist + zDist * zDist);
      float f1 = Mth.m_14116_(xDist * xDist + yDist * yDist + zDist * zDist);
      poseStack.m_85836_();
      poseStack.m_85837_(0.0D, (double)gloveHalfWidth, 0.0D);
      poseStack.m_85845_(Vector3f.f_122225_.m_122270_((float)(-Math.atan2((double)zDist, (double)xDist)) - 1.5707964F));
      poseStack.m_85845_(Vector3f.f_122223_.m_122270_((float)(-Math.atan2((double)f, (double)yDist)) - 1.5707964F));
      VertexConsumer vertexConsumer = bufferSource.m_6299_(RenderType.m_234338_(this.getTextureLocation(beam)));
      float f2 = 0.0F - ((float)beam.f_19797_ + partialTick) * 0.01F;
      float f3 = f1 / 32.0F - ((float)beam.f_19797_ + partialTick) * 0.01F;
      int k = beam.f_19797_ % 16;
      if (k > 8) {
         k = 8 + (k - 8) * -1;
      }

      int i = 8 + k;
      float f4 = 0.0F;
      float f5 = 0.25F * size;
      float f6 = 0.0F;
      Pose pose = poseStack.m_85850_();
      Matrix4f matrix4f = pose.m_85861_();
      Matrix3f matrix3f = pose.m_85864_();

      for(int j = 0; j <= i; ++j) {
         float f7 = Mth.m_14031_((float)j * 6.2831855F / (float)i) * size;
         float f8 = Mth.m_14089_((float)j * 6.2831855F / (float)i) * size;
         float f9 = (float)j / (float)i * size;
         vertexConsumer.m_85982_(matrix4f, f4, f5, 0.0F).m_6122_(red, green, blue, alpha).m_7421_(f6, f2).m_86008_(OverlayTexture.f_118083_).m_85969_(packedLight).m_85977_(matrix3f, 0.0F, -1.0F, 0.0F).m_5752_();
         vertexConsumer.m_85982_(matrix4f, f4, f5, f1).m_6122_(red, green, blue, alpha).m_7421_(f6, f3).m_86008_(OverlayTexture.f_118083_).m_85969_(packedLight).m_85977_(matrix3f, 0.0F, -1.0F, 0.0F).m_5752_();
         vertexConsumer.m_85982_(matrix4f, f7, f8, f1).m_6122_(red, green, blue, alpha).m_7421_(f9, f3).m_86008_(OverlayTexture.f_118083_).m_85969_(packedLight).m_85977_(matrix3f, 0.0F, -1.0F, 0.0F).m_5752_();
         vertexConsumer.m_85982_(matrix4f, f7, f8, 0.0F).m_6122_(red, green, blue, alpha).m_7421_(f9, f2).m_86008_(OverlayTexture.f_118083_).m_85969_(packedLight).m_85977_(matrix3f, 0.0F, -1.0F, 0.0F).m_5752_();
         f4 = f7;
         f5 = f8;
         f6 = f9;
      }

      poseStack.m_85849_();
   }
}
