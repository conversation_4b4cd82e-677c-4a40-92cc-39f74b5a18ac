package com.github.manasmods.tensura.entity.client;

import com.github.manasmods.tensura.entity.MegalodonEntity;
import com.mojang.blaze3d.vertex.PoseStack;
import com.mojang.blaze3d.vertex.VertexConsumer;
import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.client.renderer.RenderType;
import net.minecraft.client.renderer.entity.EntityRendererProvider.Context;
import net.minecraft.resources.ResourceLocation;
import software.bernie.geckolib3.renderers.geo.GeoEntityRenderer;

public class MegalodonRenderer extends GeoEntityRenderer<MegalodonEntity> {
   public MegalodonRenderer(Context renderManager) {
      super(renderManager, new MegalodonModel());
      this.f_114477_ = 1.0F;
   }

   public ResourceLocation getTextureLocation(MegalodonEntity instance) {
      return new ResourceLocation("tensura", "textures/entity/megalodon/megalodon.png");
   }

   public RenderType getRenderType(MegalodonEntity entity, float partialTicks, PoseStack stack, MultiBufferSource renderTypeBuffer, VertexConsumer vertexBuilder, int packedLightIn, ResourceLocation textureLocation) {
      return RenderType.m_110473_(this.getTextureLocation(entity));
   }
}
