package com.github.manasmods.tensura.entity.client;

import com.github.manasmods.tensura.entity.GiantCodEntity;
import net.minecraft.resources.ResourceLocation;
import software.bernie.geckolib3.model.AnimatedGeoModel;

public class GiantCodModel extends AnimatedGeoModel<GiantCodEntity> {
   public ResourceLocation getModelResource(GiantCodEntity object) {
      return new ResourceLocation("tensura", "geo/giant_cod.geo.json");
   }

   public ResourceLocation getTextureResource(GiantCodEntity object) {
      return new ResourceLocation("tensura", "textures/entity/giant_cod/giant_cod.png");
   }

   public ResourceLocation getAnimationResource(GiantCodEntity animatable) {
      return new ResourceLocation("tensura", "animations/giant_salmon.animation.json");
   }
}
