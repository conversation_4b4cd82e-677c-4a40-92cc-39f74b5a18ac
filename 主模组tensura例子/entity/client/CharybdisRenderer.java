package com.github.manasmods.tensura.entity.client;

import com.github.manasmods.tensura.entity.CharybdisEntity;
import com.mojang.blaze3d.vertex.PoseStack;
import com.mojang.blaze3d.vertex.VertexConsumer;
import com.mojang.blaze3d.vertex.PoseStack.Pose;
import com.mojang.math.Matrix3f;
import com.mojang.math.Matrix4f;
import com.mojang.math.Vector3f;
import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.client.renderer.RenderType;
import net.minecraft.client.renderer.blockentity.BeaconRenderer;
import net.minecraft.client.renderer.entity.EntityRendererProvider.Context;
import net.minecraft.client.renderer.texture.OverlayTexture;
import net.minecraft.core.BlockPos;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.util.Mth;
import net.minecraft.world.phys.Vec3;
import software.bernie.geckolib3.renderers.geo.GeoEntityRenderer;

public class CharybdisRenderer extends GeoEntityRenderer<CharybdisEntity> {
   public CharybdisRenderer(Context renderManager) {
      super(renderManager, new CharybdisModel());
      this.f_114477_ = 3.0F;
   }

   protected float getDeathMaxRotation(CharybdisEntity animatable) {
      return 0.0F;
   }

   public ResourceLocation getTextureLocation(CharybdisEntity instance) {
      return CharybdisModel.getLocation(instance);
   }

   public RenderType getRenderType(CharybdisEntity entity, float partialTicks, PoseStack stack, MultiBufferSource renderTypeBuffer, VertexConsumer vertexBuilder, int packedLightIn, ResourceLocation textureLocation) {
      float size = 0.1F * entity.getSize();
      stack.m_85841_(size, size, size);
      return RenderType.m_110473_(this.getTextureLocation(entity));
   }

   public void render(CharybdisEntity entity, float entityYaw, float partialTicks, PoseStack poseStack, MultiBufferSource bufferSource, int packedLight) {
      super.render(entity, entityYaw, partialTicks, poseStack, bufferSource, packedLight);
      if (entity.getMiscAnimation() == 3 && entity.getBeamTarget() != BlockPos.f_121853_) {
         renderBeam(entity, partialTicks, poseStack, bufferSource, 15728880, 2.5F, 189, 75, 75, 255);
         renderBeam(entity, partialTicks, poseStack, bufferSource, 15728880, 4.0F, 62, 1, 61, 155);
      }

   }

   public static void renderBeam(CharybdisEntity charybdis, float partialTick, PoseStack poseStack, MultiBufferSource bufferSource, int packedLight, float size, int red, int green, int blue, int alpha) {
      Vec3 offset = charybdis.getBeamStartOffset();
      double x = charybdis.m_20185_() + offset.m_7096_();
      double y = charybdis.m_20186_() + offset.m_7098_();
      double z = charybdis.m_20189_() + offset.m_7094_();
      float xDist = (float)(x - (double)charybdis.getBeamTarget().m_123341_()) * -1.0F;
      float yDist = (float)(y - (double)charybdis.getBeamTarget().m_123342_()) * -1.0F;
      float zDist = (float)(z - (double)charybdis.getBeamTarget().m_123343_()) * -1.0F;
      float f = Mth.m_14116_(xDist * xDist + zDist * zDist);
      float f1 = Mth.m_14116_(xDist * xDist + yDist * yDist + zDist * zDist);
      poseStack.m_85836_();
      poseStack.m_85837_(offset.m_7096_(), offset.m_7098_(), offset.m_7094_());
      poseStack.m_85845_(Vector3f.f_122225_.m_122270_((float)(-Math.atan2((double)zDist, (double)xDist)) - 1.5707964F));
      poseStack.m_85845_(Vector3f.f_122223_.m_122270_((float)(-Math.atan2((double)f, (double)yDist)) - 1.5707964F));
      VertexConsumer vertexConsumer = bufferSource.m_6299_(RenderType.m_110473_(BeaconRenderer.f_112102_));
      float f2 = 0.0F - ((float)charybdis.f_19797_ + partialTick) * 0.01F;
      float f3 = f1 / 32.0F - ((float)charybdis.f_19797_ + partialTick) * 0.01F;
      int i = 10;
      float f4 = 0.0F;
      float f5 = 0.25F;
      float f6 = 0.0F;
      Pose pose = poseStack.m_85850_();
      Matrix4f matrix4f = pose.m_85861_();
      Matrix3f matrix3f = pose.m_85864_();

      for(int j = 1; j <= i; ++j) {
         float f7 = Mth.m_14031_((float)j * 6.2831855F / (float)i) * size;
         float f8 = Mth.m_14089_((float)j * 6.2831855F / (float)i) * size;
         float f9 = (float)j / (float)i;
         vertexConsumer.m_85982_(matrix4f, f4, f5, 0.0F).m_6122_(red, green, blue, alpha).m_7421_(f6, f2).m_86008_(OverlayTexture.f_118083_).m_85969_(packedLight).m_85977_(matrix3f, 0.0F, -1.0F, 0.0F).m_5752_();
         vertexConsumer.m_85982_(matrix4f, f4, f5, f1).m_6122_(red, green, blue, alpha).m_7421_(f6, f3).m_86008_(OverlayTexture.f_118083_).m_85969_(packedLight).m_85977_(matrix3f, 0.0F, -1.0F, 0.0F).m_5752_();
         vertexConsumer.m_85982_(matrix4f, f7, f8, f1).m_6122_(red, green, blue, alpha).m_7421_(f9, f3).m_86008_(OverlayTexture.f_118083_).m_85969_(packedLight).m_85977_(matrix3f, 0.0F, -1.0F, 0.0F).m_5752_();
         vertexConsumer.m_85982_(matrix4f, f7, f8, 0.0F).m_6122_(red, green, blue, alpha).m_7421_(f9, f2).m_86008_(OverlayTexture.f_118083_).m_85969_(packedLight).m_85977_(matrix3f, 0.0F, -1.0F, 0.0F).m_5752_();
         f4 = f7;
         f5 = f8;
         f6 = f9;
      }

      poseStack.m_85849_();
   }
}
