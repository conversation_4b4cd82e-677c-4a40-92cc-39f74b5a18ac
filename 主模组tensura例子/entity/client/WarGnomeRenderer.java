package com.github.manasmods.tensura.entity.client;

import com.github.manasmods.tensura.entity.WarGnomeEntity;
import com.github.manasmods.tensura.item.custom.TempestScaleShieldItem;
import com.mojang.blaze3d.vertex.PoseStack;
import com.mojang.blaze3d.vertex.VertexConsumer;
import com.mojang.math.Vector3f;
import net.minecraft.client.model.HumanoidModel;
import net.minecraft.client.model.geom.ModelPart;
import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.client.renderer.block.model.ItemTransforms.TransformType;
import net.minecraft.client.renderer.entity.EntityRendererProvider.Context;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.world.entity.EquipmentSlot;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.ShieldItem;
import net.minecraft.world.level.block.state.BlockState;
import software.bernie.geckolib3.core.processor.IBone;
import software.bernie.geckolib3.geo.render.built.GeoBone;
import software.bernie.geckolib3.renderers.geo.ExtendedGeoEntityRenderer;

public class WarGnomeRenderer extends ExtendedGeoEntityRenderer<WarGnomeEntity> {
   protected ItemStack mainHandItem;
   protected ItemStack offHandItem;

   public WarGnomeRenderer(Context renderManager) {
      super(renderManager, new WarGnomeModel());
      this.f_114477_ = 1.0F;
   }

   protected float getDeathMaxRotation(WarGnomeEntity animatable) {
      return 0.0F;
   }

   public ResourceLocation getTextureLocation(WarGnomeEntity instance) {
      return new ResourceLocation("tensura", "textures/entity/war_gnome/war_gnome.png");
   }

   public void renderEarly(WarGnomeEntity warGnome, PoseStack poseStack, float partialTick, MultiBufferSource bufferSource, VertexConsumer buffer, int packedLight, int packedOverlay, float red, float green, float blue, float partialTicks) {
      super.renderEarly(warGnome, poseStack, partialTick, bufferSource, buffer, packedLight, packedOverlay, red, green, blue, partialTicks);
      this.mainHandItem = warGnome.m_6844_(EquipmentSlot.MAINHAND);
      this.offHandItem = warGnome.m_6844_(EquipmentSlot.OFFHAND);
   }

   protected ItemStack getHeldItemForBone(String boneName, WarGnomeEntity currentEntity) {
      byte var4 = -1;
      switch(boneName.hashCode()) {
      case 434529071:
         if (boneName.equals("RightItem")) {
            var4 = 0;
         }
         break;
      case 1782749146:
         if (boneName.equals("LeftItem")) {
            var4 = 1;
         }
      }

      ItemStack var10000;
      switch(var4) {
      case 0:
         var10000 = currentEntity.m_21526_() ? this.mainHandItem : this.offHandItem;
         break;
      case 1:
         var10000 = currentEntity.m_21526_() ? this.offHandItem : this.mainHandItem;
         break;
      default:
         var10000 = null;
      }

      return var10000;
   }

   protected TransformType getCameraTransformForItemAtBone(ItemStack boneItem, String boneName) {
      byte var4 = -1;
      switch(boneName.hashCode()) {
      case 434529071:
         if (boneName.equals("RightItem")) {
            var4 = 1;
         }
         break;
      case 1782749146:
         if (boneName.equals("LeftItem")) {
            var4 = 0;
         }
      }

      TransformType var10000;
      switch(var4) {
      case 0:
      case 1:
         var10000 = TransformType.THIRD_PERSON_RIGHT_HAND;
         break;
      default:
         var10000 = TransformType.NONE;
      }

      return var10000;
   }

   protected void preRenderItem(PoseStack stack, ItemStack item, String boneName, WarGnomeEntity currentEntity, IBone bone) {
      if (item == this.mainHandItem) {
         stack.m_85841_(2.0F, 2.0F, 2.0F);
         stack.m_85845_(Vector3f.f_122223_.m_122240_(-90.0F));
         if (item.m_41720_() instanceof ShieldItem || item.m_41720_() instanceof TempestScaleShieldItem) {
            stack.m_85845_(Vector3f.f_122225_.m_122240_(180.0F));
            stack.m_85837_(0.0D, 0.0D, -0.25D);
         }
      } else if (item == this.offHandItem) {
         stack.m_85841_(2.0F, 2.0F, 2.0F);
         stack.m_85845_(Vector3f.f_122223_.m_122240_(-90.0F));
         if (item.m_41720_() instanceof ShieldItem || item.m_41720_() instanceof TempestScaleShieldItem) {
            stack.m_85837_(0.0D, 0.0D, 0.25D);
         }
      }

   }

   protected void postRenderItem(PoseStack matrixStack, ItemStack item, String boneName, WarGnomeEntity currentEntity, IBone bone) {
   }

   protected ItemStack getArmorForBone(String boneName, WarGnomeEntity currentEntity) {
      return null;
   }

   protected EquipmentSlot getEquipmentSlotForArmorBone(String boneName, WarGnomeEntity currentEntity) {
      return null;
   }

   protected ModelPart getArmorPartForBone(String name, HumanoidModel<?> armorModel) {
      return null;
   }

   protected BlockState getHeldBlockForBone(String boneName, WarGnomeEntity currentEntity) {
      return null;
   }

   protected void preRenderBlock(PoseStack stack, BlockState block, String boneName, WarGnomeEntity currentEntity) {
   }

   protected void postRenderBlock(PoseStack stack, BlockState block, String boneName, WarGnomeEntity currentEntity) {
   }

   protected ResourceLocation getTextureForBone(String boneName, WarGnomeEntity animatable) {
      return null;
   }

   protected boolean isArmorBone(GeoBone bone) {
      return bone.getName().endsWith("Armor");
   }
}
