package com.github.manasmods.tensura.entity.client;

import com.github.manasmods.tensura.entity.LeechLizardEntity;
import com.github.manasmods.tensura.entity.variant.LeechLizardVariant;
import net.minecraft.resources.ResourceLocation;
import software.bernie.geckolib3.core.event.predicate.AnimationEvent;
import software.bernie.geckolib3.core.processor.IBone;
import software.bernie.geckolib3.model.AnimatedGeoModel;
import software.bernie.geckolib3.model.provider.data.EntityModelData;

public class LeechLizardModel extends AnimatedGeoModel<LeechLizardEntity> {
   public ResourceLocation getModelResource(LeechLizardEntity object) {
      return new ResourceLocation("tensura", "geo/leech_lizard.geo.json");
   }

   public ResourceLocation getTextureResource(LeechLizardEntity instance) {
      return (ResourceLocation)LeechLizardVariant.LOCATION_BY_VARIANT.get(instance.getVariant());
   }

   public ResourceLocation getAnimationResource(LeechLizardEntity moth) {
      return new ResourceLocation("tensura", "animations/leech_lizard.animation.json");
   }

   public void setCustomAnimations(LeechLizardEntity lizard, int instanceId, AnimationEvent customPredicate) {
      super.setCustomAnimations(lizard, instanceId, customPredicate);
      IBone chest = this.getAnimationProcessor().getBone("Chest");
      if (lizard.isChested() == chest.isHidden()) {
         chest.setHidden(!lizard.isChested());
      }

      EntityModelData extraData = (EntityModelData)customPredicate.getExtraDataOfType(EntityModelData.class).get(0);
      IBone head = this.getAnimationProcessor().getBone("Face");
      if (head != null) {
         head.setRotationX(extraData.headPitch * 0.017453292F);
         head.setRotationZ(extraData.netHeadYaw * -0.017453292F);
      }

   }
}
