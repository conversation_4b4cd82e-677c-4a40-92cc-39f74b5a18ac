package com.github.manasmods.tensura.entity.client;

import com.github.manasmods.tensura.entity.OrcEntity;
import com.github.manasmods.tensura.entity.variant.OrcVariant;
import com.github.manasmods.tensura.item.custom.TempestScaleShieldItem;
import com.mojang.blaze3d.vertex.PoseStack;
import com.mojang.blaze3d.vertex.VertexConsumer;
import com.mojang.math.Vector3f;
import net.minecraft.client.model.HumanoidModel;
import net.minecraft.client.model.geom.ModelPart;
import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.client.renderer.block.model.ItemTransforms.TransformType;
import net.minecraft.client.renderer.entity.EntityRendererProvider.Context;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.world.entity.EquipmentSlot;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.ShieldItem;
import net.minecraft.world.level.block.state.BlockState;
import software.bernie.geckolib3.core.processor.IBone;
import software.bernie.geckolib3.geo.render.built.GeoBone;
import software.bernie.geckolib3.renderers.geo.ExtendedGeoEntityRenderer;

public class OrcRenderer extends ExtendedGeoEntityRenderer<OrcEntity> {
   protected ItemStack mainHandItem;
   protected ItemStack offHandItem;
   protected ItemStack helmetItem;
   protected ItemStack chestplateItem;
   protected ItemStack leggingsItem;
   protected ItemStack bootsItem;

   public OrcRenderer(Context renderManager) {
      super(renderManager, new OrcModel());
      this.f_114477_ = 0.2F;
   }

   public ResourceLocation getTextureLocation(OrcEntity instance) {
      return (ResourceLocation)OrcVariant.LOCATION_BY_VARIANT.get(instance.getVariant());
   }

   public void renderEarly(OrcEntity orc, PoseStack poseStack, float partialTick, MultiBufferSource bufferSource, VertexConsumer buffer, int packedLight, int packedOverlay, float red, float green, float blue, float partialTicks) {
      super.renderEarly(orc, poseStack, partialTick, bufferSource, buffer, packedLight, packedOverlay, red, green, blue, partialTicks);
      this.mainHandItem = orc.m_6844_(EquipmentSlot.MAINHAND);
      this.offHandItem = orc.m_6844_(EquipmentSlot.OFFHAND);
      this.helmetItem = orc.m_6844_(EquipmentSlot.HEAD);
      this.chestplateItem = orc.m_6844_(EquipmentSlot.CHEST);
      this.leggingsItem = orc.m_6844_(EquipmentSlot.LEGS);
      this.bootsItem = orc.m_6844_(EquipmentSlot.FEET);
      if (orc.m_6162_()) {
         poseStack.m_85841_(0.5F, 0.5F, 0.5F);
      }

      int tick = 40 - orc.getEvolving();
      if (orc.getEvolving() > 0 && tick > 0) {
         float scale = 1.0F + 0.5F * ((float)tick / 40.0F);
         poseStack.m_85841_(scale, scale, scale);
      }

   }

   protected ItemStack getHeldItemForBone(String boneName, OrcEntity currentEntity) {
      byte var4 = -1;
      switch(boneName.hashCode()) {
      case -1569486677:
         if (boneName.equals("rightHand")) {
            var4 = 1;
         }
         break;
      case 1718057238:
         if (boneName.equals("leftHand")) {
            var4 = 0;
         }
      }

      ItemStack var10000;
      switch(var4) {
      case 0:
         var10000 = currentEntity.m_21526_() ? this.mainHandItem : this.offHandItem;
         break;
      case 1:
         var10000 = currentEntity.m_21526_() ? this.offHandItem : this.mainHandItem;
         break;
      default:
         var10000 = null;
      }

      return var10000;
   }

   protected TransformType getCameraTransformForItemAtBone(ItemStack boneItem, String boneName) {
      byte var4 = -1;
      switch(boneName.hashCode()) {
      case -1569486677:
         if (boneName.equals("rightHand")) {
            var4 = 1;
         }
         break;
      case 1718057238:
         if (boneName.equals("leftHand")) {
            var4 = 0;
         }
      }

      TransformType var10000;
      switch(var4) {
      case 0:
      case 1:
         var10000 = TransformType.THIRD_PERSON_RIGHT_HAND;
         break;
      default:
         var10000 = TransformType.NONE;
      }

      return var10000;
   }

   protected void preRenderItem(PoseStack stack, ItemStack item, String boneName, OrcEntity currentEntity, IBone bone) {
      if (item == this.mainHandItem) {
         stack.m_85845_(Vector3f.f_122223_.m_122240_(-90.0F));
         if (item.m_41720_() instanceof ShieldItem || item.m_41720_() instanceof TempestScaleShieldItem) {
            stack.m_85837_(0.0D, 0.0D, -0.25D);
         }
      } else if (item == this.offHandItem) {
         stack.m_85845_(Vector3f.f_122223_.m_122240_(-90.0F));
         if (item.m_41720_() instanceof ShieldItem || item.m_41720_() instanceof TempestScaleShieldItem) {
            stack.m_85837_(0.0D, 0.0D, 0.25D);
            stack.m_85845_(Vector3f.f_122225_.m_122240_(180.0F));
         }
      }

   }

   protected void postRenderItem(PoseStack matrixStack, ItemStack item, String boneName, OrcEntity currentEntity, IBone bone) {
   }

   protected ItemStack getArmorForBone(String boneName, OrcEntity currentEntity) {
      byte var4 = -1;
      switch(boneName.hashCode()) {
      case -1892840865:
         if (boneName.equals("RightArmArmor")) {
            var4 = 5;
         }
         break;
      case -1274765602:
         if (boneName.equals("ChestArmor")) {
            var4 = 4;
         }
         break;
      case -633797370:
         if (boneName.equals("LeftBootArmor")) {
            var4 = 0;
         }
         break;
      case -193443560:
         if (boneName.equals("LeftLegArmor")) {
            var4 = 2;
         }
         break;
      case 934900106:
         if (boneName.equals("LeftArmArmor")) {
            var4 = 6;
         }
         break;
      case 1273782765:
         if (boneName.equals("RightLegArmor")) {
            var4 = 3;
         }
         break;
      case 1757441823:
         if (boneName.equals("HeadArmor")) {
            var4 = 7;
         }
         break;
      case 1900545745:
         if (boneName.equals("RightBootArmor")) {
            var4 = 1;
         }
      }

      ItemStack var10000;
      switch(var4) {
      case 0:
      case 1:
         var10000 = this.bootsItem;
         break;
      case 2:
      case 3:
         var10000 = this.leggingsItem;
         break;
      case 4:
      case 5:
      case 6:
         var10000 = this.chestplateItem;
         break;
      case 7:
         var10000 = this.helmetItem;
         break;
      default:
         var10000 = null;
      }

      return var10000;
   }

   protected EquipmentSlot getEquipmentSlotForArmorBone(String boneName, OrcEntity currentEntity) {
      byte var4 = -1;
      switch(boneName.hashCode()) {
      case -1892840865:
         if (boneName.equals("RightArmArmor")) {
            var4 = 5;
         }
         break;
      case -1274765602:
         if (boneName.equals("ChestArmor")) {
            var4 = 6;
         }
         break;
      case -633797370:
         if (boneName.equals("LeftBootArmor")) {
            var4 = 0;
         }
         break;
      case -193443560:
         if (boneName.equals("LeftLegArmor")) {
            var4 = 2;
         }
         break;
      case 934900106:
         if (boneName.equals("LeftArmArmor")) {
            var4 = 4;
         }
         break;
      case 1273782765:
         if (boneName.equals("RightLegArmor")) {
            var4 = 3;
         }
         break;
      case 1757441823:
         if (boneName.equals("HeadArmor")) {
            var4 = 7;
         }
         break;
      case 1900545745:
         if (boneName.equals("RightBootArmor")) {
            var4 = 1;
         }
      }

      EquipmentSlot var10000;
      switch(var4) {
      case 0:
      case 1:
         var10000 = EquipmentSlot.FEET;
         break;
      case 2:
      case 3:
         var10000 = EquipmentSlot.LEGS;
         break;
      case 4:
         var10000 = !currentEntity.m_21526_() ? EquipmentSlot.MAINHAND : EquipmentSlot.OFFHAND;
         break;
      case 5:
         var10000 = currentEntity.m_21526_() ? EquipmentSlot.MAINHAND : EquipmentSlot.OFFHAND;
         break;
      case 6:
         var10000 = EquipmentSlot.CHEST;
         break;
      case 7:
         var10000 = EquipmentSlot.HEAD;
         break;
      default:
         var10000 = null;
      }

      return var10000;
   }

   protected ModelPart getArmorPartForBone(String name, HumanoidModel<?> armorModel) {
      byte var4 = -1;
      switch(name.hashCode()) {
      case -1892840865:
         if (name.equals("RightArmArmor")) {
            var4 = 4;
         }
         break;
      case -1274765602:
         if (name.equals("ChestArmor")) {
            var4 = 6;
         }
         break;
      case -633797370:
         if (name.equals("LeftBootArmor")) {
            var4 = 0;
         }
         break;
      case -193443560:
         if (name.equals("LeftLegArmor")) {
            var4 = 1;
         }
         break;
      case 934900106:
         if (name.equals("LeftArmArmor")) {
            var4 = 5;
         }
         break;
      case 1273782765:
         if (name.equals("RightLegArmor")) {
            var4 = 3;
         }
         break;
      case 1757441823:
         if (name.equals("HeadArmor")) {
            var4 = 7;
         }
         break;
      case 1900545745:
         if (name.equals("RightBootArmor")) {
            var4 = 2;
         }
      }

      ModelPart var10000;
      switch(var4) {
      case 0:
      case 1:
         var10000 = armorModel.f_102814_;
         break;
      case 2:
      case 3:
         var10000 = armorModel.f_102813_;
         break;
      case 4:
         var10000 = armorModel.f_102811_;
         break;
      case 5:
         var10000 = armorModel.f_102812_;
         break;
      case 6:
         var10000 = armorModel.f_102810_;
         break;
      case 7:
         var10000 = armorModel.f_102808_;
         break;
      default:
         var10000 = null;
      }

      return var10000;
   }

   protected BlockState getHeldBlockForBone(String boneName, OrcEntity currentEntity) {
      return null;
   }

   protected void preRenderBlock(PoseStack stack, BlockState block, String boneName, OrcEntity currentEntity) {
   }

   protected void postRenderBlock(PoseStack stack, BlockState block, String boneName, OrcEntity currentEntity) {
   }

   protected ResourceLocation getTextureForBone(String boneName, OrcEntity animatable) {
      return null;
   }

   protected boolean isArmorBone(GeoBone bone) {
      return bone.getName().endsWith("Armor");
   }
}
