package com.github.manasmods.tensura.entity.client.player;

import com.github.manasmods.tensura.entity.human.HinataSakaguchiEntity;
import com.github.manasmods.tensura.entity.human.IOtherworlder;
import net.minecraft.client.model.HumanoidModel;
import net.minecraft.client.model.HumanoidModel.ArmPose;
import net.minecraft.client.model.geom.ModelLayers;
import net.minecraft.client.renderer.entity.EntityRendererProvider.Context;
import net.minecraft.client.renderer.entity.layers.HumanoidArmorLayer;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.entity.TamableAnimal;
import net.minecraft.world.item.CrossbowItem;
import net.minecraft.world.item.ItemStack;
import net.minecraftforge.client.extensions.common.IClientItemExtensions;
import org.jetbrains.annotations.NotNull;

public class HinataRenderer<T extends TamableAnimal & IOtherworlder> extends PlayerLikeRenderer<HinataSakaguchiEntity> {
   public HinataRenderer(Context pContext, boolean slim) {
      super(pContext, new PlayerLikeModel(pContext.m_174023_(slim ? ModelLayers.f_171166_ : ModelLayers.f_171162_), slim), 0.5F);
      this.m_115326_(new HumanoidArmorLayer(this, new HumanoidModel(pContext.m_174023_(slim ? ModelLayers.f_171167_ : ModelLayers.f_171164_)), new HumanoidModel(pContext.m_174023_(slim ? ModelLayers.f_171168_ : ModelLayers.f_171165_))));
   }

   protected boolean shouldShowName(HinataSakaguchiEntity pEntity) {
      return true;
   }

   protected ArmPose getArmPose(HinataSakaguchiEntity entity, InteractionHand pHand) {
      ArmPose var10000;
      if (entity.getAttack() != 0) {
         switch(entity.getAttack()) {
         case 1:
         case 8:
            var10000 = ArmPose.EMPTY;
            break;
         case 2:
            var10000 = ArmPose.THROW_SPEAR;
            break;
         case 3:
         case 5:
         default:
            var10000 = pHand == InteractionHand.OFF_HAND ? ArmPose.BOW_AND_ARROW : ArmPose.EMPTY;
            break;
         case 4:
         case 6:
            var10000 = ArmPose.BOW_AND_ARROW;
            break;
         case 7:
            var10000 = ArmPose.SPYGLASS;
            break;
         case 9:
            var10000 = pHand == InteractionHand.OFF_HAND ? ArmPose.THROW_SPEAR : ArmPose.EMPTY;
         }

         return var10000;
      } else {
         ItemStack itemstack = entity.m_21120_(pHand);
         if (itemstack.m_41619_()) {
            return ArmPose.EMPTY;
         } else if (!entity.f_20911_ && itemstack.m_41720_() instanceof CrossbowItem && CrossbowItem.m_40932_(itemstack)) {
            return ArmPose.CROSSBOW_HOLD;
         } else {
            ArmPose armPose;
            if (entity.m_7655_() == pHand && entity.m_21212_() > 0) {
               switch(itemstack.m_41780_()) {
               case BLOCK:
                  var10000 = ArmPose.BLOCK;
                  break;
               case BOW:
                  var10000 = ArmPose.BOW_AND_ARROW;
                  break;
               case SPEAR:
                  var10000 = ArmPose.THROW_SPEAR;
                  break;
               case CROSSBOW:
                  var10000 = ArmPose.CROSSBOW_CHARGE;
                  break;
               case SPYGLASS:
                  var10000 = ArmPose.SPYGLASS;
                  break;
               case TOOT_HORN:
                  var10000 = ArmPose.TOOT_HORN;
                  break;
               default:
                  var10000 = null;
               }

               armPose = var10000;
               if (armPose != null) {
                  return armPose;
               }
            }

            armPose = IClientItemExtensions.of(itemstack).getArmPose(entity, pHand, itemstack);
            return armPose != null ? armPose : ArmPose.ITEM;
         }
      }
   }

   @NotNull
   public ResourceLocation getTextureLocation(HinataSakaguchiEntity entity) {
      return entity.getTextureLocation();
   }
}
