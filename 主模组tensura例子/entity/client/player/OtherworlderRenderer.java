package com.github.manasmods.tensura.entity.client.player;

import com.github.manasmods.tensura.entity.human.IOtherworlder;
import net.minecraft.client.model.HumanoidModel;
import net.minecraft.client.model.geom.ModelLayers;
import net.minecraft.client.renderer.entity.EntityRendererProvider.Context;
import net.minecraft.client.renderer.entity.layers.HumanoidArmorLayer;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.world.entity.TamableAnimal;
import org.jetbrains.annotations.NotNull;

public class OtherworlderRenderer<T extends TamableAnimal & IOtherworlder> extends PlayerLikeRenderer<T> {
   public OtherworlderRenderer(Context pContext, boolean slim) {
      super(pContext, new PlayerLikeModel(pContext.m_174023_(slim ? ModelLayers.f_171166_ : ModelLayers.f_171162_), slim), 0.5F);
      this.m_115326_(new HumanoidArmorLayer(this, new HumanoidModel(pContext.m_174023_(slim ? ModelLayers.f_171167_ : ModelLayers.f_171164_)), new HumanoidModel(pContext.m_174023_(slim ? ModelLayers.f_171168_ : ModelLayers.f_171165_))));
   }

   protected boolean shouldShowName(T pEntity) {
      return true;
   }

   @NotNull
   public ResourceLocation getTextureLocation(T entity) {
      return ((IOtherworlder)entity).getTextureLocation();
   }
}
