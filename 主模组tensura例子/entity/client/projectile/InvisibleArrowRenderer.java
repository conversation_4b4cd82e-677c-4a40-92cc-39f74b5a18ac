package com.github.manasmods.tensura.entity.client.projectile;

import com.github.manasmods.tensura.entity.projectile.InvisibleArrow;
import com.mojang.blaze3d.vertex.PoseStack;
import net.minecraft.client.Minecraft;
import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.client.renderer.entity.ArrowRenderer;
import net.minecraft.client.renderer.entity.EntityRendererProvider.Context;
import net.minecraft.resources.ResourceLocation;

public class InvisibleArrowRenderer extends ArrowRenderer<InvisibleArrow> {
   public InvisibleArrowRenderer(Context pContext) {
      super(pContext);
   }

   public void render(InvisibleArrow pEntity, float pEntityYaw, float pPartialTicks, PoseStack pMatrixStack, MultiBufferSource pBuffer, int pPackedLight) {
      Minecraft minecraft = Minecraft.m_91087_();
      if (minecraft.f_91074_ == null || !pEntity.m_20177_(minecraft.f_91074_)) {
         super.m_7392_(pEntity, pEntityYaw, pPartialTicks, pMatrixStack, pBuffer, pPackedLight);
      }
   }

   public ResourceLocation getTextureLocation(InvisibleArrow pEntity) {
      return new ResourceLocation("tensura", "textures/entity/projectiles/invisible_arrow.png");
   }
}
