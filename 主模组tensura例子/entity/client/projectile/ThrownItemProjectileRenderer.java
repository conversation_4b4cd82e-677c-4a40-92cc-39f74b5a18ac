package com.github.manasmods.tensura.entity.client.projectile;

import com.github.manasmods.tensura.entity.magic.misc.ThrownItemProjectile;
import com.github.manasmods.tensura.item.templates.custom.TensuraLongSword;
import com.mojang.blaze3d.vertex.PoseStack;
import com.mojang.math.Vector3f;
import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.client.renderer.block.model.ItemTransforms.TransformType;
import net.minecraft.client.renderer.entity.EntityRenderer;
import net.minecraft.client.renderer.entity.ItemRenderer;
import net.minecraft.client.renderer.entity.EntityRendererProvider.Context;
import net.minecraft.client.renderer.texture.OverlayTexture;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.util.Mth;
import net.minecraft.world.inventory.InventoryMenu;
import net.minecraft.world.item.BowItem;
import net.minecraft.world.item.CrossbowItem;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ShieldItem;
import net.minecraft.world.item.TieredItem;

public class ThrownItemProjectileRenderer extends EntityRenderer<ThrownItemProjectile> {
   private final ItemRenderer itemRenderer;

   public ThrownItemProjectileRenderer(Context renderManager) {
      super(renderManager);
      this.itemRenderer = renderManager.m_174025_();
   }

   public ResourceLocation getTextureLocation(ThrownItemProjectile instance) {
      return InventoryMenu.f_39692_;
   }

   public void render(ThrownItemProjectile projectile, float f, float g, PoseStack matrixStack, MultiBufferSource vertexConsumerProvider, int i) {
      float scale;
      label22: {
         matrixStack.m_85836_();
         matrixStack.m_85845_(Vector3f.f_122225_.m_122240_(Mth.m_14179_(g, projectile.f_19859_, projectile.m_146908_()) - 90.0F));
         matrixStack.m_85845_(Vector3f.f_122227_.m_122240_(Mth.m_14179_(g, projectile.f_19860_, projectile.m_146909_()) + 45.0F));
         matrixStack.m_85845_(Vector3f.f_122223_.m_122240_(180.0F));
         matrixStack.m_85837_(0.0D, -0.1D, 0.0D);
         Item item = projectile.getSourceItem().m_41720_();
         scale = 1.0F;
         if (item instanceof TensuraLongSword) {
            TensuraLongSword longSword = (TensuraLongSword)item;
            if (longSword.getRange() > 0.0D) {
               scale = (float)((double)scale + 0.5D * longSword.getRange());
               break label22;
            }
         }

         if (item instanceof BowItem) {
            BowItem bowItem = (BowItem)item;
            scale = (float)((double)scale + 0.1D * (double)bowItem.m_6615_() / 15.0D);
         } else if (item instanceof ShieldItem) {
            scale = 2.0F;
         } else if (!(item instanceof TieredItem) && !(item instanceof CrossbowItem)) {
            scale = 0.7F;
         }
      }

      matrixStack.m_85841_(scale, scale, scale);
      this.itemRenderer.m_174269_(projectile.getSourceItem(), TransformType.GUI, i, OverlayTexture.f_118083_, matrixStack, vertexConsumerProvider, projectile.m_19879_());
      matrixStack.m_85849_();
   }
}
