package com.github.manasmods.tensura.entity.client.projectile;

import com.github.manasmods.tensura.entity.magic.TensuraProjectile;
import com.mojang.blaze3d.vertex.PoseStack;
import com.mojang.blaze3d.vertex.VertexConsumer;
import com.mojang.math.Vector3f;
import java.util.Arrays;
import net.minecraft.client.Minecraft;
import net.minecraft.client.model.geom.ModelLayerLocation;
import net.minecraft.client.model.geom.ModelPart;
import net.minecraft.client.model.geom.PartPose;
import net.minecraft.client.model.geom.builders.CubeDeformation;
import net.minecraft.client.model.geom.builders.CubeListBuilder;
import net.minecraft.client.model.geom.builders.LayerDefinition;
import net.minecraft.client.model.geom.builders.MeshDefinition;
import net.minecraft.client.model.geom.builders.PartDefinition;
import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.client.renderer.RenderType;
import net.minecraft.client.renderer.entity.EntityRenderer;
import net.minecraft.client.renderer.entity.EntityRendererProvider.Context;
import net.minecraft.client.renderer.texture.OverlayTexture;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.util.Mth;
import net.minecraft.world.phys.Vec3;

public class BladeProjectileRenderer extends EntityRenderer<TensuraProjectile> {
   public static final ModelLayerLocation BLADE = new ModelLayerLocation(new ResourceLocation("tensura", "blade"), "main");
   private final ModelPart spike;

   public BladeProjectileRenderer(Context context) {
      super(context);
      ModelPart modelpart = context.m_174023_(BLADE);
      this.spike = modelpart.m_171324_("blade");
   }

   public static LayerDefinition createBodyLayer() {
      MeshDefinition meshdefinition = new MeshDefinition();
      PartDefinition partdefinition = meshdefinition.m_171576_();
      PartDefinition blade = partdefinition.m_171599_("blade", CubeListBuilder.m_171558_().m_171514_(0, -16).m_171488_(0.0F, -1.0F, -8.0F, 0.0F, 16.0F, 16.0F, new CubeDeformation(0.0F)), PartPose.m_171419_(0.0F, 0.0F, 0.0F));
      blade.m_171599_("plane_1", CubeListBuilder.m_171558_().m_171514_(32, 0).m_171488_(0.0F, -8.0F, -8.0F, 0.0F, 16.0F, 16.0F, new CubeDeformation(0.0F)), PartPose.m_171423_(0.0F, 4.0F, 0.0F, 0.0F, 0.0F, -0.7854F));
      blade.m_171599_("plane_2", CubeListBuilder.m_171558_().m_171514_(32, -16).m_171488_(0.0F, -8.0F, -8.0F, 0.0F, 16.0F, 16.0F, new CubeDeformation(0.0F)), PartPose.m_171423_(0.0F, 4.0F, 0.0F, 0.0F, 0.0F, 0.7854F));
      blade.m_171599_("plane_3", CubeListBuilder.m_171558_().m_171514_(0, 0).m_171488_(0.0F, 0.0F, -8.0F, 0.0F, 16.0F, 16.0F, new CubeDeformation(0.0F)), PartPose.m_171423_(0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 1.5708F));
      return LayerDefinition.m_171565_(meshdefinition, 64, 32);
   }

   public void render(TensuraProjectile entity, float yaw, float partialTicks, PoseStack poseStack, MultiBufferSource bufferSource, int light) {
      Minecraft minecraft = Minecraft.m_91087_();
      if (minecraft.f_91074_ == null || !entity.m_20177_(minecraft.f_91074_)) {
         poseStack.m_85836_();
         Vec3 motion = entity.m_20184_();
         float xRot = -((float)(Mth.m_14136_(motion.m_165924_(), motion.f_82480_) * 57.2957763671875D) - 90.0F);
         float yRot = -((float)(Mth.m_14136_(motion.f_82481_, motion.f_82479_) * 57.2957763671875D) + 90.0F);
         poseStack.m_85845_(Vector3f.f_122225_.m_122240_(yRot));
         poseStack.m_85845_(Vector3f.f_122223_.m_122240_(xRot));
         poseStack.m_85841_(entity.getVisualSize(), entity.getVisualSize(), entity.getVisualSize());
         VertexConsumer consumer = bufferSource.m_6299_(RenderType.m_110473_(this.getTextureLocation(entity)));
         this.spike.m_104301_(poseStack, consumer, 15728880, OverlayTexture.f_118083_);
         poseStack.m_85849_();
         super.m_7392_(entity, yaw, partialTicks, poseStack, bufferSource, light);
      }
   }

   public ResourceLocation getTextureLocation(TensuraProjectile instance) {
      ResourceLocation[] resourceLocations = instance.getTextureLocation();
      return resourceLocations == null ? new ResourceLocation("tensura", "textures/blank_texture.png") : (ResourceLocation)Arrays.stream(resourceLocations).toList().get(instance.f_19797_ % resourceLocations.length);
   }
}
