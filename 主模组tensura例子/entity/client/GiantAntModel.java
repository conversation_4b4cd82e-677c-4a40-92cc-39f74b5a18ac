package com.github.manasmods.tensura.entity.client;

import com.github.manasmods.tensura.entity.GiantAntEntity;
import net.minecraft.resources.ResourceLocation;
import software.bernie.geckolib3.core.event.predicate.AnimationEvent;
import software.bernie.geckolib3.core.processor.IBone;
import software.bernie.geckolib3.model.AnimatedGeoModel;
import software.bernie.geckolib3.model.provider.data.EntityModelData;

public class GiantAntModel extends AnimatedGeoModel<GiantAntEntity> {
   public ResourceLocation getModelResource(GiantAntEntity object) {
      return new ResourceLocation("tensura", "geo/giant_ant.geo.json");
   }

   public ResourceLocation getTextureResource(GiantAntEntity instance) {
      return new ResourceLocation("tensura", "textures/entity/giant_ant/giant_ant.png");
   }

   public ResourceLocation getAnimationResource(GiantAntEntity moth) {
      return new ResourceLocation("tensura", "animations/giant_ant.animation.json");
   }

   public void setCustomAnimations(GiantAntEntity ant, int instanceId, AnimationEvent customPredicate) {
      super.setCustomAnimations(ant, instanceId, customPredicate);
      IBone saddle = this.getAnimationProcessor().getBone("Saddle");
      if (ant.isSaddled() == saddle.isHidden()) {
         saddle.setHidden(!ant.isSaddled());
      }

      EntityModelData extraData = (EntityModelData)customPredicate.getExtraDataOfType(EntityModelData.class).get(0);
      IBone head = this.getAnimationProcessor().getBone("Head");
      if (head != null) {
         head.setRotationX(extraData.headPitch * 0.017453292F);
         head.setRotationY(extraData.netHeadYaw * 0.017453292F);
      }

   }
}
