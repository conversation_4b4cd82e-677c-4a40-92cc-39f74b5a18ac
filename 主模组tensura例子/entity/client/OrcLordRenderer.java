package com.github.manasmods.tensura.entity.client;

import com.github.manasmods.tensura.entity.OrcLordEntity;
import com.github.manasmods.tensura.item.custom.TempestScaleShieldItem;
import com.mojang.blaze3d.vertex.PoseStack;
import com.mojang.blaze3d.vertex.VertexConsumer;
import com.mojang.math.Vector3f;
import net.minecraft.client.model.HumanoidModel;
import net.minecraft.client.model.geom.ModelPart;
import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.client.renderer.block.model.ItemTransforms.TransformType;
import net.minecraft.client.renderer.entity.EntityRendererProvider.Context;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.world.entity.EquipmentSlot;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.ShieldItem;
import net.minecraft.world.level.block.state.BlockState;
import software.bernie.geckolib3.core.processor.IBone;
import software.bernie.geckolib3.geo.render.built.GeoBone;
import software.bernie.geckolib3.renderers.geo.ExtendedGeoEntityRenderer;

public class OrcLordRenderer extends ExtendedGeoEntityRenderer<OrcLordEntity> {
   protected ItemStack mainHandItem;
   protected ItemStack offHandItem;

   public OrcLordRenderer(Context renderManager) {
      super(renderManager, new OrcLordModel());
      this.f_114477_ = 0.5F;
   }

   public ResourceLocation getTextureLocation(OrcLordEntity instance) {
      return new ResourceLocation("tensura", "textures/entity/orc/orc_lord.png");
   }

   public void renderEarly(OrcLordEntity orc, PoseStack poseStack, float partialTick, MultiBufferSource bufferSource, VertexConsumer buffer, int packedLight, int packedOverlay, float red, float green, float blue, float partialTicks) {
      super.renderEarly(orc, poseStack, partialTick, bufferSource, buffer, packedLight, packedOverlay, red, green, blue, partialTicks);
      this.mainHandItem = orc.m_6844_(EquipmentSlot.MAINHAND);
      this.offHandItem = orc.m_6844_(EquipmentSlot.OFFHAND);
      if (orc.m_6162_()) {
         poseStack.m_85841_(0.5F, 0.5F, 0.5F);
      }

      int tick = 40 - orc.getEvolving();
      if (orc.getEvolving() > 0 && tick > 0) {
         float scale = 1.0F + 0.5F * ((float)tick / 40.0F);
         poseStack.m_85841_(scale, scale, scale);
      }

   }

   protected ItemStack getHeldItemForBone(String boneName, OrcLordEntity currentEntity) {
      byte var4 = -1;
      switch(boneName.hashCode()) {
      case 434529071:
         if (boneName.equals("RightItem")) {
            var4 = 1;
         }
         break;
      case 1782749146:
         if (boneName.equals("LeftItem")) {
            var4 = 0;
         }
      }

      ItemStack var10000;
      switch(var4) {
      case 0:
         var10000 = currentEntity.m_21526_() ? this.mainHandItem : this.offHandItem;
         break;
      case 1:
         var10000 = currentEntity.m_21526_() ? this.offHandItem : this.mainHandItem;
         break;
      default:
         var10000 = null;
      }

      return var10000;
   }

   protected TransformType getCameraTransformForItemAtBone(ItemStack boneItem, String boneName) {
      byte var4 = -1;
      switch(boneName.hashCode()) {
      case 434529071:
         if (boneName.equals("RightItem")) {
            var4 = 1;
         }
         break;
      case 1782749146:
         if (boneName.equals("LeftItem")) {
            var4 = 0;
         }
      }

      TransformType var10000;
      switch(var4) {
      case 0:
      case 1:
         var10000 = TransformType.THIRD_PERSON_RIGHT_HAND;
         break;
      default:
         var10000 = TransformType.NONE;
      }

      return var10000;
   }

   protected void preRenderItem(PoseStack stack, ItemStack item, String boneName, OrcLordEntity currentEntity, IBone bone) {
      if (item == this.mainHandItem) {
         stack.m_85845_(Vector3f.f_122223_.m_122240_(-90.0F));
         if (item.m_41720_() instanceof ShieldItem || item.m_41720_() instanceof TempestScaleShieldItem) {
            stack.m_85837_(0.0D, 0.0D, -0.25D);
         }
      } else if (item == this.offHandItem) {
         stack.m_85845_(Vector3f.f_122223_.m_122240_(-90.0F));
         if (item.m_41720_() instanceof ShieldItem || item.m_41720_() instanceof TempestScaleShieldItem) {
            stack.m_85837_(0.0D, 0.0D, 0.25D);
            stack.m_85845_(Vector3f.f_122225_.m_122240_(180.0F));
         }
      }

   }

   protected void postRenderItem(PoseStack matrixStack, ItemStack item, String boneName, OrcLordEntity currentEntity, IBone bone) {
   }

   protected ItemStack getArmorForBone(String boneName, OrcLordEntity currentEntity) {
      return null;
   }

   protected EquipmentSlot getEquipmentSlotForArmorBone(String boneName, OrcLordEntity currentEntity) {
      return null;
   }

   protected ModelPart getArmorPartForBone(String name, HumanoidModel<?> armorModel) {
      return null;
   }

   protected BlockState getHeldBlockForBone(String boneName, OrcLordEntity currentEntity) {
      return null;
   }

   protected void preRenderBlock(PoseStack stack, BlockState block, String boneName, OrcLordEntity currentEntity) {
   }

   protected void postRenderBlock(PoseStack stack, BlockState block, String boneName, OrcLordEntity currentEntity) {
   }

   protected ResourceLocation getTextureForBone(String boneName, OrcLordEntity animatable) {
      return null;
   }

   protected boolean isArmorBone(GeoBone bone) {
      return bone.getName().endsWith("Armor");
   }
}
