package com.github.manasmods.tensura.entity.client;

import com.github.manasmods.tensura.entity.WingedCatEntity;
import net.minecraft.resources.ResourceLocation;
import software.bernie.geckolib3.core.event.predicate.AnimationEvent;
import software.bernie.geckolib3.core.processor.IBone;
import software.bernie.geckolib3.model.AnimatedGeoModel;
import software.bernie.geckolib3.model.provider.data.EntityModelData;

public class WingedCatModel extends AnimatedGeoModel<WingedCatEntity> {
   public ResourceLocation getModelResource(WingedCatEntity object) {
      return new ResourceLocation("tensura", "geo/winged_cat.geo.json");
   }

   public ResourceLocation getTextureResource(WingedCatEntity instance) {
      return new ResourceLocation("tensura", "textures/entity/winged_cat/winged_cat.png");
   }

   public ResourceLocation getAnimationResource(WingedCatEntity entity) {
      return new ResourceLocation("tensura", "animations/winged_cat.animation.json");
   }

   public void setCustomAnimations(WingedCatEntity cat, int instanceId, AnimationEvent customPredicate) {
      super.setCustomAnimations(cat, instanceId, customPredicate);
      boolean showClaws = this.shouldShowClaw(cat);
      IBone leftClaws = this.getAnimationProcessor().getBone("LeftClaws");
      if (showClaws == leftClaws.isHidden()) {
         leftClaws.setHidden(!showClaws);
      }

      IBone rightClaws = this.getAnimationProcessor().getBone("RightClaws");
      if (showClaws == rightClaws.isHidden()) {
         rightClaws.setHidden(!showClaws);
      }

      if (!cat.m_5803_()) {
         EntityModelData extraData = (EntityModelData)customPredicate.getExtraDataOfType(EntityModelData.class).get(0);
         IBone head = this.getAnimationProcessor().getBone("Head");
         if (head != null) {
            float pitch = cat.m_21825_() ? extraData.headPitch - 35.0F : extraData.headPitch;
            head.setRotationX(pitch * 0.017453292F);
            head.setRotationY(extraData.netHeadYaw * 0.017453292F);
         }

      }
   }

   private boolean shouldShowClaw(WingedCatEntity cat) {
      if (cat.getMiscAnimation() == 2) {
         return true;
      } else {
         return cat.getMiscAnimation() == 3 ? true : cat.m_21660_();
      }
   }
}
