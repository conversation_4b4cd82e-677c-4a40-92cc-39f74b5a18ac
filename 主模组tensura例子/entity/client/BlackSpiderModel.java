package com.github.manasmods.tensura.entity.client;

import com.github.manasmods.tensura.entity.BlackSpiderEntity;
import net.minecraft.resources.ResourceLocation;
import software.bernie.geckolib3.core.event.predicate.AnimationEvent;
import software.bernie.geckolib3.core.processor.IBone;
import software.bernie.geckolib3.model.AnimatedGeoModel;
import software.bernie.geckolib3.model.provider.data.EntityModelData;

public class BlackSpiderModel extends AnimatedGeoModel<BlackSpiderEntity> {
   public ResourceLocation getModelResource(BlackSpiderEntity object) {
      return new ResourceLocation("tensura", "geo/black_spider.geo.json");
   }

   public ResourceLocation getTextureResource(BlackSpiderEntity instance) {
      return BlackSpiderRenderer.getSpiderTexture(instance);
   }

   public ResourceLocation getAnimationResource(BlackSpiderEntity entity) {
      return new ResourceLocation("tensura", "animations/black_spider.animation.json");
   }

   public void setCustomAnimations(BlackSpiderEntity spider, int instanceId, AnimationEvent customPredicate) {
      super.setCustomAnimations(spider, instanceId, customPredicate);
      IBone saddle = this.getAnimationProcessor().getBone("Saddle");
      if (spider.isSaddled() == saddle.isHidden()) {
         saddle.setHidden(!spider.isSaddled());
      }

      EntityModelData extraData = (EntityModelData)customPredicate.getExtraDataOfType(EntityModelData.class).get(0);
      IBone head = this.getAnimationProcessor().getBone("Head");
      if (head != null) {
         head.setRotationX(extraData.headPitch * 0.017453292F);
         head.setRotationY(extraData.netHeadYaw * 0.017453292F);
      }

   }
}
