package com.github.manasmods.tensura.entity.client;

import com.github.manasmods.tensura.entity.GiantBatEntity;
import com.mojang.blaze3d.vertex.PoseStack;
import com.mojang.blaze3d.vertex.VertexConsumer;
import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.client.renderer.RenderType;
import net.minecraft.client.renderer.entity.EntityRendererProvider.Context;
import net.minecraft.resources.ResourceLocation;
import software.bernie.geckolib3.renderers.geo.GeoEntityRenderer;

public class GiantBatRenderer extends GeoEntityRenderer<GiantBatEntity> {
   public GiantBatRenderer(Context renderManager) {
      super(renderManager, new GiantBatModel());
      this.f_114477_ = 1.0F;
   }

   public ResourceLocation getTextureLocation(GiantBatEntity instance) {
      return new ResourceLocation("tensura", "textures/entity/giant_bat/giant_bat.png");
   }

   public RenderType getRenderType(GiantBatEntity bat, float partialTicks, PoseStack stack, MultiBufferSource renderTypeBuffer, VertexConsumer vertexBuilder, int packedLightIn, ResourceLocation textureLocation) {
      if (bat.m_6162_()) {
         stack.m_85841_(0.5F, 0.5F, 0.5F);
      }

      return RenderType.m_110473_(this.getTextureLocation(bat));
   }
}
