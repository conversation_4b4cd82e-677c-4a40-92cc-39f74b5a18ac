package com.github.manasmods.tensura.entity.multipart;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.tensura.api.entity.ai.WanderingFollowOwnerGoal;
import com.github.manasmods.tensura.api.entity.subclass.ILivingPartEntity;
import com.github.manasmods.tensura.api.entity.subclass.IRanking;
import com.github.manasmods.tensura.api.entity.subclass.ITensuraMount;
import com.github.manasmods.tensura.config.SpawnRateConfig;
import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.entity.template.TensuraTamableEntity;
import com.github.manasmods.tensura.item.food.HealingPotionItem;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.skill.IntrinsicSkills;
import com.github.manasmods.tensura.registry.sound.TensuraSoundEvents;
import java.util.Iterator;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Predicate;
import javax.annotation.Nullable;
import net.minecraft.core.BlockPos;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.syncher.EntityDataAccessor;
import net.minecraft.network.syncher.EntityDataSerializers;
import net.minecraft.network.syncher.SynchedEntityData;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.util.Mth;
import net.minecraft.world.DifficultyInstance;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.InteractionResult;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.MobSpawnType;
import net.minecraft.world.entity.MobType;
import net.minecraft.world.entity.PlayerRideable;
import net.minecraft.world.entity.SpawnGroupData;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.ai.attributes.AttributeSupplier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.goal.FloatGoal;
import net.minecraft.world.entity.ai.goal.LookAtPlayerGoal;
import net.minecraft.world.entity.ai.goal.MeleeAttackGoal;
import net.minecraft.world.entity.ai.goal.RandomLookAroundGoal;
import net.minecraft.world.entity.ai.goal.SitWhenOrderedToGoal;
import net.minecraft.world.entity.ai.goal.target.NonTameRandomTargetGoal;
import net.minecraft.world.entity.ai.goal.target.ResetUniversalAngerTargetGoal;
import net.minecraft.world.entity.animal.Animal;
import net.minecraft.world.entity.monster.Monster;
import net.minecraft.world.entity.npc.AbstractVillager;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.food.FoodProperties;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.LevelAccessor;
import net.minecraft.world.level.ServerLevelAccessor;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.gameevent.GameEvent;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.common.ForgeMod;
import org.jetbrains.annotations.NotNull;
import software.bernie.geckolib3.core.AnimationState;
import software.bernie.geckolib3.core.IAnimatable;
import software.bernie.geckolib3.core.PlayState;
import software.bernie.geckolib3.core.builder.AnimationBuilder;
import software.bernie.geckolib3.core.builder.ILoopType.EDefaultLoopTypes;
import software.bernie.geckolib3.core.controller.AnimationController;
import software.bernie.geckolib3.core.event.predicate.AnimationEvent;
import software.bernie.geckolib3.core.manager.AnimationData;
import software.bernie.geckolib3.core.manager.AnimationFactory;
import software.bernie.geckolib3.util.GeckoLibUtil;

public class EvilCentipedeEntity extends LivingMultipartHead implements IAnimatable, IRanking, ITensuraMount, PlayerRideable {
   private static final EntityDataAccessor<Integer> MISC_ANIMATION;
   private final AnimationFactory factory = GeckoLibUtil.createFactory(this);
   public int miscAnimationTicks = 0;

   public EvilCentipedeEntity(EntityType<? extends EvilCentipedeEntity> type, Level worldIn) {
      super(type, worldIn);
      this.f_21364_ = 20;
      this.f_19793_ = 3.0F;
   }

   public static AttributeSupplier setAttributes() {
      return Monster.m_33035_().m_22268_(Attributes.f_22284_, 5.0D).m_22268_(Attributes.f_22276_, 60.0D).m_22268_(Attributes.f_22281_, 10.0D).m_22268_(Attributes.f_22277_, 32.0D).m_22268_(Attributes.f_22288_, 1.0D).m_22268_(Attributes.f_22278_, 0.5D).m_22268_(Attributes.f_22279_, 0.30000001192092896D).m_22268_((Attribute)ForgeMod.SWIM_SPEED.get(), 1.25D).m_22265_();
   }

   protected void m_8099_() {
      this.f_21345_.m_25352_(0, new FloatGoal(this));
      this.f_21345_.m_25352_(1, new SitWhenOrderedToGoal(this));
      this.f_21345_.m_25352_(2, new EvilCentipedeEntity.CentipedeAttackGoal());
      this.f_21345_.m_25352_(3, new WanderingFollowOwnerGoal(this, 1.5D, 15.0F, 7.0F, false) {
         public boolean m_8036_() {
            if (!super.m_8036_()) {
               return false;
            } else {
               EvilCentipedeEntity centipede = EvilCentipedeEntity.this;
               if (centipede.m_21826_() == null) {
                  return false;
               } else {
                  Entity var3 = centipede.m_21826_().m_20202_();
                  if (var3 instanceof ILivingPartEntity) {
                     ILivingPartEntity part = (ILivingPartEntity)var3;
                     return !Objects.equals(part.getHeadId(), centipede.m_20148_());
                  } else {
                     return true;
                  }
               }
            }
         }
      });
      this.f_21345_.m_25352_(4, new TensuraTamableEntity.WanderAroundPosGoal(this, 20, 1.0D, 15, 7));
      this.f_21345_.m_25352_(5, new LookAtPlayerGoal(this, Player.class, 6.0F));
      this.f_21345_.m_25352_(6, new RandomLookAroundGoal(this));
      this.f_21346_.m_25352_(1, new TensuraTamableEntity.TensuraOwnerHurtByTargetGoal(this));
      this.f_21346_.m_25352_(2, new TensuraTamableEntity.TensuraOwnerHurtTargetGoal(this));
      this.f_21346_.m_25352_(3, new TensuraTamableEntity.TensuraHurtByTargetGoal(this));
      this.f_21346_.m_25352_(4, new NonTameRandomTargetGoal(this, Player.class, false, (Predicate)null));
      this.f_21346_.m_25352_(4, new NonTameRandomTargetGoal(this, AbstractVillager.class, false, (Predicate)null));
      this.f_21346_.m_25352_(5, new NonTameRandomTargetGoal(this, Animal.class, false, (entity) -> {
         return entity.m_6095_().m_204039_(TensuraTags.EntityTypes.ANIMAL_PREY);
      }));
      this.f_21346_.m_25352_(8, new ResetUniversalAngerTargetGoal(this, true));
   }

   @NotNull
   public MobType m_6336_() {
      return MobType.f_21642_;
   }

   protected void m_8097_() {
      super.m_8097_();
      this.f_19804_.m_135372_(MISC_ANIMATION, 0);
   }

   public void m_7380_(CompoundTag compound) {
      super.m_7380_(compound);
      compound.m_128405_("MiscAnimation", this.getMiscAnimation());
   }

   public void m_7378_(CompoundTag compound) {
      super.m_7378_(compound);
      this.f_19804_.m_135381_(MISC_ANIMATION, compound.m_128451_("MiscAnimation"));
   }

   public int getMiscAnimation() {
      return (Integer)this.f_19804_.m_135370_(MISC_ANIMATION);
   }

   public void setMiscAnimation(int animation) {
      if (this.getMiscAnimation() == 0 || animation == 0) {
         this.f_19804_.m_135381_(MISC_ANIMATION, animation);
      }
   }

   public int m_21529_() {
      return 1;
   }

   public boolean m_6673_(DamageSource source) {
      return source == DamageSource.f_19310_ || source == DamageSource.f_19322_ || super.m_6673_(source);
   }

   public boolean m_7301_(MobEffectInstance pEffectInstance) {
      return pEffectInstance.m_19544_().equals(TensuraMobEffects.PARALYSIS.get()) ? false : super.m_7301_(pEffectInstance);
   }

   public boolean m_6573_(Player player) {
      return true;
   }

   public boolean m_7848_(Animal pOtherAnimal) {
      return false;
   }

   public boolean m_142535_(float pFallDistance, float pMultiplier, DamageSource pSource) {
      return false;
   }

   public LivingMultipartBody createBody(LivingEntity parent, boolean tail) {
      EvilCentipedeBody body = new EvilCentipedeBody((EntityType)TensuraEntityTypes.EVIL_CENTIPEDE_BODY.get(), parent);
      if (tail) {
         body.setEndSegment(true);
      }

      return body;
   }

   protected float calcPartRotation(int i) {
      float rot = this.isInFluidType() ? 40.0F : 20.0F;
      return (float)((double)rot * -Math.sin((double)(this.f_19787_ * 3.0F - (float)i)));
   }

   public void m_8119_() {
      super.m_8119_();
      if (this.getMiscAnimation() != 0) {
         ++this.miscAnimationTicks;
         if (!this.m_6084_()) {
            return;
         }

         if (this.getMiscAnimation() == 2) {
            ManasSkillInstance breath = this.getParalyzingBreath();
            if (this.miscAnimationTicks >= 1 && breath != null) {
               if (this.miscAnimationTicks == 1) {
                  breath.onPressed(this);
               } else if (this.m_5448_() != null || this.getControllingPassenger() != null) {
                  breath.onHeld(this, 0);
               }

               SkillAPI.getSkillsFrom(this).syncChanges();
            }
         }

         if (this.miscAnimationTicks >= this.getAnimationTick(this.getMiscAnimation())) {
            this.setMiscAnimation(0);
            this.miscAnimationTicks = 0;
         }
      }

   }

   private int getAnimationTick(int miscAnimation) {
      byte var10000;
      switch(miscAnimation) {
      case 2:
         var10000 = 80;
         break;
      default:
         var10000 = 7;
      }

      return var10000;
   }

   public void mountAbility(Player rider) {
      if (this.getMiscAnimation() != 2) {
         this.miscAnimationTicks = 50;
         this.setMiscAnimation(2);
      }
   }

   public boolean m_7327_(@NotNull Entity entity) {
      if (super.m_7327_(entity)) {
         if (entity instanceof LivingEntity) {
            LivingEntity target = (LivingEntity)entity;
            int para = target.m_21023_((MobEffect)TensuraMobEffects.PARALYSIS.get()) ? 1 : 0;
            target.m_147207_(new MobEffectInstance((MobEffect)TensuraMobEffects.PARALYSIS.get(), 200, para, false, false), this);
         }

         this.m_146850_(GameEvent.f_223708_);
         return true;
      } else {
         return false;
      }
   }

   public void evolve() {
      this.setSegmentCount(this.getSegmentCount() + this.f_19796_.m_216339_(2, 6));
   }

   @Nullable
   private ManasSkillInstance getParalyzingBreath() {
      Optional<ManasSkillInstance> skill = SkillAPI.getSkillsFrom(this).getSkill((ManasSkill)IntrinsicSkills.PARALYSING_BREATH.get());
      if (skill.isEmpty()) {
         return null;
      } else {
         return !((ManasSkillInstance)skill.get()).canInteractSkill(this) ? null : (ManasSkillInstance)skill.get();
      }
   }

   public boolean m_6898_(ItemStack pStack) {
      FoodProperties food = pStack.getFoodProperties(this);
      return food != null && food.m_38746_();
   }

   public InteractionResult m_6071_(Player player, InteractionHand hand) {
      ItemStack itemstack = player.m_21120_(hand);
      if (itemstack.m_41720_() instanceof HealingPotionItem) {
         return super.m_6071_(player, hand);
      } else {
         InteractionResult eating = this.handleEating(player, hand, itemstack);
         if (eating.m_19077_()) {
            return eating;
         } else if (!this.f_19853_.f_46443_) {
            if (this.m_21824_() && this.m_21830_(player)) {
               if (player.m_36341_()) {
                  this.commanding(player);
               } else if (this.m_146895_() == null) {
                  this.m_21839_(false);
                  this.setWandering(false);
                  player.m_7998_(this, false);
               }

               return InteractionResult.m_19078_(this.f_19853_.f_46443_);
            } else {
               return InteractionResult.PASS;
            }
         } else {
            boolean flag = this.m_21830_(player) || this.m_21824_();
            return flag ? InteractionResult.CONSUME : InteractionResult.PASS;
         }
      }
   }

   public InteractionResult handleEating(Player player, InteractionHand hand, ItemStack itemstack) {
      if (this.m_6898_(itemstack) && this.m_21223_() < this.m_21233_()) {
         if (!player.m_7500_()) {
            itemstack.m_41774_(1);
         }

         this.m_8035_();
         this.m_9236_().m_6269_((Player)null, this, (SoundEvent)TensuraSoundEvents.EATING.get(), SoundSource.NEUTRAL, 1.0F, 1.0F);
         return InteractionResult.SUCCESS;
      } else {
         return InteractionResult.PASS;
      }
   }

   public void m_8035_() {
      super.m_8035_();
      this.m_5634_(3.0F);
   }

   public boolean m_6146_() {
      return true;
   }

   @Nullable
   public LivingEntity getControllingPassenger() {
      Iterator var1 = this.m_20197_().iterator();

      while(var1.hasNext()) {
         Entity passenger = (Entity)var1.next();
         if (passenger instanceof Player) {
            Player player = (Player)passenger;
            if (player.equals(this.m_21826_())) {
               return player;
            }
         }
      }

      return null;
   }

   public void m_7332_(Entity passenger) {
      if (this.m_20363_(passenger)) {
         passenger.m_183634_();
         float radius = -0.75F;
         float angle = 0.017453292F * this.f_20883_;
         double extraX = (double)(radius * Mth.m_14031_((float)(3.141592653589793D + (double)angle)));
         double extraZ = (double)(radius * Mth.m_14089_(angle));
         double yOffset = this.m_20186_() + this.m_6048_() + passenger.m_6049_();
         passenger.m_6034_(this.m_20185_() + extraX, yOffset, this.m_20189_() + extraZ);
      }
   }

   public void m_7023_(Vec3 pTravelVector) {
      if (this.m_6084_()) {
         LivingEntity controller = this.getControllingPassenger();
         if (this.m_20160_() && controller != null) {
            this.m_146922_(controller.m_146908_());
            this.f_19859_ = this.m_146908_();
            this.m_146926_(controller.m_146909_() * 0.5F);
            this.m_19915_(this.m_146908_(), this.m_146909_());
            this.f_20883_ = this.m_146908_();
            this.f_20885_ = this.f_20883_;
            float f = controller.f_20900_ * 0.5F;
            float f1 = controller.f_20902_;
            if (f1 <= 0.0F) {
               f1 *= 0.25F;
            }

            this.f_20887_ = this.m_6113_() * 0.1F;
            if (this.m_6109_()) {
               float speed = (float)this.m_21133_(Attributes.f_22279_) / 6.0F;
               if (controller.m_20142_()) {
                  speed = (float)((double)speed * 1.5D);
               }

               this.m_7910_(speed);
               if (this.isInFluidType((fluidType, height) -> {
                  return height > this.m_20204_();
               }) && f1 > 0.0F) {
                  this.m_20256_(this.m_20184_().m_82520_(0.0D, 0.03D, 0.0D));
                  super.m_7023_(new Vec3((double)f, (double)controller.f_20901_, (double)f1));
               } else {
                  super.m_7023_(new Vec3((double)f, pTravelVector.f_82480_, (double)f1));
               }

               super.m_7023_(new Vec3((double)f, pTravelVector.f_82480_, (double)f1));
            } else if (controller instanceof Player) {
               this.m_20256_(Vec3.f_82478_);
            }

            this.m_146872_();
         } else {
            this.f_20887_ = 0.02F;
            super.m_7023_(pTravelVector);
         }
      }

   }

   public boolean m_5545_(LevelAccessor pLevel, MobSpawnType pSpawnReason) {
      return SpawnRateConfig.rollSpawn((Integer)SpawnRateConfig.INSTANCE.evilCentipedeSpawnRate.get(), this.m_217043_(), pSpawnReason) && super.m_5545_(pLevel, pSpawnReason);
   }

   @Nullable
   public SpawnGroupData m_6518_(ServerLevelAccessor worldIn, DifficultyInstance difficultyIn, MobSpawnType reason, @Nullable SpawnGroupData spawnDataIn, @Nullable CompoundTag dataTag) {
      this.setSegmentCount(this.f_19796_.m_216339_(10, 13));
      return super.m_6518_(worldIn, difficultyIn, reason, spawnDataIn, dataTag);
   }

   protected SoundEvent m_7975_(@NotNull DamageSource damageSourceIn) {
      return SoundEvents.f_12434_;
   }

   protected SoundEvent m_5592_() {
      return SoundEvents.f_12433_;
   }

   protected void m_7355_(@NotNull BlockPos pos, @NotNull BlockState blockIn) {
      this.m_5496_(SoundEvents.f_12435_, 1.0F, 1.0F);
   }

   private <E extends IAnimatable> PlayState predicate(AnimationEvent<E> event) {
      if (this.getMiscAnimation() == 2) {
         event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.evil_centipede.breath", EDefaultLoopTypes.LOOP));
      } else if (event.isMoving()) {
         event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.evil_centipede.walk", EDefaultLoopTypes.LOOP));
      } else {
         event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.evil_centipede.idle", EDefaultLoopTypes.LOOP));
      }

      return PlayState.CONTINUE;
   }

   private <E extends IAnimatable> PlayState miscPredicate(AnimationEvent<E> event) {
      if (event.getController().getAnimationState().equals(AnimationState.Stopped)) {
         event.getController().markNeedsReload();
         if (this.getMiscAnimation() == 1) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.evil_centipede.bite", EDefaultLoopTypes.PLAY_ONCE));
         }
      }

      return PlayState.CONTINUE;
   }

   public void registerControllers(AnimationData data) {
      data.addAnimationController(new AnimationController(this, "controller", 0.0F, this::predicate));
      data.addAnimationController(new AnimationController(this, "miscController", 0.0F, this::miscPredicate));
   }

   public AnimationFactory getFactory() {
      return this.factory;
   }

   static {
      MISC_ANIMATION = SynchedEntityData.m_135353_(EvilCentipedeEntity.class, EntityDataSerializers.f_135028_);
   }

   class CentipedeAttackGoal extends MeleeAttackGoal {
      private final EvilCentipedeEntity centipede = EvilCentipedeEntity.this;

      public CentipedeAttackGoal() {
         super(EvilCentipedeEntity.this, 1.5D, true);
      }

      public boolean m_8036_() {
         return this.centipede.m_21827_() ? false : super.m_8036_();
      }

      public boolean m_8045_() {
         return this.centipede.m_21827_() ? false : super.m_8045_();
      }

      protected void m_6739_(LivingEntity pEnemy, double pDistToEnemySqr) {
         double distance = this.m_6639_(pEnemy);
         if (this.centipede.getMiscAnimation() == 0) {
            int randomAttack = this.randomAttack(pDistToEnemySqr);
            double var10000;
            switch(randomAttack) {
            case 2:
               var10000 = 144.0D;
               break;
            default:
               var10000 = distance;
            }

            double attackRange = var10000;
            if (pDistToEnemySqr <= attackRange && this.m_25564_()) {
               this.m_25563_();
               this.centipede.setMiscAnimation(randomAttack);
               if (randomAttack == 1) {
                  this.centipede.m_7327_(pEnemy);
               }
            }
         }

      }

      protected int randomAttack(double distance) {
         return this.centipede.f_19796_.m_188503_(10) != 1 || !(distance >= 25.0D) && this.centipede.f_19796_.m_188503_(10) != 1 ? 1 : 2;
      }
   }
}
