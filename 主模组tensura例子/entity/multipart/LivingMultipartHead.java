package com.github.manasmods.tensura.entity.multipart;

import com.github.manasmods.tensura.api.entity.subclass.ILivingPartEntity;
import com.github.manasmods.tensura.entity.template.TensuraTamableEntity;
import java.util.Arrays;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import javax.annotation.Nullable;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.syncher.EntityDataAccessor;
import net.minecraft.network.syncher.EntityDataSerializers;
import net.minecraft.network.syncher.SynchedEntityData;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.util.Mth;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.TamableAnimal;
import net.minecraft.world.level.Level;
import net.minecraft.world.phys.Vec3;

public abstract class LivingMultipartHead extends TensuraTamableEntity {
   private static final EntityDataAccessor<Optional<UUID>> CHILD_UUID;
   private static final EntityDataAccessor<Integer> CHILD_ID;
   private static final EntityDataAccessor<Integer> SEGMENT_COUNT;
   public final float[] ringBuffer = new float[64];
   public int ringBufferIndex = -1;
   protected LivingMultipartBody[] parts;

   public LivingMultipartHead(EntityType<? extends LivingMultipartHead> type, Level worldIn) {
      super(type, worldIn);
   }

   protected void m_8097_() {
      super.m_8097_();
      this.f_19804_.m_135372_(CHILD_UUID, Optional.empty());
      this.f_19804_.m_135372_(CHILD_ID, -1);
      this.f_19804_.m_135372_(SEGMENT_COUNT, 5);
   }

   public void m_7380_(CompoundTag compound) {
      super.m_7380_(compound);
      if (this.getChildId() != null) {
         compound.m_128362_("ChildUUID", this.getChildId());
      }

      compound.m_128405_("SegCount", this.getSegmentCount());
   }

   public void m_7378_(CompoundTag compound) {
      super.m_7378_(compound);
      if (compound.m_128403_("ChildUUID")) {
         this.setChildId(compound.m_128342_("ChildUUID"));
      }

      this.setSegmentCount(compound.m_128451_("SegCount"));
   }

   public int getSegmentCount() {
      return Math.max((Integer)this.f_19804_.m_135370_(SEGMENT_COUNT), 1);
   }

   public void setSegmentCount(int segments) {
      this.f_19804_.m_135381_(SEGMENT_COUNT, segments);
   }

   @Nullable
   public UUID getChildId() {
      return (UUID)((Optional)this.f_19804_.m_135370_(CHILD_UUID)).orElse((Object)null);
   }

   public void setChildId(@Nullable UUID uniqueId) {
      this.f_19804_.m_135381_(CHILD_UUID, Optional.ofNullable(uniqueId));
   }

   public Entity getChild() {
      UUID id = this.getChildId();
      return id != null && !this.f_19853_.f_46443_ ? ((ServerLevel)this.f_19853_).m_8791_(id) : null;
   }

   public boolean m_7307_(Entity pEntity) {
      if (pEntity instanceof LivingMultipartBody) {
         LivingMultipartBody body = (LivingMultipartBody)pEntity;
         if (Objects.equals(body.getHeadId(), this.m_20148_())) {
            return true;
         }

         if (body.getHead() != null && pEntity.m_7307_(body.getHead())) {
            return true;
         }
      }

      return super.m_7307_(pEntity);
   }

   public boolean m_6779_(LivingEntity pTarget) {
      if (pTarget instanceof LivingMultipartBody) {
         LivingMultipartBody body = (LivingMultipartBody)pTarget;
         if (Objects.equals(body.getHeadId(), this.m_20148_())) {
            return false;
         }
      }

      return super.m_6779_(pTarget);
   }

   public int m_8132_() {
      return 1;
   }

   public int m_8085_() {
      return 1;
   }

   private boolean shouldReplaceParts() {
      if (this.parts != null && this.parts[0] != null && this.parts.length == this.getSegmentCount()) {
         for(int i = 0; i < this.getSegmentCount(); ++i) {
            if (this.parts[i] == null) {
               return true;
            }
         }

         return false;
      } else {
         return true;
      }
   }

   abstract LivingMultipartBody createBody(LivingEntity var1, boolean var2);

   public void m_6842_(boolean pInvisible) {
      super.m_6842_(pInvisible);
      if (this.getChild() != null) {
         this.getChild().m_6842_(pInvisible);
      }

   }

   public void m_21837_(boolean pSitting) {
      super.m_21837_(pSitting);
      Entity var3 = this.getChild();
      if (var3 instanceof TamableAnimal) {
         TamableAnimal child = (TamableAnimal)var3;
         child.m_21837_(pSitting);
      }

   }

   public void m_6138_() {
      List<Entity> list = this.f_19853_.m_45933_(this, this.m_20191_().m_82363_(0.2D, 0.0D, 0.2D));
      Iterator var2 = list.iterator();

      while(true) {
         Entity entity;
         ILivingPartEntity part;
         do {
            do {
               if (!var2.hasNext()) {
                  return;
               }

               entity = (Entity)var2.next();
            } while(!entity.m_6094_());

            if (!(entity instanceof ILivingPartEntity)) {
               break;
            }

            part = (ILivingPartEntity)entity;
         } while(Objects.equals(part.getHeadId(), this.m_20148_()));

         entity.m_7334_(this);
      }
   }

   protected void updateRot() {
      this.f_20883_ = Mth.m_14036_(this.m_146908_(), this.f_20883_ - 2.0F, this.f_20883_ + 2.0F);
      this.f_20885_ = this.f_20883_;
   }

   public void m_8119_() {
      super.m_8119_();
      this.f_19817_ = false;
      this.updateRot();
      this.updateRingBuffer();
      if (!this.f_19853_.f_46443_) {
         Entity child = this.getChild();
         float prevReqRot;
         LivingMultipartBody part;
         if (child == null) {
            LivingEntity partParent = this;
            this.parts = new LivingMultipartBody[this.getSegmentCount()];
            Vec3 prevPos = this.m_20182_();
            float backOffset = this.m_20205_() / 2.0F;

            for(int i = 0; i < this.getSegmentCount(); ++i) {
               float prevReqRot = this.calcPartRotation(i) + this.getPartYaw(i);
               prevReqRot = this.calcPartRotation(i + 1) + this.getPartYaw(i);
               LivingMultipartBody part = this.createBody((LivingEntity)partParent, i == this.getSegmentCount() - 1);
               part.setHead(this);
               part.setParent((Entity)partParent);
               part.setBodyIndex(i);
               if (partParent == this) {
                  this.setChildId(part.m_20148_());
                  this.f_19804_.m_135381_(CHILD_ID, part.m_19879_());
               }

               if (partParent instanceof LivingMultipartBody) {
                  part = (LivingMultipartBody)partParent;
                  part.setChildId(part.m_20148_());
               }

               part.m_146884_(part.repositionParts(backOffset, prevPos, this.m_146909_(), prevReqRot, prevReqRot, false));
               this.f_19853_.m_7967_(part);
               this.parts[i] = part;
               partParent = part;
               backOffset = part.getBackOffset();
               prevPos = part.m_20182_();
            }
         }

         if (this.f_19797_ > 1) {
            if (this.shouldReplaceParts()) {
               Entity var12 = this.getChild();
               if (var12 instanceof LivingMultipartBody) {
                  LivingMultipartBody firstBody = (LivingMultipartBody)var12;
                  this.parts = new LivingMultipartBody[this.getSegmentCount()];
                  this.parts[0] = firstBody;
                  this.f_19804_.m_135381_(CHILD_ID, this.parts[0].m_19879_());
                  LivingEntity partParent = this.parts[0];
                  Vec3 prevPos = this.m_20182_();
                  float backOffset = this.m_20205_() / 2.0F;

                  for(int i = 1; i < this.getSegmentCount(); ++i) {
                     Entity var18 = this.parts[i - 1].getChild();
                     if (var18 instanceof LivingMultipartBody) {
                        LivingMultipartBody body = (LivingMultipartBody)var18;
                        this.parts[i] = body;
                        partParent = body;
                        backOffset = body.getBackOffset();
                        prevPos = body.m_20182_();
                     } else {
                        if (this.parts[i - 1].isEndSegment()) {
                           this.parts[i - 1].setEndSegment(false);
                        }

                        prevReqRot = this.calcPartRotation(i) + this.getPartYaw(i);
                        float reqRot = this.calcPartRotation(i + 1) + this.getPartYaw(i);
                        part = this.createBody(partParent, i == this.getSegmentCount() - 1);
                        part.setHead(this);
                        part.setParent(partParent);
                        part.setBodyIndex(i);
                        if (partParent instanceof LivingMultipartBody) {
                           LivingMultipartBody body = (LivingMultipartBody)partParent;
                           body.setChildId(part.m_20148_());
                        }

                        part.m_146884_(part.repositionParts(backOffset, prevPos, this.m_146909_(), prevReqRot, reqRot, false));
                        this.f_19853_.m_7967_(part);
                        this.parts[i] = part;
                        partParent = part;
                        backOffset = part.getBackOffset();
                        prevPos = part.m_20182_();
                     }
                  }
               }
            }

            this.updatePartPosition();
         }
      }

   }

   protected void updateRingBuffer() {
      if (this.ringBufferIndex < 0) {
         Arrays.fill(this.ringBuffer, this.f_20883_);
      }

      if (this.shouldUpdateRingBuffer() || this.ringBufferIndex < 0) {
         ++this.ringBufferIndex;
      }

      if (this.ringBufferIndex == this.ringBuffer.length) {
         this.ringBufferIndex = 0;
      }

      this.ringBuffer[this.ringBufferIndex] = this.m_146908_();
   }

   protected void updatePartPosition() {
      if (this.parts != null) {
         Vec3 prev = this.m_20182_();
         float xRot = this.m_146909_();
         float backOffset = this.m_20205_() / 2.0F;

         for(int i = 0; i < this.getSegmentCount(); ++i) {
            if (this.parts[i] != null) {
               float prevReqRot = this.calcPartRotation(i) + this.getPartYaw(i);
               float reqRot = this.calcPartRotation(i + 1) + this.getPartYaw(i);
               prev = this.parts[i].repositionParts(backOffset, prev, xRot, prevReqRot, reqRot, true);
               xRot = this.parts[i].m_146909_();
               backOffset = this.parts[i].getBackOffset();
            }
         }

      }
   }

   protected boolean shouldUpdateRingBuffer() {
      if (this.m_6688_() != null) {
         return true;
      } else {
         return this.m_20184_().m_82556_() >= 0.005D;
      }
   }

   protected float getPartYaw(int i) {
      return this.getRingBuffer(4 + i, 1.0F);
   }

   protected float calcPartRotation(int i) {
      return 0.0F;
   }

   public float getRingBuffer(int bufferOffset, float partialTicks) {
      if (this.m_21224_()) {
         partialTicks = 0.0F;
      }

      partialTicks = 1.0F - partialTicks;
      int i = this.ringBufferIndex - bufferOffset & 63;
      int j = this.ringBufferIndex - bufferOffset - 1 & 63;
      float d0 = this.ringBuffer[i];
      float d1 = this.ringBuffer[j] - d0;
      return Mth.m_14177_(d0 + d1 * partialTicks);
   }

   static {
      CHILD_UUID = SynchedEntityData.m_135353_(LivingMultipartHead.class, EntityDataSerializers.f_135041_);
      CHILD_ID = SynchedEntityData.m_135353_(LivingMultipartHead.class, EntityDataSerializers.f_135028_);
      SEGMENT_COUNT = SynchedEntityData.m_135353_(LivingMultipartHead.class, EntityDataSerializers.f_135028_);
   }
}
