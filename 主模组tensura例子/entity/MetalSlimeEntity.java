package com.github.manasmods.tensura.entity;

import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.api.entity.ai.PanicAroundEntityGoal;
import com.github.manasmods.tensura.api.entity.ai.WanderingFollowOwnerGoal;
import com.github.manasmods.tensura.api.entity.controller.JumpingEntityMoveControl;
import com.github.manasmods.tensura.api.entity.subclass.IJumpingEntity;
import com.github.manasmods.tensura.entity.template.TensuraTamableEntity;
import com.github.manasmods.tensura.entity.variant.SlimeType;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import com.github.manasmods.tensura.util.damage.TensuraDamageSource;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.PathfinderMob;
import net.minecraft.world.entity.ai.attributes.AttributeSupplier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.goal.MeleeAttackGoal;
import net.minecraft.world.entity.ai.goal.RandomLookAroundGoal;
import net.minecraft.world.entity.ai.goal.SitWhenOrderedToGoal;
import net.minecraft.world.entity.monster.Monster;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.level.Level;
import net.minecraft.world.phys.Vec3;
import software.bernie.geckolib3.core.AnimationState;
import software.bernie.geckolib3.core.IAnimatable;
import software.bernie.geckolib3.core.PlayState;
import software.bernie.geckolib3.core.builder.AnimationBuilder;
import software.bernie.geckolib3.core.builder.ILoopType.EDefaultLoopTypes;
import software.bernie.geckolib3.core.controller.AnimationController;
import software.bernie.geckolib3.core.event.predicate.AnimationEvent;
import software.bernie.geckolib3.core.manager.AnimationData;
import software.bernie.geckolib3.core.manager.AnimationFactory;
import software.bernie.geckolib3.util.GeckoLibUtil;

public class MetalSlimeEntity extends SlimeEntity {
   private final AnimationFactory factory = GeckoLibUtil.createFactory(this);
   public static final AnimationBuilder IDLE;
   public static final AnimationBuilder WALK;
   public static final AnimationBuilder JUMPING;
   public static final AnimationBuilder DAMAGED;
   protected int regenAmount;

   public MetalSlimeEntity(EntityType<? extends MetalSlimeEntity> pEntityType, Level pLevel) {
      super(pEntityType, pLevel);
      this.f_21342_ = new MetalSlimeEntity.MetalSlimeMoveControl(this);
      this.regenAmount = 4;
      this.f_21364_ = 400;
      this.setSize(4, false, false, false);
      this.setType(SlimeType.MERGE);
   }

   public static AttributeSupplier setAttributes() {
      return Monster.m_33035_().m_22268_(Attributes.f_22276_, 20.0D).m_22268_(Attributes.f_22281_, 1.0D).m_22268_(Attributes.f_22278_, 1.0D).m_22268_(Attributes.f_22284_, 10.0D).m_22268_(Attributes.f_22277_, 64.0D).m_22268_(Attributes.f_22288_, 1.2D).m_22268_(Attributes.f_22279_, 2.0D).m_22265_();
   }

   protected void m_8099_() {
      this.f_21345_.m_25352_(1, new TensuraTamableEntity.WanderAroundPosGoal(this, 60, 1.0D, 10, 7));
      this.f_21345_.m_25352_(2, new RandomLookAroundGoal(this));
      this.f_21345_.m_25352_(1, new PanicAroundEntityGoal(this, 2.0D, Player.class, 16.0F));
      this.f_21346_.m_25352_(2, (new TensuraTamableEntity.TensuraHurtByTargetGoal(this)).m_26044_(new Class[0]));
      this.f_21345_.m_25352_(4, new MeleeAttackGoal(this, 1.2D, false) {
         public boolean m_8036_() {
            return !MetalSlimeEntity.this.m_21824_() ? false : super.m_8036_();
         }
      });
      this.f_21345_.m_25352_(1, new SitWhenOrderedToGoal(this));
      this.f_21345_.m_25352_(1, new WanderingFollowOwnerGoal(this, 0.2D, 20.0F, 5.0F, false));
      this.f_21346_.m_25352_(1, new TensuraTamableEntity.TensuraOwnerHurtByTargetGoal(this));
      this.f_21346_.m_25352_(2, new TensuraTamableEntity.TensuraOwnerHurtTargetGoal(this));
   }

   public void m_8119_() {
      super.m_8119_();
      this.waterMovement();
   }

   protected void selfRegen() {
      this.m_5634_((float)this.regenAmount);
      this.selfRegen = 20;
   }

   protected void waterMovement() {
      if (this.m_20069_()) {
         this.m_20256_(this.m_20184_().m_82520_(0.0D, -0.1D, 0.0D));
      }
   }

   public boolean m_6673_(DamageSource source) {
      return this.ignoreDamageSource(source) || super.m_6673_(source);
   }

   public void setSize(int pSize, boolean pResetHealth) {
      this.setSize(pSize, true, pResetHealth, false);
   }

   public boolean ignoreDamageSource(DamageSource source) {
      if (source instanceof TensuraDamageSource) {
         TensuraDamageSource damageSource = (TensuraDamageSource)source;
         if (damageSource.getIgnoreResistance() >= 1.0F) {
            return false;
         }
      }

      Entity var3 = source.m_7639_();
      if (var3 instanceof LivingEntity) {
         LivingEntity living = (LivingEntity)var3;
         if (SkillUtils.reducingResistances(living)) {
            return false;
         }
      }

      return DamageSourceHelper.isNaturalEffects(source);
   }

   public int getJumpDelay() {
      return this.f_19796_.m_188503_(10);
   }

   protected float m_6108_() {
      return 0.9F;
   }

   public boolean playerInRadius() {
      return !this.f_19853_.m_6443_(Player.class, this.m_20191_().m_82377_(10.0D, 10.0D, 10.0D), (player) -> {
         return !player.m_7500_() && !player.m_5833_();
      }).isEmpty();
   }

   protected boolean m_8028_() {
      return false;
   }

   protected void m_6135_() {
      Vec3 vec3 = this.m_20184_();
      this.m_20334_(vec3.f_82479_, (double)this.m_6118_() * 2.0D, vec3.f_82481_);
      this.f_19812_ = true;
   }

   public boolean isTamingFood(ItemStack stack) {
      return false;
   }

   public boolean isDyeable() {
      return false;
   }

   public boolean m_6785_(double pDistanceToClosestPlayer) {
      return false;
   }

   private <E extends IAnimatable> PlayState predicate(AnimationEvent<E> event) {
      if (!event.isMoving()) {
         if (this.m_20096_()) {
            event.getController().setAnimation(IDLE);
         }

         return PlayState.CONTINUE;
      } else if ((this.m_20096_() || this.m_20072_() || this.m_20077_() || this.f_19798_) && !this.isHurt()) {
         event.getController().setAnimation(WALK);
         return PlayState.CONTINUE;
      } else {
         event.getController().setAnimation(DAMAGED);
         return PlayState.CONTINUE;
      }
   }

   private <T extends IAnimatable> PlayState hurtPredicate(AnimationEvent<T> event) {
      if (event.getController().getAnimationState().equals(AnimationState.Stopped) && this.isHurt()) {
         event.getController().markNeedsReload();
         event.getController().setAnimation(DAMAGED);
      }

      return PlayState.CONTINUE;
   }

   private <T extends IAnimatable> PlayState crouchPredicate(AnimationEvent<T> event) {
      if (event.getController().getAnimationState().equals(AnimationState.Stopped) && this.jump()) {
         event.getController().markNeedsReload();
         event.getController().setAnimation(JUMPING);
      }

      return PlayState.CONTINUE;
   }

   public void registerControllers(AnimationData data) {
      data.addAnimationController(new AnimationController(this, "controller", 0.0F, this::predicate));
      data.addAnimationController(new AnimationController(this, "hurtController", 0.0F, this::hurtPredicate));
      data.addAnimationController(new AnimationController(this, "crouchController", 0.0F, this::crouchPredicate));
   }

   public AnimationFactory getFactory() {
      return this.factory;
   }

   static {
      IDLE = (new AnimationBuilder()).addAnimation("animation.slime.idle", EDefaultLoopTypes.LOOP);
      WALK = (new AnimationBuilder()).addAnimation("animation.slime.walk", EDefaultLoopTypes.LOOP);
      JUMPING = (new AnimationBuilder()).addAnimation("animation.slime.jump", EDefaultLoopTypes.PLAY_ONCE);
      DAMAGED = (new AnimationBuilder()).addAnimation("animation.slime.hurt", EDefaultLoopTypes.PLAY_ONCE);
   }

   protected static class MetalSlimeMoveControl extends JumpingEntityMoveControl {
      public MetalSlimeMoveControl(PathfinderMob slimeIn) {
         super(slimeIn, 0.05F);
      }

      public void setJumpDelay(IJumpingEntity jumpingEntity) {
         this.jumpDelay = jumpingEntity.getJumpDelay();
         if (this.mob.m_5448_() != null) {
            PathfinderMob var3 = this.mob;
            if (var3 instanceof MetalSlimeEntity) {
               MetalSlimeEntity slime = (MetalSlimeEntity)var3;
               if (slime.playerInRadius()) {
                  this.jumpDelay /= 2;
                  return;
               }
            }

         }
      }
   }
}
