package com.github.manasmods.tensura.entity;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.magic.MagicElemental;
import com.github.manasmods.tensura.ability.magic.spiritual.earth.EarthSpikesMagic;
import com.github.manasmods.tensura.api.entity.ai.FlyingFollowOwnerGoal;
import com.github.manasmods.tensura.api.entity.ai.TamableFollowParentGoal;
import com.github.manasmods.tensura.api.entity.ai.UndergroundTargetingEntitiesGoal;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.entity.human.HinataSakaguchiEntity;
import com.github.manasmods.tensura.entity.magic.TensuraProjectile;
import com.github.manasmods.tensura.entity.magic.field.GravityField;
import com.github.manasmods.tensura.entity.magic.projectile.GravitySphereProjectile;
import com.github.manasmods.tensura.entity.template.GreaterSpiritEntity;
import com.github.manasmods.tensura.entity.template.HumanoidNPCEntity;
import com.github.manasmods.tensura.entity.template.TensuraTamableEntity;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.items.TensuraMaterialItems;
import com.github.manasmods.tensura.registry.magic.SpiritualMagics;
import com.github.manasmods.tensura.registry.skill.ExtraSkills;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import com.github.manasmods.tensura.util.damage.TensuraDamageSource;
import java.util.Iterator;
import java.util.List;
import net.minecraft.commands.arguments.EntityAnchorArgument.Anchor;
import net.minecraft.core.particles.BlockParticleOption;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.server.level.ServerBossEvent;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.util.Mth;
import net.minecraft.world.SimpleContainer;
import net.minecraft.world.BossEvent.BossBarColor;
import net.minecraft.world.BossEvent.BossBarOverlay;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityDimensions;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.Mob;
import net.minecraft.world.entity.MobSpawnType;
import net.minecraft.world.entity.Pose;
import net.minecraft.world.entity.SpawnGroupData;
import net.minecraft.world.entity.Entity.RemovalReason;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.ai.attributes.AttributeSupplier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.goal.LookAtPlayerGoal;
import net.minecraft.world.entity.ai.goal.MeleeAttackGoal;
import net.minecraft.world.entity.ai.goal.RandomLookAroundGoal;
import net.minecraft.world.entity.ai.goal.SitWhenOrderedToGoal;
import net.minecraft.world.entity.ai.goal.target.ResetUniversalAngerTargetGoal;
import net.minecraft.world.entity.ai.navigation.GroundPathNavigation;
import net.minecraft.world.entity.ai.targeting.TargetingConditions;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.entity.projectile.Projectile;
import net.minecraft.world.item.Item;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.phys.AABB;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.common.ForgeMod;
import software.bernie.geckolib3.core.AnimationState;
import software.bernie.geckolib3.core.IAnimatable;
import software.bernie.geckolib3.core.PlayState;
import software.bernie.geckolib3.core.builder.AnimationBuilder;
import software.bernie.geckolib3.core.builder.ILoopType.EDefaultLoopTypes;
import software.bernie.geckolib3.core.controller.AnimationController;
import software.bernie.geckolib3.core.event.predicate.AnimationEvent;
import software.bernie.geckolib3.core.manager.AnimationData;

public class WarGnomeEntity extends GreaterSpiritEntity {
   public WarGnomeEntity(EntityType<? extends WarGnomeEntity> pEntityType, Level pLevel) {
      super(pEntityType, pLevel);
      this.bossEvent = (ServerBossEvent)(new ServerBossEvent(this.m_5446_(), BossBarColor.YELLOW, BossBarOverlay.NOTCHED_20)).m_7005_(true);
   }

   protected void switchNavigator(boolean onLand) {
      this.f_21342_ = new TensuraTamableEntity.SleepMoveControl() {
         public void m_8126_() {
            if (WarGnomeEntity.this.getMiscAnimation() != 3) {
               if (WarGnomeEntity.this.getMiscAnimation() != 4) {
                  super.m_8126_();
               }
            }
         }
      };
      this.f_21344_ = new GroundPathNavigation(this, this.f_19853_);
      this.wasFlying = false;
   }

   public static AttributeSupplier setAttributes() {
      return Mob.m_21552_().m_22268_(Attributes.f_22284_, 20.0D).m_22268_(Attributes.f_22281_, 40.0D).m_22268_(Attributes.f_22276_, 400.0D).m_22268_(Attributes.f_22279_, 0.20000000298023224D).m_22268_(Attributes.f_22277_, 64.0D).m_22268_(Attributes.f_22278_, 1.0D).m_22268_((Attribute)ForgeMod.ATTACK_RANGE.get(), 3.0D).m_22268_((Attribute)ForgeMod.SWIM_SPEED.get(), 3.0D).m_22265_();
   }

   protected void m_8099_() {
      this.f_21345_.m_25352_(1, new SitWhenOrderedToGoal(this));
      this.f_21345_.m_25352_(2, new HumanoidNPCEntity.EatingItemGoal(this, (entity) -> {
         return this.shouldHeal();
      }, 3.0F));
      this.f_21345_.m_25352_(4, new WarGnomeEntity.WarGnomeAttackGoal(this));
      this.f_21345_.m_25352_(3, new GreaterSpiritEntity.FollowHinataGoal(this, 1.0D, HinataSakaguchiEntity.class));
      this.f_21345_.m_25352_(5, new FlyingFollowOwnerGoal(this, 0.7D, 10.0F, 4.0F, true, false));
      this.f_21345_.m_25352_(6, new TamableFollowParentGoal(this, 1.5D));
      this.f_21345_.m_25352_(7, new GreaterSpiritEntity.WalkGoal(this));
      this.f_21345_.m_25352_(8, new TensuraTamableEntity.FlyingWanderAroundPosGoal(this, 1.0D, 12));
      this.f_21345_.m_25352_(8, new RandomLookAroundGoal(this));
      this.f_21345_.m_25352_(9, new LookAtPlayerGoal(this, Player.class, 6.0F));
      this.f_21346_.m_25352_(1, new TensuraTamableEntity.TensuraOwnerHurtByTargetGoal(this));
      this.f_21346_.m_25352_(2, new TensuraTamableEntity.TensuraOwnerHurtTargetGoal(this));
      this.f_21346_.m_25352_(3, (new TensuraTamableEntity.TensuraHurtByTargetGoal(this, new Class[]{WarGnomeEntity.class})).m_26044_(new Class[0]));
      this.f_21346_.m_25352_(4, new UndergroundTargetingEntitiesGoal(this, LivingEntity.class, false, 8.0F, this::shouldAttack));
      this.f_21346_.m_25352_(8, new ResetUniversalAngerTargetGoal(this, true));
   }

   public boolean usingMeleeWeapon() {
      return false;
   }

   public EntityDimensions m_6972_(Pose pPose) {
      EntityDimensions entitydimensions = super.m_6972_(pPose);
      return this.m_5803_() ? entitydimensions.m_20390_(1.0F, 0.3F) : entitydimensions;
   }

   public boolean m_6469_(DamageSource pSource, float pAmount) {
      if (this.m_6673_(pSource)) {
         return false;
      } else if (pSource.m_7639_() instanceof BeastGnomeEntity) {
         return false;
      } else {
         if (DamageSourceHelper.isNaturalEffects(pSource)) {
            pAmount *= 0.2F;
         } else {
            pAmount *= this.getPhysicalAttackInput(pSource);
         }

         int randomBlock;
         label128: {
            if (!this.m_21827_() && (this.getMiscAnimation() == 0 || this.getMiscAnimation() <= -2) && (double)this.f_19796_.m_188501_() <= 0.3D) {
               randomBlock = this.m_217043_().m_188503_(4);
               if (DamageSourceHelper.isPhysicalAttack(pSource) && !pSource.m_19378_()) {
                  if (!(pSource instanceof TensuraDamageSource)) {
                     break label128;
                  }

                  TensuraDamageSource damageSource = (TensuraDamageSource)pSource;
                  if (!(damageSource.getIgnoreBarrier() >= 2.0F)) {
                     break label128;
                  }
               }
            }

            boolean hurt = super.m_6469_(pSource, pAmount);
            if (hurt) {
               Entity var5 = pSource.m_7639_();
               if (var5 instanceof LivingEntity) {
                  LivingEntity damageSource = (LivingEntity)var5;
                  if (!damageSource.m_6084_()) {
                     return true;
                  }

                  if (damageSource instanceof Player) {
                     Player player = (Player)damageSource;
                     if (player.m_7500_() || player.m_5833_()) {
                        return true;
                     }
                  }

                  List<BeastGnomeEntity> list = this.f_19853_.m_6443_(BeastGnomeEntity.class, this.m_20191_().m_82400_(32.0D), (entity) -> {
                     return !entity.m_21824_();
                  });
                  if (!list.isEmpty()) {
                     list.forEach((gnome) -> {
                        gnome.m_6710_(damageSource);
                     });
                  }
               }
            }

            return hurt;
         }

         Entity var7 = pSource.m_7640_();
         Vec3 vec3;
         if (var7 instanceof LivingEntity) {
            LivingEntity target = (LivingEntity)var7;
            if (randomBlock == 3) {
               this.setMiscAnimation(-4);
               SkillHelper.knockBack(this, target, 3.0F);
            } else if (randomBlock == 2) {
               this.setMiscAnimation(-3);
               vec3 = (new Vec3(target.m_20185_() - this.m_20185_(), target.m_20186_() - this.m_20186_() + 1.0D, target.m_20189_() - this.m_20189_())).m_82490_(1.0D / (double)target.m_20270_(this));
               target.m_20256_(vec3.m_82541_().m_82490_(1.0D));
               target.f_19812_ = true;
               target.f_19864_ = true;
            } else {
               this.setMiscAnimation(-2);
            }
         } else {
            label73: {
               var7 = pSource.m_7640_();
               if (var7 instanceof Projectile) {
                  Projectile target = (Projectile)var7;
                  if (!(target instanceof TensuraProjectile)) {
                     if (randomBlock != 3 && randomBlock != 2) {
                        this.setMiscAnimation(-2);
                     } else {
                        this.setMiscAnimation(randomBlock == 3 ? -4 : -3);
                        vec3 = (new Vec3(target.m_20185_() - this.m_20185_(), target.m_20186_() - this.m_20186_() + (randomBlock == 2 ? 2.0D : 0.5D), target.m_20189_() - this.m_20189_())).m_82490_(1.0D / (double)target.m_20270_(this));
                        target.m_20256_(vec3.m_82541_().m_82490_(2.0D));
                        target.f_19812_ = true;
                        target.f_19864_ = true;
                     }
                     break label73;
                  }
               }

               if (randomBlock == 3) {
                  this.setMiscAnimation(-4);
               } else if (randomBlock == 2) {
                  this.setMiscAnimation(-3);
               } else {
                  this.setMiscAnimation(-2);
               }
            }
         }

         this.m_5496_(SoundEvents.f_12346_, 1.0F, 1.0F);
         return false;
      }
   }

   public boolean m_7307_(Entity entity) {
      if (super.m_7307_(entity)) {
         return true;
      } else if (entity instanceof BeastGnomeEntity) {
         BeastGnomeEntity gnome = (BeastGnomeEntity)entity;
         return gnome.m_21824_() == this.m_21824_();
      } else if (entity instanceof WarGnomeEntity) {
         WarGnomeEntity gnome = (WarGnomeEntity)entity;
         return gnome.m_21824_() == this.m_21824_();
      } else {
         return false;
      }
   }

   public void m_8119_() {
      super.m_8119_();
      if (!this.f_19853_.m_5776_()) {
         this.m_7311_(Math.min(60, this.m_20094_()));
      }

   }

   protected void breakBlocks() {
      if (!this.m_21824_()) {
         this.breakBlocks(this, 2.0F, false, 1, (SimpleContainer)null, false);
      }

   }

   protected void handleFlying() {
   }

   protected void miscAnimationHandler() {
      if (this.getMiscAnimation() != 0) {
         ++this.miscAnimationTicks;
         if (!this.m_6084_()) {
            return;
         }

         if (!this.m_9236_().m_5776_()) {
            LivingEntity target;
            if (this.getMiscAnimation() == 1 && this.miscAnimationTicks == 7) {
               target = this.m_5448_();
               if (target != null) {
                  this.m_7618_(Anchor.EYES, target.m_146892_());
                  this.m_7327_(target);
               }

               this.m_5496_(SoundEvents.f_12317_, 10.0F, 0.95F + this.f_19796_.m_188501_() * 0.1F);
            } else if (this.getMiscAnimation() == 2 && this.miscAnimationTicks == 7) {
               target = this.m_5448_();
               if (target != null) {
                  this.m_7618_(Anchor.EYES, target.m_146892_());
                  this.m_7327_(target);
                  Vec3 vec3 = (new Vec3(target.m_20185_() - this.m_20185_(), target.m_20186_() - this.m_20186_() + 1.0D, target.m_20189_() - this.m_20189_())).m_82490_(1.0D / (double)target.m_20270_(this));
                  target.m_20256_(vec3.m_82541_().m_82490_(2.0D));
                  target.f_19812_ = true;
                  target.f_19864_ = true;
               }

               this.m_5496_(SoundEvents.f_11667_, 10.0F, 0.95F + this.f_19796_.m_188501_() * 0.1F);
            } else if (this.getMiscAnimation() == 3 && this.miscAnimationTicks == 12) {
               this.stomp();
               TensuraParticleHelper.spawnGroundSlamParticle(this, 5, 2.5F);
               TensuraParticleHelper.spawnServerParticles(this.f_19853_, new BlockParticleOption(ParticleTypes.f_123794_, Blocks.f_220844_.m_49966_()), this.m_20182_().m_7096_(), this.m_20182_().m_7098_(), this.m_20182_().m_7094_(), 55, 0.08D, 0.08D, 0.08D, 0.15D, true);
               TensuraParticleHelper.spawnServerParticles(this.f_19853_, new BlockParticleOption(ParticleTypes.f_123794_, Blocks.f_152537_.m_49966_()), this.m_20182_().m_7096_(), this.m_20182_().m_7098_(), this.m_20182_().m_7094_(), 55, 0.08D, 0.08D, 0.08D, 0.15D, true);
               this.m_9236_().m_6263_((Player)null, this.m_20185_(), this.m_20186_(), this.m_20189_(), SoundEvents.f_215778_, SoundSource.NEUTRAL, 5.0F, 1.0F);
            } else if (this.getMiscAnimation() != 4) {
               if (this.getMiscAnimation() == 5 && this.miscAnimationTicks == 15) {
                  this.m_21573_().m_26573_();
                  GravityField sphere = new GravityField(this.m_9236_(), this);
                  sphere.setLife(1200);
                  sphere.setRadius(10.0F);
                  sphere.m_146884_(this.m_20182_().m_82520_(0.0D, -10.0D, 0.0D));
                  this.m_9236_().m_7967_(sphere);
                  this.m_5496_(SoundEvents.f_11868_, 8.0F, 0.95F + this.f_19796_.m_188501_() * 0.1F);
               } else if (this.getMiscAnimation() == 7) {
                  this.m_21573_().m_26573_();
                  switch(this.miscAnimationTicks) {
                  case 7:
                     this.summonBeastGnome(3, 7);
                     break;
                  case 14:
                     this.summonBeastGnome(5, 10);
                     break;
                  case 21:
                     this.summonBeastGnome(7, 15);
                  }

                  this.m_5496_(SoundEvents.f_144135_, 10.0F, 0.95F + this.f_19796_.m_188501_() * 0.1F);
               }
            } else {
               this.m_21573_().m_26573_();
               if (this.miscAnimationTicks >= 20 && this.miscAnimationTicks <= 45) {
                  this.gravitySphere();
               } else {
                  this.setMagicID(0);
               }

               target = this.m_5448_();
               if (target != null) {
                  this.m_7618_(Anchor.EYES, target.m_146892_());
               }

               this.m_5496_(SoundEvents.f_11862_, 8.0F, 0.95F + this.f_19796_.m_188501_() * 0.1F);
            }
         }

         if (this.miscAnimationTicks > this.getAnimationTick(this.getMiscAnimation())) {
            this.setMiscAnimation(0);
            this.miscAnimationTicks = 0;
         }
      }

   }

   private int getAnimationTick(int miscAnimation) {
      short var10000;
      switch(miscAnimation) {
      case -3:
         var10000 = 10;
         break;
      case -2:
         var10000 = 5;
         break;
      case -1:
         var10000 = 200;
         break;
      case 0:
      case 2:
      case 6:
      default:
         var10000 = 15;
         break;
      case 1:
         var10000 = 14;
         break;
      case 3:
         var10000 = 20;
         break;
      case 4:
         var10000 = 63;
         break;
      case 5:
      case 7:
         var10000 = 26;
      }

      return var10000;
   }

   private void gravitySphere() {
      int ballId = this.getMagicID();
      if (ballId == 0) {
         GravitySphereProjectile sphere = new GravitySphereProjectile(this.f_19853_, this);
         sphere.setDamage((float)(this.m_21133_(Attributes.f_22281_) * 3.0D));
         sphere.setSpiritAttack(true);
         sphere.setSize(0.5F);
         sphere.setMpCost(500.0D);
         sphere.setSkill((ManasSkillInstance)SkillAPI.getSkillsFrom(this).getSkill((ManasSkill)ExtraSkills.EARTH_MANIPULATION.get()).orElse((Object)null));
         sphere.m_146884_(this.m_146892_().m_82520_(0.0D, 4.0D, 0.0D));
         sphere.setOwnerOffset(new Vec3(0.0D, 3.0D, 0.0D));
         sphere.setLookDistance(30.0F);
         sphere.setDelayTick(25);
         sphere.m_20242_(true);
         sphere.setMobEffect(new MobEffectInstance((MobEffect)TensuraMobEffects.BURDEN.get(), 100, 0, false, false, false));
         sphere.setEffectRange(3.0F);
         this.m_9236_().m_7967_(sphere);
         this.setMagicID(sphere.m_19879_());
      } else {
         Entity entity = this.m_9236_().m_6815_(ballId);
         if (entity instanceof GravitySphereProjectile) {
            GravitySphereProjectile ball = (GravitySphereProjectile)entity;
            if (ball.getDelayTick() > 0) {
               ball.setSize(ball.getSize() + 0.1F);
            }
         } else {
            this.setMagicID(0);
            this.gravitySphere();
         }
      }

      this.m_5496_(SoundEvents.f_12521_, 10.0F, 0.95F + this.f_19796_.m_188501_() * 0.1F);
   }

   public void stomp() {
      double damageMultiplier = 1.5D;
      float damage = (float)(this.m_21133_(Attributes.f_22281_) * damageMultiplier);
      ManasSkillInstance instance = SkillUtils.getSkillOrNull(this, (ManasSkill)SpiritualMagics.EARTH_SPIKES.get());

      for(int i = 0; i < 10; ++i) {
         float angle = 0.017453292F * this.f_20883_ + (float)i;
         double extraX = (double)(4.0F * Mth.m_14031_((float)(3.141592653589793D + (double)angle)));
         double extraZ = (double)(4.0F * Mth.m_14089_(angle));
         Vec3 groundPos = new Vec3((double)Mth.m_14107_(this.m_20185_() + extraX), this.m_20186_(), (double)Mth.m_14107_(this.m_20189_() + extraZ));
         EarthSpikesMagic.spawnSpikes(groundPos, this, instance, damage, 200.0D);
      }

      AABB aabb = this.m_20191_().m_82400_(this.m_21133_((Attribute)ForgeMod.ATTACK_RANGE.get()) + 10.0D);
      List<LivingEntity> list = this.f_19853_.m_6443_(LivingEntity.class, aabb, this::shouldAttack);
      if (!list.isEmpty()) {
         DamageSource damageSource = DamageSourceHelper.addSkillAndCost(DamageSource.m_19370_(this).m_19389_(), 100.0D, SkillUtils.getSkillOrNull(this, (ManasSkill)ExtraSkills.EARTH_MANIPULATION.get())).setNotTensuraMagic();
         Iterator var8 = list.iterator();

         while(var8.hasNext()) {
            LivingEntity target = (LivingEntity)var8.next();
            if (target.m_20270_(this) > 5.0F) {
               target.m_6469_(damageSource, damage / 1.5F);
            } else {
               target.m_6469_(damageSource, damage);
            }

            target.m_7292_(new MobEffectInstance(MobEffects.f_19597_, 200, 1, false, false, false));
            target.m_20334_(0.0D, 0.1D, 0.0D);
            SkillHelper.knockBack(this, target, 2.0F);
         }

      }
   }

   private void summonBeastGnome(int minRadius, int maxRadius) {
      Level var4 = this.m_9236_();
      if (var4 instanceof ServerLevel) {
         ServerLevel serverLevel = (ServerLevel)var4;
         int i = Mth.m_14107_(this.m_20185_());
         int j = Mth.m_14107_(this.m_20186_());
         int k = Mth.m_14107_(this.m_20189_());
         BeastGnomeEntity gnome = new BeastGnomeEntity((EntityType)TensuraEntityTypes.BEAST_GNOME.get(), serverLevel);

         for(int l = 0; l < 50; ++l) {
            int i1 = i + Mth.m_216271_(this.f_19796_, minRadius, maxRadius) * Mth.m_216271_(this.f_19796_, -1, 1);
            int j1 = j + Mth.m_216271_(this.f_19796_, minRadius, maxRadius) * Mth.m_216271_(this.f_19796_, -1, 1);
            int k1 = k + Mth.m_216271_(this.f_19796_, minRadius, maxRadius) * Mth.m_216271_(this.f_19796_, -1, 1);
            gnome.m_6034_((double)i1, (double)j1, (double)k1);
            if (serverLevel.m_45784_(gnome) && serverLevel.m_45786_(gnome)) {
               gnome.m_6518_(serverLevel, serverLevel.m_6436_(gnome.m_20183_()), MobSpawnType.MOB_SUMMONED, (SpawnGroupData)null, (CompoundTag)null);
               gnome.m_6710_(this.m_5448_());
               serverLevel.m_47205_(gnome);
               gnome.setSummonerUUID(this.getSummonerUUID());
               TensuraParticleHelper.addServerParticlesAroundSelf(gnome, new BlockParticleOption(ParticleTypes.f_123794_, Blocks.f_220844_.m_49966_()), 2.0D);
               TensuraParticleHelper.addServerParticlesAroundSelf(gnome, new BlockParticleOption(ParticleTypes.f_123794_, Blocks.f_152537_.m_49966_()), 2.0D);
               break;
            }
         }

      }
   }

   public MagicElemental getElemental() {
      return MagicElemental.EARTH;
   }

   public Item getElementalCore() {
      return (Item)TensuraMaterialItems.ELEMENT_CORE_EARTH.get();
   }

   protected void m_6153_() {
      if (++this.f_20919_ >= 29) {
         this.m_142687_(RemovalReason.KILLED);
         this.m_5496_(SoundEvents.f_11913_, 10.0F, 1.0F);
         this.spawnDeathParticles();
      }

   }

   protected void spawnDeathParticles() {
      TensuraParticleHelper.addServerParticlesAroundSelf(this, new BlockParticleOption(ParticleTypes.f_123794_, Blocks.f_220844_.m_49966_()));
      TensuraParticleHelper.addServerParticlesAroundSelf(this, new BlockParticleOption(ParticleTypes.f_123794_, Blocks.f_152537_.m_49966_()));
      TensuraParticleHelper.addServerParticlesAroundSelf(this, new BlockParticleOption(ParticleTypes.f_123794_, Blocks.f_220844_.m_49966_()), 2.0D);
      TensuraParticleHelper.addServerParticlesAroundSelf(this, new BlockParticleOption(ParticleTypes.f_123794_, Blocks.f_152537_.m_49966_()), 2.0D);
   }

   protected SoundEvent m_7515_() {
      return SoundEvents.f_12476_;
   }

   protected SoundEvent m_7975_(DamageSource pDamageSource) {
      return SoundEvents.f_12008_;
   }

   protected SoundEvent m_5592_() {
      return SoundEvents.f_12059_;
   }

   private <E extends IAnimatable> PlayState predicate(AnimationEvent<E> event) {
      if (this.m_5803_()) {
         event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.war_gnome.relax", EDefaultLoopTypes.LOOP));
      } else if (this.m_21825_()) {
         if (this.getMiscAnimation() == -1) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.war_gnome.idle_train", EDefaultLoopTypes.LOOP));
         } else {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.war_gnome.stay", EDefaultLoopTypes.LOOP));
         }
      } else if (event.isMoving() && this.getMiscAnimation() != 3 && this.getMiscAnimation() != 4 && !this.m_21525_()) {
         if (!this.m_20096_()) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.war_gnome.run", EDefaultLoopTypes.LOOP));
         } else {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.war_gnome.walk", EDefaultLoopTypes.LOOP));
         }
      } else {
         event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.war_gnome.idle", EDefaultLoopTypes.LOOP));
      }

      return PlayState.CONTINUE;
   }

   private <E extends IAnimatable> PlayState miscPredicate(AnimationEvent<E> event) {
      if (event.getController().getAnimationState().equals(AnimationState.Stopped)) {
         event.getController().markNeedsReload();
         if (this.getMiscAnimation() == -4) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.war_gnome.block_fling_away", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == -3) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.war_gnome.block_fling_up", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == -2) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.war_gnome.block", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 1) {
            if (this.f_19796_.m_188499_()) {
               event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.war_gnome.swing", EDefaultLoopTypes.PLAY_ONCE));
            } else {
               event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.war_gnome.swing2", EDefaultLoopTypes.PLAY_ONCE));
            }
         } else if (this.getMiscAnimation() == 2) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.war_gnome.shield_bash", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 3) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.war_gnome.stomp", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 4) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.war_gnome.sphere", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 5) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.war_gnome.summon", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 7) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.war_gnome.summon", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 8) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.war_gnome.death", EDefaultLoopTypes.PLAY_ONCE));
         }
      }

      return PlayState.CONTINUE;
   }

   public void registerControllers(AnimationData data) {
      data.addAnimationController(new AnimationController(this, "controller", 0.0F, this::predicate));
      data.addAnimationController(new AnimationController(this, "miscController", 0.0F, this::miscPredicate));
   }

   static class WarGnomeAttackGoal extends MeleeAttackGoal {
      public final WarGnomeEntity gnome;

      public WarGnomeAttackGoal(WarGnomeEntity beast) {
         super(beast, 2.0D, true);
         this.gnome = beast;
      }

      public boolean m_8036_() {
         return this.gnome.m_21827_() ? false : super.m_8036_();
      }

      protected void m_6739_(LivingEntity pEnemy, double pDistToEnemySqr) {
         double d0 = this.m_6639_(pEnemy);
         if (this.gnome.getMiscAnimation() <= 1) {
            int randomAttack = this.randomAttack(d0);
            double var10000;
            switch(randomAttack) {
            case 3:
               var10000 = 100.0D;
               break;
            case 4:
            case 7:
               var10000 = 1000.0D;
               break;
            case 5:
            case 6:
            default:
               var10000 = d0;
            }

            double attackRange = var10000;
            if (pDistToEnemySqr <= attackRange && this.m_25564_()) {
               this.m_25563_();
               this.gnome.setMiscAnimation(randomAttack);
            }
         }

      }

      private boolean canSummonGnomes() {
         if (this.gnome.m_21824_()) {
            return false;
         } else {
            List<BeastGnomeEntity> list = this.gnome.f_19853_.m_45971_(BeastGnomeEntity.class, TargetingConditions.m_148353_().m_26883_(30.0D).m_148355_().m_26893_(), this.gnome, this.gnome.m_20191_().m_82400_(30.0D));
            if (!list.isEmpty()) {
               Iterator var2 = list.iterator();

               while(var2.hasNext()) {
                  BeastGnomeEntity entity = (BeastGnomeEntity)var2.next();
                  entity.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.RAMPAGE.get(), 200, 0, false, false, false));
               }
            }

            return list.size() < 2;
         }
      }

      protected int randomAttack(double distance) {
         if (distance < 12.0D || (double)this.gnome.m_217043_().m_188501_() <= 0.2D) {
            if ((double)this.gnome.m_217043_().m_188501_() <= 0.2D) {
               return 3;
            }

            if ((double)this.gnome.m_217043_().m_188501_() <= 0.1D) {
               return 5;
            }
         }

         if ((!(distance > 20.0D) || !((double)this.gnome.m_217043_().m_188501_() <= 0.3D)) && !((double)this.gnome.m_217043_().m_188501_() <= 0.1D)) {
            if ((double)this.gnome.m_217043_().m_188501_() <= 0.2D) {
               return 2;
            } else {
               return (double)this.gnome.m_217043_().m_188501_() <= 0.01D && this.canSummonGnomes() ? 7 : 1;
            }
         } else {
            return 4;
         }
      }
   }
}
