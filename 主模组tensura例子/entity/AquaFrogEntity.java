package com.github.manasmods.tensura.entity;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.magic.MagicElemental;
import com.github.manasmods.tensura.ability.magic.spiritual.SpiritualMagic;
import com.github.manasmods.tensura.api.entity.ai.LeapWithStrengthGoal;
import com.github.manasmods.tensura.api.entity.ai.SemiAquaticFollowOwnerGoal;
import com.github.manasmods.tensura.api.entity.ai.SemiAquaticRandomSwimmingGoal;
import com.github.manasmods.tensura.api.entity.controller.AquaticMoveController;
import com.github.manasmods.tensura.api.entity.controller.JumpingEntityMoveControl;
import com.github.manasmods.tensura.api.entity.navigator.SemiAquaticNavigator;
import com.github.manasmods.tensura.api.entity.navigator.StraightFlightNavigator;
import com.github.manasmods.tensura.api.entity.subclass.IElementalSpirit;
import com.github.manasmods.tensura.api.entity.subclass.IFollower;
import com.github.manasmods.tensura.api.entity.subclass.IJumpingEntity;
import com.github.manasmods.tensura.api.entity.subclass.ISemiAquatic;
import com.github.manasmods.tensura.api.entity.subclass.ITensuraMount;
import com.github.manasmods.tensura.client.keybind.TensuraKeybinds;
import com.github.manasmods.tensura.config.SpawnRateConfig;
import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.entity.magic.projectile.PoisonBallProjectile;
import com.github.manasmods.tensura.entity.template.TensuraTamableEntity;
import com.github.manasmods.tensura.item.food.HealingPotionItem;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.items.TensuraMaterialItems;
import com.github.manasmods.tensura.registry.items.TensuraMobDropItems;
import com.github.manasmods.tensura.registry.skill.ExtraSkills;
import com.github.manasmods.tensura.registry.sound.TensuraSoundEvents;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import com.mojang.math.Vector3f;
import java.util.Iterator;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import javax.annotation.Nullable;
import net.minecraft.commands.arguments.EntityAnchorArgument.Anchor;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.syncher.EntityDataAccessor;
import net.minecraft.network.syncher.EntityDataSerializers;
import net.minecraft.network.syncher.SynchedEntityData;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.util.Mth;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.InteractionResult;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.MobSpawnType;
import net.minecraft.world.entity.MoverType;
import net.minecraft.world.entity.PlayerRideableJumping;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.ai.attributes.AttributeInstance;
import net.minecraft.world.entity.ai.attributes.AttributeSupplier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.goal.LookAtPlayerGoal;
import net.minecraft.world.entity.ai.goal.MeleeAttackGoal;
import net.minecraft.world.entity.ai.goal.RandomLookAroundGoal;
import net.minecraft.world.entity.ai.goal.SitWhenOrderedToGoal;
import net.minecraft.world.entity.ai.goal.target.NearestAttackableTargetGoal;
import net.minecraft.world.entity.ai.goal.target.ResetUniversalAngerTargetGoal;
import net.minecraft.world.entity.animal.Animal;
import net.minecraft.world.entity.monster.Monster;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.level.ItemLike;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.LevelAccessor;
import net.minecraft.world.level.pathfinder.BlockPathTypes;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.common.ForgeHooks;
import net.minecraftforge.common.ForgeMod;
import net.minecraftforge.event.ForgeEventFactory;
import net.minecraftforge.fluids.FluidType;
import software.bernie.geckolib3.core.AnimationState;
import software.bernie.geckolib3.core.IAnimatable;
import software.bernie.geckolib3.core.PlayState;
import software.bernie.geckolib3.core.builder.AnimationBuilder;
import software.bernie.geckolib3.core.builder.ILoopType.EDefaultLoopTypes;
import software.bernie.geckolib3.core.controller.AnimationController;
import software.bernie.geckolib3.core.event.predicate.AnimationEvent;
import software.bernie.geckolib3.core.manager.AnimationData;
import software.bernie.geckolib3.core.manager.AnimationFactory;
import software.bernie.geckolib3.util.GeckoLibUtil;

public class AquaFrogEntity extends TensuraTamableEntity implements IAnimatable, IJumpingEntity, ISemiAquatic, IFollower, ITensuraMount, IElementalSpirit, PlayerRideableJumping {
   private static final EntityDataAccessor<Integer> MISC_ANIMATION;
   private static final EntityDataAccessor<Integer> SUMMONING_TICK;
   protected static final EntityDataAccessor<Optional<UUID>> SUMMONER_UUID;
   private static final EntityDataAccessor<Boolean> JUMP;
   protected boolean playerJumping;
   private final AnimationFactory factory = GeckoLibUtil.createFactory(this);
   public boolean wasOnGround;
   protected float playerJumpPendingScale;
   public float prevSwimProgress;
   public float swimProgress;
   private int swimTimer = -1000;
   private boolean isLandNavigator;
   public int jumpTicks = 0;
   public int miscAnimationTicks = 0;

   public AquaFrogEntity(EntityType<? extends AquaFrogEntity> pEntityType, Level pLevel) {
      super(pEntityType, pLevel);
      this.f_21364_ = 40;
      this.f_19793_ = 1.0F;
      this.m_21441_(BlockPathTypes.WATER, 0.0F);
      this.m_21441_(BlockPathTypes.WATER_BORDER, 0.0F);
      this.switchNavigator(true);
   }

   public static AttributeSupplier setAttributes() {
      return Monster.m_33035_().m_22268_(Attributes.f_22284_, 5.0D).m_22268_(Attributes.f_22276_, 50.0D).m_22268_(Attributes.f_22281_, 15.0D).m_22268_(Attributes.f_22277_, 32.0D).m_22268_(Attributes.f_22288_, 1.5D).m_22268_(Attributes.f_22278_, 0.3D).m_22268_(Attributes.f_22279_, 0.3499999940395355D).m_22268_((Attribute)ForgeMod.SWIM_SPEED.get(), 5.0D).m_22265_();
   }

   protected void m_8099_() {
      this.f_21345_.m_25352_(1, new SitWhenOrderedToGoal(this));
      this.f_21345_.m_25352_(2, new AquaFrogEntity.FrogAttackGoal(this));
      this.f_21345_.m_25352_(3, new AquaFrogEntity.LeapJumpGoal(this));
      this.f_21345_.m_25352_(4, new SemiAquaticFollowOwnerGoal(this, 1.5D, 10.0F, 5.0F, false, false));
      this.f_21345_.m_25352_(4, new SemiAquaticRandomSwimmingGoal(this, 1.2D, 30));
      this.f_21345_.m_25352_(5, new IElementalSpirit.FollowGreaterSpiritGoal(this, 1.0D, UndineEntity.class));
      this.f_21345_.m_25352_(5, new TensuraTamableEntity.WanderAroundPosGoal(this, 60, 1.0D, 10, 7));
      this.f_21345_.m_25352_(6, new RandomLookAroundGoal(this));
      this.f_21345_.m_25352_(7, new LookAtPlayerGoal(this, Player.class, 6.0F));
      this.f_21346_.m_25352_(1, new TensuraTamableEntity.TensuraOwnerHurtByTargetGoal(this));
      this.f_21346_.m_25352_(2, new TensuraTamableEntity.TensuraOwnerHurtTargetGoal(this));
      this.f_21346_.m_25352_(2, (new TensuraTamableEntity.TensuraHurtByTargetGoal(this, new Class[]{UndineEntity.class})).m_26044_(new Class[0]));
      this.f_21346_.m_25352_(4, new NearestAttackableTargetGoal(this, Player.class, 10, true, false, this::m_21674_));
      this.f_21346_.m_25352_(8, new ResetUniversalAngerTargetGoal(this, true));
   }

   private void switchNavigator(boolean onLand) {
      if (onLand) {
         this.f_21342_ = new JumpingEntityMoveControl(this, 2.0F, 2.0F) {
            public void m_8126_() {
               if (!AquaFrogEntity.this.m_5803_()) {
                  super.m_8126_();
               }
            }
         };
         this.f_21344_ = new StraightFlightNavigator(this, this.f_19853_);
         this.isLandNavigator = true;
      } else {
         this.f_21342_ = new AquaticMoveController(this, 1.0F) {
            public void m_8126_() {
               if (!AquaFrogEntity.this.m_5803_()) {
                  super.m_8126_();
               }
            }
         };
         this.f_21344_ = new SemiAquaticNavigator(this, this.f_19853_);
         this.isLandNavigator = false;
      }

   }

   protected void m_8097_() {
      super.m_8097_();
      this.f_19804_.m_135372_(SUMMONER_UUID, Optional.empty());
      this.f_19804_.m_135372_(MISC_ANIMATION, 0);
      this.f_19804_.m_135372_(SUMMONING_TICK, -1);
      this.f_19804_.m_135372_(JUMP, Boolean.FALSE);
   }

   public void m_7380_(CompoundTag compound) {
      super.m_7380_(compound);
      if (this.getSummonerUUID() != null) {
         compound.m_128362_("Summoner", this.getSummonerUUID());
      }

      compound.m_128405_("MiscAnimation", this.getMiscAnimation());
      compound.m_128405_("SummoningTick", this.getSummoningTick());
      compound.m_128379_("wasOnGround", this.wasOnGround);
      compound.m_128379_("Jump", this.isJumpingAnimation());
      compound.m_128405_("SwimTimer", this.swimTimer);
   }

   public void m_7378_(CompoundTag compound) {
      super.m_7378_(compound);
      if (compound.m_128403_("Summoner")) {
         this.setSummonerUUID(compound.m_128342_("Summoner"));
      }

      this.setMiscAnimation(compound.m_128451_("MiscAnimation"));
      this.setSummoningTick(compound.m_128451_("SummoningTick"));
      this.wasOnGround = compound.m_128471_("wasOnGround");
      this.setJumpAnimation(compound.m_128471_("Jump"));
      this.swimTimer = compound.m_128451_("SwimTimer");
   }

   public void setMiscAnimation(int animation) {
      if (this.getMiscAnimation() == 0 || animation == 0) {
         this.f_19804_.m_135381_(MISC_ANIMATION, animation);
      }
   }

   public int getMiscAnimation() {
      return (Integer)this.f_19804_.m_135370_(MISC_ANIMATION);
   }

   public int getSummoningTick() {
      return (Integer)this.f_19804_.m_135370_(SUMMONING_TICK);
   }

   public void setSummoningTick(int tick) {
      this.f_19804_.m_135381_(SUMMONING_TICK, tick);
   }

   @Nullable
   public UUID getSummonerUUID() {
      return (UUID)((Optional)this.f_19804_.m_135370_(SUMMONER_UUID)).orElse((Object)null);
   }

   public void setSummonerUUID(@Nullable UUID pUuid) {
      this.f_19804_.m_135381_(SUMMONER_UUID, Optional.ofNullable(pUuid));
   }

   @Nullable
   public LivingEntity m_21826_() {
      return this.getSummonerUUID() != null ? null : super.m_21826_();
   }

   public boolean canSleep() {
      return !this.m_21525_();
   }

   public boolean m_6469_(DamageSource pSource, float pAmount) {
      if (this.m_6673_(pSource)) {
         return false;
      } else {
         if (!this.m_21824_()) {
            Entity var4 = pSource.m_7639_();
            if (var4 instanceof AquaFrogEntity) {
               AquaFrogEntity frog = (AquaFrogEntity)var4;
               if (!frog.m_21824_()) {
                  return false;
               }
            }

            var4 = pSource.m_7639_();
            if (var4 instanceof UndineEntity) {
               UndineEntity undine = (UndineEntity)var4;
               if (!undine.m_21824_()) {
                  return false;
               }
            }
         }

         pAmount *= this.getPhysicalAttackInput(pSource);
         boolean hurt = super.m_6469_(pSource, pAmount);
         if (hurt) {
            Entity var5 = pSource.m_7640_();
            if (var5 instanceof LivingEntity) {
               LivingEntity living = (LivingEntity)var5;
               if (DamageSourceHelper.isPhysicalAttack(pSource) && !living.m_7307_(this)) {
                  SkillHelper.addEffectWithSource(living, this, (MobEffect)TensuraMobEffects.FATAL_POISON.get(), 200, 1, true);
               }
            }
         }

         return hurt;
      }
   }

   public boolean m_7307_(Entity entity) {
      if (super.m_7307_(entity)) {
         return true;
      } else if (this.getSummonerUUID() != null) {
         if (entity instanceof IElementalSpirit) {
            IElementalSpirit spirit = (IElementalSpirit)entity;
            return Objects.equals(spirit.getSummonerUUID(), this.getSummonerUUID());
         } else {
            return Objects.equals(entity.m_20148_(), this.getSummonerUUID());
         }
      } else if (entity instanceof AquaFrogEntity) {
         AquaFrogEntity frog = (AquaFrogEntity)entity;
         return frog.m_21824_() == this.m_21824_();
      } else if (entity instanceof UndineEntity) {
         UndineEntity undine = (UndineEntity)entity;
         return undine.m_21824_() == this.m_21824_();
      } else {
         return false;
      }
   }

   public boolean m_6779_(LivingEntity pTarget) {
      return this.m_7307_(pTarget) ? false : super.m_6779_(pTarget);
   }

   public boolean m_7848_(Animal pOtherAnimal) {
      return false;
   }

   public boolean isPushedByFluid(FluidType type) {
      return false;
   }

   public boolean canDrownInFluidType(FluidType type) {
      return false;
   }

   public boolean m_6673_(DamageSource source) {
      return source == DamageSource.f_19315_ || source == DamageSource.f_19312_ || source == DamageSource.f_19310_ || source == DamageSource.f_19311_ || super.m_6673_(source);
   }

   public boolean shouldEnterWater() {
      return !this.shouldLeaveWater() && this.swimTimer <= -1000;
   }

   public boolean shouldLeaveWater() {
      if (this.m_5448_() != null && !this.m_5448_().m_20069_()) {
         return true;
      } else {
         return this.swimTimer > 600;
      }
   }

   public boolean shouldStopMoving() {
      return this.m_21827_();
   }

   public int getWaterSearchRange() {
      return 45;
   }

   public boolean shouldFollow() {
      return !this.m_21827_() && !this.isWandering() && (this.m_5448_() == null || !this.m_5448_().m_6084_());
   }

   public void m_8119_() {
      super.m_8119_();
      this.wasOnGround = this.f_19861_;
      this.prevSwimProgress = this.swimProgress;
      boolean ground = !this.m_20072_();
      if (!ground && this.isLandNavigator) {
         this.switchNavigator(false);
      }

      if (ground && !this.isLandNavigator) {
         this.switchNavigator(true);
      }

      if (ground && this.swimProgress > 0.0F) {
         --this.swimProgress;
      }

      if (!ground && this.swimProgress < 5.0F) {
         ++this.swimProgress;
      }

      if (!this.f_19853_.m_5776_()) {
         this.summoningTicking(this);
         if (this.m_20069_()) {
            ++this.swimTimer;
         } else {
            --this.swimTimer;
         }
      }

      if (this.setWantedTarget(this, 16.0D)) {
         this.wasOnGround = true;
      } else if (!this.setWantedOwner(this, 100.0D)) {
         this.wasOnGround = true;
      }

      this.animationHandler();
   }

   private void animationHandler() {
      if (this.isJumpingAnimation()) {
         ++this.jumpTicks;
         if (this.jumpTicks >= 20) {
            this.setJumpAnimation(Boolean.FALSE);
            this.jumpTicks = 0;
         }
      }

      if (this.getMiscAnimation() != 0) {
         ++this.miscAnimationTicks;
         if (!this.m_6084_()) {
            return;
         }

         if (this.getMiscAnimation() == 1 && this.miscAnimationTicks == 10) {
            LivingEntity target = this.m_5448_();
            if (target != null) {
               this.m_7618_(Anchor.EYES, target.m_146892_());
            }

            this.poisonSpit();
            this.f_19853_.m_6263_((Player)null, this.m_20185_(), this.m_20186_(), this.m_20189_(), SoundEvents.f_12098_, SoundSource.NEUTRAL, 1.0F, 1.0F);
         } else if (this.getMiscAnimation() == 2 && this.miscAnimationTicks == 10) {
            if (this.m_5448_() != null) {
               this.m_7327_(this.m_5448_());
            }

            this.m_9236_().m_6263_((Player)null, this.m_20185_(), this.m_20186_(), this.m_20189_(), SoundEvents.f_215697_, SoundSource.NEUTRAL, 1.5F, 1.0F);
         }

         if (this.miscAnimationTicks >= 20) {
            this.setMiscAnimation(0);
            this.miscAnimationTicks = 0;
         }
      }

   }

   public void poisonSpit() {
      PoisonBallProjectile ball = new PoisonBallProjectile(this.f_19853_, this);
      ball.setSkill(SkillUtils.getSkillOrNull(this, (ManasSkill)ExtraSkills.WATER_MANIPULATION.get()));
      float radius = 1.0F;
      float angle = 0.017453292F * this.f_20883_;
      double extraX = (double)(radius * Mth.m_14031_((float)(3.141592653589793D + (double)angle)));
      double extraZ = (double)(radius * Mth.m_14089_(angle));
      ball.m_6034_(this.m_20185_() + extraX, this.m_20188_(), this.m_20189_() + extraZ);
      ball.setDamage((float)this.m_21133_(Attributes.f_22281_));
      ball.setMobEffect(new MobEffectInstance((MobEffect)TensuraMobEffects.FATAL_POISON.get(), 200, 1, false, false, false));
      ball.setEffectRange(2.0F);
      ball.setSpiritAttack(true);
      Vector3f vector3f = new Vector3f(this.m_146895_() != null ? this.m_146895_().m_20252_(2.0F) : this.m_20252_(2.0F));
      ball.m_6686_((double)vector3f.m_122239_(), (double)vector3f.m_122260_(), (double)vector3f.m_122269_(), 2.0F, 0.0F);
      this.f_19853_.m_7967_(ball);
   }

   public void mountAbility(Player rider) {
      if (this.getMiscAnimation() != 1) {
         this.setMiscAnimation(1);
      }
   }

   public boolean m_7327_(Entity pEntity) {
      if (super.m_7327_(pEntity) && pEntity instanceof LivingEntity) {
         LivingEntity living = (LivingEntity)pEntity;
         SkillHelper.addEffectWithSource(living, this, (MobEffect)TensuraMobEffects.FATAL_POISON.get(), 200, 2, true);
         return true;
      } else {
         return false;
      }
   }

   public boolean m_6898_(ItemStack pStack) {
      return pStack.m_204117_(TensuraTags.Items.SPIRIT_FOOD);
   }

   public boolean isTamingFood(ItemStack pStack) {
      return pStack.m_150930_((Item)TensuraMaterialItems.ELEMENT_CORE_WATER.get());
   }

   public MagicElemental getElemental() {
      return MagicElemental.WATER;
   }

   public SpiritualMagic.SpiritLevel getSpiritLevel() {
      return SpiritualMagic.SpiritLevel.MEDIUM;
   }

   public InteractionResult m_6071_(Player player, InteractionHand hand) {
      ItemStack itemstack = player.m_21120_(hand);
      if (itemstack.m_41720_() instanceof HealingPotionItem) {
         return super.m_6071_(player, hand);
      } else {
         InteractionResult eating = this.handleEating(player, hand, itemstack);
         if (eating.m_19077_()) {
            return eating;
         } else if (this.f_19853_.f_46443_) {
            boolean flag = this.m_21830_(player) || this.m_21824_() || this.isTamingFood(itemstack);
            return flag ? InteractionResult.CONSUME : InteractionResult.PASS;
         } else {
            if (this.m_21824_()) {
               if (this.m_21830_(player)) {
                  if (player.m_36341_()) {
                     this.commanding(player);
                  } else if (!this.convertElementalCore(this, player, hand, (Item)TensuraMaterialItems.ELEMENT_CORE_WATER.get()) && player.m_146895_() == null) {
                     this.m_21839_(false);
                     this.setWandering(false);
                     player.m_7998_(this, false);
                  }

                  return InteractionResult.m_19078_(this.f_19853_.f_46443_);
               }
            } else if (this.isTamingFood(itemstack)) {
               if (!player.m_7500_()) {
                  itemstack.m_41774_(1);
               }

               if (this.f_19796_.m_188503_(10) == 7 && !ForgeEventFactory.onAnimalTame(this, player)) {
                  this.m_21828_(player);
                  this.f_21344_.m_26573_();
                  this.m_6710_((LivingEntity)null);
                  this.m_21839_(true);
                  this.f_19853_.m_7605_(this, (byte)7);
               } else {
                  if (this.f_19796_.m_188503_(20) == 0) {
                     this.m_6710_(player);
                  }

                  this.f_19853_.m_7605_(this, (byte)6);
               }

               return InteractionResult.SUCCESS;
            }

            return InteractionResult.PASS;
         }
      }
   }

   public InteractionResult handleEating(Player player, InteractionHand hand, ItemStack itemstack) {
      if (this.m_6898_(itemstack) && this.m_21223_() < this.m_21233_()) {
         if (!player.m_7500_()) {
            itemstack.m_41774_(1);
         }

         this.m_8035_();
         this.m_9236_().m_6269_((Player)null, this, (SoundEvent)TensuraSoundEvents.EATING.get(), SoundSource.NEUTRAL, 1.0F, 1.0F);
         return InteractionResult.SUCCESS;
      } else {
         return InteractionResult.PASS;
      }
   }

   public void m_8035_() {
      super.m_8035_();
      this.m_5634_(3.0F);
      this.setMiscAnimation(3);
   }

   @Nullable
   public LivingEntity getControllingPassenger() {
      Iterator var1 = this.m_20197_().iterator();

      while(var1.hasNext()) {
         Entity passenger = (Entity)var1.next();
         if (passenger instanceof Player) {
            Player player = (Player)passenger;
            if (player.equals(this.m_21826_())) {
               return player;
            }
         }
      }

      return null;
   }

   public void m_7332_(Entity passenger) {
      if (this.m_20363_(passenger)) {
         passenger.m_183634_();
         float radius = -0.25F;
         float angle = 0.017453292F * this.f_20883_;
         double extraX = (double)(radius * Mth.m_14031_((float)(3.141592653589793D + (double)angle)));
         double extraZ = (double)(radius * Mth.m_14089_(angle));
         double yOffset = this.m_20186_() + this.m_6048_() + passenger.m_6049_();
         if (this.m_20072_()) {
            yOffset -= 0.5D;
         }

         passenger.m_6034_(this.m_20185_() + extraX, yOffset, this.m_20189_() + extraZ);
      }
   }

   public boolean m_6146_() {
      return true;
   }

   public boolean m_142535_(float pFallDistance, float pMultiplier, DamageSource pSource) {
      return false;
   }

   public boolean m_7132_() {
      return this.m_20096_();
   }

   public double getCustomJump() {
      return this.m_21133_(Attributes.f_22288_);
   }

   public void m_7199_(int pJumpPower) {
      if (this.m_20096_()) {
         this.m_5496_(SoundEvents.f_215695_, 0.4F, 1.0F);
      }
   }

   public void m_8012_() {
   }

   public void m_7888_(int pJumpPower) {
      if (pJumpPower < 0) {
         pJumpPower = 0;
      }

      if (pJumpPower >= 90) {
         this.playerJumpPendingScale = 1.0F;
      } else {
         this.playerJumpPendingScale = 0.4F + 0.4F * (float)pJumpPower / 90.0F;
      }

   }

   public void m_7023_(Vec3 pTravelVector) {
      if (this.m_6084_()) {
         LivingEntity controller = this.getControllingPassenger();
         if (this.m_20160_() && controller != null) {
            this.m_146922_(controller.m_146908_());
            this.f_19859_ = this.m_146908_();
            this.m_146926_(controller.m_146909_() * 0.5F);
            this.m_19915_(this.m_146908_(), this.m_146909_());
            this.f_20883_ = this.m_146908_();
            this.f_20885_ = this.f_20883_;
            float f = controller.f_20900_ * 0.5F;
            float f1 = controller.f_20902_;
            if (f1 <= 0.0F) {
               f1 *= 0.25F;
            }

            if (this.playerJumpPendingScale > 0.0F && !this.isPlayerJumping()) {
               this.setJumpAnimation(Boolean.TRUE);
               double d0 = this.getCustomJump() * (double)this.playerJumpPendingScale * (double)this.m_20098_();
               double d1 = d0 + this.m_182332_();
               Vec3 vec3 = this.m_20184_();
               this.m_20334_(vec3.f_82479_, d1, vec3.f_82481_);
               this.setPlayerJumping(true);
               this.f_19812_ = true;
               ForgeHooks.onLivingJump(this);
               if (f1 > 0.0F) {
                  float f2 = Mth.m_14031_(this.m_146908_() * 0.017453292F);
                  float f3 = Mth.m_14089_(this.m_146908_() * 0.017453292F);
                  this.m_20256_(this.m_20184_().m_82520_((double)(-0.4F * f2 * this.playerJumpPendingScale), 0.0D, (double)(0.4F * f3 * this.playerJumpPendingScale)));
               }

               this.playerJumpPendingScale = 0.0F;
            }

            this.f_20887_ = this.m_6113_() * 0.1F;
            if (this.m_6109_()) {
               float speed = (float)this.m_21133_(Attributes.f_22279_);
               if (controller.m_20142_()) {
                  speed = (float)((double)speed * 1.5D);
               }

               if (!this.isInFluidType()) {
                  if (this.m_20096_()) {
                     speed /= 2.0F;
                  }

                  this.m_7910_(speed);
                  super.m_7023_(new Vec3((double)f, pTravelVector.f_82480_, (double)f1));
               } else {
                  if (this.isInFluidType((fluidType, height) -> {
                     return height > this.m_20204_();
                  }) && controller.f_20899_) {
                     this.m_20256_(this.m_20184_().m_82520_(0.0D, 0.07D, 0.0D));
                  } else if (TensuraKeybinds.MOUNT_DESCENDING.m_90857_()) {
                     this.m_20256_(this.m_20184_().m_82520_(0.0D, -0.07D, 0.0D));
                  }

                  AttributeInstance instance = this.m_21204_().m_22146_((Attribute)ForgeMod.SWIM_SPEED.get());
                  if (instance != null) {
                     instance.m_22100_(controller.m_20142_() ? 8.0D : 6.0D);
                  }

                  super.m_7023_(new Vec3((double)f, pTravelVector.f_82480_, (double)f1));
               }
            } else if (controller instanceof Player) {
               this.m_20256_(Vec3.f_82478_);
            }

            if (this.f_19861_) {
               this.playerJumpPendingScale = 0.0F;
               this.setPlayerJumping(false);
            }

            this.m_146872_();
         } else {
            AttributeInstance instance = this.m_21204_().m_22146_((Attribute)ForgeMod.SWIM_SPEED.get());
            if (instance != null && instance.m_22115_() != 5.0D) {
               instance.m_22100_(5.0D);
            }

            this.f_20887_ = 0.02F;
            if (this.m_6142_() && this.m_20069_()) {
               this.m_19920_(this.m_6113_(), pTravelVector);
               this.m_6478_(MoverType.SELF, this.m_20184_());
               this.m_20256_(this.m_20184_().m_82490_(0.9D));
               if (this.m_5448_() == null) {
                  this.m_20256_(this.m_20184_().m_82520_(0.0D, -0.005D, 0.0D));
               }
            } else {
               super.m_7023_(pTravelVector);
            }
         }

      }
   }

   public int getJumpDelay() {
      return this.f_19796_.m_188503_(40);
   }

   protected void m_6135_() {
      Vec3 vec3 = this.m_20184_();
      this.m_20334_(vec3.f_82479_, (double)this.m_6118_() * 2.0D, vec3.f_82481_);
      this.f_19812_ = true;
   }

   protected SoundEvent m_7975_(DamageSource pDamageSource) {
      return SoundEvents.f_215693_;
   }

   protected SoundEvent m_5592_() {
      return SoundEvents.f_215691_;
   }

   public float getJumpSoundVolume() {
      return 0.4F;
   }

   public SoundEvent getJumpSound() {
      return SoundEvents.f_215695_;
   }

   protected boolean m_8028_() {
      return false;
   }

   public boolean m_5545_(LevelAccessor pLevel, MobSpawnType pSpawnReason) {
      return SpawnRateConfig.rollSpawn((Integer)SpawnRateConfig.INSTANCE.aquaFrogSpawnRate.get(), this.m_217043_(), pSpawnReason) && super.m_5545_(pLevel, pSpawnReason);
   }

   protected void m_6668_(DamageSource pDamageSource) {
      if (this.getSummoningTick() >= 0) {
         this.m_5907_();
      } else {
         super.m_6668_(pDamageSource);
      }

   }

   protected void m_7472_(DamageSource pSource, int pLooting, boolean pRecentlyHit) {
      super.m_7472_(pSource, pLooting, pRecentlyHit);
      if (!((double)this.f_19796_.m_188501_() > 0.1D)) {
         this.m_19998_((ItemLike)TensuraMobDropItems.ELEMENTAL_ESSENCE.get());
      }
   }

   private <E extends IAnimatable> PlayState predicate(AnimationEvent<E> event) {
      if (this.m_5803_()) {
         event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.aqua_frog.sleep", EDefaultLoopTypes.LOOP));
         return PlayState.CONTINUE;
      } else if (this.isJumpingAnimation()) {
         event.getController().clearAnimationCache();
         return PlayState.STOP;
      } else {
         if (this.m_20072_() && !this.m_21525_()) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.aqua_frog.swim", EDefaultLoopTypes.LOOP));
         } else if (this.m_21825_()) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.aqua_frog.sit", EDefaultLoopTypes.LOOP));
         } else if (event.isMoving() && !this.m_21525_()) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.aqua_frog.walk", EDefaultLoopTypes.LOOP));
         } else {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.aqua_frog.idle", EDefaultLoopTypes.LOOP));
         }

         return PlayState.CONTINUE;
      }
   }

   private <E extends IAnimatable> PlayState playOncePredicate(AnimationEvent<E> event) {
      if (event.getController().getAnimationState().equals(AnimationState.Stopped)) {
         event.getController().markNeedsReload();
         if (this.getMiscAnimation() == 1) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.aqua_frog.spit", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 2) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.aqua_frog.tongue", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 3) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.aqua_frog.eat", EDefaultLoopTypes.PLAY_ONCE));
         }
      }

      return PlayState.CONTINUE;
   }

   private <T extends IAnimatable> PlayState jumpPredicate(AnimationEvent<T> event) {
      if (event.getController().getAnimationState().equals(AnimationState.Stopped) && this.isJumpingAnimation()) {
         event.getController().markNeedsReload();
         event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.aqua_frog.jump", EDefaultLoopTypes.PLAY_ONCE));
      }

      return PlayState.CONTINUE;
   }

   public void registerControllers(AnimationData data) {
      data.addAnimationController(new AnimationController(this, "controller", 0.0F, this::predicate));
      data.addAnimationController(new AnimationController(this, "playOnceController", 0.0F, this::playOncePredicate));
      data.addAnimationController(new AnimationController(this, "jumpController", 0.0F, this::jumpPredicate));
   }

   public boolean isJumpingAnimation() {
      return (Boolean)this.f_19804_.m_135370_(JUMP);
   }

   public void setJumpAnimation(boolean jump) {
      this.f_19804_.m_135381_(JUMP, jump);
   }

   public boolean isPlayerJumping() {
      return this.playerJumping;
   }

   public void setPlayerJumping(boolean playerJumping) {
      this.playerJumping = playerJumping;
   }

   public AnimationFactory getFactory() {
      return this.factory;
   }

   static {
      MISC_ANIMATION = SynchedEntityData.m_135353_(AquaFrogEntity.class, EntityDataSerializers.f_135028_);
      SUMMONING_TICK = SynchedEntityData.m_135353_(AquaFrogEntity.class, EntityDataSerializers.f_135028_);
      SUMMONER_UUID = SynchedEntityData.m_135353_(AquaFrogEntity.class, EntityDataSerializers.f_135041_);
      JUMP = SynchedEntityData.m_135353_(AquaFrogEntity.class, EntityDataSerializers.f_135035_);
   }

   static class FrogAttackGoal extends MeleeAttackGoal {
      public final AquaFrogEntity frog;

      public FrogAttackGoal(AquaFrogEntity beast) {
         super(beast, 1.2D, true);
         this.frog = beast;
      }

      public boolean m_8036_() {
         return this.frog.m_21827_() ? false : super.m_8036_();
      }

      protected void m_6739_(LivingEntity pEnemy, double pDistToEnemySqr) {
         double d0 = this.m_6639_(pEnemy);
         if (this.frog.getMiscAnimation() == 0) {
            int randomAttack = this.randomAttack(d0);
            double var10000;
            switch(randomAttack) {
            case 1:
               var10000 = 600.0D;
               break;
            case 2:
               var10000 = 25.0D;
               break;
            default:
               var10000 = d0;
            }

            double attackRange = var10000;
            if (pDistToEnemySqr <= attackRange && this.m_25564_()) {
               this.m_25563_();
               this.frog.setMiscAnimation(randomAttack);
            }
         }

      }

      protected int randomAttack(double distanceSqr) {
         return this.frog.f_19796_.m_188503_(10) != 5 && !(distanceSqr >= 25.0D) ? 2 : 1;
      }
   }

   static class LeapJumpGoal extends LeapWithStrengthGoal {
      private final AquaFrogEntity frog;

      public LeapJumpGoal(AquaFrogEntity frog) {
         super(frog, 0.5F, 2.5F, 3.0D, 20.0D, 20);
         this.frog = frog;
      }

      public boolean m_8036_() {
         return !this.frog.m_21827_() && !this.frog.m_5803_() ? super.m_8036_() : false;
      }

      public void m_8056_() {
         this.frog.setJumpAnimation(Boolean.TRUE);
         super.m_8056_();
      }

      public boolean m_8045_() {
         return !this.frog.m_21827_() && !this.frog.m_5803_() ? this.frog.m_20096_() : false;
      }
   }
}
