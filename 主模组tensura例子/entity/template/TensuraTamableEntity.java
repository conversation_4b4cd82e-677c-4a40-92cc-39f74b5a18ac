package com.github.manasmods.tensura.entity.template;

import com.github.manasmods.tensura.api.entity.ai.BetterWanderAroundGoal;
import com.github.manasmods.tensura.api.entity.subclass.ILivingPartEntity;
import com.github.manasmods.tensura.config.TensuraConfig;
import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.item.food.HealingPotionItem;
import com.github.manasmods.tensura.registry.sound.TensuraSoundEvents;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import javax.annotation.Nullable;
import net.minecraft.ChatFormatting;
import net.minecraft.core.BlockPos;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.MutableComponent;
import net.minecraft.network.chat.Style;
import net.minecraft.network.syncher.EntityDataAccessor;
import net.minecraft.network.syncher.EntityDataSerializers;
import net.minecraft.network.syncher.SynchedEntityData;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundSource;
import net.minecraft.tags.BlockTags;
import net.minecraft.util.RandomSource;
import net.minecraft.util.TimeUtil;
import net.minecraft.world.Difficulty;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.InteractionResult;
import net.minecraft.world.entity.AgeableMob;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityDimensions;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.Mob;
import net.minecraft.world.entity.MobSpawnType;
import net.minecraft.world.entity.NeutralMob;
import net.minecraft.world.entity.Pose;
import net.minecraft.world.entity.TamableAnimal;
import net.minecraft.world.entity.ai.control.LookControl;
import net.minecraft.world.entity.ai.control.MoveControl;
import net.minecraft.world.entity.ai.goal.WaterAvoidingRandomFlyingGoal;
import net.minecraft.world.entity.ai.goal.target.HurtByTargetGoal;
import net.minecraft.world.entity.ai.goal.target.NearestAttackableTargetGoal;
import net.minecraft.world.entity.ai.goal.target.OwnerHurtByTargetGoal;
import net.minecraft.world.entity.ai.goal.target.OwnerHurtTargetGoal;
import net.minecraft.world.entity.ai.util.AirAndWaterRandomPos;
import net.minecraft.world.entity.ai.util.HoverRandomPos;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.LevelAccessor;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.event.ForgeEventFactory;
import net.minecraftforge.eventbus.api.Event.Result;

public class TensuraTamableEntity extends TamableAnimal implements NeutralMob {
   private static final EntityDataAccessor<Integer> DATA_REMAINING_ANGER_TIME;
   private static final EntityDataAccessor<Integer> CUSTOM_OWNER_COMMAND;
   private static final EntityDataAccessor<Integer> CUSTOM_BEHAVIOUR;
   protected static final EntityDataAccessor<Boolean> SLEEPING;
   private static final EntityDataAccessor<BlockPos> WANDER_POS;
   protected int sleepingTime;
   protected int maxSleepTime;
   @Nullable
   private UUID persistentAngerTarget;

   protected TensuraTamableEntity(EntityType<? extends TamableAnimal> pEntityType, Level pLevel) {
      super(pEntityType, pLevel);
      this.f_21365_ = new TensuraTamableEntity.SleepLookControl();
      this.f_21342_ = new TensuraTamableEntity.SleepMoveControl();
      this.f_21346_.m_25352_(7, new TensuraTamableEntity.TargetingBehaviourGoal(this));
   }

   protected void m_8097_() {
      super.m_8097_();
      this.f_19804_.m_135372_(DATA_REMAINING_ANGER_TIME, 0);
      this.f_19804_.m_135372_(CUSTOM_OWNER_COMMAND, 0);
      this.f_19804_.m_135372_(CUSTOM_BEHAVIOUR, 0);
      this.f_19804_.m_135372_(SLEEPING, false);
      this.f_19804_.m_135372_(WANDER_POS, BlockPos.f_121853_);
   }

   public void m_7380_(CompoundTag compound) {
      super.m_7380_(compound);
      compound.m_128405_("OwnerCommand", this.getOwnerCommand());
      compound.m_128405_("Behaviour", this.getBehaviour());
      compound.m_128379_("Sleeping", this.m_5803_());
      compound.m_128405_("WanderPosX", this.getWanderPos().m_123341_());
      compound.m_128405_("WanderPosY", this.getWanderPos().m_123342_());
      compound.m_128405_("WanderPosZ", this.getWanderPos().m_123343_());
      this.m_21678_(compound);
      if (this.m_21805_() == null) {
         compound.m_128473_("Owner");
      }

   }

   public void m_7378_(CompoundTag compound) {
      super.m_7378_(compound);
      this.setOwnerCommand(compound.m_128451_("OwnerCommand"));
      this.setBehaviour(compound.m_128451_("Behaviour"));
      this.f_19804_.m_135381_(SLEEPING, compound.m_128471_("Sleeping"));
      this.setWanderPos(new BlockPos(compound.m_128451_("WanderPosX"), compound.m_128451_("WanderPosY"), compound.m_128451_("WanderPosZ")));
      this.m_147285_(this.f_19853_, compound);
   }

   public int getOwnerCommand() {
      return (Integer)this.f_19804_.m_135370_(CUSTOM_OWNER_COMMAND);
   }

   public void setOwnerCommand(int command) {
      this.f_19804_.m_135381_(CUSTOM_OWNER_COMMAND, command);
   }

   public int getBehaviour() {
      return (Integer)this.f_19804_.m_135370_(CUSTOM_BEHAVIOUR);
   }

   public void setBehaviour(int behaviour) {
      this.f_19804_.m_135381_(CUSTOM_BEHAVIOUR, behaviour);
   }

   public boolean isWandering() {
      return this.getOwnerCommand() == 1;
   }

   public void setWandering(boolean wandering) {
      int mode = wandering ? 1 : 0;
      this.f_19804_.m_135381_(CUSTOM_OWNER_COMMAND, mode);
   }

   public boolean canSleep() {
      return false;
   }

   public boolean m_5803_() {
      return !this.canSleep() ? false : (Boolean)this.f_19804_.m_135370_(SLEEPING);
   }

   public void setSleeping(boolean sleeping) {
      this.f_19804_.m_135381_(SLEEPING, sleeping);
      this.m_6210_();
      this.sleepingTime = 0;
      if (sleeping == Boolean.TRUE) {
         this.maxSleepTime = 200 + this.f_19796_.m_188503_(550);
      } else {
         this.maxSleepTime = 100 + this.f_19796_.m_188503_(50);
      }

   }

   public void setWanderPos(BlockPos pPos) {
      this.f_19804_.m_135381_(WANDER_POS, pPos);
   }

   public BlockPos getWanderPos() {
      return (BlockPos)this.f_19804_.m_135370_(WANDER_POS);
   }

   public void m_7350_(EntityDataAccessor<?> pKey) {
      if (SLEEPING.equals(pKey)) {
         this.m_6210_();
      }

      super.m_7350_(pKey);
   }

   public boolean m_6573_(Player pPlayer) {
      return !this.m_21523_() && this.m_21830_(pPlayer);
   }

   public AgeableMob m_142606_(ServerLevel pLevel, AgeableMob pOtherParent) {
      return null;
   }

   public EntityDimensions m_6972_(Pose pPose) {
      EntityDimensions entitydimensions = super.m_6972_(pPose);
      return this.m_5803_() ? entitydimensions.m_20390_(1.0F, 0.5F) : entitydimensions;
   }

   public boolean m_7757_(LivingEntity pTarget, LivingEntity pOwner) {
      return !ILivingPartEntity.checkForHead(pTarget).m_7307_(pOwner);
   }

   public void resetOwner(@Nullable UUID ownerUUID) {
      if (ownerUUID == null) {
         this.m_7105_(false);
         this.m_21816_((UUID)null);
         this.f_19804_.m_135381_(f_21799_, Optional.empty());
      } else {
         this.m_7105_(true);
         this.m_21816_(ownerUUID);
      }

      this.m_21839_(false);
      this.setBehaviour(0);
      this.setWandering(false);
      this.m_6710_((LivingEntity)null);
   }

   public void m_8119_() {
      super.m_8119_();
      if (this.canSleep()) {
         this.sleepHandler();
      }

   }

   protected void targetingMovementHelper() {
      LivingEntity livingentity = this.m_5448_();
      LivingEntity owner = this.m_21826_();
      if (livingentity != null && livingentity.m_6084_() && this.m_20280_(livingentity) > 9.0D) {
         this.f_21342_.m_6849_(livingentity.m_20185_(), livingentity.m_20186_(), livingentity.m_20189_(), this.f_21342_.m_24999_());
      } else if (owner != null && owner.m_6084_() && (double)this.m_20270_(owner) > 10.0D && !this.m_21827_() && !this.isWandering()) {
         this.f_21342_.m_6849_(owner.m_20185_(), owner.m_20186_(), owner.m_20189_(), this.f_21342_.m_24999_());
      }

   }

   protected void sleepHandler() {
      if (!this.m_9236_().m_5776_()) {
         if (this.m_5803_() && (this.m_5448_() != null || this.m_27593_() || this.isInFluidType() || this.m_20160_() || this.m_20159_() || ++this.sleepingTime > this.maxSleepTime && this.f_19853_.m_46461_() || this.shouldFollowOwner())) {
            this.setSleeping(false);
         }

         if (this.m_5448_() == null && this.f_19853_.m_46462_() && !this.shouldFollowOwner() && !this.m_5803_() && !this.isInFluidType() && !this.m_20160_() && !this.m_20159_() && this.f_19796_.m_188503_(100) == 0) {
            if (this.m_217043_().m_188499_()) {
               this.setSleeping(true);
            } else {
               this.sleepingTime = 0;
               this.maxSleepTime = 100 + this.f_19796_.m_188503_(550);
            }
         }

      }
   }

   public boolean shouldFollowOwner() {
      LivingEntity owner = this.m_21826_();
      if (owner == null) {
         return false;
      } else if (owner.m_20270_(this) < 10.0F) {
         return false;
      } else {
         return !this.m_21827_() && !this.isWandering();
      }
   }

   public void m_8107_() {
      super.m_8107_();
      if (!this.m_9236_().m_5776_()) {
         this.m_21666_((ServerLevel)this.f_19853_, true);
      }

      if (this.m_5803_()) {
         this.f_20899_ = false;
         this.f_20900_ = 0.0F;
         this.f_20902_ = 0.0F;
      }

   }

   public int m_6784_() {
      return (Integer)this.f_19804_.m_135370_(DATA_REMAINING_ANGER_TIME);
   }

   public void m_7870_(int pTime) {
      this.f_19804_.m_135381_(DATA_REMAINING_ANGER_TIME, pTime);
   }

   @Nullable
   public UUID m_6120_() {
      return this.persistentAngerTarget;
   }

   public void m_6925_(@Nullable UUID pTarget) {
      this.persistentAngerTarget = pTarget;
   }

   public void m_6825_() {
      this.m_7870_(TimeUtil.m_145020_(20, 39).m_214085_(this.f_19796_));
   }

   public boolean m_7307_(Entity entityIn) {
      LivingEntity livingentity = this.m_21826_();
      if (this.m_21824_() && livingentity != null) {
         if (entityIn == livingentity) {
            return true;
         } else if (entityIn instanceof TamableAnimal) {
            TamableAnimal tamableAnimal = (TamableAnimal)entityIn;
            return tamableAnimal.m_21830_(livingentity);
         } else if (entityIn instanceof TensuraHorseEntity) {
            TensuraHorseEntity horse = (TensuraHorseEntity)entityIn;
            return horse.isOwnedBy(livingentity);
         } else {
            return livingentity.m_7307_(entityIn);
         }
      } else {
         return super.m_7307_(entityIn);
      }
   }

   public InteractionResult handleEating(Player player, InteractionHand hand, ItemStack itemstack) {
      if (this.m_6898_(itemstack)) {
         if (this.m_21223_() < this.m_21233_()) {
            if (!player.m_7500_()) {
               itemstack.m_41774_(1);
            }

            this.m_5634_(3.0F);
            this.m_9236_().m_6269_((Player)null, this, (SoundEvent)TensuraSoundEvents.EATING.get(), SoundSource.NEUTRAL, 1.0F, 1.0F);
            return InteractionResult.SUCCESS;
         }

         if (this.m_6162_()) {
            this.m_142075_(player, hand, itemstack);
            this.m_146740_(m_216967_(-this.m_146764_()), true);
            this.m_9236_().m_6269_(player, this, (SoundEvent)TensuraSoundEvents.EATING.get(), SoundSource.NEUTRAL, 1.0F, 1.0F);
            return InteractionResult.m_19078_(this.f_19853_.f_46443_);
         }
      }

      return InteractionResult.PASS;
   }

   public InteractionResult m_6071_(Player player, InteractionHand hand) {
      ItemStack itemstack = player.m_21120_(hand);
      if (itemstack.m_41720_() instanceof HealingPotionItem) {
         return super.m_6071_(player, hand);
      } else {
         InteractionResult eating = this.handleEating(player, hand, itemstack);
         if (eating.m_19077_()) {
            return eating;
         } else if (!this.f_19853_.f_46443_) {
            if (this.m_21824_() && !super.m_6071_(player, hand).m_19077_() && this.m_21830_(player)) {
               this.commanding(player);
               return InteractionResult.SUCCESS;
            } else {
               return super.m_6071_(player, hand);
            }
         } else {
            boolean flag = this.m_21830_(player) || this.m_21824_();
            return flag ? InteractionResult.CONSUME : InteractionResult.PASS;
         }
      }
   }

   public void commanding(Player player) {
      MutableComponent message;
      if (this.m_21827_()) {
         message = Component.m_237110_("tensura.message.pet.follow", new Object[]{this.m_5446_()});
         this.m_21839_(false);
         this.setWandering(false);
      } else if (!this.isWandering()) {
         message = Component.m_237110_("tensura.message.pet.wander", new Object[]{this.m_5446_()});
         this.m_6710_((LivingEntity)null);
         this.m_21839_(false);
         this.setWandering(true);
         this.setWanderPos(player.m_20097_().m_7494_());
      } else {
         message = Component.m_237110_("tensura.message.pet.stay", new Object[]{this.m_5446_()});
         this.m_21573_().m_26573_();
         this.m_21839_(true);
         this.setWandering(false);
         this.m_6710_((LivingEntity)null);
      }

      player.m_5661_(message.m_6270_(Style.f_131099_.m_131140_(ChatFormatting.AQUA)), true);
   }

   public void cycleBehaviour(LivingEntity owner) {
      int behaviour = this.getBehaviour() + 1;
      MutableComponent message;
      switch(this.getBehaviour()) {
      case 1:
         message = Component.m_237110_("tensura.message.pet.aggressive", new Object[]{this.m_5446_()});
         break;
      case 2:
         this.m_6710_((LivingEntity)null);
         message = Component.m_237110_("tensura.message.pet.protect", new Object[]{this.m_5446_()});
         break;
      case 3:
         behaviour = 0;
         this.m_6710_((LivingEntity)null);
         message = Component.m_237110_("tensura.message.pet.neutral", new Object[]{this.m_5446_()});
         break;
      default:
         this.m_6710_((LivingEntity)null);
         message = Component.m_237110_("tensura.message.pet.passive", new Object[]{this.m_5446_()});
      }

      this.setBehaviour(behaviour);
      if (owner instanceof Player) {
         Player player = (Player)owner;
         player.m_5661_(message.m_6270_(Style.f_131099_.m_131140_(ChatFormatting.AQUA)), true);
      }

   }

   public boolean shouldTarget(LivingEntity target) {
      if (target instanceof Player) {
         Player player = (Player)target;
         if (player.m_7500_() || player.m_5833_()) {
            return false;
         }
      }

      if (this.isWandering()) {
         double distance = (Double)TensuraConfig.INSTANCE.entitiesConfig.tamedWanderRadius.get();
         if (target.m_20238_(Vec3.m_82512_(this.getWanderPos())) > distance * distance) {
            return false;
         }
      }

      if (this.getBehaviour() == 2) {
         if (this.m_21826_() != null && target.m_7307_(this.m_21826_())) {
            return false;
         } else {
            return target != this && !this.m_7307_(target);
         }
      } else if (this.getBehaviour() != 3) {
         return false;
      } else if (target.m_7307_(this)) {
         return false;
      } else {
         if (target instanceof Mob) {
            Mob mobTarget = (Mob)target;
            if (mobTarget.m_5448_() != null) {
               return mobTarget.m_5448_().m_7307_(this);
            }
         }

         if (this.m_21826_() != null) {
            if (target.m_7307_(this.m_21826_())) {
               return false;
            } else {
               return this.m_21826_().m_21214_() == target || this.m_21826_().m_21188_() == target;
            }
         } else {
            return false;
         }
      }
   }

   protected boolean canSpawnSpecialVariant(MobSpawnType pSpawnType) {
      if (pSpawnType == MobSpawnType.MOB_SUMMONED) {
         return false;
      } else {
         return pSpawnType != MobSpawnType.BUCKET;
      }
   }

   public static boolean checkGrassMobSpawnRules(EntityType<? extends Mob> mob, LevelAccessor pLevel, MobSpawnType pSpawnType, BlockPos pPos, RandomSource pRandom) {
      if (pSpawnType == MobSpawnType.SPAWNER) {
         return true;
      } else {
         return !pLevel.m_8055_(pPos.m_7495_()).m_204336_(BlockTags.f_184228_) ? false : Mob.m_217057_(mob, pLevel, pSpawnType, pPos, pRandom);
      }
   }

   public static boolean checkSandMobSpawnRules(EntityType<? extends Mob> mob, LevelAccessor pLevel, MobSpawnType pSpawnType, BlockPos pPos, RandomSource pRandom) {
      if (pSpawnType == MobSpawnType.SPAWNER) {
         return true;
      } else {
         return !pLevel.m_8055_(pPos.m_7495_()).m_204336_(BlockTags.f_184228_) && !pLevel.m_8055_(pPos.m_7495_()).m_204336_(BlockTags.f_13029_) ? false : Mob.m_217057_(mob, pLevel, pSpawnType, pPos, pRandom);
      }
   }

   public static boolean checkTensuraMobSpawnRules(EntityType<? extends Mob> mob, LevelAccessor pLevel, MobSpawnType pSpawnType, BlockPos pPos, RandomSource pRandom) {
      if (pSpawnType == MobSpawnType.SPAWNER) {
         return true;
      } else {
         return !pLevel.m_8055_(pPos.m_7495_()).m_204336_(TensuraTags.Blocks.MOBS_SPAWNABLE_ON) ? false : Mob.m_217057_(mob, pLevel, pSpawnType, pPos, pRandom);
      }
   }

   public static boolean checkHostileGrassMobSpawnRules(EntityType<? extends Mob> mob, LevelAccessor pLevel, MobSpawnType pSpawnType, BlockPos pPos, RandomSource pRandom) {
      if (pLevel.m_46791_() == Difficulty.PEACEFUL) {
         return false;
      } else if (pSpawnType == MobSpawnType.SPAWNER) {
         return true;
      } else {
         return !pLevel.m_8055_(pPos.m_7495_()).m_204336_(BlockTags.f_184228_) ? false : Mob.m_217057_(mob, pLevel, pSpawnType, pPos, pRandom);
      }
   }

   public static boolean checkHostileSandMobSpawnRules(EntityType<? extends Mob> mob, LevelAccessor pLevel, MobSpawnType pSpawnType, BlockPos pPos, RandomSource pRandom) {
      if (pLevel.m_46791_() == Difficulty.PEACEFUL) {
         return false;
      } else if (pSpawnType == MobSpawnType.SPAWNER) {
         return true;
      } else {
         return !pLevel.m_8055_(pPos.m_7495_()).m_204336_(BlockTags.f_184228_) && !pLevel.m_8055_(pPos.m_7495_()).m_204336_(BlockTags.f_13029_) ? false : Mob.m_217057_(mob, pLevel, pSpawnType, pPos, pRandom);
      }
   }

   public static boolean checkHostileMobSpawnRules(EntityType<? extends Mob> mob, LevelAccessor pLevel, MobSpawnType pSpawnType, BlockPos pPos, RandomSource pRandom) {
      if (pLevel.m_46791_() == Difficulty.PEACEFUL) {
         return false;
      } else if (pSpawnType == MobSpawnType.SPAWNER) {
         return true;
      } else {
         return !pLevel.m_8055_(pPos.m_7495_()).m_204336_(TensuraTags.Blocks.MOBS_SPAWNABLE_ON) ? false : Mob.m_217057_(mob, pLevel, pSpawnType, pPos, pRandom);
      }
   }

   protected boolean m_8028_() {
      return !this.m_21824_();
   }

   protected boolean removeWhenNoAction() {
      return this.m_6785_(256.0D);
   }

   public boolean m_6785_(double pDistanceToClosestPlayer) {
      if (this.getSpawnType() == MobSpawnType.STRUCTURE) {
         return false;
      } else if (this.getSpawnType() == MobSpawnType.BREEDING) {
         return false;
      } else {
         return !this.m_21824_();
      }
   }

   public void m_6043_() {
      if (this.f_19853_.m_46791_() == Difficulty.PEACEFUL && this.m_8028_()) {
         this.m_146870_();
      } else if (!this.m_21532_() && !this.m_8023_()) {
         Entity entity = this.f_19853_.m_45930_(this, -1.0D);
         Result result = ForgeEventFactory.canEntityDespawn(this);
         if (result == Result.DENY) {
            this.f_20891_ = 0;
            entity = null;
         } else if (result == Result.ALLOW) {
            this.m_146870_();
            entity = null;
         }

         if (entity != null) {
            double distance = entity.m_20280_(this);
            int removeDistance = this.m_6095_().m_20674_().m_21611_();
            if (distance > (double)(removeDistance * removeDistance) && this.m_6785_(distance)) {
               this.m_146870_();
            }

            int noRemoveDistance = this.m_6095_().m_20674_().m_21612_();
            int l = noRemoveDistance * noRemoveDistance;
            if (this.f_20891_ > 600 && this.f_19796_.m_188503_(800) == 0 && distance > (double)l && this.removeWhenNoAction() && this.m_6785_(distance)) {
               this.m_146870_();
            } else if (distance < (double)l) {
               this.f_20891_ = 0;
            }
         }
      } else {
         this.f_20891_ = 0;
      }

   }

   static {
      DATA_REMAINING_ANGER_TIME = SynchedEntityData.m_135353_(TensuraTamableEntity.class, EntityDataSerializers.f_135028_);
      CUSTOM_OWNER_COMMAND = SynchedEntityData.m_135353_(TensuraTamableEntity.class, EntityDataSerializers.f_135028_);
      CUSTOM_BEHAVIOUR = SynchedEntityData.m_135353_(TensuraTamableEntity.class, EntityDataSerializers.f_135028_);
      SLEEPING = SynchedEntityData.m_135353_(TensuraTamableEntity.class, EntityDataSerializers.f_135035_);
      WANDER_POS = SynchedEntityData.m_135353_(TensuraTamableEntity.class, EntityDataSerializers.f_135038_);
   }

   public class SleepLookControl extends LookControl {
      public SleepLookControl() {
         super(TensuraTamableEntity.this);
      }

      public void m_8128_() {
         if (!TensuraTamableEntity.this.m_5803_()) {
            super.m_8128_();
         }

      }
   }

   public class SleepMoveControl extends MoveControl {
      public SleepMoveControl() {
         super(TensuraTamableEntity.this);
      }

      public void m_8126_() {
         if (!TensuraTamableEntity.this.m_5803_()) {
            super.m_8126_();
         }

      }
   }

   public static class TargetingBehaviourGoal extends NearestAttackableTargetGoal<LivingEntity> {
      private final TensuraTamableEntity entity;
      private boolean shouldStop;

      public TargetingBehaviourGoal(TensuraTamableEntity entity) {
         Objects.requireNonNull(entity);
         super(entity, LivingEntity.class, true, entity::shouldTarget);
         this.entity = entity;
      }

      public boolean m_8036_() {
         if (!this.entity.m_21824_()) {
            return false;
         } else if (this.entity.getBehaviour() < 2) {
            return false;
         } else if (super.m_8036_()) {
            this.shouldStop = false;
            return true;
         } else {
            return false;
         }
      }

      public boolean m_8045_() {
         if (!this.entity.m_21824_()) {
            return false;
         } else if (this.entity.getBehaviour() < 2) {
            return false;
         } else {
            return !this.shouldStop;
         }
      }

      public boolean m_183429_() {
         return true;
      }

      public void m_8037_() {
         if (!super.m_8045_()) {
            this.shouldStop = true;
         } else if (this.isWanderingTooFar()) {
            this.entity.m_21661_();
            Vec3 pos = Vec3.m_82512_(this.entity.getWanderPos());
            this.entity.m_21566_().m_6849_(pos.f_82479_, pos.f_82480_, pos.f_82481_, 2.0D);
            this.shouldStop = true;
         }

      }

      private boolean isWanderingTooFar() {
         Vec3 pos = Vec3.m_82512_(this.entity.getWanderPos());
         double distance = (Double)TensuraConfig.INSTANCE.entitiesConfig.tamedWanderRadius.get();
         return this.entity.isWandering() && this.entity.m_20238_(pos) >= distance * distance;
      }
   }

   public class FlyingWanderAroundPosGoal extends WaterAvoidingRandomFlyingGoal {
      private final TensuraTamableEntity entity;
      private final int yRange;

      public FlyingWanderAroundPosGoal(TensuraTamableEntity mob) {
         this(mob, 1.0D, 7);
      }

      public FlyingWanderAroundPosGoal(TensuraTamableEntity mob, double speed, int yRange) {
         super(mob, speed);
         this.entity = mob;
         this.yRange = yRange;
      }

      public boolean m_8045_() {
         return this.isWanderingTooFar() && !this.entity.m_20160_() ? true : super.m_8045_();
      }

      private boolean isWanderingTooFar() {
         Vec3 pos = Vec3.m_82512_(this.entity.getWanderPos());
         double distance = (Double)TensuraConfig.INSTANCE.entitiesConfig.tamedWanderRadius.get();
         return this.entity.isWandering() && this.entity.m_20238_(pos) >= distance * distance;
      }

      @Nullable
      protected Vec3 m_7037_() {
         return this.isWanderingTooFar() ? Vec3.m_82512_(this.entity.getWanderPos()) : this.getSuperPosition();
      }

      @Nullable
      protected Vec3 getSuperPosition() {
         Vec3 vec3 = this.f_25725_.m_20252_(0.0F);
         Vec3 vec31 = HoverRandomPos.m_148465_(this.f_25725_, 8, this.yRange, vec3.f_82479_, vec3.f_82481_, 1.5707964F, 3, 1);
         return vec31 != null ? vec31 : AirAndWaterRandomPos.m_148357_(this.f_25725_, 8, 4, -2, vec3.f_82479_, vec3.f_82481_, 1.5707963705062866D);
      }
   }

   public static class WanderAroundPosGoal extends BetterWanderAroundGoal {
      private final TensuraTamableEntity entity;

      public WanderAroundPosGoal(TensuraTamableEntity mob, int interval, double speed, int xzRange, int yRange) {
         super(mob, interval, speed, xzRange, yRange);
         this.entity = mob;
      }

      public WanderAroundPosGoal(TensuraTamableEntity mob, double speed, int xzRange, int yRange) {
         super(mob, speed, xzRange, yRange);
         this.entity = mob;
      }

      public WanderAroundPosGoal(TensuraTamableEntity mob, double speed) {
         super(mob, speed);
         this.entity = mob;
      }

      public WanderAroundPosGoal(TensuraTamableEntity mob) {
         super(mob);
         this.entity = mob;
      }

      public boolean m_8045_() {
         return this.isWanderingTooFar() && !this.entity.m_20160_() ? true : super.m_8045_();
      }

      private boolean isWanderingTooFar() {
         Vec3 pos = Vec3.m_82512_(this.entity.getWanderPos());
         double distance = (Double)TensuraConfig.INSTANCE.entitiesConfig.tamedWanderRadius.get();
         return this.entity.isWandering() && this.entity.m_20238_(pos) >= distance * distance;
      }

      @Nullable
      protected Vec3 m_7037_() {
         return this.isWanderingTooFar() ? Vec3.m_82512_(this.entity.getWanderPos()) : super.m_7037_();
      }
   }

   public static class TensuraOwnerHurtTargetGoal extends OwnerHurtTargetGoal {
      private final TensuraTamableEntity entity;

      public TensuraOwnerHurtTargetGoal(TensuraTamableEntity entity) {
         super(entity);
         this.entity = entity;
      }

      public boolean m_8036_() {
         return this.entity.getBehaviour() == 1 ? false : super.m_8036_();
      }
   }

   public static class TensuraOwnerHurtByTargetGoal extends OwnerHurtByTargetGoal {
      private final TensuraTamableEntity entity;

      public TensuraOwnerHurtByTargetGoal(TensuraTamableEntity entity) {
         super(entity);
         this.entity = entity;
      }

      public boolean m_8036_() {
         return this.entity.getBehaviour() == 1 ? false : super.m_8036_();
      }
   }

   public static class TensuraHurtByTargetGoal extends HurtByTargetGoal {
      private final TensuraTamableEntity entity;

      public TensuraHurtByTargetGoal(TensuraTamableEntity entity, Class<?>... pToIgnoreDamage) {
         super(entity, pToIgnoreDamage);
         this.entity = entity;
      }

      public TensuraHurtByTargetGoal(TensuraTamableEntity entity) {
         super(entity, new Class[0]);
         this.entity = entity;
      }

      public boolean m_8036_() {
         return this.entity.getBehaviour() == 1 ? false : super.m_8036_();
      }
   }
}
