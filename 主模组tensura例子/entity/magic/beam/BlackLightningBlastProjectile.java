package com.github.manasmods.tensura.entity.magic.beam;

import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.entity.magic.lightning.BlackLightningBolt;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.particle.TensuraParticles;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import java.awt.Color;
import net.minecraft.core.particles.ParticleOptions;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.item.ItemEntity;
import net.minecraft.world.level.Level;
import net.minecraft.world.phys.Vec3;

public class BlackLightningBlastProjectile extends BeamProjectile {
   public BlackLightningBlastProjectile(EntityType<? extends BlackLightningBlastProjectile> entityType, Level level) {
      super(entityType, level);
      this.beamColorAndSize.put(new Color(255, 255, 255, 255), 0.2F);
      this.beamColorAndSize.put(new Color(0, 0, 0, 150), 0.4F);
      this.beamColorAndSize.put(new Color(255, 255, 255, 50), 0.6F);
   }

   public BlackLightningBlastProjectile(Level levelIn, LivingEntity shooter) {
      this((EntityType)TensuraEntityTypes.BLACK_LIGHTNING_BLAST.get(), levelIn);
      this.m_5602_(shooter);
   }

   public ResourceLocation[] getTextureLocation() {
      return new ResourceLocation[]{new ResourceLocation("tensura", "textures/entity/projectiles/beam/electric_beam.png")};
   }

   protected void dealDamage(Entity target) {
      if (!(this.damage <= 0.0F) && !(target instanceof ItemEntity)) {
         DamageSource damagesource = TensuraDamageSources.BLACK_LIGHTNING;
         if (this.m_37282_() != null) {
            damagesource = TensuraDamageSources.blackLightning(this.m_37282_());
         }

         if (target.m_6469_(DamageSourceHelper.addSkillAndCost(damagesource, this.getMpCost(), this.getSkill()), this.getDamage())) {
            TensuraParticleHelper.addServerParticlesAroundSelf(target, (ParticleOptions)TensuraParticles.BLACK_LIGHTNING_EFFECT.get());
            if (target instanceof LivingEntity) {
               LivingEntity living = (LivingEntity)target;
               BlackLightningBolt dummyBolt = new BlackLightningBolt(this.f_19853_, this.m_37282_());
               dummyBolt.setTensuraDamage(0.0F);
               living.m_8038_((ServerLevel)this.f_19853_, dummyBolt);
               living.m_147207_(new MobEffectInstance((MobEffect)TensuraMobEffects.PARALYSIS.get(), 100, 1, false, false, true), this.m_37282_());
            }
         }

      }
   }

   public void hitParticles(double x, double y, double z) {
      if (this.f_19797_ % 3 == 0) {
         Vec3 end = new Vec3(x, y, z);
         TensuraParticleHelper.addParticlesAroundPos(this.m_9236_().f_46441_, this.f_19853_, end, (ParticleOptions)TensuraParticles.BLACK_LIGHTNING_SPARK.get(), (double)this.getSize(), 3);
      }
   }

   public void rayParticles(Vec3 pos, int i) {
      if (this.f_19797_ % this.f_19796_.m_216339_(10, 16) == 0) {
         TensuraParticleHelper.addParticlesAroundPos(this.f_19796_, this.f_19853_, pos, (ParticleOptions)TensuraParticles.BLACK_LIGHTNING_SPARK.get(), (double)this.getVisualSize(), 1);
      }
   }
}
