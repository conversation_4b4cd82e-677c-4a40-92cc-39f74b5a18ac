package com.github.manasmods.tensura.entity.magic.skill;

import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.entity.magic.TensuraProjectile;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.particle.TensuraParticles;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import java.util.Iterator;
import java.util.List;
import java.util.Optional;
import net.minecraft.core.particles.ParticleOptions;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.util.RandomSource;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.level.Level;

public class HeatSphereProjectile extends TensuraProjectile {
   protected static final ResourceLocation[] TEXTURES = new ResourceLocation[]{new ResourceLocation("tensura", "textures/entity/projectiles/heat_sphere/heat_sphere_0.png"), new ResourceLocation("tensura", "textures/entity/projectiles/heat_sphere/heat_sphere_1.png"), new ResourceLocation("tensura", "textures/entity/projectiles/heat_sphere/heat_sphere_2.png"), new ResourceLocation("tensura", "textures/entity/projectiles/heat_sphere/heat_sphere_3.png"), new ResourceLocation("tensura", "textures/entity/projectiles/heat_sphere/heat_sphere_4.png"), new ResourceLocation("tensura", "textures/entity/projectiles/heat_sphere/heat_sphere_5.png"), new ResourceLocation("tensura", "textures/entity/projectiles/heat_sphere/heat_sphere_6.png")};

   public HeatSphereProjectile(EntityType<? extends HeatSphereProjectile> entityType, Level level) {
      super(entityType, level);
   }

   public HeatSphereProjectile(Level levelIn, LivingEntity shooter) {
      super((EntityType)TensuraEntityTypes.HEAT_SPHERE.get(), levelIn);
      this.m_5602_(shooter);
   }

   public String getMagic() {
      return "heat_wave";
   }

   public boolean shouldDiscardInWater() {
      return false;
   }

   public boolean shouldDiscardInLava() {
      return false;
   }

   public boolean piercingEntity() {
      return true;
   }

   public ResourceLocation[] getTextureLocation() {
      return TEXTURES;
   }

   public void setPosAndShoot(LivingEntity entity) {
      this.m_146884_(entity.m_20182_().m_82520_(0.0D, (double)entity.m_20192_(), 0.0D));
      this.shootFromRot(entity.m_20154_());
   }

   protected void dealDamage(Entity target) {
      DamageSource damageSource = TensuraDamageSources.HEAT_WAVE;
      Entity var4 = this.m_37282_();
      if (var4 instanceof LivingEntity) {
         LivingEntity owner = (LivingEntity)var4;
         damageSource = TensuraDamageSources.heatWave(owner);
      }

      if (this.damage > 0.0F) {
         target.m_6469_(DamageSourceHelper.addSkillAndCost(damageSource, this.getMpCost(), this.getSkill()), this.getDamage());
      }

   }

   public void m_8119_() {
      super.m_8119_();
      this.applyEffectAround((double)this.getEffectRange());
   }

   public void applyEffectAround(double inflateRadius) {
      List<LivingEntity> livingEntityList = this.f_19853_.m_6443_(LivingEntity.class, this.m_20191_().m_82400_(inflateRadius), (entityData) -> {
         return this.m_37282_() == null || !entityData.m_7307_(this.m_37282_()) && !entityData.m_7306_(this.m_37282_());
      });
      if (!livingEntityList.isEmpty()) {
         Iterator var4 = livingEntityList.iterator();

         while(var4.hasNext()) {
            LivingEntity target = (LivingEntity)var4.next();
            if (!RandomSource.m_216327_().m_188499_()) {
               DamageSource damageSource = TensuraDamageSources.HEAT_WAVE;
               Entity var8 = this.m_37282_();
               if (var8 instanceof LivingEntity) {
                  LivingEntity owner = (LivingEntity)var8;
                  damageSource = TensuraDamageSources.heatWave(owner);
               }

               if (this.damage > 0.0F) {
                  target.m_6469_(DamageSourceHelper.addSkillAndCost(damageSource, this.getMpCost() / 10.0D), this.getDamage() / 6.0F);
               }
            }
         }

      }
   }

   public Optional<SoundEvent> hitSound() {
      return Optional.of(SoundEvents.f_11913_);
   }

   public void hitParticles(double x, double y, double z) {
      TensuraParticleHelper.spawnServerParticles(this.f_19853_, ParticleTypes.f_123813_, x, y, z, 1, 0.12D, 0.12D, 0.12D, 0.15D, false);
      TensuraParticleHelper.spawnServerParticles(this.f_19853_, (ParticleOptions)TensuraParticles.HEAT_EFFECT.get(), x, y, z, 10, 0.5D, 0.5D, 0.5D, 0.1D, false);
   }

   public void flyingParticles() {
      if ((double)this.f_19796_.m_188501_() <= 0.8D) {
         double dx = this.f_19853_.f_46441_.m_188500_() * 0.05D - 0.05D;
         double dy = this.f_19853_.f_46441_.m_188500_() * 0.05D - 0.05D;
         double dz = this.f_19853_.f_46441_.m_188500_() * 0.05D - 0.05D;
         double x = (this.f_19853_.f_46441_.m_188500_() - 0.5D) * 4.0D;
         double y = (this.f_19853_.f_46441_.m_188500_() - 0.5D) * 4.0D;
         double z = (this.f_19853_.f_46441_.m_188500_() - 0.5D) * 4.0D;
         this.f_19853_.m_7106_((ParticleOptions)TensuraParticles.HEAT_EFFECT.get(), this.m_20185_() + x, this.m_20186_() + y, this.m_20189_() + z, dx, dy, dz);
      }

   }
}
