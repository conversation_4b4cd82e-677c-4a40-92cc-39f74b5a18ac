package com.github.manasmods.tensura.entity.magic.projectile;

import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.entity.magic.TensuraProjectile;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.particle.TensuraParticles;
import java.util.Optional;
import net.minecraft.core.particles.ParticleOptions;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.level.Level;

public class LightningLanceProjectile extends TensuraProjectile {
   protected static final ResourceLocation[] TEXTURES = new ResourceLocation[]{new ResourceLocation("tensura", "textures/entity/projectiles/lightning_lance.png")};

   public LightningLanceProjectile(EntityType<? extends LightningLanceProjectile> entityType, Level level) {
      super(entityType, level);
   }

   public LightningLanceProjectile(Level levelIn, LivingEntity shooter) {
      super((EntityType)TensuraEntityTypes.LIGHTNING_LANCE.get(), levelIn);
      this.m_5602_(shooter);
   }

   public String getMagic() {
      return "tensura.wind_attack";
   }

   public boolean piercingEntity() {
      return true;
   }

   public ResourceLocation[] getTextureLocation() {
      return this.m_20202_() != null ? null : TEXTURES;
   }

   public void m_8119_() {
      super.m_8119_();
      if (this.getEffectRange() == -2.0F) {
         Entity entity = this.m_20202_();
         if (entity instanceof LivingEntity) {
            LivingEntity target = (LivingEntity)entity;
            if (target.m_6084_()) {
               if (this.f_19797_ == 140) {
                  this.setDamage(this.damage / 2.0F);
                  this.dealDamage(entity);
                  TensuraParticleHelper.addServerParticlesAroundSelf(target, (ParticleOptions)TensuraParticles.LIGHTNING_SPARK.get());
                  this.m_146870_();
               }

               return;
            }
         }

         this.m_146870_();
      }

   }

   protected void hitEntity(Entity entity) {
      super.hitEntity(entity);
      if (entity.m_6084_() && this.getEffectRange() == -1.0F) {
         this.m_7998_(entity, true);
         this.setEffectRange(-2.0F);
         this.f_19797_ = 100;
      }

   }

   public Optional<SoundEvent> hitSound() {
      return Optional.of(SoundEvents.f_12089_);
   }

   public void hitParticles(double x, double y, double z) {
      TensuraParticleHelper.spawnServerParticles(this.f_19853_, (ParticleOptions)TensuraParticles.LIGHTNING_SPARK.get(), x, y, z, 10, 0.5D, 0.5D, 0.5D, 0.1D, false);
   }

   public void flyingParticles() {
      this.f_19853_.m_7106_((ParticleOptions)TensuraParticles.LIGHTNING_SPARK.get(), this.m_20185_(), this.m_20186_(), this.m_20189_(), 0.0D, 0.0D, 0.0D);
   }
}
