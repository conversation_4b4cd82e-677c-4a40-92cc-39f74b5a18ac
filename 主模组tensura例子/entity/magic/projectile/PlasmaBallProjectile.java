package com.github.manasmods.tensura.entity.magic.projectile;

import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.particle.TensuraParticles;
import java.util.Iterator;
import java.util.List;
import net.minecraft.core.particles.ParticleOptions;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import net.minecraft.world.phys.Vec3;

public class PlasmaBallProjectile extends FireBoltProjectile {
   protected static final ResourceLocation[] TEXTURES = new ResourceLocation[]{new ResourceLocation("tensura", "textures/entity/projectiles/plasma_ball/plasma_ball_0.png"), new ResourceLocation("tensura", "textures/entity/projectiles/plasma_ball/plasma_ball_1.png"), new ResourceLocation("tensura", "textures/entity/projectiles/plasma_ball/plasma_ball_2.png"), new ResourceLocation("tensura", "textures/entity/projectiles/plasma_ball/plasma_ball_3.png"), new ResourceLocation("tensura", "textures/entity/projectiles/plasma_ball/plasma_ball_4.png"), new ResourceLocation("tensura", "textures/entity/projectiles/plasma_ball/plasma_ball_5.png")};

   public PlasmaBallProjectile(EntityType<? extends PlasmaBallProjectile> entityType, Level level) {
      super(entityType, level);
      this.setSize(1.5F);
   }

   public PlasmaBallProjectile(Level levelIn, LivingEntity shooter) {
      super((EntityType)TensuraEntityTypes.PLASMA_BALL.get(), levelIn);
      this.m_5602_(shooter);
      this.setSize(1.5F);
   }

   public boolean shouldDiscardInWater() {
      return false;
   }

   public ResourceLocation[] getTextureLocation() {
      return TEXTURES;
   }

   public void m_8119_() {
      super.m_8119_();
      if (!this.f_19853_.m_5776_()) {
         List<LivingEntity> livingEntityList = this.f_19853_.m_6443_(LivingEntity.class, this.m_20191_().m_82400_(4.0D), (entity) -> {
            return (this.m_37282_() == null || !entity.m_7307_(this.m_37282_()) && !entity.m_7306_(this.m_37282_())) && !entity.m_5825_() && !entity.m_21023_(MobEffects.f_19607_);
         });
         if (!livingEntityList.isEmpty()) {
            Iterator var2 = livingEntityList.iterator();

            while(var2.hasNext()) {
               LivingEntity pLivingEntity = (LivingEntity)var2.next();
               pLivingEntity.m_20254_(this.getBurnTicks());
               Entity var5 = this.m_37282_();
               if (var5 instanceof Player) {
                  Player player = (Player)var5;
                  pLivingEntity.m_6598_(player);
               }
            }

         }
      }
   }

   public void hitParticles(double x, double y, double z) {
      TensuraParticleHelper.spawnServerParticles(this.f_19853_, (ParticleOptions)TensuraParticles.PLASMA_FIRE.get(), x, y, z, 30, 1.5D, 0.1D, 1.5D, 0.2D, false);
      TensuraParticleHelper.spawnServerParticles(this.f_19853_, ParticleTypes.f_123799_, x, y, z, 30, 1.5D, 0.1D, 1.5D, 0.2D, false);
   }

   public void flyingParticles() {
      Vec3 vec3 = this.m_20184_();
      double d0 = this.m_20185_() - vec3.f_82479_;
      double d1 = this.m_20186_() - vec3.f_82480_;
      double d2 = this.m_20189_() - vec3.f_82481_;

      for(int i = 0; i < 8; ++i) {
         Vec3 motion = this.vec3Random().m_82490_(0.10000000149011612D).m_82546_(this.m_20184_().m_82490_(0.10000000149011612D));
         Vec3 pos = this.vec3Random().m_82490_(0.30000001192092896D);
         this.f_19853_.m_7106_((ParticleOptions)TensuraParticles.PLASMA_FIRE.get(), d0 + pos.f_82479_, d1 + 0.5D + pos.f_82480_, d2 + pos.f_82481_, motion.f_82479_, motion.f_82480_, motion.f_82481_);
         this.f_19853_.m_7106_(ParticleTypes.f_123762_, d0 + pos.f_82479_, d1 + 0.5D + pos.f_82480_, d2 + pos.f_82481_, motion.f_82479_, motion.f_82480_, motion.f_82481_);
      }

   }
}
