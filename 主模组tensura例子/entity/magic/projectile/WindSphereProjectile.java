package com.github.manasmods.tensura.entity.magic.projectile;

import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.entity.magic.TensuraProjectile;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.particle.TensuraParticles;
import java.util.Iterator;
import java.util.List;
import java.util.Optional;
import net.minecraft.core.particles.ParticleOptions;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import net.minecraft.world.phys.BlockHitResult;
import org.jetbrains.annotations.NotNull;

public class WindSphereProjectile extends TensuraProjectile {
   protected static final ResourceLocation[] TEXTURES = new ResourceLocation[]{new ResourceLocation("tensura", "textures/entity/projectiles/wind_sphere/wind_sphere_0.png"), new ResourceLocation("tensura", "textures/entity/projectiles/wind_sphere/wind_sphere_1.png"), new ResourceLocation("tensura", "textures/entity/projectiles/wind_sphere/wind_sphere_2.png"), new ResourceLocation("tensura", "textures/entity/projectiles/wind_sphere/wind_sphere_3.png"), new ResourceLocation("tensura", "textures/entity/projectiles/wind_sphere/wind_sphere_4.png")};

   public WindSphereProjectile(EntityType<? extends WindSphereProjectile> entityType, Level level) {
      super(entityType, level);
   }

   public WindSphereProjectile(Level levelIn, LivingEntity shooter) {
      super((EntityType)TensuraEntityTypes.WIND_SPHERE.get(), levelIn);
      this.m_5602_(shooter);
   }

   public String getMagic() {
      return "tensura.wind_attack";
   }

   public boolean shouldDiscardInWater() {
      return false;
   }

   public ResourceLocation[] getTextureLocation() {
      return TEXTURES;
   }

   protected void m_8060_(@NotNull BlockHitResult blockHitResult) {
      List<LivingEntity> list = this.f_19853_.m_6443_(LivingEntity.class, this.m_20191_().m_82400_(3.0D), LivingEntity::m_6084_);
      if (!list.isEmpty()) {
         Iterator var3 = list.iterator();

         while(var3.hasNext()) {
            LivingEntity target = (LivingEntity)var3.next();
            target.m_5997_(0.0D, 1.0D, 0.0D);
            TensuraParticleHelper.addServerParticlesAroundSelf(target, (ParticleOptions)TensuraParticles.GUST.get(), 2.0D);
            TensuraParticleHelper.addServerParticlesAroundSelf(target, (ParticleOptions)TensuraParticles.SMALL_GUST.get(), 2.0D);
            Entity var6 = this.m_37282_();
            if (var6 instanceof Player) {
               Player player = (Player)var6;
               target.m_6598_(player);
            }
         }
      }

      super.m_8060_(blockHitResult);
   }

   public Optional<SoundEvent> hitSound() {
      return Optional.of(SoundEvents.f_12317_);
   }

   public void hitParticles(double x, double y, double z) {
      TensuraParticleHelper.addServerParticlesAroundSelf(this, (ParticleOptions)TensuraParticles.GUST.get(), 3.0D);
      TensuraParticleHelper.spawnServerParticles(this.f_19853_, (ParticleOptions)TensuraParticles.SMALL_GUST.get(), x, y, z, 20, 0.1D, 0.1D, 0.1D, 0.15D, true);
   }

   public void flyingParticles() {
      double dx = this.f_19853_.f_46441_.m_188500_() * 0.05D - 0.05D;
      double dy = this.f_19853_.f_46441_.m_188500_() * 0.05D - 0.05D;
      double dz = this.f_19853_.f_46441_.m_188500_() * 0.05D - 0.05D;
      double x = (this.f_19853_.f_46441_.m_188500_() - 0.5D) * 4.0D;
      double y = (this.f_19853_.f_46441_.m_188500_() - 0.5D) * 4.0D;
      double z = (this.f_19853_.f_46441_.m_188500_() - 0.5D) * 4.0D;
      this.f_19853_.m_7106_((ParticleOptions)TensuraParticles.SMALL_GUST.get(), this.m_20185_() + x, this.m_20186_() + y, this.m_20189_() + z, dx, dy, dz);
   }
}
