package com.github.manasmods.tensura.entity.magic.projectile;

import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.entity.magic.TensuraProjectile;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.particle.TensuraParticles;
import java.util.Iterator;
import java.util.List;
import java.util.Optional;
import net.minecraft.core.particles.ParticleOptions;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.level.Level;
import net.minecraft.world.phys.BlockHitResult;
import net.minecraft.world.phys.Vec3;
import org.jetbrains.annotations.NotNull;

public class WaterBallProjectile extends TensuraProjectile {
   public WaterBallProjectile(EntityType<? extends WaterBallProjectile> entityType, Level level) {
      super(entityType, level);
      this.setSize(1.5F);
   }

   public WaterBallProjectile(Level levelIn, LivingEntity shooter) {
      super((EntityType)TensuraEntityTypes.WATER_BALL.get(), levelIn);
      this.m_5602_(shooter);
      this.setSize(1.5F);
   }

   public String getMagic() {
      return "tensura.water_attack";
   }

   public boolean shouldDiscardInWater() {
      return false;
   }

   public ResourceLocation[] getTextureLocation() {
      return new ResourceLocation[]{new ResourceLocation("tensura", "textures/entity/projectiles/water_ball.png")};
   }

   protected void m_8060_(@NotNull BlockHitResult blockHitResult) {
      List<LivingEntity> livingEntityList = this.f_19853_.m_6443_(LivingEntity.class, this.m_20191_().m_82400_(2.0D), LivingEntity::m_6084_);
      if (!livingEntityList.isEmpty()) {
         Iterator var3 = livingEntityList.iterator();

         while(var3.hasNext()) {
            LivingEntity pLivingEntity = (LivingEntity)var3.next();
            pLivingEntity.m_20095_();
         }
      }

      super.m_8060_(blockHitResult);
   }

   public Optional<SoundEvent> hitSound() {
      return Optional.of(SoundEvents.f_11807_);
   }

   public void hitParticles(double x, double y, double z) {
      TensuraParticleHelper.spawnServerParticles(this.f_19853_, (ParticleOptions)TensuraParticles.WATER_EFFECT.get(), x, y, z, 55, 0.08D, 0.08D, 0.08D, 0.15D, true);
      TensuraParticleHelper.spawnServerParticles(this.f_19853_, (ParticleOptions)TensuraParticles.WATER_BUBBLE.get(), x, y, z, 25, 0.08D, 0.08D, 0.08D, 0.15D, false);
   }

   public void flyingParticles() {
      Vec3 vec3 = this.m_20182_().m_82546_(this.m_20184_().m_82490_(2.0D));
      this.m_9236_().m_7106_((ParticleOptions)TensuraParticles.WATER_EFFECT.get(), vec3.f_82479_, vec3.f_82480_, vec3.f_82481_, 0.0D, 0.0D, 0.0D);

      for(int i = 0; i < 2; ++i) {
         Vec3 random = this.vec3Random().m_82490_(0.10000000149011612D);
         this.m_9236_().m_7106_((ParticleOptions)TensuraParticles.WATER_EFFECT.get(), vec3.f_82479_, vec3.f_82480_, vec3.f_82481_, random.f_82479_, random.f_82480_, random.f_82481_);
      }

   }
}
