package com.github.manasmods.tensura.entity.human;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.tensura.ability.skill.unique.BerserkerSkill;
import com.github.manasmods.tensura.api.entity.ai.CrossbowAttackGoal;
import com.github.manasmods.tensura.api.entity.ai.WanderingFollowOwnerGoal;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.entity.template.HumanoidNPCEntity;
import com.github.manasmods.tensura.entity.template.TensuraTamableEntity;
import com.github.manasmods.tensura.registry.particle.TensuraParticles;
import com.github.manasmods.tensura.registry.skill.UniqueSkills;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import javax.annotation.Nullable;
import net.minecraft.core.particles.ParticleOptions;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.Mob;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.ai.attributes.AttributeInstance;
import net.minecraft.world.entity.ai.attributes.AttributeModifier;
import net.minecraft.world.entity.ai.attributes.AttributeSupplier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.attributes.AttributeModifier.Operation;
import net.minecraft.world.entity.ai.goal.FloatGoal;
import net.minecraft.world.entity.ai.goal.LookAtPlayerGoal;
import net.minecraft.world.entity.ai.goal.RandomLookAroundGoal;
import net.minecraft.world.entity.ai.goal.RangedBowAttackGoal;
import net.minecraft.world.entity.ai.goal.SitWhenOrderedToGoal;
import net.minecraft.world.entity.ai.goal.WaterAvoidingRandomStrollGoal;
import net.minecraft.world.entity.ai.goal.target.NonTameRandomTargetGoal;
import net.minecraft.world.entity.ai.goal.target.ResetUniversalAngerTargetGoal;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import net.minecraftforge.common.ForgeMod;

public class ShogoTaguchiEntity extends OtherworlderEntity {
   public ShogoTaguchiEntity(EntityType<? extends ShogoTaguchiEntity> pEntityType, Level pLevel) {
      super(pEntityType, pLevel);
   }

   public static AttributeSupplier setAttributes() {
      return Mob.m_21552_().m_22268_(Attributes.f_22284_, 0.0D).m_22268_(Attributes.f_22281_, 20.0D).m_22268_(Attributes.f_22282_, 5.0D).m_22268_(Attributes.f_22276_, 200.0D).m_22268_(Attributes.f_22279_, 0.20000000298023224D).m_22268_(Attributes.f_22277_, 32.0D).m_22268_(Attributes.f_22278_, 0.0D).m_22268_((Attribute)ForgeMod.SWIM_SPEED.get(), 1.0D).m_22268_((Attribute)ForgeMod.ATTACK_RANGE.get(), 2.0D).m_22265_();
   }

   protected void m_8099_() {
      this.f_21345_.m_25352_(0, new FloatGoal(this));
      this.f_21345_.m_25352_(1, new SitWhenOrderedToGoal(this));
      this.f_21345_.m_25352_(2, new HumanoidNPCEntity.EatingItemGoal(this, (entity) -> {
         return this.shouldHeal();
      }, 3.0F));
      this.f_21345_.m_25352_(3, new CrossbowAttackGoal(this, 1.2D, 20.0F));
      this.f_21345_.m_25352_(3, new RangedBowAttackGoal(this, 1.0D, 20, 20.0F));
      this.f_21345_.m_25352_(3, new HumanoidNPCEntity.SpearTypeAttackGoal(this, 1.0D, 20, 20.0F));
      this.f_21345_.m_25352_(3, new HumanoidNPCEntity.NPCMeleeAttackGoal(this, 2.0D, true));
      this.f_21345_.m_25352_(4, new WanderingFollowOwnerGoal(this, 1.5D, 10.0F, 5.0F, false));
      this.f_21345_.m_25352_(7, new WaterAvoidingRandomStrollGoal(this, 1.2D));
      this.f_21345_.m_25352_(8, new RandomLookAroundGoal(this));
      this.f_21345_.m_25352_(9, new LookAtPlayerGoal(this, Player.class, 6.0F));
      this.f_21346_.m_25352_(1, new TensuraTamableEntity.TensuraOwnerHurtByTargetGoal(this));
      this.f_21346_.m_25352_(2, new TensuraTamableEntity.TensuraOwnerHurtTargetGoal(this));
      this.f_21346_.m_25352_(3, (new TensuraTamableEntity.TensuraHurtByTargetGoal(this, new Class[]{ShogoTaguchiEntity.class})).m_26044_(new Class[0]));
      this.f_21346_.m_25352_(5, new NonTameRandomTargetGoal(this, Player.class, false, this::shouldAttackPlayer));
      this.f_21346_.m_25352_(6, new NonTameRandomTargetGoal(this, LivingEntity.class, false, (entity) -> {
         return entity.m_6095_().m_204039_(TensuraTags.EntityTypes.OTHERWORLDER_PREY);
      }));
      this.f_21346_.m_25352_(8, new ResetUniversalAngerTargetGoal(this, true));
   }

   public ResourceLocation getTextureLocation() {
      return new ResourceLocation("tensura", "textures/entity/otherworlder/shogo_taguchi.png");
   }

   public List<ManasSkill> getUniqueSkills() {
      return List.of((ManasSkill)UniqueSkills.BERSERKER.get(), (ManasSkill)UniqueSkills.SURVIVOR.get());
   }

   public void m_8119_() {
      super.m_8119_();
      if (this.m_6084_() && this.m_21223_() < this.m_21233_() && this.f_19797_ % 40 == 0 && this.getSurvivor() != null) {
         this.m_5634_(10.0F);
         TensuraParticleHelper.addServerParticlesAroundSelf(this, ParticleTypes.f_123750_);
      }

   }

   public void m_6710_(@Nullable LivingEntity pLivingEntity) {
      LivingEntity oldTarget = this.m_5448_();
      super.m_6710_(pLivingEntity);
      LivingEntity newTarget = this.m_5448_();
      ManasSkillInstance instance = this.getBerserker();
      if (instance != null) {
         if (oldTarget == null && newTarget != null) {
            this.activateBerserker();
         } else if (oldTarget != null && newTarget == null) {
            this.activateBerserker();
         }

      }
   }

   private void activateBerserker() {
      double EP = TensuraEPCapability.getEP(this) * 12.5D;
      AttributeInstance armor = (AttributeInstance)Objects.requireNonNull(this.m_21051_(Attributes.f_22284_));
      if (armor.m_22111_(BerserkerSkill.BERSERKER) != null) {
         armor.m_22127_(BerserkerSkill.BERSERKER);
         this.m_9236_().m_6263_((Player)null, this.m_20185_(), this.m_20186_(), this.m_20189_(), SoundEvents.f_11738_, SoundSource.PLAYERS, 1.0F, 1.0F);
      } else {
         AttributeModifier armorModifier = new AttributeModifier(BerserkerSkill.BERSERKER, "BerserkerArmor", BerserkerSkill.getArmor(EP) / 4.0D, Operation.ADDITION);
         armor.m_22125_(armorModifier);
         this.m_9236_().m_6263_((Player)null, this.m_20185_(), this.m_20186_(), this.m_20189_(), SoundEvents.f_11736_, SoundSource.PLAYERS, 1.0F, 1.0F);
         this.m_9236_().m_6263_((Player)null, this.m_20185_(), this.m_20186_(), this.m_20189_(), SoundEvents.f_11913_, SoundSource.PLAYERS, 0.5F, 1.0F);
         TensuraParticleHelper.addServerParticlesAroundSelf(this, (ParticleOptions)TensuraParticles.SOLAR_FLASH.get(), 1.0D);
         TensuraParticleHelper.addServerParticlesAroundSelf(this, (ParticleOptions)TensuraParticles.YELLOW_LIGHTNING_SPARK.get(), 3.0D);
         TensuraParticleHelper.addServerParticlesAroundSelf(this, (ParticleOptions)TensuraParticles.YELLOW_LIGHTNING_SPARK.get(), 2.0D);
         TensuraParticleHelper.spawnServerParticles(this.f_19853_, (ParticleOptions)TensuraParticles.PURPLE_LIGHTNING_SPARK.get(), this.m_20185_(), this.m_20186_() + (double)(this.m_20206_() / 2.0F), this.m_20189_(), 100, 0.08D, 0.08D, 0.08D, 0.2D, true);
      }

      AttributeInstance damage = (AttributeInstance)Objects.requireNonNull(this.m_21051_(Attributes.f_22281_));
      if (damage.m_22111_(BerserkerSkill.BERSERKER) != null) {
         damage.m_22127_(BerserkerSkill.BERSERKER);
      } else {
         damage.m_22125_(new AttributeModifier(BerserkerSkill.BERSERKER, "BerserkerAttack", BerserkerSkill.getAttack(EP) * 1.5D, Operation.ADDITION));
      }

      AttributeInstance speed = (AttributeInstance)Objects.requireNonNull(this.m_21051_(Attributes.f_22279_));
      if (speed.m_22111_(BerserkerSkill.BERSERKER) != null) {
         speed.m_22127_(BerserkerSkill.BERSERKER);
      } else {
         speed.m_22125_(new AttributeModifier(BerserkerSkill.BERSERKER, "BerserkerSpeed", BerserkerSkill.getSpeed(EP) / 200.0D, Operation.ADDITION));
      }

   }

   @Nullable
   private ManasSkillInstance getBerserker() {
      Optional<ManasSkillInstance> skill = SkillAPI.getSkillsFrom(this).getSkill((ManasSkill)UniqueSkills.BERSERKER.get());
      if (skill.isEmpty()) {
         return null;
      } else {
         return !((ManasSkillInstance)skill.get()).canInteractSkill(this) ? null : (ManasSkillInstance)skill.get();
      }
   }

   @Nullable
   private ManasSkillInstance getSurvivor() {
      Optional<ManasSkillInstance> skill = SkillAPI.getSkillsFrom(this).getSkill((ManasSkill)UniqueSkills.SURVIVOR.get());
      if (skill.isEmpty()) {
         return null;
      } else {
         return !((ManasSkillInstance)skill.get()).canInteractSkill(this) ? null : (ManasSkillInstance)skill.get();
      }
   }
}
