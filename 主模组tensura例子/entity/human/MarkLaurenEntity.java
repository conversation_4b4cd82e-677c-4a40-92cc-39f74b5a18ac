package com.github.manasmods.tensura.entity.human;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.tensura.ability.skill.unique.ThrowerSkill;
import com.github.manasmods.tensura.api.entity.ai.CrossbowAttackGoal;
import com.github.manasmods.tensura.api.entity.ai.WanderingFollowOwnerGoal;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.entity.template.HumanoidNPCEntity;
import com.github.manasmods.tensura.entity.template.TensuraTamableEntity;
import com.github.manasmods.tensura.registry.items.TensuraToolItems;
import com.github.manasmods.tensura.registry.skill.UniqueSkills;
import com.mojang.math.Vector3f;
import java.util.List;
import java.util.Optional;
import javax.annotation.Nullable;
import net.minecraft.ChatFormatting;
import net.minecraft.commands.arguments.EntityAnchorArgument.Anchor;
import net.minecraft.core.particles.BlockParticleOption;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.chat.Component;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.util.RandomSource;
import net.minecraft.world.DifficultyInstance;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.EquipmentSlot;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.Mob;
import net.minecraft.world.entity.MobSpawnType;
import net.minecraft.world.entity.SpawnGroupData;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.ai.attributes.AttributeSupplier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.goal.FloatGoal;
import net.minecraft.world.entity.ai.goal.LookAtPlayerGoal;
import net.minecraft.world.entity.ai.goal.RandomLookAroundGoal;
import net.minecraft.world.entity.ai.goal.RangedBowAttackGoal;
import net.minecraft.world.entity.ai.goal.SitWhenOrderedToGoal;
import net.minecraft.world.entity.ai.goal.WaterAvoidingRandomStrollGoal;
import net.minecraft.world.entity.ai.goal.target.NonTameRandomTargetGoal;
import net.minecraft.world.entity.ai.goal.target.ResetUniversalAngerTargetGoal;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.entity.projectile.AbstractArrow;
import net.minecraft.world.entity.projectile.Projectile;
import net.minecraft.world.entity.projectile.ThrowableItemProjectile;
import net.minecraft.world.entity.projectile.AbstractArrow.Pickup;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Items;
import net.minecraft.world.level.ItemLike;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.ServerLevelAccessor;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.common.ForgeMod;

public class MarkLaurenEntity extends OtherworlderEntity {
   public MarkLaurenEntity(EntityType<? extends MarkLaurenEntity> pEntityType, Level pLevel) {
      super(pEntityType, pLevel);
   }

   public static AttributeSupplier setAttributes() {
      return Mob.m_21552_().m_22268_(Attributes.f_22284_, 15.0D).m_22268_(Attributes.f_22281_, 10.0D).m_22268_(Attributes.f_22276_, 500.0D).m_22268_(Attributes.f_22279_, 0.20000000298023224D).m_22268_(Attributes.f_22277_, 32.0D).m_22268_((Attribute)ForgeMod.SWIM_SPEED.get(), 1.0D).m_22268_((Attribute)ForgeMod.ATTACK_RANGE.get(), 2.0D).m_22265_();
   }

   protected void m_8099_() {
      this.f_21345_.m_25352_(0, new FloatGoal(this));
      this.f_21345_.m_25352_(1, new SitWhenOrderedToGoal(this));
      this.f_21345_.m_25352_(2, new HumanoidNPCEntity.EatingItemGoal(this, (entity) -> {
         return this.shouldHeal();
      }, 3.0F));
      this.f_21345_.m_25352_(3, new CrossbowAttackGoal(this, 1.2D, 20.0F));
      this.f_21345_.m_25352_(3, new RangedBowAttackGoal(this, 1.0D, 20, 20.0F));
      this.f_21345_.m_25352_(3, new HumanoidNPCEntity.SpearTypeAttackGoal(this, 1.0D, 20, 20.0F));
      this.f_21345_.m_25352_(3, new MarkLaurenEntity.ThrowerAttackGoal(this));
      this.f_21345_.m_25352_(4, new WanderingFollowOwnerGoal(this, 1.5D, 10.0F, 5.0F, false));
      this.f_21345_.m_25352_(7, new WaterAvoidingRandomStrollGoal(this, 1.2D));
      this.f_21345_.m_25352_(8, new RandomLookAroundGoal(this));
      this.f_21345_.m_25352_(9, new LookAtPlayerGoal(this, Player.class, 6.0F));
      this.f_21346_.m_25352_(1, new TensuraTamableEntity.TensuraOwnerHurtByTargetGoal(this));
      this.f_21346_.m_25352_(2, new TensuraTamableEntity.TensuraOwnerHurtTargetGoal(this));
      this.f_21346_.m_25352_(3, (new TensuraTamableEntity.TensuraHurtByTargetGoal(this, new Class[]{MarkLaurenEntity.class})).m_26044_(new Class[0]));
      this.f_21346_.m_25352_(6, new NonTameRandomTargetGoal(this, Player.class, false, this::shouldAttackPlayer));
      this.f_21346_.m_25352_(5, new NonTameRandomTargetGoal(this, LivingEntity.class, false, (entity) -> {
         return entity.m_6095_().m_204039_(TensuraTags.EntityTypes.OTHERWORLDER_PREY);
      }));
      this.f_21346_.m_25352_(8, new ResetUniversalAngerTargetGoal(this, true));
   }

   public ResourceLocation getTextureLocation() {
      return new ResourceLocation("tensura", "textures/entity/otherworlder/mark_lauren.png");
   }

   public List<ManasSkill> getUniqueSkills() {
      return List.of((ManasSkill)UniqueSkills.THROWER.get());
   }

   public boolean m_7327_(Entity target) {
      boolean flag = super.m_7327_(target);
      if (flag && this.getThrower() != null) {
         TensuraParticleHelper.addServerParticlesAroundSelf(target, ParticleTypes.f_123796_, 1.0D);
         this.m_9236_().m_6263_((Player)null, this.m_20185_(), this.m_20186_(), this.m_20189_(), SoundEvents.f_12317_, SoundSource.NEUTRAL, 1.0F, 1.0F);
         Vec3 vec3 = new Vec3(target.m_20185_() - this.m_20185_(), target.m_20186_() - this.m_20186_(), target.m_20189_() - this.m_20189_());
         target.m_20256_(vec3.m_82541_().m_82490_(3.0D).m_82520_(0.0D, 0.5D, 0.0D));
         target.f_19812_ = true;
         target.f_19864_ = true;
      }

      return flag;
   }

   private void throwItem() {
      ItemStack thrownStack = this.getThrownItem();
      if (thrownStack != null) {
         if (this.m_5448_() != null) {
            this.m_7618_(Anchor.EYES, this.m_5448_().m_146892_());
         }

         Projectile projectile = ThrowerSkill.getProjectile(this.m_9236_(), this, thrownStack, this.getThrower());
         Vector3f vector3f = new Vector3f(this.m_20252_(1.0F));
         if (projectile instanceof AbstractArrow) {
            AbstractArrow arrow = (AbstractArrow)projectile;
            if (thrownStack.m_41783_() != null && thrownStack.m_41783_().m_128471_("FakeItem")) {
               arrow.f_36705_ = Pickup.CREATIVE_ONLY;
            } else {
               arrow.f_36705_ = Pickup.ALLOWED;
            }
         } else if (projectile instanceof ThrowableItemProjectile) {
            ThrowableItemProjectile itemProjectile = (ThrowableItemProjectile)projectile;
            itemProjectile.m_37446_(thrownStack);
         }

         projectile.m_6686_((double)vector3f.m_122239_(), (double)vector3f.m_122260_(), (double)vector3f.m_122269_(), 2.0F, 0.0F);
         this.m_9236_().m_7967_(projectile);
         this.m_21011_(InteractionHand.OFF_HAND, true);
         this.m_9236_().m_6263_((Player)null, this.m_20185_(), this.m_20186_(), this.m_20189_(), SoundEvents.f_11687_, SoundSource.NEUTRAL, 1.0F, 1.0F);
      }
   }

   @Nullable
   private ItemStack getThrownItem() {
      ItemStack offhandItem = this.m_21206_();
      if (offhandItem.m_41619_()) {
         BlockState state = this.m_9236_().m_8055_(this.m_20097_());
         ItemStack stack = new ItemStack((ItemLike)(state.m_204336_(TensuraTags.Blocks.SKILL_UNOBTAINABLE) ? Items.f_41852_ : state.m_60734_()));
         stack.m_41784_().m_128379_("FakeItem", true);
         this.m_8061_(EquipmentSlot.OFFHAND, stack);
         TensuraParticleHelper.addServerParticlesAroundPos(this.m_217043_(), this.m_9236_(), this.m_20182_().m_82520_(0.0D, 0.5D, 0.0D), new BlockParticleOption(ParticleTypes.f_123794_, state), 1.0D);
         this.f_19853_.m_6263_((Player)null, this.m_20185_(), this.m_20186_(), this.m_20189_(), state.m_60734_().m_49962_(state).m_56775_(), this.m_5720_(), 2.0F, 1.0F);
         return null;
      } else {
         ItemStack thrownItem = offhandItem.m_41777_();
         offhandItem.m_41774_(1);
         return thrownItem;
      }
   }

   @Nullable
   private ManasSkillInstance getThrower() {
      Optional<ManasSkillInstance> skill = SkillAPI.getSkillsFrom(this).getSkill((ManasSkill)UniqueSkills.THROWER.get());
      if (skill.isEmpty()) {
         return null;
      } else {
         return !((ManasSkillInstance)skill.get()).canInteractSkill(this) ? null : (ManasSkillInstance)skill.get();
      }
   }

   protected float m_21519_(EquipmentSlot pSlot) {
      if (this.m_21824_()) {
         return 0.0F;
      } else {
         return pSlot.equals(EquipmentSlot.MAINHAND) ? 0.05F : super.m_21519_(pSlot);
      }
   }

   @Nullable
   public SpawnGroupData m_6518_(ServerLevelAccessor pLevel, DifficultyInstance pDifficulty, MobSpawnType pReason, @Nullable SpawnGroupData pSpawnData, @Nullable CompoundTag pDataTag) {
      if (!pReason.equals(MobSpawnType.BUCKET)) {
         this.m_213945_(this.f_19796_, pDifficulty);
      }

      return super.m_6518_(pLevel, pDifficulty, pReason, pSpawnData, pDataTag);
   }

   protected void m_213945_(RandomSource pRandom, DifficultyInstance pDifficulty) {
      super.m_213945_(pRandom, pDifficulty);
      ItemStack stack = new ItemStack((ItemLike)TensuraToolItems.MITHRIL_AXE.get());
      stack.m_41714_(Component.m_237113_("Minos Bardiche").m_130940_(ChatFormatting.GOLD));
      this.m_8061_(EquipmentSlot.MAINHAND, stack);
      this.inventory.m_6836_(4, stack);
      this.inventory.m_6596_();
   }

   @Nullable
   public Item getEquipmentForArmorSlot(EquipmentSlot pSlot, int pChance) {
      Item var10000;
      switch(pSlot) {
      case HEAD:
         var10000 = pChance == 3 ? Items.f_42468_ : null;
         break;
      case CHEST:
         var10000 = pChance == 3 ? Items.f_42469_ : (pChance == 1 ? Items.f_42465_ : null);
         break;
      case LEGS:
         var10000 = pChance == 3 ? Items.f_42470_ : (pChance == 1 ? Items.f_42466_ : null);
         break;
      case FEET:
         var10000 = pChance == 3 ? Items.f_42471_ : (pChance == 1 ? Items.f_42467_ : null);
         break;
      default:
         var10000 = null;
      }

      return var10000;
   }

   public class ThrowerAttackGoal extends HumanoidNPCEntity.NPCMeleeAttackGoal {
      public final MarkLaurenEntity entity;

      public ThrowerAttackGoal(MarkLaurenEntity entity) {
         super(entity, 2.0D, true);
         this.entity = entity;
      }

      protected void m_6739_(LivingEntity pEnemy, double pDistToEnemySqr) {
         double d0 = this.m_6639_(pEnemy);
         int randomAttack = this.randomAttack(pEnemy, pDistToEnemySqr);
         if (randomAttack != 0) {
            if (pDistToEnemySqr <= (randomAttack == 1 ? d0 : d0 + 900.0D) && this.m_25564_()) {
               this.m_25563_();
               if (randomAttack == 1) {
                  this.entity.m_7327_(pEnemy);
               } else {
                  this.entity.throwItem();
               }
            }

         }
      }

      protected int randomAttack(LivingEntity target, double distSqr) {
         if (this.entity.getThrower() == null) {
            return 1;
         } else {
            return !(distSqr > this.m_6639_(target)) && this.entity.f_19796_.m_188503_(4) != 2 ? 1 : 2;
         }
      }
   }
}
