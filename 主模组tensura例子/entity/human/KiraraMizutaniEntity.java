package com.github.manasmods.tensura.entity.human;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.skill.intrinsic.CharmSkill;
import com.github.manasmods.tensura.api.entity.ai.CrossbowAttackGoal;
import com.github.manasmods.tensura.api.entity.ai.WanderingFollowOwnerGoal;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.entity.magic.TensuraProjectile;
import com.github.manasmods.tensura.entity.magic.projectile.FireBoltProjectile;
import com.github.manasmods.tensura.entity.magic.projectile.IceLanceProjectile;
import com.github.manasmods.tensura.entity.magic.projectile.StoneShotProjectile;
import com.github.manasmods.tensura.entity.magic.projectile.WaterBallProjectile;
import com.github.manasmods.tensura.entity.magic.projectile.WindBladeProjectile;
import com.github.manasmods.tensura.entity.template.HumanoidNPCEntity;
import com.github.manasmods.tensura.entity.template.TensuraTamableEntity;
import com.github.manasmods.tensura.race.RaceHelper;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.skill.ResistanceSkills;
import com.github.manasmods.tensura.registry.skill.UniqueSkills;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import java.util.Iterator;
import java.util.List;
import java.util.Optional;
import javax.annotation.Nullable;
import net.minecraft.ChatFormatting;
import net.minecraft.commands.arguments.EntityAnchorArgument.Anchor;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.chat.Component;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.util.RandomSource;
import net.minecraft.world.DifficultyInstance;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.EquipmentSlot;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.Mob;
import net.minecraft.world.entity.MobSpawnType;
import net.minecraft.world.entity.NeutralMob;
import net.minecraft.world.entity.SpawnGroupData;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.ai.attributes.AttributeSupplier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.goal.FloatGoal;
import net.minecraft.world.entity.ai.goal.LookAtPlayerGoal;
import net.minecraft.world.entity.ai.goal.RandomLookAroundGoal;
import net.minecraft.world.entity.ai.goal.RangedBowAttackGoal;
import net.minecraft.world.entity.ai.goal.SitWhenOrderedToGoal;
import net.minecraft.world.entity.ai.goal.WaterAvoidingRandomStrollGoal;
import net.minecraft.world.entity.ai.goal.target.NonTameRandomTargetGoal;
import net.minecraft.world.entity.ai.goal.target.ResetUniversalAngerTargetGoal;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Items;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.ServerLevelAccessor;
import net.minecraftforge.common.ForgeMod;

public class KiraraMizutaniEntity extends OtherworlderEntity {
   public KiraraMizutaniEntity(EntityType<? extends KiraraMizutaniEntity> pEntityType, Level pLevel) {
      super(pEntityType, pLevel);
      this.f_21364_ = 100;
      this.f_19793_ = 1.0F;
   }

   public static AttributeSupplier setAttributes() {
      return Mob.m_21552_().m_22268_(Attributes.f_22284_, 0.0D).m_22268_(Attributes.f_22281_, 10.0D).m_22268_(Attributes.f_22276_, 150.0D).m_22268_(Attributes.f_22279_, 0.25D).m_22268_(Attributes.f_22277_, 32.0D).m_22268_((Attribute)ForgeMod.SWIM_SPEED.get(), 1.0D).m_22268_((Attribute)ForgeMod.ATTACK_RANGE.get(), 2.0D).m_22265_();
   }

   protected void m_8099_() {
      this.f_21345_.m_25352_(0, new FloatGoal(this));
      this.f_21345_.m_25352_(1, new SitWhenOrderedToGoal(this));
      this.f_21345_.m_25352_(2, new HumanoidNPCEntity.EatingItemGoal(this, (entity) -> {
         return this.shouldHeal();
      }, 3.0F));
      this.f_21345_.m_25352_(3, new CrossbowAttackGoal(this, 1.2D, 20.0F));
      this.f_21345_.m_25352_(3, new RangedBowAttackGoal(this, 1.0D, 20, 20.0F));
      this.f_21345_.m_25352_(3, new HumanoidNPCEntity.SpearTypeAttackGoal(this, 1.0D, 20, 20.0F));
      this.f_21345_.m_25352_(3, new KiraraMizutaniEntity.BewilderAttackGoal(this));
      this.f_21345_.m_25352_(4, new WanderingFollowOwnerGoal(this, 1.5D, 10.0F, 5.0F, false));
      this.f_21345_.m_25352_(7, new WaterAvoidingRandomStrollGoal(this, 1.2D));
      this.f_21345_.m_25352_(8, new RandomLookAroundGoal(this));
      this.f_21345_.m_25352_(9, new LookAtPlayerGoal(this, Player.class, 6.0F));
      this.f_21346_.m_25352_(1, new TensuraTamableEntity.TensuraOwnerHurtByTargetGoal(this));
      this.f_21346_.m_25352_(2, new TensuraTamableEntity.TensuraOwnerHurtTargetGoal(this));
      this.f_21346_.m_25352_(3, (new TensuraTamableEntity.TensuraHurtByTargetGoal(this, new Class[]{KiraraMizutaniEntity.class})).m_26044_(new Class[0]));
      this.f_21346_.m_25352_(6, new NonTameRandomTargetGoal(this, Player.class, false, this::shouldAttackPlayer));
      this.f_21346_.m_25352_(5, new NonTameRandomTargetGoal(this, LivingEntity.class, false, (entity) -> {
         return entity.m_6095_().m_204039_(TensuraTags.EntityTypes.OTHERWORLDER_PREY);
      }));
      this.f_21346_.m_25352_(8, new ResetUniversalAngerTargetGoal(this, true));
   }

   public ResourceLocation getTextureLocation() {
      return new ResourceLocation("tensura", "textures/entity/otherworlder/kirara_mizutani.png");
   }

   public List<ManasSkill> getUniqueSkills() {
      return List.of((ManasSkill)UniqueSkills.BEWILDER.get());
   }

   private void shootMagicProjectile() {
      if (this.m_5448_() != null) {
         this.m_7618_(Anchor.EYES, this.m_5448_().m_146892_());
      }

      int randomProjectile = this.m_217043_().m_188503_(5);
      Object var10000;
      switch(randomProjectile) {
      case 1:
         var10000 = new StoneShotProjectile(this.m_9236_(), this);
         break;
      case 2:
         var10000 = new IceLanceProjectile(this.m_9236_(), this);
         break;
      case 3:
         var10000 = new WaterBallProjectile(this.m_9236_(), this);
         break;
      case 4:
         var10000 = new WindBladeProjectile(this.m_9236_(), this);
         break;
      default:
         var10000 = new FireBoltProjectile(this.m_9236_(), this);
      }

      TensuraProjectile projectile = var10000;
      ((TensuraProjectile)projectile).setSpeed(1.5F);
      ((TensuraProjectile)projectile).setDamage(20.0F);
      if (randomProjectile >= 2) {
         ((TensuraProjectile)projectile).setBurnTicks(-1);
      } else if (randomProjectile == 0) {
         ((TensuraProjectile)projectile).setBurnTicks(10);
      } else {
         ((TensuraProjectile)projectile).setKnockForce(1.0F);
      }

      ((TensuraProjectile)projectile).m_20242_(true);
      ((TensuraProjectile)projectile).setPosAndShoot(this);
      this.m_9236_().m_7967_((Entity)projectile);
      this.m_9236_().m_6263_((Player)null, this.m_20185_(), this.m_20186_(), this.m_20189_(), SoundEvents.f_11705_, SoundSource.PLAYERS, 1.0F, 1.0F);
   }

   private void mindControlSurrounding() {
      LivingEntity livingTarget = this.m_5448_();
      if (livingTarget != null) {
         this.f_19853_.m_6263_((Player)null, this.m_20185_(), this.m_20186_(), this.m_20189_(), SoundEvents.f_11733_, SoundSource.PLAYERS, 1.0F, 1.0F);
         List<Mob> list = this.f_19853_.m_6443_(Mob.class, this.m_20191_().m_82400_(20.0D), (mob) -> {
            return !mob.m_7306_(this) && mob.m_6084_();
         });
         if (!list.isEmpty()) {
            Iterator var3 = list.iterator();

            while(var3.hasNext()) {
               Mob target = (Mob)var3.next();
               if (CharmSkill.canMindControl(target, this.f_19853_, true) && !target.m_21023_((MobEffect)TensuraMobEffects.RAMPAGE.get()) && !RaceHelper.isSpiritualLifeForm(target) && !SkillUtils.isSkillToggled(target, (ManasSkill)ResistanceSkills.SPIRITUAL_ATTACK_NULLIFICATION.get())) {
                  target.m_21561_(true);
                  target.m_6710_(livingTarget);
                  if (target instanceof NeutralMob) {
                     NeutralMob neutralMob = (NeutralMob)target;
                     neutralMob.m_6710_(livingTarget);
                  }

                  TensuraParticleHelper.addServerParticlesAroundSelf(target, ParticleTypes.f_123798_);
               }
            }

         }
      }
   }

   private void instantKill() {
      LivingEntity target = this.m_5448_();
      if (target != null) {
         this.f_19853_.m_6263_((Player)null, this.m_20185_(), this.m_20186_(), this.m_20189_(), SoundEvents.f_11733_, SoundSource.PLAYERS, 1.0F, 1.0F);
         if (CharmSkill.canMindControl(target, this.f_19853_, true)) {
            if (!target.m_21023_((MobEffect)TensuraMobEffects.RAMPAGE.get())) {
               if (!RaceHelper.isSpiritualLifeForm(target)) {
                  if (!SkillUtils.isSkillToggled(target, (ManasSkill)ResistanceSkills.SPIRITUAL_ATTACK_NULLIFICATION.get())) {
                     if (!(TensuraEPCapability.getEP(this) < TensuraEPCapability.getEP(target))) {
                        float damage = target.m_21223_();
                        if (SkillUtils.isSkillToggled(target, (ManasSkill)ResistanceSkills.SPIRITUAL_ATTACK_RESISTANCE.get())) {
                           damage /= 2.0F;
                        }

                        if (target.m_6469_(TensuraDamageSources.selfKill(this), damage)) {
                           TensuraParticleHelper.addServerParticlesAroundSelf(target, ParticleTypes.f_123808_);
                           TensuraParticleHelper.addServerParticlesAroundSelf(target, ParticleTypes.f_123792_);
                        }

                     }
                  }
               }
            }
         }
      }
   }

   @Nullable
   private ManasSkillInstance getBewilder() {
      Optional<ManasSkillInstance> skill = SkillAPI.getSkillsFrom(this).getSkill((ManasSkill)UniqueSkills.BEWILDER.get());
      if (skill.isEmpty()) {
         return null;
      } else {
         return !((ManasSkillInstance)skill.get()).canInteractSkill(this) ? null : (ManasSkillInstance)skill.get();
      }
   }

   @Nullable
   public SpawnGroupData m_6518_(ServerLevelAccessor pLevel, DifficultyInstance pDifficulty, MobSpawnType pReason, @Nullable SpawnGroupData pSpawnData, @Nullable CompoundTag pDataTag) {
      if (!pReason.equals(MobSpawnType.BUCKET)) {
         this.m_213945_(this.f_19796_, pDifficulty);
      }

      return super.m_6518_(pLevel, pDifficulty, pReason, pSpawnData, pDataTag);
   }

   protected void m_213945_(RandomSource pRandom, DifficultyInstance pDifficulty) {
      super.m_213945_(pRandom, pDifficulty);
      ItemStack stack = new ItemStack(Items.f_42398_);
      stack.m_41714_(Component.m_237113_("Staff").m_130940_(ChatFormatting.GOLD));
      this.m_8061_(EquipmentSlot.MAINHAND, stack);
      this.inventory.m_6836_(4, stack);
      this.inventory.m_6596_();
   }

   @Nullable
   public Item getEquipmentForArmorSlot(EquipmentSlot pSlot, int pChance) {
      return null;
   }

   public class BewilderAttackGoal extends HumanoidNPCEntity.NPCMeleeAttackGoal {
      public final KiraraMizutaniEntity entity;

      public BewilderAttackGoal(KiraraMizutaniEntity entity) {
         super(entity, 1.5D, true);
         this.entity = entity;
      }

      protected void m_6739_(LivingEntity pEnemy, double pDistToEnemySqr) {
         double d0 = this.m_6639_(pEnemy);
         int randomAttack = this.randomAttack(pDistToEnemySqr);
         if (randomAttack != 0) {
            if (pDistToEnemySqr <= (randomAttack == 4 ? d0 : d0 + 900.0D) && this.m_25564_()) {
               this.m_25563_();
               switch(randomAttack) {
               case 2:
                  this.entity.mindControlSurrounding();
                  break;
               case 3:
                  this.entity.instantKill();
                  break;
               case 4:
                  this.entity.m_7327_(pEnemy);
                  break;
               default:
                  this.entity.shootMagicProjectile();
               }

               this.entity.m_21011_(InteractionHand.MAIN_HAND, true);
            }

         }
      }

      protected int randomAttack(double distSqr) {
         boolean hasBewilder = this.entity.getBewilder() != null;
         if (this.entity.f_19796_.m_188503_(7) == 1 && distSqr <= 400.0D && hasBewilder) {
            return 3;
         } else if (this.entity.f_19796_.m_188503_(4) == 1 && hasBewilder) {
            return 2;
         } else {
            return this.entity.m_21205_().m_150930_(Items.f_42398_) ? 1 : 4;
         }
      }
   }
}
