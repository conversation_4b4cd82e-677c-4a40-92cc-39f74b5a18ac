package com.github.manasmods.tensura.entity.human;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.tensura.api.entity.ai.CrossbowAttackGoal;
import com.github.manasmods.tensura.api.entity.ai.WanderingFollowOwnerGoal;
import com.github.manasmods.tensura.api.entity.subclass.ITeleportation;
import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.entity.magic.projectile.SpatialArrowProjectile;
import com.github.manasmods.tensura.entity.template.HumanoidNPCEntity;
import com.github.manasmods.tensura.entity.template.TensuraTamableEntity;
import com.github.manasmods.tensura.registry.items.TensuraToolItems;
import com.github.manasmods.tensura.registry.skill.UniqueSkills;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import java.util.List;
import java.util.Optional;
import javax.annotation.Nullable;
import net.minecraft.ChatFormatting;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.chat.Component;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.util.RandomSource;
import net.minecraft.world.DifficultyInstance;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.EquipmentSlot;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.Mob;
import net.minecraft.world.entity.MobSpawnType;
import net.minecraft.world.entity.SpawnGroupData;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.ai.attributes.AttributeSupplier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.goal.FloatGoal;
import net.minecraft.world.entity.ai.goal.LookAtPlayerGoal;
import net.minecraft.world.entity.ai.goal.RandomLookAroundGoal;
import net.minecraft.world.entity.ai.goal.RangedBowAttackGoal;
import net.minecraft.world.entity.ai.goal.SitWhenOrderedToGoal;
import net.minecraft.world.entity.ai.goal.WaterAvoidingRandomStrollGoal;
import net.minecraft.world.entity.ai.goal.target.NonTameRandomTargetGoal;
import net.minecraft.world.entity.ai.goal.target.ResetUniversalAngerTargetGoal;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.BowItem;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.enchantment.Enchantments;
import net.minecraft.world.level.ItemLike;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.ServerLevelAccessor;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.common.ForgeMod;
import net.minecraftforge.event.entity.ProjectileImpactEvent;

public class MaiFurukiEntity extends OtherworlderEntity implements ITeleportation {
   public MaiFurukiEntity(EntityType<? extends MaiFurukiEntity> pEntityType, Level pLevel) {
      super(pEntityType, pLevel);
   }

   public static AttributeSupplier setAttributes() {
      return Mob.m_21552_().m_22268_(Attributes.f_22284_, 10.0D).m_22268_(Attributes.f_22281_, 30.0D).m_22268_(Attributes.f_22282_, 2.0D).m_22268_(Attributes.f_22276_, 300.0D).m_22268_(Attributes.f_22279_, 0.30000001192092896D).m_22268_(Attributes.f_22277_, 32.0D).m_22268_(Attributes.f_22278_, 0.5D).m_22268_((Attribute)ForgeMod.SWIM_SPEED.get(), 1.0D).m_22268_((Attribute)ForgeMod.ATTACK_RANGE.get(), 2.0D).m_22265_();
   }

   protected void m_8099_() {
      this.f_21345_.m_25352_(0, new FloatGoal(this));
      this.f_21345_.m_25352_(1, new SitWhenOrderedToGoal(this));
      this.f_21345_.m_25352_(2, new HumanoidNPCEntity.EatingItemGoal(this, (entity) -> {
         return this.shouldHeal();
      }, 3.0F));
      this.f_21345_.m_25352_(3, new CrossbowAttackGoal(this, 1.2D, 20.0F));
      this.f_21345_.m_25352_(3, new RangedBowAttackGoal(this, 1.0D, 20, 20.0F));
      this.f_21345_.m_25352_(3, new HumanoidNPCEntity.SpearTypeAttackGoal(this, 1.0D, 20, 20.0F));
      this.f_21345_.m_25352_(3, new HumanoidNPCEntity.NPCMeleeAttackGoal(this, 1.5D, true));
      this.f_21345_.m_25352_(4, new WanderingFollowOwnerGoal(this, 1.5D, 10.0F, 5.0F, false));
      this.f_21345_.m_25352_(7, new WaterAvoidingRandomStrollGoal(this, 1.2D));
      this.f_21345_.m_25352_(8, new RandomLookAroundGoal(this));
      this.f_21345_.m_25352_(9, new LookAtPlayerGoal(this, Player.class, 6.0F));
      this.f_21346_.m_25352_(1, new TensuraTamableEntity.TensuraOwnerHurtByTargetGoal(this));
      this.f_21346_.m_25352_(2, new TensuraTamableEntity.TensuraOwnerHurtTargetGoal(this));
      this.f_21346_.m_25352_(3, (new TensuraTamableEntity.TensuraHurtByTargetGoal(this, new Class[]{MaiFurukiEntity.class})).m_26044_(new Class[0]));
      this.f_21346_.m_25352_(5, new NonTameRandomTargetGoal(this, Player.class, false, this::shouldAttackPlayer));
      this.f_21346_.m_25352_(6, new NonTameRandomTargetGoal(this, LivingEntity.class, false, (entity) -> {
         return entity.m_6095_().m_204039_(TensuraTags.EntityTypes.OTHERWORLDER_PREY);
      }));
      this.f_21346_.m_25352_(8, new ResetUniversalAngerTargetGoal(this, true));
   }

   public ResourceLocation getTextureLocation() {
      return new ResourceLocation("tensura", "textures/entity/otherworlder/mai_furuki.png");
   }

   public List<ManasSkill> getUniqueSkills() {
      return List.of((ManasSkill)UniqueSkills.TRAVELER.get());
   }

   public void m_8119_() {
      super.m_8119_();
      LivingEntity target = this.m_5448_();
      if (target != null) {
         if (this.m_6084_()) {
            if (this.f_19797_ % 20 == 0) {
               if (target.m_20270_(this) <= 5.0F && this.getTraveler() != null) {
                  this.teleportTowards(this, target, 12.0D);
               } else if (target.m_20270_(this) >= 20.0F && this.getTraveler() != null) {
                  this.teleportTowards(this, target, 20.0D);
               }

            }
         }
      }
   }

   public boolean m_6469_(DamageSource source, float pAmount) {
      if (this.m_6673_(source)) {
         return false;
      } else {
         return this.shouldDodge(source) ? false : super.m_6469_(source, pAmount);
      }
   }

   public void onProjectileImpact(ProjectileImpactEvent event) {
      if (!event.isCanceled()) {
         if (this.getTraveler() != null) {
            this.m_9236_().m_6263_((Player)null, this.m_20185_(), this.m_20186_(), this.m_20189_(), SoundEvents.f_11852_, SoundSource.PLAYERS, 2.0F, 1.0F);
            if (!this.m_9236_().m_5776_() && this.m_5448_() != null) {
               this.teleportTowards(this, this.m_5448_(), 16.0D);
            }

            event.setCanceled(true);
         }
      }
   }

   private boolean shouldDodge(DamageSource source) {
      if (this.m_9236_().m_5776_()) {
         return false;
      } else if (this.getTraveler() == null) {
         return false;
      } else {
         Entity var3 = source.m_7640_();
         if (var3 instanceof LivingEntity) {
            LivingEntity entity = (LivingEntity)var3;
            if (source.m_19378_()) {
               return false;
            } else if (!DamageSourceHelper.isPhysicalAttack(source)) {
               return false;
            } else if ((double)entity.m_217043_().m_188501_() >= 0.25D) {
               return false;
            } else {
               this.m_9236_().m_6263_((Player)null, this.m_20185_(), this.m_20186_(), this.m_20189_(), SoundEvents.f_11852_, SoundSource.PLAYERS, 2.0F, 1.0F);
               if (!this.m_9236_().m_5776_() && this.m_5448_() != null) {
                  this.teleportTowards(this, this.m_5448_(), 12.0D);
               }

               return true;
            }
         } else {
            return false;
         }
      }
   }

   protected void performBowAttack(ItemStack weapon, BowItem bow, LivingEntity pTarget, float distance) {
      if (this.getTraveler() == null) {
         super.performBowAttack(weapon, bow, pTarget, distance);
      } else if ((double)this.f_19796_.m_188501_() <= 0.3D) {
         this.stardustRain(pTarget);
      } else {
         SpatialArrowProjectile arrow = new SpatialArrowProjectile(this.m_9236_(), this);
         arrow.setLife(50);
         arrow.setDamage((float)this.m_21133_(Attributes.f_22281_));
         arrow.setMpCost(150.0D);
         arrow.setSkill(this.getTraveler());
         if (this.m_21223_() < this.m_21233_() / 2.0F) {
            arrow.shootFromBehind(pTarget, 2.0F, 0.0F);
         } else {
            arrow.m_6034_(this.m_20185_(), this.m_20188_() - 0.1D, this.m_20189_());
            double d0 = pTarget.m_20185_() - this.m_20185_();
            double d1 = pTarget.m_20227_(0.3333333333333333D) - arrow.m_20186_();
            double d2 = pTarget.m_20189_() - this.m_20189_();
            double d3 = Math.sqrt(d0 * d0 + d2 * d2);
            arrow.m_6686_(d0, d1 + d3 * 0.20000000298023224D, d2, 1.6F, 0.0F);
         }

         this.m_5496_(SoundEvents.f_11687_, 1.0F, 1.0F / (this.m_217043_().m_188501_() * 0.4F + 0.8F));
         this.f_19853_.m_7967_(arrow);
      }
   }

   protected void stardustRain(LivingEntity target) {
      Vec3 pos = target.m_20182_().m_82520_(0.0D, (double)target.m_20192_(), 0.0D);
      int arrowAmount = 12;

      for(int i = 0; i < arrowAmount; ++i) {
         Vec3 arrowPos = pos.m_82549_((new Vec3(0.0D, Math.random() - 0.5D, 0.6D)).m_82541_().m_82490_((double)(target.m_20205_() + 6.0F)).m_82524_(360.0F * (float)i * 0.017453292F / (float)arrowAmount));
         SpatialArrowProjectile arrow = new SpatialArrowProjectile(this.f_19853_, this);
         arrow.setSpeed(1.0F);
         arrow.m_146884_(arrowPos);
         arrow.shootFromRot(pos.m_82546_(arrowPos).m_82541_());
         arrow.setSkill(this.getTraveler());
         arrow.setLife(50);
         arrow.setDamage((float)this.m_21133_(Attributes.f_22281_));
         arrow.setMpCost((double)(5000.0F / (float)arrowAmount));
         this.f_19853_.m_7967_(arrow);
         this.f_19853_.m_6263_((Player)null, arrow.m_20185_(), arrow.m_20186_(), arrow.m_20189_(), SoundEvents.f_11687_, SoundSource.PLAYERS, 1.0F, 1.0F);
      }

   }

   @Nullable
   private ManasSkillInstance getTraveler() {
      Optional<ManasSkillInstance> skill = SkillAPI.getSkillsFrom(this).getSkill((ManasSkill)UniqueSkills.TRAVELER.get());
      if (skill.isEmpty()) {
         return null;
      } else {
         return !((ManasSkillInstance)skill.get()).canInteractSkill(this) ? null : (ManasSkillInstance)skill.get();
      }
   }

   protected float m_21519_(EquipmentSlot pSlot) {
      if (this.m_21824_()) {
         return 0.0F;
      } else {
         return pSlot.equals(EquipmentSlot.MAINHAND) ? 0.05F : super.m_21519_(pSlot);
      }
   }

   @Nullable
   public SpawnGroupData m_6518_(ServerLevelAccessor pLevel, DifficultyInstance pDifficulty, MobSpawnType pReason, @Nullable SpawnGroupData pSpawnData, @Nullable CompoundTag pDataTag) {
      if (!pReason.equals(MobSpawnType.BUCKET)) {
         this.m_213945_(this.f_19796_, pDifficulty);
      }

      return super.m_6518_(pLevel, pDifficulty, pReason, pSpawnData, pDataTag);
   }

   protected void m_213945_(RandomSource pRandom, DifficultyInstance pDifficulty) {
      ItemStack stack = new ItemStack((ItemLike)TensuraToolItems.LONG_SPIDER_BOW.get());
      stack.m_41714_(Component.m_237113_("Crescent Bow").m_130940_(ChatFormatting.GOLD));
      stack.m_41663_(Enchantments.f_44952_, 1);
      stack.m_41663_(Enchantments.f_44962_, 1);
      this.m_8061_(EquipmentSlot.MAINHAND, stack);
      this.inventory.m_6836_(4, stack);
      this.inventory.m_6596_();
   }
}
