package com.github.manasmods.tensura.entity.human;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.api.entity.ai.CrossbowAttackGoal;
import com.github.manasmods.tensura.api.entity.ai.WanderingFollowOwnerGoal;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.entity.projectile.KunaiProjectile;
import com.github.manasmods.tensura.entity.template.HumanoidNPCEntity;
import com.github.manasmods.tensura.entity.template.TensuraTamableEntity;
import com.github.manasmods.tensura.item.templates.custom.SimpleSpearItem;
import com.github.manasmods.tensura.registry.items.TensuraToolItems;
import com.github.manasmods.tensura.registry.skill.UniqueSkills;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import com.mojang.math.Vector3f;
import java.util.List;
import java.util.Optional;
import javax.annotation.Nullable;
import net.minecraft.commands.arguments.EntityAnchorArgument.Anchor;
import net.minecraft.core.Direction;
import net.minecraft.core.BlockPos.MutableBlockPos;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.util.RandomSource;
import net.minecraft.world.DifficultyInstance;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.EquipmentSlot;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.Mob;
import net.minecraft.world.entity.MobSpawnType;
import net.minecraft.world.entity.SpawnGroupData;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.ai.attributes.AttributeSupplier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.goal.FloatGoal;
import net.minecraft.world.entity.ai.goal.LookAtPlayerGoal;
import net.minecraft.world.entity.ai.goal.RandomLookAroundGoal;
import net.minecraft.world.entity.ai.goal.RangedBowAttackGoal;
import net.minecraft.world.entity.ai.goal.SitWhenOrderedToGoal;
import net.minecraft.world.entity.ai.goal.WaterAvoidingRandomStrollGoal;
import net.minecraft.world.entity.ai.goal.target.NonTameRandomTargetGoal;
import net.minecraft.world.entity.ai.goal.target.ResetUniversalAngerTargetGoal;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.entity.projectile.AbstractArrow.Pickup;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.TridentItem;
import net.minecraft.world.level.ItemLike;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.ServerLevelAccessor;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.common.ForgeMod;
import net.minecraftforge.event.entity.ProjectileImpactEvent;

public class ShinRyuseiEntity extends OtherworlderEntity {
   public ShinRyuseiEntity(EntityType<? extends ShinRyuseiEntity> pEntityType, Level pLevel) {
      super(pEntityType, pLevel);
   }

   public static AttributeSupplier setAttributes() {
      return Mob.m_21552_().m_22268_(Attributes.f_22284_, 5.0D).m_22268_(Attributes.f_22281_, 20.0D).m_22268_(Attributes.f_22282_, 1.0D).m_22268_(Attributes.f_22276_, 400.0D).m_22268_(Attributes.f_22279_, 0.25D).m_22268_(Attributes.f_22277_, 32.0D).m_22268_(Attributes.f_22278_, 0.5D).m_22268_((Attribute)ForgeMod.SWIM_SPEED.get(), 1.0D).m_22268_((Attribute)ForgeMod.ATTACK_RANGE.get(), 2.0D).m_22265_();
   }

   protected void m_8099_() {
      this.f_21345_.m_25352_(0, new FloatGoal(this));
      this.f_21345_.m_25352_(1, new SitWhenOrderedToGoal(this));
      this.f_21345_.m_25352_(2, new HumanoidNPCEntity.EatingItemGoal(this, (entity) -> {
         return this.shouldHeal();
      }, 3.0F));
      this.f_21345_.m_25352_(3, new CrossbowAttackGoal(this, 1.2D, 20.0F));
      this.f_21345_.m_25352_(3, new RangedBowAttackGoal(this, 1.0D, 20, 20.0F));
      this.f_21345_.m_25352_(3, new HumanoidNPCEntity.SpearTypeAttackGoal(this, 1.0D, 20, 20.0F));
      this.f_21345_.m_25352_(3, new ShinRyuseiEntity.KunaiThrowAttackGoal(this));
      this.f_21345_.m_25352_(4, new WanderingFollowOwnerGoal(this, 1.5D, 10.0F, 5.0F, false));
      this.f_21345_.m_25352_(7, new WaterAvoidingRandomStrollGoal(this, 1.2D));
      this.f_21345_.m_25352_(8, new RandomLookAroundGoal(this));
      this.f_21345_.m_25352_(9, new LookAtPlayerGoal(this, Player.class, 6.0F));
      this.f_21346_.m_25352_(1, new TensuraTamableEntity.TensuraOwnerHurtByTargetGoal(this));
      this.f_21346_.m_25352_(2, new TensuraTamableEntity.TensuraOwnerHurtTargetGoal(this));
      this.f_21346_.m_25352_(3, (new TensuraTamableEntity.TensuraHurtByTargetGoal(this, new Class[]{ShinRyuseiEntity.class})).m_26044_(new Class[0]));
      this.f_21346_.m_25352_(5, new NonTameRandomTargetGoal(this, Player.class, false, this::shouldAttackPlayer));
      this.f_21346_.m_25352_(6, new NonTameRandomTargetGoal(this, LivingEntity.class, false, (entity) -> {
         return entity.m_6095_().m_204039_(TensuraTags.EntityTypes.OTHERWORLDER_PREY);
      }));
      this.f_21346_.m_25352_(8, new ResetUniversalAngerTargetGoal(this, true));
   }

   public ResourceLocation getTextureLocation() {
      return new ResourceLocation("tensura", "textures/entity/otherworlder/shin_ryusei.png");
   }

   public List<ManasSkill> getUniqueSkills() {
      return List.of((ManasSkill)UniqueSkills.OBSERVER.get());
   }

   public void m_8119_() {
      super.m_8119_();
      LivingEntity target = this.m_5448_();
      if (target != null) {
         if (this.m_6084_()) {
            if (this.f_19797_ % 100 == 0) {
               if (target.m_20270_(this) <= 7.0F) {
                  this.teleportTowards(target, 7.0D);
               } else if (target.m_20270_(this) >= 20.0F) {
                  this.teleportTowards(target, 12.0D);
               }
            }

         }
      }
   }

   public boolean m_6469_(DamageSource source, float pAmount) {
      if (this.m_6673_(source)) {
         return false;
      } else {
         this.dodge(source);
         return super.m_6469_(source, pAmount);
      }
   }

   public void onProjectileImpact(ProjectileImpactEvent event) {
      if (!this.m_9236_().m_5776_()) {
         if (!event.isCanceled()) {
            if (this.getObserver() != null) {
               this.m_146884_(this.m_20182_().m_82520_((double)(this.m_217043_().m_188501_() * 2.0F - 1.0F), 0.0D, (double)(this.m_217043_().m_188501_() * 2.0F - 1.0F)));
            }
         }
      }
   }

   private void dodge(DamageSource source) {
      if (this.getObserver() != null) {
         if (!this.m_9236_().m_5776_()) {
            if (source.m_7640_() instanceof LivingEntity) {
               if (source.m_19378_()) {
                  return;
               }

               if (!DamageSourceHelper.isPhysicalAttack(source)) {
                  return;
               }

               if (SkillUtils.canNegateDodge(this, source)) {
                  return;
               }

               this.m_146884_(this.m_20182_().m_82520_((double)this.m_217043_().m_188501_() - 0.5D, 0.0D, (double)this.m_217043_().m_188501_() - 0.5D));
            }

         }
      }
   }

   private void throwKunai() {
      if (this.m_5448_() != null) {
         this.m_7618_(Anchor.EYES, this.m_5448_().m_146892_());
      }

      KunaiProjectile kunai = new KunaiProjectile(this.m_9236_(), this, this.m_21206_(), true);
      Vector3f vector3f = new Vector3f(this.m_20252_(1.0F));
      kunai.m_36781_(14.0D);
      kunai.f_36705_ = Pickup.CREATIVE_ONLY;
      kunai.setMultishot(true);
      kunai.m_6686_((double)vector3f.m_122239_(), (double)vector3f.m_122260_(), (double)vector3f.m_122269_(), 4.0F, 0.0F);
      this.m_9236_().m_7967_(kunai);
      this.m_21011_(InteractionHand.OFF_HAND, true);
      this.m_9236_().m_6263_((Player)null, this.m_20185_(), this.m_20186_(), this.m_20189_(), SoundEvents.f_11687_, SoundSource.NEUTRAL, 1.0F, 1.0F);
   }

   private void teleportTowards(Entity pTarget, double distance) {
      Vec3 vec3 = (new Vec3(this.m_20185_() - pTarget.m_20185_(), this.m_20227_(0.5D) - pTarget.m_20188_(), this.m_20189_() - pTarget.m_20189_())).m_82541_();
      double d1 = this.m_20185_() + (this.f_19796_.m_188500_() - 0.5D) * 8.0D - vec3.f_82479_ * distance;
      double d2 = this.m_20186_() + (double)(this.f_19796_.m_188503_(16) - 8) - vec3.f_82480_ * distance;
      double d3 = this.m_20189_() + (this.f_19796_.m_188500_() - 0.5D) * 8.0D - vec3.f_82481_ * distance;
      this.teleport(d1, d2, d3);
      this.throwKunai();
   }

   private void teleport(double pX, double pY, double pZ) {
      Vec3 oldPosition = new Vec3(this.f_19854_, this.f_19855_ + (double)(this.m_20206_() / 2.0F), this.f_19856_);
      MutableBlockPos pos = new MutableBlockPos(pX, pY, pZ);

      while(pos.m_123342_() > this.f_19853_.m_141937_() && !this.f_19853_.m_8055_(pos).m_60767_().m_76334_()) {
         pos.m_122173_(Direction.DOWN);
      }

      BlockState state = this.f_19853_.m_8055_(pos);
      if (state.m_60767_().m_76334_() && state.m_60819_().m_76178_() && this.m_20984_(pX, pY, pZ, false)) {
         TensuraParticleHelper.addServerParticlesAroundPos(this.m_217043_(), this.m_9236_(), oldPosition, ParticleTypes.f_123778_, 1.0D);
         TensuraParticleHelper.addServerParticlesAroundPos(this.m_217043_(), this.m_9236_(), oldPosition, ParticleTypes.f_123813_, 1.0D);
         TensuraParticleHelper.addServerParticlesAroundPos(this.m_217043_(), this.m_9236_(), oldPosition, ParticleTypes.f_123777_, 2.0D);
         if (!this.m_20067_()) {
            this.f_19853_.m_6263_((Player)null, this.f_19854_, this.f_19855_, this.f_19856_, SoundEvents.f_11913_, this.m_5720_(), 1.0F, 1.0F);
         }
      }

   }

   @Nullable
   private ManasSkillInstance getObserver() {
      Optional<ManasSkillInstance> skill = SkillAPI.getSkillsFrom(this).getSkill((ManasSkill)UniqueSkills.OBSERVER.get());
      if (skill.isEmpty()) {
         return null;
      } else {
         return !((ManasSkillInstance)skill.get()).canInteractSkill(this) ? null : (ManasSkillInstance)skill.get();
      }
   }

   @Nullable
   public SpawnGroupData m_6518_(ServerLevelAccessor pLevel, DifficultyInstance pDifficulty, MobSpawnType pReason, @Nullable SpawnGroupData pSpawnData, @Nullable CompoundTag pDataTag) {
      if (!pReason.equals(MobSpawnType.BUCKET)) {
         this.m_6593_(this.m_7755_());
         this.m_213945_(this.f_19796_, pDifficulty);
      }

      return super.m_6518_(pLevel, pDifficulty, pReason, pSpawnData, pDataTag);
   }

   protected void m_213945_(RandomSource pRandom, DifficultyInstance pDifficulty) {
      super.m_213945_(pRandom, pDifficulty);
      ItemStack stack = new ItemStack((ItemLike)TensuraToolItems.KUNAI.get());
      this.m_8061_(EquipmentSlot.MAINHAND, stack);
      this.inventory.m_6836_(4, stack);
      this.m_8061_(EquipmentSlot.OFFHAND, stack);
      this.inventory.m_6836_(5, stack);
      this.inventory.m_6596_();
   }

   protected boolean isSpearType(ItemStack weapon) {
      return weapon.m_41720_() instanceof SimpleSpearItem || weapon.m_41720_() instanceof TridentItem;
   }

   public class KunaiThrowAttackGoal extends HumanoidNPCEntity.NPCMeleeAttackGoal {
      public final ShinRyuseiEntity entity;

      public KunaiThrowAttackGoal(ShinRyuseiEntity entity) {
         super(entity, 2.0D, true);
         this.entity = entity;
      }

      protected void m_6739_(LivingEntity pEnemy, double pDistToEnemySqr) {
         double d0 = this.m_6639_(pEnemy);
         int randomAttack = this.randomAttack(pEnemy, pDistToEnemySqr);
         if (randomAttack != 0) {
            if (pDistToEnemySqr <= (randomAttack == 1 ? d0 : d0 + 400.0D) && this.m_25564_()) {
               this.m_25563_();
               if (randomAttack == 1) {
                  this.entity.m_7327_(pEnemy);
               } else {
                  this.entity.throwKunai();
               }
            }

         }
      }

      protected int randomAttack(LivingEntity target, double distSqr) {
         return distSqr > this.m_6639_(target) ? 2 : 1;
      }
   }
}
