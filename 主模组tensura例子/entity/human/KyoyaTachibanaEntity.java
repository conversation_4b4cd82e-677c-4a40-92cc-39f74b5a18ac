package com.github.manasmods.tensura.entity.human;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.tensura.api.entity.ai.CrossbowAttackGoal;
import com.github.manasmods.tensura.api.entity.ai.WanderingFollowOwnerGoal;
import com.github.manasmods.tensura.config.SpawnRateConfig;
import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.data.pack.OtherworlderSpawning;
import com.github.manasmods.tensura.data.pack.TensuraData;
import com.github.manasmods.tensura.entity.projectile.SevererBladeProjectile;
import com.github.manasmods.tensura.entity.template.HumanoidNPCEntity;
import com.github.manasmods.tensura.entity.template.TensuraTamableEntity;
import com.github.manasmods.tensura.registry.enchantment.TensuraEnchantments;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.items.TensuraToolItems;
import com.github.manasmods.tensura.registry.skill.UniqueSkills;
import com.mojang.math.Vector3f;
import java.util.Iterator;
import java.util.List;
import java.util.Optional;
import javax.annotation.Nullable;
import net.minecraft.commands.arguments.EntityAnchorArgument.Anchor;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.util.RandomSource;
import net.minecraft.world.DifficultyInstance;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.EquipmentSlot;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.Mob;
import net.minecraft.world.entity.MobSpawnType;
import net.minecraft.world.entity.SpawnGroupData;
import net.minecraft.world.entity.Entity.RemovalReason;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.ai.attributes.AttributeSupplier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.goal.FloatGoal;
import net.minecraft.world.entity.ai.goal.LookAtPlayerGoal;
import net.minecraft.world.entity.ai.goal.RandomLookAroundGoal;
import net.minecraft.world.entity.ai.goal.RangedBowAttackGoal;
import net.minecraft.world.entity.ai.goal.SitWhenOrderedToGoal;
import net.minecraft.world.entity.ai.goal.WaterAvoidingRandomStrollGoal;
import net.minecraft.world.entity.ai.goal.target.NonTameRandomTargetGoal;
import net.minecraft.world.entity.ai.goal.target.ResetUniversalAngerTargetGoal;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Items;
import net.minecraft.world.item.enchantment.Enchantment;
import net.minecraft.world.item.enchantment.EnchantmentHelper;
import net.minecraft.world.level.ItemLike;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.LevelAccessor;
import net.minecraft.world.level.ServerLevelAccessor;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.common.ForgeMod;

public class KyoyaTachibanaEntity extends OtherworlderEntity {
   public KyoyaTachibanaEntity(EntityType<? extends KyoyaTachibanaEntity> pEntityType, Level pLevel) {
      super(pEntityType, pLevel);
      this.f_21364_ = 100;
      this.f_19793_ = 1.0F;
   }

   public static AttributeSupplier setAttributes() {
      return Mob.m_21552_().m_22268_(Attributes.f_22284_, 5.0D).m_22268_(Attributes.f_22281_, 5.0D).m_22268_(Attributes.f_22276_, 200.0D).m_22268_(Attributes.f_22279_, 0.25D).m_22268_(Attributes.f_22277_, 32.0D).m_22268_((Attribute)ForgeMod.SWIM_SPEED.get(), 1.0D).m_22268_((Attribute)ForgeMod.ATTACK_RANGE.get(), 2.0D).m_22265_();
   }

   protected void m_8099_() {
      this.f_21345_.m_25352_(0, new FloatGoal(this));
      this.f_21345_.m_25352_(1, new SitWhenOrderedToGoal(this));
      this.f_21345_.m_25352_(2, new HumanoidNPCEntity.EatingItemGoal(this, (entity) -> {
         return this.shouldHeal();
      }, 3.0F));
      this.f_21345_.m_25352_(3, new CrossbowAttackGoal(this, 1.2D, 20.0F));
      this.f_21345_.m_25352_(3, new RangedBowAttackGoal(this, 1.0D, 20, 20.0F));
      this.f_21345_.m_25352_(3, new HumanoidNPCEntity.SpearTypeAttackGoal(this, 1.0D, 20, 20.0F));
      this.f_21345_.m_25352_(3, new KyoyaTachibanaEntity.SevererAttackGoal(this));
      this.f_21345_.m_25352_(4, new WanderingFollowOwnerGoal(this, 1.5D, 10.0F, 5.0F, false));
      this.f_21345_.m_25352_(7, new WaterAvoidingRandomStrollGoal(this, 1.2D));
      this.f_21345_.m_25352_(8, new RandomLookAroundGoal(this));
      this.f_21345_.m_25352_(9, new LookAtPlayerGoal(this, Player.class, 6.0F));
      this.f_21346_.m_25352_(1, new TensuraTamableEntity.TensuraOwnerHurtByTargetGoal(this));
      this.f_21346_.m_25352_(2, new TensuraTamableEntity.TensuraOwnerHurtTargetGoal(this));
      this.f_21346_.m_25352_(3, (new TensuraTamableEntity.TensuraHurtByTargetGoal(this, new Class[]{KyoyaTachibanaEntity.class})).m_26044_(new Class[0]));
      this.f_21346_.m_25352_(6, new NonTameRandomTargetGoal(this, Player.class, false, this::shouldAttackPlayer));
      this.f_21346_.m_25352_(5, new NonTameRandomTargetGoal(this, LivingEntity.class, false, (entity) -> {
         return entity.m_6095_().m_204039_(TensuraTags.EntityTypes.OTHERWORLDER_PREY);
      }));
      this.f_21346_.m_25352_(8, new ResetUniversalAngerTargetGoal(this, true));
   }

   public ResourceLocation getTextureLocation() {
      return new ResourceLocation("tensura", "textures/entity/otherworlder/kyoya_tachibana.png");
   }

   public List<ManasSkill> getUniqueSkills() {
      return List.of((ManasSkill)UniqueSkills.SEVERER.get());
   }

   private void bladeShoot() {
      if (this.m_21205_().m_150930_((Item)TensuraToolItems.SPATIAL_BLADE.get())) {
         if (this.m_5448_() != null) {
            this.m_7618_(Anchor.EYES, this.m_5448_().m_146892_());
         }

         SevererBladeProjectile blade = new SevererBladeProjectile(this.f_19853_, this, true, this.m_21205_());
         if (EnchantmentHelper.m_44914_(this) > 0) {
            blade.m_20254_(600);
         }

         Vector3f vector3f = new Vector3f(this.m_20252_(1.0F));
         blade.m_6686_((double)vector3f.m_122239_(), (double)vector3f.m_122260_(), (double)vector3f.m_122269_(), 3.0F, 0.0F);
         this.f_19853_.m_7967_(blade);
         this.f_19853_.m_6263_((Player)null, this.m_20185_(), this.m_20186_(), this.m_20189_(), SoundEvents.f_12317_, SoundSource.NEUTRAL, 0.5F, 0.4F + this.f_19796_.m_188501_() * 0.4F + 0.8F);
      }
   }

   private void bladeStorm(int arrowAmount) {
      if (this.m_5448_() != null) {
         this.m_7618_(Anchor.EYES, this.m_5448_().m_146892_());
      }

      int arrowRot = 360 / arrowAmount;

      for(int i = 0; i < arrowAmount; ++i) {
         Vec3 arrowOffset = (new Vec3(0.0D, 1.0D, 0.0D)).m_82535_(((float)(arrowRot * i) - (float)arrowRot / 2.0F) * 0.017453292F);
         Vec3 arrowPos = this.m_146892_().m_82549_(this.m_20154_().m_82541_().m_82490_(1.0D)).m_82549_(arrowOffset.m_82496_(-this.m_146909_() * 0.017453292F).m_82524_(-this.m_146908_() * 0.017453292F));
         SevererBladeProjectile blade = new SevererBladeProjectile(this.m_9236_(), this, this.getSpatialBlade());
         blade.m_20242_(true);
         blade.m_146884_(arrowPos);
         blade.setOwnerOffset(arrowOffset);
         blade.setLookDistance(30.0F);
         blade.setDelayTick(20);
         this.m_9236_().m_7967_(blade);
         this.f_19853_.m_6263_((Player)null, arrowPos.m_7096_(), arrowPos.m_7098_(), arrowPos.m_7094_(), SoundEvents.f_11687_, SoundSource.NEUTRAL, 1.0F, 1.0F);
      }

   }

   private void bladeJail(int amount) {
      Entity target = this.m_5448_();
      if (target != null) {
         for(int i = 0; i < amount; ++i) {
            Vec3 bladePos = target.m_146892_().m_82549_((new Vec3(0.0D, Math.random() - 0.5D, 0.6D)).m_82541_().m_82490_((double)(target.m_20205_() + 5.0F)).m_82524_((float)(360 * i) * 0.017453292F / (float)amount));
            SevererBladeProjectile blade = new SevererBladeProjectile(this.m_9236_(), this, this.getSpatialBlade());
            blade.m_20242_(true);
            blade.m_146884_(bladePos);
            blade.setDelayVec(target.m_20182_().m_82546_(bladePos).m_82541_());
            blade.setDelayTick(20);
            this.m_9236_().m_7967_(blade);
            this.f_19853_.m_6263_((Player)null, bladePos.m_7096_(), bladePos.m_7098_(), bladePos.m_7094_(), SoundEvents.f_11887_, SoundSource.NEUTRAL, 1.0F, 1.0F);
         }

      }
   }

   private ItemStack getSpatialBlade() {
      if (this.m_21205_().m_150930_((Item)TensuraToolItems.SPATIAL_BLADE.get())) {
         return this.m_21205_();
      } else {
         ItemStack stack = new ItemStack((ItemLike)TensuraToolItems.SPATIAL_BLADE.get());
         stack.m_41784_().m_128379_("dummy", true);
         stack.m_41663_((Enchantment)TensuraEnchantments.SEVERANCE.get(), 5);
         this.m_8061_(EquipmentSlot.MAINHAND, stack.m_41777_());
         this.inventory.m_6836_(4, stack.m_41777_());
         this.inventory.m_6596_();
         return stack;
      }
   }

   @Nullable
   private ManasSkillInstance getSeverer() {
      Optional<ManasSkillInstance> skill = SkillAPI.getSkillsFrom(this).getSkill((ManasSkill)UniqueSkills.SEVERER.get());
      if (skill.isEmpty()) {
         return null;
      } else {
         return !((ManasSkillInstance)skill.get()).canInteractSkill(this) ? null : (ManasSkillInstance)skill.get();
      }
   }

   @Nullable
   public SpawnGroupData m_6518_(ServerLevelAccessor pLevel, DifficultyInstance pDifficulty, MobSpawnType pReason, @Nullable SpawnGroupData pSpawnData, @Nullable CompoundTag pDataTag) {
      if (pReason == MobSpawnType.NATURAL || pReason == MobSpawnType.CHUNK_GENERATION) {
         if (SpawnRateConfig.rollChance((Integer)SpawnRateConfig.INSTANCE.shizuChance.get(), this.f_19796_)) {
            ShizuEntity shizu = new ShizuEntity((EntityType)TensuraEntityTypes.SHIZU.get(), this.m_9236_());
            shizu.m_6034_(this.m_20185_(), this.m_20186_(), this.m_20189_());
            shizu.m_6518_(pLevel, this.f_19853_.m_6436_(this.m_20183_()), MobSpawnType.NATURAL, (SpawnGroupData)null, (CompoundTag)null);
            this.m_9236_().m_7967_(shizu);
            this.m_142467_(RemovalReason.DISCARDED);
            return super.m_6518_(pLevel, pDifficulty, pReason, pSpawnData, pDataTag);
         }

         if (SpawnRateConfig.rollChance((Integer)SpawnRateConfig.INSTANCE.hinataChance.get(), this.f_19796_)) {
            HinataSakaguchiEntity hinata = new HinataSakaguchiEntity((EntityType)TensuraEntityTypes.HINATA_SAKAGUCHI.get(), this.m_9236_());
            hinata.m_6034_(this.m_20185_(), this.m_20186_(), this.m_20189_());
            hinata.m_6518_(pLevel, this.f_19853_.m_6436_(this.m_20183_()), MobSpawnType.NATURAL, (SpawnGroupData)null, (CompoundTag)null);
            this.m_9236_().m_7967_(hinata);
            this.m_142467_(RemovalReason.DISCARDED);
            return super.m_6518_(pLevel, pDifficulty, pReason, pSpawnData, pDataTag);
         }

         float chance = this.m_217043_().m_188501_();

         OtherworlderSpawning spawning;
         for(Iterator var7 = TensuraData.getOtherworlderSpawning().iterator(); var7.hasNext(); chance -= spawning.getChance()) {
            spawning = (OtherworlderSpawning)var7.next();
            if (!(chance >= spawning.getChance())) {
               if (!spawning.getId().equals(EntityType.m_20613_(this.m_6095_()))) {
                  Optional<EntityType<?>> entityType = EntityType.m_20632_(spawning.getId().toString());
                  if (!entityType.isEmpty()) {
                     Entity entity = ((EntityType)entityType.get()).m_20615_(this.m_9236_());
                     if (entity instanceof Mob) {
                        Mob mob = (Mob)entity;
                        entity.m_146884_(this.m_20182_());
                        mob.m_6518_(pLevel, pDifficulty, pReason, pSpawnData, pDataTag);
                        this.m_9236_().m_7967_(entity);
                        this.m_142467_(RemovalReason.DISCARDED);
                        return super.m_6518_(pLevel, pDifficulty, pReason, pSpawnData, pDataTag);
                     }
                  }
               }
               break;
            }
         }
      }

      if (!pReason.equals(MobSpawnType.BUCKET)) {
         this.m_213945_(this.f_19796_, pDifficulty);
      }

      return super.m_6518_(pLevel, pDifficulty, pReason, pSpawnData, pDataTag);
   }

   public boolean m_5545_(LevelAccessor pLevel, MobSpawnType pSpawnReason) {
      return SpawnRateConfig.rollSpawn((Integer)SpawnRateConfig.INSTANCE.otherworlderSpawnRate.get(), this.m_217043_(), pSpawnReason) && super.m_5545_(pLevel, pSpawnReason);
   }

   protected void m_213945_(RandomSource pRandom, DifficultyInstance pDifficulty) {
      super.m_213945_(pRandom, pDifficulty);
      this.getSpatialBlade();
      if (!(pRandom.m_188501_() >= 0.2F)) {
         ItemStack stack = new ItemStack(Items.f_42740_);
         this.m_8061_(EquipmentSlot.OFFHAND, stack);
         this.inventory.m_6836_(5, stack);
         this.inventory.m_6596_();
      }
   }

   @Nullable
   public Item getEquipmentForArmorSlot(EquipmentSlot pSlot, int pChance) {
      Item var10000;
      switch(pSlot) {
      case HEAD:
         var10000 = pChance == 1 ? Items.f_42407_ : null;
         break;
      case CHEST:
         var10000 = pChance == 1 ? Items.f_42408_ : (pChance == 3 ? Items.f_42465_ : null);
         break;
      case LEGS:
         var10000 = pChance == 1 ? Items.f_42462_ : (pChance == 3 ? Items.f_42466_ : null);
         break;
      case FEET:
         var10000 = pChance == 1 ? Items.f_42463_ : (pChance == 3 ? Items.f_42467_ : null);
         break;
      default:
         var10000 = null;
      }

      return var10000;
   }

   public class SevererAttackGoal extends HumanoidNPCEntity.NPCMeleeAttackGoal {
      public final KyoyaTachibanaEntity entity;

      public SevererAttackGoal(KyoyaTachibanaEntity entity) {
         super(entity, 2.0D, true);
         this.entity = entity;
      }

      protected void m_6739_(LivingEntity pEnemy, double pDistToEnemySqr) {
         double d0 = this.m_6639_(pEnemy);
         int randomAttack = this.randomAttack(pEnemy, pDistToEnemySqr);
         if (randomAttack != 0) {
            if (pDistToEnemySqr <= (randomAttack == 1 ? d0 : d0 + 900.0D) && this.m_25564_()) {
               this.m_25563_();
               switch(randomAttack) {
               case 2:
                  this.entity.bladeShoot();
                  break;
               case 3:
                  this.entity.m_21573_().m_26573_();
                  this.entity.bladeStorm(10);
                  break;
               case 4:
                  this.entity.bladeJail(10);
                  break;
               default:
                  this.entity.m_7327_(pEnemy);
               }

               this.entity.m_21011_(InteractionHand.MAIN_HAND, true);
            }

         }
      }

      protected int randomAttack(LivingEntity target, double distSqr) {
         if (this.entity.getSeverer() == null) {
            return 1;
         } else if (!(distSqr > this.m_6639_(target)) && this.entity.f_19796_.m_188503_(10) != 1) {
            return 1;
         } else if ((double)this.entity.f_19796_.m_188501_() <= 0.1D) {
            return 4;
         } else {
            return (double)this.entity.f_19796_.m_188501_() <= 0.3D ? 3 : 2;
         }
      }
   }
}
