package com.github.manasmods.tensura.entity.variant;

import com.google.common.collect.Maps;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Map;
import net.minecraft.Util;
import net.minecraft.resources.ResourceLocation;

public enum LizardmanVariant {
   GREEN(0),
   BLUE(1),
   PURPLE(2),
   YELLOW(3),
   RED(4),
   DARK_GREEN(5);

   private static final LizardmanVariant[] BY_ID = (LizardmanVariant[])Arrays.stream(values()).sorted(Comparator.comparingInt(LizardmanVariant::getId)).toArray((x$0) -> {
      return new LizardmanVariant[x$0];
   });
   private final int id;
   public static final Map<LizardmanVariant, ResourceLocation> LOCATION_BY_VARIANT = (Map)Util.m_137469_(Maps.newEnumMap(LizardmanVariant.class), (variant) -> {
      variant.put(GREEN, new ResourceLocation("tensura", "textures/entity/lizardman/lizardman_green.png"));
      variant.put(BLUE, new ResourceLocation("tensura", "textures/entity/lizardman/lizardman_blue.png"));
      variant.put(PURPLE, new ResourceLocation("tensura", "textures/entity/lizardman/lizardman_purple.png"));
      variant.put(YELLOW, new ResourceLocation("tensura", "textures/entity/lizardman/lizardman_yellow.png"));
      variant.put(RED, new ResourceLocation("tensura", "textures/entity/lizardman/lizardman_red.png"));
      variant.put(DARK_GREEN, new ResourceLocation("tensura", "textures/entity/lizardman/lizardman_dark_green.png"));
   });

   private LizardmanVariant(int id) {
      this.id = id;
   }

   public int getId() {
      return this.id;
   }

   public static LizardmanVariant byId(int id) {
      return BY_ID[id % BY_ID.length];
   }

   // $FF: synthetic method
   private static LizardmanVariant[] $values() {
      return new LizardmanVariant[]{GREEN, BLUE, PURPLE, YELLOW, RED, DARK_GREEN};
   }
}
