package com.github.manasmods.tensura.entity;

import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.api.entity.ai.BetterHorseRunAroundGoal;
import com.github.manasmods.tensura.api.entity.ai.TamableFollowParentGoal;
import com.github.manasmods.tensura.api.entity.subclass.ITensuraMount;
import com.github.manasmods.tensura.client.keybind.TensuraKeybinds;
import com.github.manasmods.tensura.config.SpawnRateConfig;
import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.entity.template.TensuraHorseEntity;
import com.github.manasmods.tensura.entity.variant.HoverLizardVariant;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.sound.TensuraSoundEvents;
import java.util.EnumSet;
import java.util.Iterator;
import java.util.UUID;
import javax.annotation.Nullable;
import net.minecraft.Util;
import net.minecraft.core.BlockPos;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.syncher.EntityDataAccessor;
import net.minecraft.network.syncher.EntityDataSerializers;
import net.minecraft.network.syncher.SynchedEntityData;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.tags.FluidTags;
import net.minecraft.util.Mth;
import net.minecraft.util.TimeUtil;
import net.minecraft.world.DifficultyInstance;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.InteractionResult;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.entity.AgeableMob;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityDimensions;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.Mob;
import net.minecraft.world.entity.MobSpawnType;
import net.minecraft.world.entity.NeutralMob;
import net.minecraft.world.entity.Pose;
import net.minecraft.world.entity.SpawnGroupData;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.ai.attributes.AttributeSupplier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.control.LookControl;
import net.minecraft.world.entity.ai.control.MoveControl;
import net.minecraft.world.entity.ai.goal.BreedGoal;
import net.minecraft.world.entity.ai.goal.EatBlockGoal;
import net.minecraft.world.entity.ai.goal.FloatGoal;
import net.minecraft.world.entity.ai.goal.LookAtPlayerGoal;
import net.minecraft.world.entity.ai.goal.MeleeAttackGoal;
import net.minecraft.world.entity.ai.goal.RandomLookAroundGoal;
import net.minecraft.world.entity.ai.goal.Goal.Flag;
import net.minecraft.world.entity.ai.goal.target.NearestAttackableTargetGoal;
import net.minecraft.world.entity.ai.goal.target.ResetUniversalAngerTargetGoal;
import net.minecraft.world.entity.ai.goal.target.TargetGoal;
import net.minecraft.world.entity.ai.navigation.GroundPathNavigation;
import net.minecraft.world.entity.ai.navigation.PathNavigation;
import net.minecraft.world.entity.ai.targeting.TargetingConditions;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Items;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.LevelAccessor;
import net.minecraft.world.level.ServerLevelAccessor;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.block.SoundType;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.material.FluidState;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.common.ForgeHooks;
import net.minecraftforge.common.ForgeMod;
import net.minecraftforge.fluids.FluidType;
import software.bernie.geckolib3.core.AnimationState;
import software.bernie.geckolib3.core.IAnimatable;
import software.bernie.geckolib3.core.PlayState;
import software.bernie.geckolib3.core.builder.AnimationBuilder;
import software.bernie.geckolib3.core.builder.ILoopType.EDefaultLoopTypes;
import software.bernie.geckolib3.core.controller.AnimationController;
import software.bernie.geckolib3.core.event.predicate.AnimationEvent;
import software.bernie.geckolib3.core.manager.AnimationData;
import software.bernie.geckolib3.core.manager.AnimationFactory;
import software.bernie.geckolib3.util.GeckoLibUtil;

public class HoverLizardEntity extends TensuraHorseEntity implements IAnimatable, ITensuraMount, NeutralMob {
   private static final EntityDataAccessor<Integer> DATA_REMAINING_ANGER_TIME;
   private static final EntityDataAccessor<Integer> DATA_ID_TYPE_VARIANT;
   private static final EntityDataAccessor<Boolean> STRIKING;
   private static final EntityDataAccessor<Integer> MISC_ANIMATION;
   private static final EntityDataAccessor<Boolean> SLEEPING;
   public int miscAnimationTicks = 0;
   public int itchingCountDown = 0;
   private int sleepingTime;
   private int maxSleepTime;
   @Nullable
   private UUID persistentAngerTarget;
   private final AnimationFactory factory = GeckoLibUtil.createFactory(this);

   public HoverLizardEntity(EntityType<? extends HoverLizardEntity> pEntityType, Level pLevel) {
      super(pEntityType, pLevel);
      this.f_21365_ = new HoverLizardEntity.LizardLookControl();
      this.f_21342_ = new HoverLizardEntity.LizardMoveControl();
      this.f_19793_ = 1.5F;
      this.f_21364_ = 12;
      this.f_30523_ = false;
   }

   public static AttributeSupplier setAttributes() {
      return Mob.m_21552_().m_22268_(Attributes.f_22288_, 1.0D).m_22268_(Attributes.f_22276_, 30.0D).m_22268_(Attributes.f_22281_, 5.0D).m_22268_(Attributes.f_22284_, 2.0D).m_22268_(Attributes.f_22279_, 0.30000001192092896D).m_22268_(Attributes.f_22278_, 0.009999999776482582D).m_22268_(Attributes.f_22277_, 32.0D).m_22268_((Attribute)ForgeMod.SWIM_SPEED.get(), 3.0D).m_22265_();
   }

   public void m_8099_() {
      this.f_21345_.m_25352_(1, new FloatGoal(this));
      this.f_21345_.m_25352_(1, new HoverLizardEntity.HoverLizardRunAroundGoal(this, 1.5D, 3));
      this.f_21345_.m_25352_(2, new TensuraHorseEntity.HorseSitWhenOrderedToGoal(this));
      this.f_21345_.m_25352_(2, new TensuraHorseEntity.HorseFollowOwnerGoal(this, 1.0D, 10.0F, 2.0F, true));
      this.f_21345_.m_25352_(3, new BreedGoal(this, 0.8D, HoverLizardEntity.class));
      this.f_21345_.m_25352_(4, new TamableFollowParentGoal(this, 1.0D));
      this.f_21345_.m_25352_(5, new EatBlockGoal(this));
      this.f_21345_.m_25352_(6, new TensuraHorseEntity.WanderAroundPosGoal(this, 0.8D));
      this.f_21345_.m_25352_(7, new LookAtPlayerGoal(this, Player.class, 6.0F));
      this.f_21345_.m_25352_(8, new RandomLookAroundGoal(this));
      this.f_21346_.m_25352_(1, new HoverLizardEntity.LizardOwnerHurtByTargetGoal(this));
      this.f_21346_.m_25352_(2, new HoverLizardEntity.LizardOwnerHurtTargetGoal(this));
      this.f_21345_.m_25352_(3, new HoverLizardEntity.HoverLizardAttackGoal());
      this.f_21346_.m_25352_(4, (new TensuraHorseEntity.TensuraHurtByTargetGoal(this)).m_26044_(new Class[0]));
      this.f_21346_.m_25352_(4, new NearestAttackableTargetGoal(this, Player.class, 10, true, false, this::m_21674_));
      this.f_21346_.m_25352_(8, new ResetUniversalAngerTargetGoal(this, true));
   }

   protected void m_8097_() {
      super.m_8097_();
      this.f_19804_.m_135372_(DATA_ID_TYPE_VARIANT, 0);
      this.f_19804_.m_135372_(STRIKING, false);
      this.f_19804_.m_135372_(DATA_REMAINING_ANGER_TIME, 0);
      this.f_19804_.m_135372_(MISC_ANIMATION, 0);
      this.f_19804_.m_135372_(SLEEPING, false);
   }

   public void m_7380_(CompoundTag compound) {
      super.m_7380_(compound);
      compound.m_128405_("Variant", this.getTypeVariant());
      compound.m_128405_("MiscAnimation", this.getMiscAnimation());
      compound.m_128379_("Sleeping", this.m_5803_());
      this.m_21678_(compound);
   }

   public void m_7378_(CompoundTag compound) {
      super.m_7378_(compound);
      this.f_19804_.m_135381_(DATA_ID_TYPE_VARIANT, compound.m_128451_("Variant"));
      this.f_19804_.m_135381_(MISC_ANIMATION, compound.m_128451_("MiscAnimation"));
      this.f_19804_.m_135381_(SLEEPING, compound.m_128471_("Sleeping"));
      this.m_147285_(this.f_19853_, compound);
   }

   public HoverLizardVariant getVariant() {
      return HoverLizardVariant.byId(this.getTypeVariant() & 255);
   }

   private int getTypeVariant() {
      return (Integer)this.f_19804_.m_135370_(DATA_ID_TYPE_VARIANT);
   }

   public void setVariant(HoverLizardVariant variant) {
      this.f_19804_.m_135381_(DATA_ID_TYPE_VARIANT, variant.getId() & 255);
   }

   public void setStriking(boolean striking) {
      this.f_19804_.m_135381_(STRIKING, striking);
   }

   public boolean isStriking() {
      return (Boolean)this.f_19804_.m_135370_(STRIKING);
   }

   public boolean m_5803_() {
      return (Boolean)this.f_19804_.m_135370_(SLEEPING);
   }

   public void setSleeping(boolean sleeping) {
      this.f_19804_.m_135381_(SLEEPING, sleeping);
      this.sleepingTime = 0;
      if (sleeping == Boolean.TRUE) {
         this.setMiscAnimation(2);
         this.maxSleepTime = 200 + this.f_19796_.m_188503_(550);
      } else {
         this.setMiscAnimation(6);
         this.maxSleepTime = 100 + this.f_19796_.m_188503_(50);
      }

   }

   public int getMiscAnimation() {
      return (Integer)this.f_19804_.m_135370_(MISC_ANIMATION);
   }

   public void setMiscAnimation(int animation) {
      if (this.getMiscAnimation() == 0 || animation == 0) {
         this.f_19804_.m_135381_(MISC_ANIMATION, animation);
      }
   }

   public void m_7350_(EntityDataAccessor<?> pKey) {
      if (SLEEPING.equals(pKey)) {
         this.m_6210_();
      }

      super.m_7350_(pKey);
   }

   public boolean m_6785_(double pDistanceToClosestPlayer) {
      if (this.getSpawnType() == MobSpawnType.STRUCTURE) {
         return false;
      } else {
         return !this.m_30614_();
      }
   }

   public double m_20204_() {
      float threshold = this.m_6162_() ? 0.25F : 0.5F;
      return super.m_20204_() + (double)threshold;
   }

   public EntityDimensions m_6972_(Pose pPose) {
      EntityDimensions dimensions = super.m_6972_(pPose);
      return this.m_5803_() ? EntityDimensions.m_20398_(dimensions.f_20377_, this.m_6162_() ? 0.6F : 1.2F) : dimensions;
   }

   public boolean shouldFollowOwner() {
      LivingEntity owner = this.getOwner();
      if (owner == null) {
         return false;
      } else if (owner.m_20270_(this) < 10.0F) {
         return false;
      } else {
         return !this.isSitting();
      }
   }

   public void m_8119_() {
      super.m_8119_();
      if (!this.f_19853_.f_46443_) {
         if (this.m_5803_() && (this.m_5448_() != null || this.m_27593_() || this.m_20072_() || this.m_20077_() || this.m_20160_() || ++this.sleepingTime > this.maxSleepTime && this.f_19853_.m_46461_() || this.shouldFollowOwner())) {
            this.setSleeping(false);
         }

         if (this.m_5448_() == null && this.m_20184_().m_82556_() < 0.03D && this.f_19853_.m_46462_() && !this.shouldFollowOwner() && !this.m_5803_() && !this.m_20072_() && !this.m_20160_() && this.f_19796_.m_188503_(100) == 0) {
            if (this.m_217043_().m_188499_()) {
               this.setSleeping(true);
            } else {
               this.sleepingTime = 0;
               this.maxSleepTime = 100 + this.f_19796_.m_188503_(550);
            }
         }
      }

      if (this.getMiscAnimation() == 0 && !this.isStriking()) {
         if (this.itchingCountDown++ > 400 && !this.m_5803_() && this.m_217043_().m_188503_(10) <= 3) {
            this.itchingCountDown = 0;
            this.setMiscAnimation(1);
         }
      } else {
         ++this.miscAnimationTicks;
         if (this.miscAnimationTicks > 15 || this.getMiscAnimation() == 7 && this.miscAnimationTicks > 2) {
            this.setStriking(Boolean.FALSE);
            this.setMiscAnimation(0);
            this.miscAnimationTicks = 0;
         }
      }

   }

   public void m_8107_() {
      if (this.m_5803_()) {
         this.f_20899_ = false;
         this.f_20900_ = 0.0F;
         this.f_20902_ = 0.0F;
      }

      super.m_8107_();
   }

   public void mountAbility(Player rider) {
      if (this.getMiscAnimation() == 1 || this.getMiscAnimation() == 2 || this.getMiscAnimation() == 3 || this.getMiscAnimation() == 0) {
         if (!this.m_6162_()) {
            LivingEntity target = SkillHelper.getTargetingEntity(rider, 6.0D, false);
            if (target != null) {
               this.setStriking(true);
               this.m_7327_(target);
            }
         }
      }
   }

   public void m_7023_(Vec3 pTravelVector) {
      if (this.m_6084_()) {
         LivingEntity controller = this.m_6688_();
         if (this.m_20160_() && controller != null) {
            this.m_146922_(controller.m_146908_());
            this.f_19859_ = this.m_146908_();
            this.m_146926_(controller.m_146909_() * 0.5F);
            this.m_19915_(this.m_146908_(), this.m_146909_());
            this.f_20883_ = this.m_146908_();
            this.f_20885_ = this.f_20883_;
            float f = controller.f_20900_ * 0.5F;
            float f1 = controller.f_20902_;
            if (f1 <= 0.0F) {
               f1 *= 0.25F;
               this.f_30524_ = 0;
            }

            if (this.f_30522_ > 0.0F && !this.m_30616_() && this.f_19861_) {
               double d0 = this.m_30626_() * (double)this.f_30522_ * (double)this.m_20098_();
               double d1 = d0 + this.m_182332_();
               Vec3 vec3 = this.m_20184_();
               this.m_20334_(vec3.f_82479_, d1, vec3.f_82481_);
               this.m_30655_(true);
               this.f_19812_ = true;
               ForgeHooks.onLivingJump(this);
               if (f1 > 0.0F) {
                  float f2 = Mth.m_14031_(this.m_146908_() * 0.017453292F);
                  float f3 = Mth.m_14089_(this.m_146908_() * 0.017453292F);
                  this.m_20256_(this.m_20184_().m_82520_((double)(-0.4F * f2 * this.f_30522_), 0.0D, (double)(0.4F * f3 * this.f_30522_)));
               }

               this.f_30522_ = 0.0F;
            }

            this.f_20887_ = this.m_6113_() * 0.1F;
            if (this.m_6109_()) {
               float speed = (float)this.m_21133_(Attributes.f_22279_);
               if (controller.m_20142_()) {
                  speed = (float)((double)speed * 1.5D);
               }

               this.m_7910_(speed);
               if (this.isInFluidType()) {
                  if (this.isInFluidType((fluidType, height) -> {
                     return height > this.m_20204_();
                  }) && controller.f_20899_) {
                     this.m_20256_(this.m_20184_().m_82520_(0.0D, 0.05D, 0.0D));
                  } else if (this.isInFluidType() && TensuraKeybinds.MOUNT_DESCENDING.m_90857_()) {
                     this.descending(this, controller);
                  }
               }

               super.m_7023_(new Vec3((double)f, pTravelVector.f_82480_, (double)f1));
            } else if (controller instanceof Player) {
               this.m_20256_(Vec3.f_82478_);
            }

            if (this.f_19861_) {
               this.f_30522_ = 0.0F;
               this.m_30655_(false);
            }

            this.m_21043_(this, false);
            this.m_146872_();
         } else {
            this.f_20887_ = 0.02F;
            super.m_7023_(pTravelVector);
         }
      }

   }

   public int m_6784_() {
      return (Integer)this.f_19804_.m_135370_(DATA_REMAINING_ANGER_TIME);
   }

   public void m_7870_(int pTime) {
      this.f_19804_.m_135381_(DATA_REMAINING_ANGER_TIME, pTime);
   }

   @Nullable
   public UUID m_6120_() {
      return this.persistentAngerTarget;
   }

   public void m_6925_(@Nullable UUID pTarget) {
      this.persistentAngerTarget = pTarget;
   }

   public void m_6825_() {
      this.m_7870_(TimeUtil.m_145020_(20, 39).m_214085_(this.f_19796_));
   }

   protected PathNavigation m_6037_(Level pLevel) {
      return new HoverLizardEntity.HoverLizardPathNavigation(this, pLevel);
   }

   public boolean m_6146_() {
      return true;
   }

   public boolean isPushedByFluid(FluidType type) {
      return !type.canDrownIn(this);
   }

   public boolean m_6673_(DamageSource source) {
      return source == DamageSource.f_19314_ || source == DamageSource.f_19325_ || super.m_6673_(source);
   }

   public double m_6048_() {
      if (this.m_6688_() != null) {
         if (this.m_5803_()) {
            return 0.0D;
         }

         if (this.m_6688_().m_20142_()) {
            return 1.5D;
         }
      }

      return 1.7D;
   }

   protected void m_6835_(Player pPlayer) {
      if (!this.m_5803_()) {
         super.m_6835_(pPlayer);
      }
   }

   public int m_5792_() {
      return 2;
   }

   public int m_7555_() {
      return 200;
   }

   public boolean m_142535_(float pFallDistance, float pMultiplier, DamageSource pSource) {
      if (pFallDistance < 7.0F) {
         return false;
      } else {
         int i = this.m_5639_(pFallDistance - 7.0F, pMultiplier);
         if (i <= 0) {
            return false;
         } else {
            this.m_6469_(pSource, (float)i);
            if (this.m_20160_()) {
               Iterator var5 = this.m_146897_().iterator();

               while(var5.hasNext()) {
                  Entity entity = (Entity)var5.next();
                  entity.m_6469_(pSource, (float)i);
               }
            }

            this.m_21229_();
            return true;
         }
      }
   }

   public boolean m_203441_(FluidState fluidState) {
      if (!fluidState.m_205070_(FluidTags.f_13131_)) {
         return false;
      } else {
         return !this.f_19853_.m_8055_(this.m_20183_().m_7495_()).m_60713_(Blocks.f_49990_);
      }
   }

   protected void m_6135_() {
      this.setMiscAnimation(7);
      super.m_6135_();
   }

   public void m_30655_(boolean pJumping) {
      if (pJumping) {
         this.setMiscAnimation(7);
      }

      super.m_30655_(pJumping);
   }

   public void m_6710_(@Nullable LivingEntity pTarget) {
      if (!this.m_6162_()) {
         super.m_6710_(pTarget);
      }

   }

   public AgeableMob m_142606_(ServerLevel pLevel, AgeableMob pOtherParent) {
      HoverLizardEntity lizard = (HoverLizardEntity)((EntityType)TensuraEntityTypes.HOVER_LIZARD.get()).m_20615_(pLevel);
      if (lizard == null) {
         return null;
      } else {
         UUID uuid = this.m_21805_();
         if (uuid != null) {
            lizard.m_30586_(uuid);
            lizard.m_30651_(true);
         }

         int i = this.f_19796_.m_188503_(9);
         HoverLizardVariant variant;
         if (i < 4) {
            variant = this.getVariant();
         } else if (i < 8 && pOtherParent instanceof HoverLizardEntity) {
            HoverLizardEntity hoverLizard = (HoverLizardEntity)pOtherParent;
            variant = hoverLizard.getVariant();
         } else {
            variant = (HoverLizardVariant)Util.m_214670_(HoverLizardVariant.values(), this.f_19796_);
         }

         lizard.setVariant(variant);
         return lizard;
      }
   }

   public void m_7564_() {
      super.m_7564_();
      this.setStriking(Boolean.TRUE);
   }

   public InteractionResult m_6071_(Player pPlayer, InteractionHand pHand) {
      ItemStack itemstack = pPlayer.m_21120_(pHand);
      if (itemstack.m_41619_() && !this.m_6162_()) {
         if (this.m_20160_()) {
            return InteractionResult.PASS;
         }

         if (this.m_30614_() && this.isOwnedBy(pPlayer) && pPlayer.m_36341_() && !this.f_19853_.f_46443_) {
            this.commanding(pPlayer);
            return InteractionResult.SUCCESS;
         }

         if (this.m_30614_() && this.isOwnedBy(pPlayer) || !this.m_30614_()) {
            if (this.m_5803_()) {
               this.setSleeping(false);
            }

            if (this.isSitting()) {
               this.setSitting(false);
            }

            this.m_6835_(pPlayer);
            return InteractionResult.m_19078_(this.f_19853_.f_46443_);
         }
      } else {
         boolean saddling;
         if (this.m_6898_(itemstack)) {
            if (this.m_5803_()) {
               return InteractionResult.PASS;
            }

            saddling = this.m_5994_(pPlayer, itemstack);
            if (!pPlayer.m_150110_().f_35937_) {
               itemstack.m_41774_(1);
            }

            this.setMiscAnimation(this.m_217043_().m_188503_(10) < 5 ? 5 : 4);
            if (this.f_19853_.f_46443_) {
               return InteractionResult.CONSUME;
            }

            return saddling ? InteractionResult.SUCCESS : InteractionResult.PASS;
         }

         if (!this.m_30614_()) {
            if (!this.m_5803_()) {
               this.m_7564_();
               return InteractionResult.m_19078_(this.f_19853_.f_46443_);
            }
         } else {
            if (!this.m_30502_() && itemstack.m_150930_(Blocks.f_50087_.m_5456_())) {
               this.m_30504_(true);
               this.m_5496_(SoundEvents.f_11811_, 1.0F, (this.f_19796_.m_188501_() - this.f_19796_.m_188501_()) * 0.2F + 1.0F);
               if (!pPlayer.m_150110_().f_35937_) {
                  itemstack.m_41774_(1);
               }

               this.m_30625_();
               return InteractionResult.m_19078_(this.f_19853_.f_46443_);
            }

            if (this.m_30502_() && itemstack.m_150930_(Items.f_42574_)) {
               this.dropChest();
               this.m_30504_(false);
               this.m_5496_(SoundEvents.f_12344_, 1.0F, (this.f_19796_.m_188501_() - this.f_19796_.m_188501_()) * 0.2F + 1.0F);
               return InteractionResult.m_19078_(this.f_19853_.f_46443_);
            }

            saddling = !this.m_6162_() && !this.m_6254_() && itemstack.m_150930_(Items.f_42450_);
            if (this.m_6010_(itemstack) || saddling) {
               this.m_213583_(pPlayer);
               return InteractionResult.m_19078_(this.f_19853_.f_46443_);
            }
         }
      }

      return InteractionResult.PASS;
   }

   public boolean m_6898_(ItemStack pStack) {
      return pStack.m_204117_(TensuraTags.Items.FISHES);
   }

   protected boolean m_5994_(Player pPlayer, ItemStack pStack) {
      boolean flag = false;
      if (this.m_30614_() && this.m_146764_() == 0 && this.m_5957_()) {
         flag = true;
         this.m_27595_(pPlayer);
      } else if (this.m_30624_() < this.m_7555_()) {
         flag = true;
         if (!this.f_19853_.f_46443_) {
            this.m_30653_(3);
         }
      }

      if (this.m_21223_() < this.m_21233_()) {
         flag = true;
         this.m_5634_(3.0F);
      }

      if (this.m_6162_()) {
         this.f_19853_.m_7106_(ParticleTypes.f_123748_, this.m_20208_(1.0D), this.m_20187_() + 0.5D, this.m_20262_(1.0D), 0.0D, 0.0D, 0.0D);
         if (!this.f_19853_.f_46443_) {
            this.m_146758_(10);
         }

         flag = true;
      }

      if (flag && !this.m_20067_() && this.m_7872_() != null) {
         this.f_19853_.m_6263_((Player)null, this.m_20185_(), this.m_20186_(), this.m_20189_(), this.m_7872_(), this.m_5720_(), 1.0F, 1.0F + (this.f_19796_.m_188501_() - this.f_19796_.m_188501_()) * 0.2F);
      }

      return flag;
   }

   public void m_7822_(byte pId) {
      if (pId == 10 && this.getMiscAnimation() != 3) {
         if (!this.m_5803_()) {
            this.setMiscAnimation(3);
         }

         this.m_5634_(5.0F);
      } else {
         super.m_7822_(pId);
      }

   }

   public boolean m_5545_(LevelAccessor pLevel, MobSpawnType pSpawnReason) {
      return SpawnRateConfig.rollSpawn((Integer)SpawnRateConfig.INSTANCE.hoverLizardSpawnRate.get(), this.m_217043_(), pSpawnReason) && super.m_5545_(pLevel, pSpawnReason);
   }

   @Nullable
   public SpawnGroupData m_6518_(ServerLevelAccessor pLevel, DifficultyInstance pDifficulty, MobSpawnType pReason, @Nullable SpawnGroupData pSpawnData, @Nullable CompoundTag pDataTag) {
      this.setVariant((HoverLizardVariant)Util.m_214670_(HoverLizardVariant.values(), this.f_19796_));
      return super.m_6518_(pLevel, pDifficulty, pReason, pSpawnData, pDataTag);
   }

   protected void m_5877_(SoundType pSoundType) {
      ItemStack stack = this.f_30520_.m_8020_(1);
      if (this.m_6010_(stack)) {
         stack.onHorseArmorTick(this.f_19853_, this);
      }

   }

   protected SoundEvent m_7515_() {
      return SoundEvents.f_11799_;
   }

   protected SoundEvent m_5592_() {
      return SoundEvents.f_11802_;
   }

   protected SoundEvent m_7872_() {
      return (SoundEvent)TensuraSoundEvents.EATING.get();
   }

   protected SoundEvent m_7975_(DamageSource pDamageSource) {
      this.setAdditionalTemper(1);
      int i = this.m_30624_() - 5;
      this.m_30649_(Math.max(i, 0));
      return SoundEvents.f_11804_;
   }

   @Nullable
   protected SoundEvent m_7871_() {
      return null;
   }

   protected void m_7355_(BlockPos pPos, BlockState pState) {
      if (!pState.m_60767_().m_76332_()) {
         BlockState blockstate = this.f_19853_.m_8055_(pPos.m_7494_());
         SoundType soundtype = blockstate.m_60713_(Blocks.f_50125_) ? blockstate.getSoundType(this.f_19853_, pPos, this) : pState.getSoundType(this.f_19853_, pPos, this);
         this.m_5496_(soundtype.m_56776_(), soundtype.m_56773_() * 0.15F, soundtype.m_56774_());
      }

   }

   protected void m_7486_() {
      this.m_5496_((SoundEvent)TensuraSoundEvents.SMALL_JUMP_IMPACT.get(), 0.8F, 1.0F);
   }

   private <E extends IAnimatable> PlayState predicate(AnimationEvent<E> event) {
      if (this.m_5803_() && this.getMiscAnimation() != 2) {
         event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.hover_lizard.sleeping", EDefaultLoopTypes.LOOP));
      } else if (event.isMoving()) {
         if (this.m_20069_() && this.f_19853_.m_8055_(this.m_20183_().m_6625_(2)).m_60713_(Blocks.f_49990_)) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.hover_lizard.deep_swim", EDefaultLoopTypes.LOOP));
         } else if (this.m_6688_() != null && this.m_6688_().m_20142_()) {
            if (this.m_21223_() < this.m_21233_() / 4.0F) {
               event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.hover_lizard.run_hurt", EDefaultLoopTypes.LOOP));
            } else {
               event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.hover_lizard.run", EDefaultLoopTypes.LOOP));
            }
         } else if (this.m_21223_() < this.m_21233_() / 4.0F) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.hover_lizard.walk_hurt", EDefaultLoopTypes.LOOP));
         } else {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.hover_lizard.walk", EDefaultLoopTypes.LOOP));
         }
      } else if (this.isSitting()) {
         event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.hover_lizard.idle_tail_swing", EDefaultLoopTypes.LOOP));
      } else if (this.m_21223_() < this.m_21233_() / 4.0F) {
         event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.hover_lizard.idle_hurt", EDefaultLoopTypes.LOOP));
      } else {
         event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.hover_lizard.idle", EDefaultLoopTypes.LOOP));
      }

      return PlayState.CONTINUE;
   }

   private <E extends IAnimatable> PlayState playOncePredicate(AnimationEvent<E> event) {
      if (event.getController().getAnimationState().equals(AnimationState.Stopped)) {
         event.getController().markNeedsReload();
         if (this.getMiscAnimation() == 2) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.hover_lizard.into_sleep", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 6) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.hover_lizard.awaking", EDefaultLoopTypes.PLAY_ONCE));
         } else if (!this.m_5803_()) {
            if (!this.isStriking() && !this.f_20911_) {
               if (this.getMiscAnimation() == 1) {
                  event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.hover_lizard.itching", EDefaultLoopTypes.PLAY_ONCE));
               } else if (this.getMiscAnimation() == 3) {
                  event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.hover_lizard.drinking", EDefaultLoopTypes.PLAY_ONCE));
               } else if (this.getMiscAnimation() == 4) {
                  event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.hover_lizard.eating_one_hand", EDefaultLoopTypes.PLAY_ONCE));
               } else if (this.getMiscAnimation() == 5) {
                  event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.hover_lizard.eating_both_hand", EDefaultLoopTypes.PLAY_ONCE));
               } else if (this.getMiscAnimation() == 7) {
                  event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.hover_lizard.hop", EDefaultLoopTypes.PLAY_ONCE));
               }
            } else {
               event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.hover_lizard.biting", EDefaultLoopTypes.PLAY_ONCE));
            }
         }
      }

      return PlayState.CONTINUE;
   }

   public void registerControllers(AnimationData data) {
      data.addAnimationController(new AnimationController(this, "controller", 0.0F, this::predicate));
      data.addAnimationController(new AnimationController(this, "playOnceController", 0.0F, this::playOncePredicate));
   }

   public AnimationFactory getFactory() {
      return this.factory;
   }

   static {
      DATA_REMAINING_ANGER_TIME = SynchedEntityData.m_135353_(HoverLizardEntity.class, EntityDataSerializers.f_135028_);
      DATA_ID_TYPE_VARIANT = SynchedEntityData.m_135353_(HoverLizardEntity.class, EntityDataSerializers.f_135028_);
      STRIKING = SynchedEntityData.m_135353_(HoverLizardEntity.class, EntityDataSerializers.f_135035_);
      MISC_ANIMATION = SynchedEntityData.m_135353_(HoverLizardEntity.class, EntityDataSerializers.f_135028_);
      SLEEPING = SynchedEntityData.m_135353_(HoverLizardEntity.class, EntityDataSerializers.f_135035_);
   }

   public class LizardLookControl extends LookControl {
      public LizardLookControl() {
         super(HoverLizardEntity.this);
      }

      public void m_8128_() {
         if (!HoverLizardEntity.this.m_5803_()) {
            super.m_8128_();
         }

      }
   }

   class LizardMoveControl extends MoveControl {
      public LizardMoveControl() {
         super(HoverLizardEntity.this);
      }

      public void m_8126_() {
         if (!HoverLizardEntity.this.m_5803_()) {
            super.m_8126_();
         }

      }
   }

   static class HoverLizardRunAroundGoal extends BetterHorseRunAroundGoal {
      HoverLizardEntity hoverLizard;

      public HoverLizardRunAroundGoal(HoverLizardEntity lizard, double pSpeedModifier, int additionalTemper) {
         super(lizard, pSpeedModifier, additionalTemper);
         this.hoverLizard = lizard;
      }

      public void m_8037_() {
         if (!this.hoverLizard.m_30614_()) {
            Entity entity = (Entity)this.hoverLizard.m_20197_().get(0);
            if (entity instanceof Player) {
               Player player = (Player)entity;
               if (!player.m_7500_() && !player.m_5833_() && this.hoverLizard.m_5448_() == null) {
                  this.hoverLizard.m_6710_(player);
               }

               if (this.hoverLizard.m_5803_()) {
                  this.hoverLizard.setSleeping(false);
               }
            }

            super.m_8037_();
         }
      }

      public void m_8056_() {
         if (this.hoverLizard.m_5803_()) {
            this.hoverLizard.setSleeping(false);
         }

         super.m_8056_();
      }
   }

   static class LizardOwnerHurtByTargetGoal extends TargetGoal {
      private final HoverLizardEntity lizard;
      private LivingEntity ownerLastHurtBy;
      private int timestamp;

      public LizardOwnerHurtByTargetGoal(HoverLizardEntity pTameAnimal) {
         super(pTameAnimal, false);
         this.lizard = pTameAnimal;
         this.m_7021_(EnumSet.of(Flag.TARGET));
      }

      public boolean m_8036_() {
         if (!this.lizard.m_30614_()) {
            return false;
         } else if (this.lizard.isSitting()) {
            return false;
         } else if (this.lizard.getBehaviour() == 1) {
            return false;
         } else {
            LivingEntity livingentity = this.lizard.getOwner();
            if (livingentity == null) {
               return false;
            } else {
               this.ownerLastHurtBy = livingentity.m_21188_();
               int i = livingentity.m_21213_();
               return i != this.timestamp && this.m_26150_(this.ownerLastHurtBy, TargetingConditions.f_26872_) && !this.lizard.m_7307_(this.ownerLastHurtBy);
            }
         }
      }

      public void m_8056_() {
         if (this.lizard.m_5803_()) {
            this.lizard.setSleeping(false);
         }

         this.f_26135_.m_6710_(this.ownerLastHurtBy);
         LivingEntity livingentity = this.lizard.getOwner();
         if (livingentity != null) {
            this.timestamp = livingentity.m_21213_();
         }

         super.m_8056_();
      }
   }

   static class LizardOwnerHurtTargetGoal extends TargetGoal {
      private final TensuraHorseEntity tameAnimal;
      private LivingEntity ownerLastHurt;
      private int timestamp;

      public LizardOwnerHurtTargetGoal(TensuraHorseEntity pTameAnimal) {
         super(pTameAnimal, false);
         this.tameAnimal = pTameAnimal;
         this.m_7021_(EnumSet.of(Flag.TARGET));
      }

      public boolean m_8036_() {
         if (!this.tameAnimal.m_30614_()) {
            return false;
         } else if (this.tameAnimal.isSitting()) {
            return false;
         } else if (this.tameAnimal.getBehaviour() == 1) {
            return false;
         } else {
            LivingEntity livingentity = this.tameAnimal.getOwner();
            if (livingentity == null) {
               return false;
            } else {
               this.ownerLastHurt = livingentity.m_21214_();
               int i = livingentity.m_21215_();
               return i != this.timestamp && this.m_26150_(this.ownerLastHurt, TargetingConditions.f_26872_) && !this.tameAnimal.m_7307_(this.ownerLastHurt);
            }
         }
      }

      public void m_8056_() {
         this.f_26135_.m_6710_(this.ownerLastHurt);
         LivingEntity livingentity = this.tameAnimal.getOwner();
         if (livingentity != null) {
            this.timestamp = livingentity.m_21215_();
         }

         super.m_8056_();
      }
   }

   class HoverLizardAttackGoal extends MeleeAttackGoal {
      public HoverLizardAttackGoal() {
         super(HoverLizardEntity.this, 1.25D, true);
      }

      protected void m_6739_(LivingEntity pEnemy, double pDistToEnemySqr) {
         double d0 = this.m_6639_(pEnemy);
         if (pDistToEnemySqr <= d0 && this.m_25564_()) {
            this.m_25563_();
            this.f_25540_.m_7327_(pEnemy);
            HoverLizardEntity.this.setStriking(false);
         } else if (pDistToEnemySqr <= d0 * 2.0D) {
            if (this.m_25564_()) {
               HoverLizardEntity.this.setStriking(false);
               this.m_25563_();
            }

            if (this.m_25565_() <= 10) {
               HoverLizardEntity.this.setStriking(true);
            }
         } else {
            this.m_25563_();
            HoverLizardEntity.this.setStriking(false);
         }

      }

      public void m_8056_() {
         if (HoverLizardEntity.this.m_5803_()) {
            HoverLizardEntity.this.setSleeping(false);
         }

         super.m_8056_();
      }

      public void m_8041_() {
         HoverLizardEntity.this.setStriking(false);
         super.m_8041_();
      }

      protected double m_6639_(LivingEntity pAttackTarget) {
         return (double)(10.0F + pAttackTarget.m_20205_());
      }
   }

   static class HoverLizardPathNavigation extends GroundPathNavigation {
      HoverLizardPathNavigation(HoverLizardEntity hoverLizard, Level pLevel) {
         super(hoverLizard, pLevel);
      }

      public boolean m_6342_(BlockPos pPos) {
         if (this.f_26495_.m_8055_(pPos).m_60713_(Blocks.f_49990_)) {
            return !this.f_26495_.m_8055_(pPos.m_7495_()).m_60713_(Blocks.f_49990_);
         } else {
            return super.m_6342_(pPos);
         }
      }
   }
}
