package com.github.manasmods.tensura.entity.projectile;

import com.github.manasmods.tensura.config.TensuraConfig;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.items.TensuraToolItems;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.projectile.AbstractArrow;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.level.ItemLike;
import net.minecraft.world.level.Level;

public class SpearedFinArrow extends AbstractArrow {
   public SpearedFinArrow(EntityType<? extends SpearedFinArrow> pEntityType, Level pLevel) {
      super(pEntityType, pLevel);
   }

   public SpearedFinArrow(Level pLevel, LivingEntity pShooter) {
      super((EntityType)TensuraEntityTypes.SPEARED_FIN_ARROW.get(), pShooter, pLevel);
   }

   public SpearedFinArrow(Level pLevel, double pX, double pY, double pZ) {
      super((EntityType)TensuraEntityTypes.SPEARED_FIN_ARROW.get(), pX, pY, pZ, pLevel);
   }

   public ItemStack m_7941_() {
      return new ItemStack((ItemLike)TensuraToolItems.SPEARED_FIN_ARROW.get());
   }

   protected float m_6882_() {
      return 1.1F;
   }

   public void m_6901_() {
      if (++this.f_36697_ >= (Integer)TensuraConfig.INSTANCE.entitiesConfig.spearDespawn.get()) {
         this.m_146870_();
      }

   }
}
