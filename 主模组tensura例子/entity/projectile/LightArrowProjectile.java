package com.github.manasmods.tensura.entity.projectile;

import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.entity.magic.TensuraProjectile;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.level.Level;
import net.minecraft.world.phys.HitResult;
import net.minecraft.world.phys.HitResult.Type;

public class LightArrowProjectile extends TensuraProjectile {
   public LightArrowProjectile(EntityType<? extends LightArrowProjectile> entityType, Level level) {
      super(entityType, level);
   }

   public LightArrowProjectile(Level levelIn, LivingEntity shooter) {
      super((EntityType)TensuraEntityTypes.LIGHT_ARROW.get(), levelIn);
      this.m_5602_(shooter);
   }

   public ResourceLocation[] getTextureLocation() {
      return new ResourceLocation[]{new ResourceLocation("tensura", "textures/entity/projectiles/light_arrow.png")};
   }

   public boolean piercingBlock() {
      return true;
   }

   public boolean shouldDiscardInLava() {
      return false;
   }

   public boolean shouldDiscardInWater() {
      return false;
   }

   protected void hitEntity(Entity entity) {
      if (entity != this.m_37282_()) {
         super.hitEntity(entity);
      }
   }

   protected void dealDamage(Entity target) {
      if (!(this.getDamage() <= 0.0F)) {
         DamageSource damageSource = TensuraDamageSources.indirectElementalAttack("tensura.light_attack", this, this.m_37282_(), true);
         if (target.m_6469_(DamageSourceHelper.addSkillAndCost(damageSource, this.getMpCost(), this.getSkill()), this.getDamage())) {
            target.f_19802_ = 0;
         }

      }
   }

   protected void playHitSound(SoundEvent sound, HitResult hitresult) {
      if (hitresult.m_6662_().equals(Type.ENTITY)) {
         super.playHitSound(sound, hitresult);
      }

   }

   public void hitParticles(double x, double y, double z) {
      TensuraParticleHelper.spawnServerParticles(this.f_19853_, ParticleTypes.f_123808_, x, y, z, 15, 0.1D, 0.1D, 0.1D, 0.1D, true);
   }

   public void flyingParticles() {
   }
}
