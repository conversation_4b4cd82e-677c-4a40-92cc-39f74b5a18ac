package com.github.manasmods.tensura.entity.projectile;

import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.entity.CharybdisEntity;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.particle.TensuraParticles;
import net.minecraft.core.particles.ParticleOptions;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.protocol.Packet;
import net.minecraft.network.protocol.game.ClientboundAddEntityPacket;
import net.minecraft.network.syncher.EntityDataAccessor;
import net.minecraft.network.syncher.EntityDataSerializers;
import net.minecraft.network.syncher.SynchedEntityData;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityDimensions;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.MobSpawnType;
import net.minecraft.world.entity.MoverType;
import net.minecraft.world.entity.Pose;
import net.minecraft.world.entity.SpawnGroupData;
import net.minecraft.world.entity.Entity.MovementEmission;
import net.minecraft.world.level.GameRules;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.Explosion.BlockInteraction;
import net.minecraft.world.phys.Vec3;

public class PrimedCharybdisCoreEntity extends Entity {
   private static final EntityDataAccessor<Integer> DATA_FUSE_ID;

   public PrimedCharybdisCoreEntity(EntityType<? extends PrimedCharybdisCoreEntity> pEntityType, Level pLevel) {
      super(pEntityType, pLevel);
      this.f_19850_ = true;
   }

   public PrimedCharybdisCoreEntity(Level pLevel, double pX, double pY, double pZ) {
      this((EntityType)TensuraEntityTypes.CHARYBDIS_CORE.get(), pLevel);
      this.m_6034_(pX, pY, pZ);
      this.unstableJump();
      this.setFuse(200);
      this.f_19854_ = pX;
      this.f_19855_ = pY;
      this.f_19856_ = pZ;
   }

   private void unstableJump() {
      double d0 = this.f_19853_.f_46441_.m_188500_() * 3.141592653589793D * 2.0D;
      this.m_20334_(-Math.sin(d0) * 0.1D, 0.30000001192092896D, -Math.cos(d0) * 0.1D);
   }

   public void m_8119_() {
      if (!this.m_20068_()) {
         this.m_20256_(this.m_20184_().m_82520_(0.0D, -0.04D, 0.0D));
      }

      this.m_6478_(MoverType.SELF, this.m_20184_());
      this.m_20256_(this.m_20184_().m_82490_(0.98D));
      if (this.m_20096_()) {
         this.unstableJump();
      }

      int i = this.getFuse() - 1;
      this.setFuse(i);
      if (i <= 0) {
         this.m_146870_();
         if (!this.f_19853_.f_46443_) {
            this.explode();
         }
      } else {
         this.m_20073_();
         if (i % 10 != 0) {
            return;
         }

         TensuraParticleHelper.addServerParticlesAroundSelf(this, ParticleTypes.f_235898_);
         TensuraParticleHelper.addServerParticlesAroundSelf(this, (ParticleOptions)TensuraParticles.SOUL.get());
      }

   }

   protected void explode() {
      this.f_19853_.m_46511_(this, this.m_20185_(), this.m_20227_(0.0625D), this.m_20189_(), 10.0F, this.f_19853_.m_46469_().m_46207_(GameRules.f_46132_) ? BlockInteraction.DESTROY : BlockInteraction.NONE);
      Vec3 vec3 = this.m_20182_().m_82520_(0.0D, 1.0D, 0.0D);
      CharybdisEntity entity = new CharybdisEntity((EntityType)TensuraEntityTypes.CHARYBDIS.get(), this.f_19853_);
      entity.setSize(1.0F);
      entity.m_20219_(vec3);
      entity.m_6518_((ServerLevel)this.f_19853_, this.f_19853_.m_6436_(this.m_20183_()), MobSpawnType.EVENT, (SpawnGroupData)null, (CompoundTag)null);
      this.f_19853_.m_7967_(entity);
      TensuraParticleHelper.addServerParticlesAroundSelf(this, ParticleTypes.f_123812_, 4.0D);
      TensuraParticleHelper.addServerParticlesAroundSelf(this, ParticleTypes.f_123796_, 4.0D);
      TensuraParticleHelper.addServerParticlesAroundSelf(this, ParticleTypes.f_123747_, 4.0D);
      TensuraParticleHelper.addServerParticlesAroundSelf(this, ParticleTypes.f_123747_, 3.0D);
      TensuraParticleHelper.addServerParticlesAroundSelf(this, (ParticleOptions)TensuraParticles.SOLAR_FLASH.get(), 3.0D);
   }

   protected void m_8097_() {
      this.f_19804_.m_135372_(DATA_FUSE_ID, 80);
   }

   protected void m_7380_(CompoundTag pCompound) {
      pCompound.m_128376_("Fuse", (short)this.getFuse());
   }

   protected void m_7378_(CompoundTag pCompound) {
      this.setFuse(pCompound.m_128448_("Fuse"));
   }

   protected float m_6380_(Pose pPose, EntityDimensions pSize) {
      return 0.15F;
   }

   public void setFuse(int pLife) {
      this.f_19804_.m_135381_(DATA_FUSE_ID, pLife);
   }

   public int getFuse() {
      return (Integer)this.f_19804_.m_135370_(DATA_FUSE_ID);
   }

   public Packet<?> m_5654_() {
      return new ClientboundAddEntityPacket(this);
   }

   protected MovementEmission m_142319_() {
      return MovementEmission.NONE;
   }

   public boolean m_6087_() {
      return !this.m_213877_();
   }

   static {
      DATA_FUSE_ID = SynchedEntityData.m_135353_(PrimedCharybdisCoreEntity.class, EntityDataSerializers.f_135028_);
   }
}
