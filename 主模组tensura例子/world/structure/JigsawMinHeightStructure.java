package com.github.manasmods.tensura.world.structure;

import com.github.manasmods.tensura.registry.structure.TensuraStructureTypes;
import com.mojang.serialization.Codec;
import com.mojang.serialization.DataResult;
import com.mojang.serialization.codecs.RecordCodecBuilder;
import java.util.Optional;
import java.util.function.Function;
import net.minecraft.core.BlockPos;
import net.minecraft.core.Holder;
import net.minecraft.data.worldgen.Pools;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.world.level.ChunkPos;
import net.minecraft.world.level.levelgen.WorldGenerationContext;
import net.minecraft.world.level.levelgen.Heightmap.Types;
import net.minecraft.world.level.levelgen.heightproviders.HeightProvider;
import net.minecraft.world.level.levelgen.structure.Structure;
import net.minecraft.world.level.levelgen.structure.StructureType;
import net.minecraft.world.level.levelgen.structure.Structure.GenerationContext;
import net.minecraft.world.level.levelgen.structure.Structure.GenerationStub;
import net.minecraft.world.level.levelgen.structure.Structure.StructureSettings;
import net.minecraft.world.level.levelgen.structure.pools.JigsawPlacement;
import net.minecraft.world.level.levelgen.structure.pools.StructureTemplatePool;

public class JigsawMinHeightStructure extends Structure {
   public static final Codec<JigsawMinHeightStructure> CODEC = RecordCodecBuilder.mapCodec((instance) -> {
      return instance.group(m_226567_(instance), StructureTemplatePool.f_210555_.fieldOf("start_pool").forGetter((structure) -> {
         return structure.startPool;
      }), ResourceLocation.f_135803_.optionalFieldOf("start_jigsaw_name").forGetter((structure) -> {
         return structure.startJigsawName;
      }), Codec.intRange(0, 7).fieldOf("size").forGetter((structure) -> {
         return structure.maxDepth;
      }), HeightProvider.f_161970_.fieldOf("start_height").forGetter((structure) -> {
         return structure.startHeight;
      }), Codec.BOOL.fieldOf("use_expansion_hack").forGetter((structure) -> {
         return structure.useExpansionHack;
      }), Types.f_64274_.optionalFieldOf("project_start_to_heightmap").forGetter((structure) -> {
         return structure.projectStartToHeightmap;
      }), Codec.intRange(1, 256).fieldOf("max_distance_from_center").forGetter((structure) -> {
         return structure.maxDistanceFromCenter;
      }), Codec.INT.fieldOf("minY").orElse(0).forGetter((structure) -> {
         return structure.minY;
      })).apply(instance, JigsawMinHeightStructure::new);
   }).flatXmap(verifyRange(), verifyRange()).codec();
   private final Holder<StructureTemplatePool> startPool;
   private final Optional<ResourceLocation> startJigsawName;
   private final int maxDepth;
   private final HeightProvider startHeight;
   private final boolean useExpansionHack;
   private final Optional<Types> projectStartToHeightmap;
   private final int maxDistanceFromCenter;
   private final int minY;

   private static Function<JigsawMinHeightStructure, DataResult<JigsawMinHeightStructure>> verifyRange() {
      return (structure) -> {
         byte var10000;
         switch(structure.m_226620_()) {
         case NONE:
            var10000 = 0;
            break;
         case BURY:
         case BEARD_THIN:
         case BEARD_BOX:
            var10000 = 12;
            break;
         default:
            throw new IncompatibleClassChangeError();
         }

         int i = var10000;
         return structure.maxDistanceFromCenter + i > 256 ? DataResult.error("Structure size including terrain adaptation must not exceed 256") : DataResult.success(structure);
      };
   }

   public JigsawMinHeightStructure(StructureSettings settings, Holder<StructureTemplatePool> holder, Optional<ResourceLocation> location, int depth, HeightProvider height, boolean hack, Optional<Types> project, int distance, int minY) {
      super(settings);
      this.startPool = holder;
      this.startJigsawName = location;
      this.maxDepth = depth;
      this.startHeight = height;
      this.useExpansionHack = hack;
      this.projectStartToHeightmap = project;
      this.maxDistanceFromCenter = distance;
      this.minY = minY;
   }

   public Optional<GenerationStub> m_214086_(GenerationContext context) {
      ChunkPos chunkpos = context.f_226628_();
      int i = this.startHeight.m_213859_(context.f_226626_(), new WorldGenerationContext(context.f_226622_(), context.f_226629_()));
      BlockPos blockpos = new BlockPos(chunkpos.m_45604_(), i, chunkpos.m_45605_());
      if (this.projectStartToHeightmap.isPresent()) {
         Types heightType;
         switch((Types)this.projectStartToHeightmap.get()) {
         case WORLD_SURFACE_WG:
            heightType = Types.WORLD_SURFACE;
            break;
         case OCEAN_FLOOR_WG:
            heightType = Types.OCEAN_FLOOR;
            break;
         default:
            heightType = (Types)this.projectStartToHeightmap.get();
         }

         int y = context.f_226622_().m_223221_(blockpos.m_123341_(), blockpos.m_123343_(), heightType, context.f_226629_(), context.f_226624_());
         if (y < this.minY) {
            return Optional.empty();
         }
      }

      Pools.m_236490_();
      return JigsawPlacement.m_227238_(context, this.startPool, this.startJigsawName, this.maxDepth, blockpos, this.useExpansionHack, this.projectStartToHeightmap, this.maxDistanceFromCenter);
   }

   public StructureType<?> m_213658_() {
      return (StructureType)TensuraStructureTypes.JIGSAW_MIN_HEIGHT.get();
   }
}
