package com.github.manasmods.tensura.world.savedata;

import com.github.manasmods.tensura.capability.effects.TensuraEffectsCapability;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.level.saveddata.SavedData;
import net.minecraft.world.phys.Vec3;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

public class LabyrinthSaveData extends SavedData {
   private Vec3 entrancePos;
   private Vec3 fallOffPos;
   private Vec3 colossusPos;
   private Vec3 passedEntrance;
   private double startFallOffY;
   private boolean loaded;
   private boolean havingColossus;

   public static LabyrinthSaveData get(ServerLevel overworld) {
      return (LabyrinthSaveData)overworld.m_8895_().m_164861_(LabyrinthSaveData::new, LabyrinthSaveData::new, "tensura_labyrinth");
   }

   private LabyrinthSaveData() {
      this.entrancePos = new Vec3(42.029D, 42.0D, 17.295D);
      this.colossusPos = new Vec3(42.01D, 79.0D, 550.01D);
      this.passedEntrance = new Vec3(42.01D, 78.0D, 645.01D);
      this.fallOffPos = new Vec3(41.977D, 79.0D, 324.407D);
      this.startFallOffY = 25.0D;
      this.loaded = false;
      this.havingColossus = false;
   }

   private LabyrinthSaveData(CompoundTag tag) {
      this();
      this.loaded = tag.m_128471_("loaded");
      this.havingColossus = tag.m_128471_("hasColossus");
      this.entrancePos = new Vec3(tag.m_128459_("entranceX"), tag.m_128459_("entranceY"), tag.m_128459_("entranceZ"));
      this.colossusPos = new Vec3(tag.m_128459_("colossusX"), tag.m_128459_("colossusY"), tag.m_128459_("colossusZ"));
      this.passedEntrance = new Vec3(tag.m_128459_("passedEntranceX"), tag.m_128459_("passedEntranceY"), tag.m_128459_("passedEntranceZ"));
      this.fallOffPos = new Vec3(tag.m_128459_("fallOffX"), tag.m_128459_("fallOffY"), tag.m_128459_("fallOffZ"));
      this.startFallOffY = tag.m_128459_("startFallOffY");
   }

   @NotNull
   public CompoundTag m_7176_(CompoundTag tag) {
      tag.m_128379_("loaded", this.loaded);
      tag.m_128379_("hasColossus", this.havingColossus);
      tag.m_128347_("entranceX", this.entrancePos.f_82479_);
      tag.m_128347_("entranceY", this.entrancePos.f_82480_);
      tag.m_128347_("entranceZ", this.entrancePos.f_82481_);
      tag.m_128347_("colossusX", this.colossusPos.f_82479_);
      tag.m_128347_("colossusY", this.colossusPos.f_82480_);
      tag.m_128347_("colossusZ", this.colossusPos.f_82481_);
      tag.m_128347_("passedEntranceX", this.passedEntrance.f_82479_);
      tag.m_128347_("passedEntranceY", this.passedEntrance.f_82480_);
      tag.m_128347_("passedEntranceZ", this.passedEntrance.f_82481_);
      tag.m_128347_("fallOffX", this.fallOffPos.f_82479_);
      tag.m_128347_("fallOffY", this.fallOffPos.f_82480_);
      tag.m_128347_("fallOffZ", this.fallOffPos.f_82481_);
      tag.m_128347_("startFallOffY", this.startFallOffY);
      return tag;
   }

   public void setLoaded(boolean loaded) {
      this.loaded = loaded;
      this.m_77762_();
   }

   public void setHavingColossus(boolean havingColossus) {
      this.havingColossus = havingColossus;
      this.m_77762_();
   }

   public void setEntrancePos(Vec3 entrancePos) {
      this.entrancePos = entrancePos;
      this.m_77762_();
   }

   public void setColossusPos(Vec3 colossusPos) {
      this.colossusPos = colossusPos;
      this.m_77762_();
   }

   public void setPassedEntrance(Vec3 passedEntrance) {
      this.passedEntrance = passedEntrance;
      this.m_77762_();
   }

   public void setFallOffPos(Vec3 fallOffPos) {
      this.fallOffPos = fallOffPos;
      this.m_77762_();
   }

   public void setStartFallOffY(double startFallOffY) {
      this.startFallOffY = startFallOffY;
      this.m_77762_();
   }

   public static void addPassedEntity(LivingEntity entity, boolean won) {
      if (!entity.m_9236_().m_5776_()) {
         TensuraEffectsCapability.getFrom(entity).ifPresent((cap) -> {
            cap.setColossusStarted(false);
            cap.setColossusPassed(true);
            if (won) {
               cap.setColossusWon(true);
            }

         });
         TensuraEffectsCapability.sync(entity);
      }
   }

   public static void removePassedEntity(LivingEntity entity, boolean addStarted) {
      if (!entity.m_9236_().m_5776_()) {
         TensuraEffectsCapability.getFrom(entity).ifPresent((cap) -> {
            cap.setColossusPassed(false);
            if (addStarted) {
               cap.setColossusStarted(true);
            }

         });
         TensuraEffectsCapability.sync(entity);
      }
   }

   public static void removeStartedEntity(LivingEntity entity) {
      if (!entity.m_9236_().m_5776_()) {
         TensuraEffectsCapability.getFrom(entity).ifPresent((cap) -> {
            cap.setColossusStarted(false);
         });
         TensuraEffectsCapability.sync(entity);
      }
   }

   public static boolean isEntityPassedColossus(@Nullable Entity entity) {
      if (entity instanceof LivingEntity) {
         LivingEntity living = (LivingEntity)entity;
         return TensuraEffectsCapability.isColossusPassed(living);
      } else {
         return false;
      }
   }

   public Vec3 getEntrancePos() {
      return this.entrancePos;
   }

   public Vec3 getFallOffPos() {
      return this.fallOffPos;
   }

   public Vec3 getColossusPos() {
      return this.colossusPos;
   }

   public Vec3 getPassedEntrance() {
      return this.passedEntrance;
   }

   public double getStartFallOffY() {
      return this.startFallOffY;
   }

   public boolean isLoaded() {
      return this.loaded;
   }

   public boolean isHavingColossus() {
      return this.havingColossus;
   }
}
