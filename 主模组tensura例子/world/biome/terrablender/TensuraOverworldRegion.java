package com.github.manasmods.tensura.world.biome.terrablender;

import com.github.manasmods.tensura.registry.biome.TensuraBiomes;
import com.mojang.datafixers.util.Pair;
import java.util.function.Consumer;
import net.minecraft.core.Registry;
import net.minecraft.resources.ResourceKey;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.world.level.biome.Biome;
import net.minecraft.world.level.biome.Biomes;
import net.minecraft.world.level.biome.Climate.ParameterPoint;
import terrablender.api.Region;
import terrablender.api.RegionType;

public class TensuraOverworldRegion extends Region {
   public TensuraOverworldRegion() {
      super(new ResourceLocation("tensura", "overworld"), RegionType.OVERWORLD, 2);
   }

   public void addBiomes(Registry<Biome> registry, Consumer<Pair<ParameterPoint, ResourceKey<Biome>>> mapper) {
      this.addModifiedVanillaOverworldBiomes(mapper, (builder) -> {
         builder.replaceBiome(Biomes.f_48205_, TensuraBiomes.SAKURA_FOREST.getKey());
      });
   }
}
