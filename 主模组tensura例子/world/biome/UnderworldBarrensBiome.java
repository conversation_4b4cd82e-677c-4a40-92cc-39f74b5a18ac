package com.github.manasmods.tensura.world.biome;

import com.github.manasmods.manascore.api.world.gen.biome.BiomeGenerationSettingsHelper;
import com.github.manasmods.manascore.api.world.gen.biome.MobSpawnHelper;
import com.github.manasmods.tensura.registry.biome.TensuraPlacedFeatures;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import net.minecraft.core.Holder;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.MobCategory;
import net.minecraft.world.level.biome.Biome;
import net.minecraft.world.level.biome.Biome.BiomeBuilder;
import net.minecraft.world.level.biome.Biome.Precipitation;
import net.minecraft.world.level.biome.Biome.TemperatureModifier;
import net.minecraft.world.level.levelgen.GenerationStep.Decoration;
import net.minecraftforge.common.world.BiomeSpecialEffectsBuilder;

public class UnderworldBarrensBiome {
   public static Biome create() {
      BiomeGenerationSettingsHelper generationSettingsHelper = (new BiomeGenerationSettingsHelper()).addFeature(Decoration.LOCAL_MODIFICATIONS, (Holder)TensuraPlacedFeatures.HELL_BLOCK_BLOB.getHolder().get());
      MobSpawnHelper mobSpawnHelper = (new MobSpawnHelper()).addSpawn(MobCategory.CREATURE, (EntityType)TensuraEntityTypes.HOUND_DOG.get(), 1, 1, 2);
      BiomeSpecialEffectsBuilder effects = BiomeSpecialEffectsBuilder.create(1586208, 1, 1, 0);
      return (new BiomeBuilder()).m_47597_(Precipitation.NONE).m_47601_(generationSettingsHelper.finishBiomeSettings()).m_47605_(mobSpawnHelper.finishMobSpawnSettings()).m_47611_(0.0F).m_47609_(0.6F).m_47599_(TemperatureModifier.NONE).m_47603_(effects.m_48018_()).m_47592_();
   }
}
