package com.github.manasmods.tensura.world.tree.trunk;

import com.github.manasmods.tensura.registry.biome.TensuraTrunkPlacer;
import com.google.common.collect.ImmutableList;
import com.mojang.serialization.Codec;
import com.mojang.serialization.codecs.RecordCodecBuilder;
import java.util.List;
import java.util.function.BiConsumer;
import net.minecraft.core.BlockPos;
import net.minecraft.core.Direction;
import net.minecraft.util.RandomSource;
import net.minecraft.world.level.LevelSimulatedReader;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.levelgen.feature.configurations.TreeConfiguration;
import net.minecraft.world.level.levelgen.feature.foliageplacers.FoliagePlacer.FoliageAttachment;
import net.minecraft.world.level.levelgen.feature.trunkplacers.TrunkPlacer;
import net.minecraft.world.level.levelgen.feature.trunkplacers.TrunkPlacerType;

public class PalmTrunkPlacer extends TrunkPlacer {
   public static final Codec<PalmTrunkPlacer> CODEC = RecordCodecBuilder.create((p_70261_) -> {
      return m_70305_(p_70261_).apply(p_70261_, PalmTrunkPlacer::new);
   });

   public PalmTrunkPlacer(int pBaseHeight, int pHeightRandA, int pHeightRandB) {
      super(pBaseHeight, pHeightRandA, pHeightRandB);
   }

   protected TrunkPlacerType<?> m_7362_() {
      return (TrunkPlacerType)TensuraTrunkPlacer.PALM_TRUNK_PLACER.get();
   }

   public List<FoliageAttachment> m_213934_(LevelSimulatedReader pLevel, BiConsumer<BlockPos, BlockState> pBlockSetter, RandomSource pRandom, int pFreeTreeHeight, BlockPos pPos, TreeConfiguration pConfig) {
      m_226169_(pLevel, pBlockSetter, pRandom, pPos.m_7495_(), pConfig);
      int randomNumber = pRandom.m_188503_(4) + 1;
      Direction var10000;
      switch(randomNumber) {
      case 1:
         var10000 = Direction.NORTH;
         break;
      case 2:
         var10000 = Direction.EAST;
         break;
      case 3:
         var10000 = Direction.SOUTH;
         break;
      case 4:
         var10000 = Direction.WEST;
         break;
      default:
         throw new IllegalStateException("Calculated " + randomNumber + " but it should be between 1 and 4");
      }

      Direction treeDirection = var10000;
      int currentHeight = 1;
      BlockPos lastPos = this.placeAndContinue(pLevel, pBlockSetter, pRandom, pPos, pConfig);
      lastPos = this.placeAndContinue(pLevel, pBlockSetter, pRandom, lastPos.m_121945_(treeDirection), pConfig);
      BlockPos var10004 = lastPos.m_7494_();
      int currentHeight = currentHeight + 1;
      lastPos = this.placeAndContinueIf(pLevel, pBlockSetter, pRandom, var10004, pConfig, pFreeTreeHeight, currentHeight);
      lastPos = this.placeAndContinueIf(pLevel, pBlockSetter, pRandom, lastPos.m_7494_(), pConfig, pFreeTreeHeight, currentHeight++);
      lastPos = this.placeAndContinue(pLevel, pBlockSetter, pRandom, lastPos.m_121945_(treeDirection), pConfig);
      lastPos = this.placeAndContinueIf(pLevel, pBlockSetter, pRandom, lastPos.m_7494_(), pConfig, pFreeTreeHeight, currentHeight++);
      lastPos = this.placeAndContinueIf(pLevel, pBlockSetter, pRandom, lastPos.m_7494_(), pConfig, pFreeTreeHeight, currentHeight++);
      lastPos = this.placeAndContinueIf(pLevel, pBlockSetter, pRandom, lastPos.m_7494_(), pConfig, pFreeTreeHeight, currentHeight++);

      for(lastPos = this.placeAndContinue(pLevel, pBlockSetter, pRandom, lastPos.m_121945_(treeDirection), pConfig); !lastPos.equals(this.placeAndContinueIf(pLevel, pBlockSetter, pRandom, lastPos.m_7494_(), pConfig, pFreeTreeHeight, currentHeight)); ++currentHeight) {
      }

      return ImmutableList.of(new FoliageAttachment(lastPos.m_7494_().m_7494_(), 0, false));
   }

   private BlockPos placeAndContinue(LevelSimulatedReader pLevel, BiConsumer<BlockPos, BlockState> pBlockSetter, RandomSource pRandom, BlockPos pPos, TreeConfiguration pConfig) {
      this.m_226187_(pLevel, pBlockSetter, pRandom, pPos, pConfig);
      return pPos;
   }

   private BlockPos placeAndContinueIf(LevelSimulatedReader pLevel, BiConsumer<BlockPos, BlockState> pBlockSetter, RandomSource pRandom, BlockPos pPos, TreeConfiguration pConfig, int maxHeight, int allowedHeight) {
      return maxHeight >= allowedHeight ? this.placeAndContinue(pLevel, pBlockSetter, pRandom, pPos, pConfig) : pPos.m_7495_();
   }
}
