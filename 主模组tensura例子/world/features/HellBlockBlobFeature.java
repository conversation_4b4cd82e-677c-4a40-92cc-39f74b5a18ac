package com.github.manasmods.tensura.world.features;

import com.google.common.collect.ImmutableList;
import java.util.Iterator;
import net.minecraft.core.BlockPos;
import net.minecraft.util.RandomSource;
import net.minecraft.world.level.WorldGenLevel;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.levelgen.feature.Feature;
import net.minecraft.world.level.levelgen.feature.FeaturePlaceContext;
import net.minecraft.world.level.levelgen.feature.configurations.NoneFeatureConfiguration;

public class HellBlockBlobFeature extends Feature<NoneFeatureConfiguration> {
   private final ImmutableList<BlockState> BLOCKS;

   public HellBlockBlobFeature() {
      super(NoneFeatureConfiguration.f_67815_);
      this.BLOCKS = ImmutableList.of(Blocks.f_152496_.m_49966_(), Blocks.f_50652_.m_49966_(), Blocks.f_50069_.m_49966_(), Blocks.f_49994_.m_49966_());
   }

   public boolean m_142674_(FeaturePlaceContext<NoneFeatureConfiguration> context) {
      BlockPos pos = context.m_159777_();
      WorldGenLevel level = context.m_159774_();
      RandomSource random = context.m_225041_();

      for(boolean var5 = false; pos.m_123342_() > level.m_141937_() + 3 && (level.m_46859_(pos.m_7495_()) || !this.isValidPosition(level, pos.m_7495_())); pos = pos.m_7495_()) {
      }

      if (pos.m_123342_() <= level.m_141937_() + 3) {
         return false;
      } else {
         BlockState state = (BlockState)this.BLOCKS.get(random.m_216332_(0, this.BLOCKS.size() - 1));

         for(int i = 0; i < 3; ++i) {
            int i1 = random.m_188503_(2);
            int i2 = random.m_188503_(2);
            int i3 = random.m_188503_(2);
            float v = (float)(i1 + i2 + i3) * 0.333F + 0.5F;
            Iterator var11 = BlockPos.m_121940_(pos.m_7918_(-i1, -i2, -i3), pos.m_7918_(i1, i2, i3)).iterator();

            while(var11.hasNext()) {
               BlockPos blockPos = (BlockPos)var11.next();
               if (!(blockPos.m_123331_(pos) >= (double)(v * v))) {
                  level.m_7731_(blockPos, state, 4);
               }
            }

            pos = pos.m_7918_(-1 + random.m_188503_(2), -random.m_188503_(2), -1 + random.m_188503_(2));
         }

         return true;
      }
   }

   private boolean isValidPosition(WorldGenLevel level, BlockPos pos) {
      BlockState state = level.m_8055_(pos);
      return state.m_60713_(Blocks.f_50069_) || state.m_60713_(Blocks.f_50122_) || state.m_60713_(Blocks.f_50334_) || state.m_60713_(Blocks.f_50652_);
   }
}
