package com.github.manasmods.tensura.event;

import javax.annotation.Nullable;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraftforge.event.entity.living.LivingEvent;
import net.minecraftforge.eventbus.api.Cancelable;

@Cancelable
public class EnergyDrainEvent extends LivingEvent {
   @Nullable
   private final Entity attacker;
   private final EnergyDrainEvent.DrainType drainType;
   private boolean drainingMax;
   private double amount;
   private boolean percentage;

   public EnergyDrainEvent(LivingEntity target, @Nullable Entity entity, EnergyDrainEvent.DrainType type, double amount, boolean percentage, boolean drainingMax) {
      super(target);
      this.attacker = entity;
      this.drainType = type;
      this.amount = amount;
      this.percentage = percentage;
      this.drainingMax = drainingMax;
   }

   @Nullable
   public Entity getAttacker() {
      return this.attacker;
   }

   public EnergyDrainEvent.DrainType getDrainType() {
      return this.drainType;
   }

   public boolean isDrainingMax() {
      return this.drainingMax;
   }

   public double getAmount() {
      return this.amount;
   }

   public boolean isPercentage() {
      return this.percentage;
   }

   public void setAmount(double amount) {
      this.amount = amount;
   }

   public void setPercentage(boolean percentage) {
      this.percentage = percentage;
   }

   public static enum DrainType {
      MAGICULE,
      AURA,
      BOTH,
      EP;

      // $FF: synthetic method
      private static EnergyDrainEvent.DrainType[] $values() {
         return new EnergyDrainEvent.DrainType[]{MAGICULE, AURA, BOTH, EP};
      }
   }
}
