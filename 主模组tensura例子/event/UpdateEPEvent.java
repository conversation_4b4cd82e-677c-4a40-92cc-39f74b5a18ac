package com.github.manasmods.tensura.event;

import net.minecraft.world.entity.LivingEntity;
import net.minecraftforge.event.entity.living.LivingEvent;
import net.minecraftforge.eventbus.api.Cancelable;

@Cancelable
public class UpdateEPEvent extends LivingEvent {
   private double oldEP;
   private double newEP;

   public UpdateEPEvent(LivingEntity entity, double oldEP, double newEP) {
      super(entity);
      this.oldEP = oldEP;
      this.newEP = newEP;
   }

   public double getOldEP() {
      return this.oldEP;
   }

   public void setOldEP(double oldEP) {
      this.oldEP = oldEP;
   }

   public double getNewEP() {
      return this.newEP;
   }

   public void setNewEP(double newEP) {
      this.newEP = newEP;
   }
}
