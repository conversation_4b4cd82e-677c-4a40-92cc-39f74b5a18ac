package com.github.manasmods.tensura.event;

import javax.annotation.Nullable;
import net.minecraft.world.entity.Entity;
import net.minecraftforge.event.entity.EntityTeleportEvent;
import net.minecraftforge.eventbus.api.Cancelable;

@Cancelable
public class ForcedTeleportationEvent extends EntityTeleportEvent {
   @Nullable
   private final Entity teleporter;

   public ForcedTeleportationEvent(Entity target, @Nullable Entity entity, double targetX, double targetY, double targetZ) {
      super(target, targetX, targetY, targetZ);
      this.teleporter = entity;
   }

   @Nullable
   public Entity getTeleporter() {
      return this.teleporter;
   }
}
