package com.github.manasmods.tensura.event;

import javax.annotation.Nullable;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.item.ItemStack;
import net.minecraftforge.eventbus.api.Cancelable;
import net.minecraftforge.eventbus.api.Event;

@Cancelable
public class ItemDamageEvent extends Event {
   private ItemStack stack;
   private int amount;
   @Nullable
   private final ServerPlayer player;

   public ItemDamageEvent(ItemStack stack, int amount, @Nullable ServerPlayer player) {
      this.stack = stack;
      this.player = player;
      this.amount = amount;
   }

   public ItemStack getStack() {
      return this.stack;
   }

   public int getAmount() {
      return this.amount;
   }

   @Nullable
   public ServerPlayer getPlayer() {
      return this.player;
   }

   public void setStack(ItemStack stack) {
      this.stack = stack;
   }

   public void setAmount(int amount) {
      this.amount = amount;
   }
}
