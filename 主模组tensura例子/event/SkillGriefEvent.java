package com.github.manasmods.tensura.event;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import javax.annotation.Nullable;
import net.minecraft.core.BlockPos;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.eventbus.api.Cancelable;
import net.minecraftforge.eventbus.api.Event;

public class SkillGriefEvent extends Event {
   @Nullable
   private final Entity skillCaster;
   @Nullable
   private final ManasSkillInstance skillInstance;
   private BlockPos blockPos;

   public SkillGriefEvent(@Nullable Entity entity, @Nullable ManasSkillInstance skillInstance, double posX, double posY, double posZ) {
      this.skillCaster = entity;
      this.skillInstance = skillInstance;
      this.blockPos = new BlockPos(posX, posY, posZ);
   }

   public SkillGriefEvent(@Nullable Entity entity, @Nullable ManasSkillInstance skillInstance, Vec3 position) {
      this.skillCaster = entity;
      this.skillInstance = skillInstance;
      this.blockPos = new BlockPos(position);
   }

   public SkillGriefEvent(@Nullable Entity entity, @Nullable ManasSkillInstance skillInstance, BlockPos pos) {
      this.skillCaster = entity;
      this.skillInstance = skillInstance;
      this.blockPos = pos;
   }

   @Nullable
   public Entity getSkillCaster() {
      return this.skillCaster;
   }

   @Nullable
   public ManasSkillInstance getSkillInstance() {
      return this.skillInstance;
   }

   public BlockPos getBlockPos() {
      return this.blockPos;
   }

   public void setBlockPos(BlockPos blockPos) {
      this.blockPos = blockPos;
   }

   public static class Post extends SkillGriefEvent {
      public Post(@Nullable Entity entity, @Nullable ManasSkillInstance skillInstance, double posX, double posY, double posZ) {
         super(entity, skillInstance, posX, posY, posZ);
      }

      public Post(@Nullable Entity entity, @Nullable ManasSkillInstance skillInstance, BlockPos position) {
         super(entity, skillInstance, position);
      }
   }

   @Cancelable
   public static class Pre extends SkillGriefEvent {
      public Pre(@Nullable Entity entity, @Nullable ManasSkillInstance skillInstance, double posX, double posY, double posZ) {
         super(entity, skillInstance, posX, posY, posZ);
      }

      public Pre(@Nullable Entity entity, @Nullable ManasSkillInstance skillInstance, BlockPos position) {
         super(entity, skillInstance, position);
      }
   }
}
