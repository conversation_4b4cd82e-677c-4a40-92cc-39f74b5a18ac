package com.github.manasmods.tensura.event;

import javax.annotation.Nullable;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraftforge.event.entity.living.LivingEvent;
import net.minecraftforge.eventbus.api.Cancelable;

@Cancelable
public class PossessionEvent extends LivingEvent {
   @Nullable
   private final Entity attacker;

   public PossessionEvent(LivingEntity target, @Nullable Entity entity) {
      super(target);
      this.attacker = entity;
   }

   @Nullable
   public Entity getAttacker() {
      return this.attacker;
   }
}
