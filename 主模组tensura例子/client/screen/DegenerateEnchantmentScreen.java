package com.github.manasmods.tensura.client.screen;

import com.github.manasmods.manascore.api.client.gui.widget.ImagePredicateButton;
import com.github.manasmods.tensura.client.TensuraGUIHelper;
import com.github.manasmods.tensura.menu.DegenerateEnchantmentMenu;
import com.github.manasmods.tensura.network.TensuraNetwork;
import com.github.manasmods.tensura.network.play2server.RequestDisenchantPacket;
import com.github.manasmods.tensura.network.play2server.RequestMenuSwitchPacket;
import com.mojang.blaze3d.platform.InputConstants;
import com.mojang.blaze3d.systems.RenderSystem;
import com.mojang.blaze3d.vertex.PoseStack;
import java.awt.Color;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import net.minecraft.ChatFormatting;
import net.minecraft.client.Minecraft;
import net.minecraft.client.gui.components.Button.OnPress;
import net.minecraft.client.gui.components.Button.OnTooltip;
import net.minecraft.client.gui.screens.inventory.AbstractContainerScreen;
import net.minecraft.client.renderer.GameRenderer;
import net.minecraft.client.resources.sounds.SimpleSoundInstance;
import net.minecraft.network.chat.Component;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.util.Mth;
import net.minecraft.world.entity.player.Inventory;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Items;
import net.minecraft.world.item.enchantment.Enchantment;

public class DegenerateEnchantmentScreen extends AbstractContainerScreen<DegenerateEnchantmentMenu> {
   private static final ResourceLocation BACKGROUND = new ResourceLocation("tensura", "textures/gui/degenerate/degenerate_tab_2.png");
   private static final ResourceLocation ENCHANTMENT_BUTTON = new ResourceLocation("tensura", "textures/gui/skill_button.png");
   private static final ResourceLocation SCROLL_BAR = new ResourceLocation("tensura", "textures/gui/scroll_bar.png");
   private static final ResourceLocation BLANK = new ResourceLocation("tensura", "textures/blank_texture.png");
   private List<Enchantment> enchantments = new ArrayList();
   private List<Integer> levels = new ArrayList();
   private boolean scrolling;
   private float scrollOffs;
   private int startIndex;

   public DegenerateEnchantmentScreen(DegenerateEnchantmentMenu pMenu, Inventory pPlayerInventory, Component pTitle) {
      super(pMenu, pPlayerInventory, pTitle.m_6881_().m_130940_(ChatFormatting.WHITE));
      pMenu.registerUpdateListener(this::containerChanged);
      this.f_97726_ = 210;
      this.f_97727_ = 199;
   }

   protected void m_7856_() {
      super.m_7856_();
      this.scrollOffs = 0.0F;
      this.startIndex = 0;
      ResourceLocation var10006 = BLANK;
      OnPress var10007 = (pButton) -> {
         TensuraNetwork.INSTANCE.sendToServer(new RequestMenuSwitchPacket(RequestMenuSwitchPacket.SwitchType.DEGENERATE_TAB_2_TO_TAB_1));
      };
      OnTooltip var10008 = (button, poseStack, i, i1) -> {
         this.m_96602_(poseStack, Component.m_237115_("tooltip.tensura.degenerate_menu.tab_1"), i, i1);
      };
      DegenerateEnchantmentMenu var10009 = (DegenerateEnchantmentMenu)this.f_97732_;
      Objects.requireNonNull(var10009);
      ImagePredicateButton tabSwitch = new ImagePredicateButton(0, 0, 26, 22, var10006, var10007, var10008, var10009::check);
      tabSwitch.f_93620_ = this.getGuiLeft() + 5;
      tabSwitch.f_93621_ = this.getGuiTop() + 2;
      this.m_142416_(tabSwitch);
   }

   public void m_6305_(PoseStack pPoseStack, int pMouseX, int pMouseY, float pPartialTick) {
      super.m_6305_(pPoseStack, pMouseX, pMouseY, pPartialTick);
      this.m_7025_(pPoseStack, pMouseX, pMouseY);
   }

   protected void m_7027_(PoseStack pPoseStack, int pMouseX, int pMouseY) {
      this.f_96547_.m_92889_(pPoseStack, Component.m_237115_("tensura.degenerate_menu.synthesis"), 10.0F, (float)this.f_97729_ + 28.0F, (new Color(225, 225, 225)).getRGB());
      this.f_96547_.m_92889_(pPoseStack, Component.m_237115_("tensura.degenerate_menu.separation"), 10.0F, (float)this.f_97729_ + 48.0F, (new Color(225, 225, 225)).getRGB());
      this.f_96547_.m_92889_(pPoseStack, this.f_169604_, 80.0F, (float)this.f_97731_ + 35.0F, (new Color(63, 63, 64)).getRGB());
   }

   protected void m_7286_(PoseStack pPoseStack, float pPartialTick, int pMouseX, int pMouseY) {
      this.m_7333_(pPoseStack);
      RenderSystem.m_157427_(GameRenderer::m_172817_);
      RenderSystem.m_157429_(1.0F, 1.0F, 1.0F, 1.0F);
      RenderSystem.m_157456_(0, BACKGROUND);
      int x = (this.f_96543_ - this.f_97726_) / 2;
      int y = (this.f_96544_ - this.f_97727_) / 2;
      this.m_93228_(pPoseStack, x, y, 0, 0, this.f_97726_, this.f_97727_);
      int k = (int)(26.0F * this.scrollOffs);
      RenderSystem.m_157456_(0, SCROLL_BAR);
      m_93133_(pPoseStack, this.f_97735_ + 161, this.f_97736_ + 63 + k, 0.0F, this.isScrollBarActive() ? 13.0F : 0.0F, 10, 13, 10, 26);
      int lastVisibleElementIndex = this.startIndex + 3;
      this.enchantments = ((DegenerateEnchantmentMenu)this.f_97732_).getInputEnchantments().keySet().stream().toList();
      this.levels = ((DegenerateEnchantmentMenu)this.f_97732_).getInputEnchantments().values().stream().toList();
      this.renderButtons(pPoseStack, pMouseX, pMouseY, lastVisibleElementIndex);
      this.renderTabIcons();
   }

   private void renderButtons(PoseStack pPoseStack, int pMouseX, int pMouseY, int pLastVisibleElementIndex) {
      for(int i = this.startIndex; i < pLastVisibleElementIndex && i < this.enchantments.size(); ++i) {
         int x = this.getGuiLeft() + 69;
         int y = this.getGuiTop() + 63 + (i - this.startIndex) * 13;
         int offset = 0;
         boolean hovering = pMouseX >= x && pMouseY >= y && pMouseX < x + 89 && pMouseY < y + 13;
         if (hovering) {
            offset = 13;
         }

         RenderSystem.m_157456_(0, ENCHANTMENT_BUTTON);
         m_93133_(pPoseStack, x, y, 0.0F, (float)offset, 89, 13, 89, 26);
         Enchantment enchantment = (Enchantment)this.enchantments.get(i);
         Component name = enchantment.m_44700_((Integer)this.levels.get(i));
         if (!enchantment.m_6589_()) {
            name = ((Component)name).m_6881_().m_130940_(ChatFormatting.WHITE);
         }

         TensuraGUIHelper.renderScaledShadowText(pPoseStack, this.f_96547_, TensuraGUIHelper.shortenTextComponent((Component)name, 16), (float)(this.getGuiLeft() + 72), (float)(this.getGuiTop() + 67 + (i - this.startIndex) * 13), 84.0F, 9.0F, Color.WHITE.getRGB(), 2.0F, 0.01F);
         if (hovering) {
            this.m_96602_(pPoseStack, (Component)name, pMouseX, pMouseY);
         }
      }

   }

   private void renderTabIcons() {
      if (this.f_96541_ != null) {
         this.f_96541_.m_91291_().m_115203_(new ItemStack(Items.f_41960_), this.getGuiLeft() + 10, this.getGuiTop() + 7);
         this.f_96541_.m_91291_().m_115203_(new ItemStack(Items.f_42690_), this.getGuiLeft() + 38, this.getGuiTop() + 6);
      }
   }

   protected void m_7025_(PoseStack pPoseStack, int pMouseX, int pMouseY) {
      if (((DegenerateEnchantmentMenu)this.f_97732_).m_142621_().m_41619_() && this.f_97734_ != null && this.f_97734_.m_6657_()) {
         this.m_6057_(pPoseStack, this.f_97734_.m_7993_(), pMouseX, pMouseY);
      }

   }

   public boolean m_6375_(double pMouseX, double pMouseY, int pButton) {
      this.scrolling = false;
      int lastDisplayedRecipeIndex = this.startIndex + 3;

      for(int i = this.startIndex; i < lastDisplayedRecipeIndex; ++i) {
         int x = this.getGuiLeft() + 69;
         int y = this.getGuiTop() + 63 + (i - this.startIndex) * 13;
         if (this.enchantments.size() <= i) {
            break;
         }

         if (pMouseX >= (double)x && pMouseY >= (double)y && pMouseX < (double)(x + 89) && pMouseY < (double)(y + 13)) {
            Minecraft.m_91087_().m_91106_().m_120367_(SimpleSoundInstance.m_119752_(SoundEvents.f_11887_, 1.0F));
            TensuraNetwork.INSTANCE.sendToServer(new RequestDisenchantPacket((Enchantment)this.enchantments.get(i), (Integer)this.levels.get(i)));
            return true;
         }
      }

      if (pMouseX >= (double)(this.getGuiLeft() + 161) && pMouseX < (double)(this.getGuiLeft() + 172) && pMouseY >= (double)(this.getGuiTop() + 63) && pMouseY < (double)(this.getGuiTop() + 102)) {
         this.scrolling = true;
      }

      return super.m_6375_(pMouseX, pMouseY, pButton);
   }

   public boolean m_6050_(double pMouseX, double pMouseY, double pDelta) {
      if (this.isScrollBarActive()) {
         int i = this.getOffscreenRows();
         float f = (float)pDelta / (float)i;
         this.scrollOffs = Mth.m_14036_(this.scrollOffs - f, 0.0F, 1.0F);
         this.startIndex = Math.max(0, Math.min(this.startIndex - (int)pDelta, this.enchantments.size() - 3));
      }

      return true;
   }

   public boolean m_7979_(double pMouseX, double pMouseY, int pButton, double pDragX, double pDragY) {
      if (this.scrolling && this.isScrollBarActive()) {
         int i = this.getGuiTop() + 63;
         int j = i + 34;
         this.scrollOffs = (float)((pMouseY - (double)i - 6.5D) / (double)((float)(j - i) - 13.0F));
         this.scrollOffs = Mth.m_14036_(this.scrollOffs, 0.0F, 1.0F);
         this.startIndex = (int)((double)(this.scrollOffs * (float)this.getOffscreenRows()) + 0.5D);
         return true;
      } else {
         return super.m_7979_(pMouseX, pMouseY, pButton, pDragX, pDragY);
      }
   }

   private boolean isScrollBarActive() {
      return this.enchantments.size() > 3;
   }

   private int getOffscreenRows() {
      return this.enchantments.size() - 3;
   }

   private void containerChanged() {
      this.scrollOffs = 0.0F;
      this.startIndex = 0;
   }

   public boolean m_7933_(int pKeyCode, int pScanCode, int pModifiers) {
      return this.f_96541_ != null && this.f_96541_.f_91066_.f_92092_.isActiveAndMatches(InputConstants.m_84827_(pKeyCode, pScanCode)) ? true : super.m_7933_(pKeyCode, pScanCode, pModifiers);
   }
}
