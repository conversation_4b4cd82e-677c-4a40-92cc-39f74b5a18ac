package com.github.manasmods.tensura.client.screen;

import com.github.manasmods.manascore.api.client.gui.widget.ImagePredicateButton;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.client.TensuraGUIHelper;
import com.github.manasmods.tensura.menu.GreatSageRefiningMenu;
import com.github.manasmods.tensura.network.TensuraNetwork;
import com.github.manasmods.tensura.network.play2server.RequestMenuSwitchPacket;
import com.mojang.blaze3d.platform.InputConstants;
import com.mojang.blaze3d.systems.RenderSystem;
import com.mojang.blaze3d.vertex.PoseStack;
import java.awt.Color;
import java.util.Objects;
import net.minecraft.client.gui.components.Button.OnPress;
import net.minecraft.client.gui.components.Button.OnTooltip;
import net.minecraft.client.gui.screens.inventory.AbstractContainerScreen;
import net.minecraft.client.renderer.GameRenderer;
import net.minecraft.network.chat.Component;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.world.entity.player.Inventory;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Items;

public class GreatSageRefiningScreen extends AbstractContainerScreen<GreatSageRefiningMenu> {
   private static final ResourceLocation BACKGROUND = new ResourceLocation("tensura", "textures/gui/great_sage/great_sage_refining.png");
   private static final ResourceLocation REPEAT = new ResourceLocation("tensura", "textures/gui/great_sage/repeat_button.png");
   private static final ResourceLocation BREW = new ResourceLocation("tensura", "textures/gui/great_sage/brew_start.png");
   private static final ResourceLocation BLANK = new ResourceLocation("tensura", "textures/blank_texture.png");
   private final Player player;
   private final GreatSageRefiningMenu craftingMenu;

   public GreatSageRefiningScreen(GreatSageRefiningMenu pMenu, Inventory pPlayerInventory) {
      super(pMenu, pPlayerInventory, Component.m_237115_("tooltip.tensura.great_sage_menu.refining"));
      this.player = pPlayerInventory.f_35978_;
      this.craftingMenu = pMenu;
      this.f_97726_ = 249;
      this.f_97727_ = 192;
   }

   protected void m_7856_() {
      super.m_7856_();
      ResourceLocation var10006 = BLANK;
      OnPress var10007 = (pButton) -> {
         TensuraNetwork.INSTANCE.sendToServer(new RequestMenuSwitchPacket(RequestMenuSwitchPacket.SwitchType.GREAT_SAGE_TAB_2_TO_TAB_1));
      };
      OnTooltip var10008 = (button, poseStack, i, i1) -> {
         this.m_96602_(poseStack, Component.m_237115_("tooltip.tensura.great_sage_menu.crafting"), i, i1);
      };
      GreatSageRefiningMenu var10009 = (GreatSageRefiningMenu)this.f_97732_;
      Objects.requireNonNull(var10009);
      ImagePredicateButton tabSwitch = new ImagePredicateButton(0, 0, 26, 22, var10006, var10007, var10008, var10009::check);
      tabSwitch.f_93620_ = this.getGuiLeft() + 5;
      tabSwitch.f_93621_ = this.getGuiTop() + 2;
      this.m_142416_(tabSwitch);
      var10006 = BREW;
      var10007 = (pButton) -> {
         TensuraGUIHelper.buttonClick(this.player, this, (SoundEvent)null, 2);
      };
      var10008 = (button, poseStack, i, i1) -> {
         this.m_96602_(poseStack, Component.m_237115_("tooltip.tensura.great_sage_menu.brew"), i, i1);
      };
      var10009 = (GreatSageRefiningMenu)this.f_97732_;
      Objects.requireNonNull(var10009);
      ImagePredicateButton brew = new ImagePredicateButton(0, 0, 12, 12, var10006, var10007, var10008, var10009::check);
      brew.f_93620_ = this.getGuiLeft() + 182;
      brew.f_93621_ = this.getGuiTop() + 58;
      this.m_142416_(brew);
   }

   public void m_6305_(PoseStack pPoseStack, int pMouseX, int pMouseY, float pPartialTick) {
      this.m_7333_(pPoseStack);
      super.m_6305_(pPoseStack, pMouseX, pMouseY, pPartialTick);
      this.m_7025_(pPoseStack, pMouseX, pMouseY);
      if (this.f_96541_ != null) {
         this.f_96541_.m_91291_().m_115203_(new ItemStack(Items.f_41960_), this.getGuiLeft() + 10, this.getGuiTop() + 7);
         this.f_96541_.m_91291_().m_115203_(new ItemStack(Items.f_42543_), this.getGuiLeft() + 38, this.getGuiTop() + 6);
      }
   }

   protected void m_7027_(PoseStack pPoseStack, int pMouseX, int pMouseY) {
      TensuraGUIHelper.renderCenteredXText(this.f_96547_, pPoseStack, this.f_96539_, 0, this.f_97729_ + 25, 249, new Color(225, 225, 225), false);
      TensuraGUIHelper.renderCenteredXText(this.f_96547_, pPoseStack, this.f_169604_, 0, this.f_97731_ + 26, 249, new Color(63, 63, 64), false);
   }

   protected void m_7286_(PoseStack pPoseStack, float pPartialTick, int pX, int pY) {
      RenderSystem.m_157427_(GameRenderer::m_172817_);
      RenderSystem.m_157429_(1.0F, 1.0F, 1.0F, 1.0F);
      RenderSystem.m_157456_(0, BACKGROUND);
      int x = (this.f_96543_ - this.f_97726_) / 2;
      int y = (this.f_96544_ - this.f_97727_) / 2;
      this.m_93228_(pPoseStack, x, y, 0, 0, this.f_97726_, this.f_97727_);
      ManasSkillInstance instance = this.craftingMenu.getSkillInstance(this.player);
      if (instance != null && instance.getOrCreateTag().m_128471_("Brewing")) {
         RenderSystem.m_157456_(0, BACKGROUND);
         m_93133_(pPoseStack, x + 7, y + 35, 1.0F, 193.0F, 204, 58, 256, 256);
      }

      RenderSystem.m_157456_(0, REPEAT);
      if (instance != null && instance.getOrCreateTag().m_128471_("RepeatBrewing")) {
         if (TensuraGUIHelper.mouseOver(pX, pY, this.getGuiLeft() + 221, this.getGuiLeft() + 233, this.getGuiTop() + 83, this.getGuiTop() + 95)) {
            m_93133_(pPoseStack, x + 220, y + 82, 14.0F, 14.0F, 14, 14, 28, 28);
         } else {
            m_93133_(pPoseStack, x + 220, y + 82, 0.0F, 14.0F, 14, 14, 28, 28);
         }
      } else if (TensuraGUIHelper.mouseOver(pX, pY, this.getGuiLeft() + 221, this.getGuiLeft() + 233, this.getGuiTop() + 83, this.getGuiTop() + 95)) {
         m_93133_(pPoseStack, x + 220, y + 82, 14.0F, 0.0F, 14, 14, 28, 28);
      }

   }

   protected void m_7025_(PoseStack pPoseStack, int pX, int pY) {
      if (TensuraGUIHelper.mouseOver(pX, pY, this.getGuiLeft() + 221, this.getGuiLeft() + 233, this.getGuiTop() + 83, this.getGuiTop() + 95)) {
         this.m_96602_(pPoseStack, Component.m_237115_("tooltip.tensura.great_sage_menu.automate"), pX, pY);
      }

      super.m_7025_(pPoseStack, pX, pY);
   }

   public boolean m_7933_(int pKeyCode, int pScanCode, int pModifiers) {
      if (this.f_96541_ != null) {
         if (this.f_96541_.f_91066_.f_92092_.isActiveAndMatches(InputConstants.m_84827_(pKeyCode, pScanCode))) {
            return true;
         }

         if (this.f_96541_.f_91066_.f_92093_.isActiveAndMatches(InputConstants.m_84827_(pKeyCode, pScanCode))) {
            return true;
         }
      }

      return super.m_7933_(pKeyCode, pScanCode, pModifiers);
   }

   public boolean m_6375_(double pMouseX, double pMouseY, int pButton) {
      return TensuraGUIHelper.mouseOver(pMouseX, pMouseY, this.getGuiLeft() + 221, this.getGuiLeft() + 233, this.getGuiTop() + 83, this.getGuiTop() + 95) ? TensuraGUIHelper.buttonClick(this.player, this, SoundEvents.f_12490_, 1) : super.m_6375_(pMouseX, pMouseY, pButton);
   }
}
