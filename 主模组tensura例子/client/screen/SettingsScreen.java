package com.github.manasmods.tensura.client.screen;

import com.github.manasmods.tensura.client.TensuraGUIHelper;
import com.github.manasmods.tensura.client.screen.widget.SettingsButton;
import com.github.manasmods.tensura.client.screen.widget.SettingsEditBox;
import com.github.manasmods.tensura.client.screen.widget.SettingsOption;
import com.github.manasmods.tensura.config.client.DisplayConfig;
import com.github.manasmods.tensura.config.client.TensuraClientConfig;
import com.github.manasmods.tensura.network.TensuraNetwork;
import com.github.manasmods.tensura.network.play2server.GUISwitchPacket;
import com.github.manasmods.tensura.util.SimpleScreen;
import com.mojang.blaze3d.systems.RenderSystem;
import com.mojang.blaze3d.vertex.PoseStack;
import com.mojang.datafixers.util.Pair;
import java.awt.Color;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Consumer;
import java.util.function.Supplier;
import net.minecraft.ChatFormatting;
import net.minecraft.client.Minecraft;
import net.minecraft.client.gui.components.Button;
import net.minecraft.client.gui.components.Widget;
import net.minecraft.client.gui.components.Button.OnPress;
import net.minecraft.client.gui.components.events.GuiEventListener;
import net.minecraft.client.renderer.GameRenderer;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.MutableComponent;
import net.minecraftforge.client.event.ScreenEvent.BackgroundRendered;
import net.minecraftforge.common.MinecraftForge;
import org.jetbrains.annotations.Nullable;

public class SettingsScreen extends SimpleScreen {
   private DisplayConfig cfg;
   private List<Pair<SettingsOption, Widget>> settings;
   private SettingsButton nextPage;
   private SettingsButton previousPage;
   private SettingsButton resetAll;
   private SettingsButton returnToMenu;
   private SettingsEditBox editBoxFocused;
   private int screenWidth;
   private int screenHeight;
   private int posY;
   private int tempX;
   private int tempY;
   private int startIndex = 0;
   private int actualEndIndex;
   private int endIndex;
   private int pageNumber;
   private int totalPages;
   private int hudVariant;
   private int appraisalOpacity;
   private boolean resetWarned;
   private boolean titleScreen;
   private boolean arachnophobia;
   private boolean leftSideStats;
   private boolean flippedSkills;
   private boolean leftSideAnalysis;
   private double hudSize;
   private final int itemsPerPage = 5;
   private static int hudX;
   private static int hudY;
   private static int skillsX;
   private static int skillsY;
   private static boolean editingStatPositions;
   private static boolean editingSkillPositions;

   public SettingsScreen() {
      super(Component.m_237119_(), Minecraft.m_91087_().m_91268_().m_85445_(), Minecraft.m_91087_().m_91268_().m_85446_());
      this.endIndex = this.itemsPerPage;
      this.pageNumber = 1;
      this.editBoxFocused = null;
      this.resetWarned = false;
   }

   public void m_7856_() {
      super.m_7856_();
      this.cfg = TensuraClientConfig.INSTANCE.displayConfig;
      this.screenWidth = Minecraft.m_91087_().m_91268_().m_85445_();
      this.screenHeight = Minecraft.m_91087_().m_91268_().m_85446_();
      this.guiLeft = 0;
      this.guiRight = this.screenWidth;
      this.guiTop = 0;
      this.guiBottom = this.screenHeight;
      this.guiCenterX = this.guiRight / 2;
      this.guiCenterY = this.guiBottom / 2;
      this.resetEverything(false);
      this.totalPages = (int)Math.ceil((double)this.settings.size() / (double)this.itemsPerPage);

      int i;
      for(i = 0; i < this.settings.size(); i += this.itemsPerPage) {
      }

      this.actualEndIndex = i;
      this.nextPage = this.simpleButton(this.guiCenterX + 20, this.screenHeight - 75, 10, Component.m_237113_(">"), (onPress) -> {
         if (this.pageNumber >= this.totalPages) {
            this.pageNumber = 1;
            this.startIndex = 0;
            this.endIndex = this.itemsPerPage;
         } else {
            ++this.pageNumber;
            this.startIndex += this.itemsPerPage;
            this.endIndex += this.itemsPerPage;
         }

         this.switchStatesForButtons();
      });
      this.previousPage = this.simpleButton(this.guiCenterX - 30, this.screenHeight - 75, 10, Component.m_237113_("<"), (onPress) -> {
         if (this.pageNumber <= 1) {
            this.pageNumber = this.totalPages;
            this.endIndex = this.actualEndIndex;
         } else {
            --this.pageNumber;
            this.endIndex -= this.itemsPerPage;
         }

         this.startIndex = Math.max(this.endIndex - this.itemsPerPage, 0);
         this.switchStatesForButtons();
      });
      this.resetAll = this.simpleButton(this.guiRight - 240, this.screenHeight - 25, () -> {
         return this.resetWarned ? Component.m_237115_("tensura.settings.reset.warn").m_130940_(ChatFormatting.DARK_RED) : Component.m_237115_("tensura.settings.reset").m_130940_(ChatFormatting.RED);
      }, (onPress) -> {
         if (this.resetWarned) {
            DisplayConfig.resetEverything();
            this.resetEverything(true);
            this.resetWarned = false;
         } else {
            this.resetWarned = true;
         }

      });
      this.returnToMenu = this.simpleButton(this.guiRight - 120, this.screenHeight - 25, 100, Component.m_237115_("tooltip.tensura.return"), (onPress) -> {
         TensuraNetwork.INSTANCE.sendToServer(new GUISwitchPacket(0, true));
      });
      this.m_142416_(this.nextPage);
      this.m_142416_(this.previousPage);
      this.m_142416_(this.resetAll);
      this.m_142416_(this.returnToMenu);
   }

   protected void renderWidgets(PoseStack pPoseStack, int pMouseX, int pMouseY, float pPartialTick) {
      if (!editingStatPositions && !editingSkillPositions) {
         if (this.editBoxFocused != null) {
            this.editBoxFocused.m_6305_(pPoseStack, pMouseX, pMouseY, pPartialTick);
         } else {
            super.renderWidgets(pPoseStack, pMouseX, pMouseY, pPartialTick);
         }
      }
   }

   protected void renderBg(PoseStack pPoseStack, int pMouseX, int pMouseY, float pPartialTick) {
      this.poseStack = pPoseStack;
      if (this.editBoxFocused == null && !editingStatPositions && !editingSkillPositions) {
         this.m_7333_(this.poseStack);
         RenderSystem.m_157427_(GameRenderer::m_172817_);
         RenderSystem.m_157429_(1.0F, 1.0F, 1.0F, 1.0F);
         this.m_93179_(this.poseStack, 0, this.screenHeight - 50, this.screenWidth, this.screenHeight, -1072689136, -804253680);
         MinecraftForge.EVENT_BUS.post(new BackgroundRendered(this, this.poseStack));
         int iteration = 0;
         int posY = this.guiTop + 26;

         for(int optionIndex = this.startIndex; optionIndex < this.endIndex && optionIndex < this.settings.size(); ++optionIndex) {
            ++iteration;
            SettingsOption option = (SettingsOption)((Pair)this.settings.get(optionIndex)).getFirst();
            if (TensuraGUIHelper.mouseOver(this.mouseX, this.mouseY, this.guiLeft + 39, this.guiRight - 20, this.guiTop + 19 + 30 * (iteration - 1), this.guiTop + 40 + 30 * (iteration - 1))) {
               this.m_93179_(this.poseStack, this.guiLeft + 40, this.guiTop + 20 + 30 * (iteration - 1), this.guiRight - 20, this.guiTop + 40 + 30 * (iteration - 1), -1072689136, -804253680);
               option.renderDescription(this.f_96547_, this.poseStack, this.guiLeft + 20, this.screenHeight - 40);
            }

            option.renderText(this.f_96547_, this.poseStack, this.guiLeft + 60, posY);
            posY += 30;
            Widget widget = (Widget)((Pair)this.settings.get(optionIndex)).getSecond();
            if (widget != null) {
               widget.m_6305_(this.poseStack, pMouseX, pMouseY, pPartialTick);
            }
         }

         this.f_96547_.m_92883_(this.poseStack, String.format("%s / %s", this.pageNumber, this.totalPages), (float)(this.guiCenterX - 12), (float)(this.screenHeight - 69), Color.WHITE.getRGB());
         this.renderTooltip(this.poseStack, pMouseX, pMouseY);
      }
   }

   public boolean m_7043_() {
      return true;
   }

   public void m_7522_(@Nullable GuiEventListener pListener) {
      super.m_7522_(pListener);
      if (pListener instanceof SettingsEditBox) {
         SettingsEditBox editBox = (SettingsEditBox)pListener;
         this.editBoxFocused = editBox;
         this.editBoxFocused.moveBox(true);
         this.switchStatesForGeneral(false);
      }

   }

   public boolean m_7979_(double pMouseX, double pMouseY, int pButton, double pDragX, double pDragY) {
      if (editingStatPositions) {
         hudX = (int)pMouseX;
         hudY = (int)pMouseY;
      } else if (editingSkillPositions) {
         skillsX = (int)pMouseX;
         skillsY = (int)pMouseY;
      }

      return super.m_7979_(pMouseX, pMouseY, pButton, pDragX, pDragY);
   }

   public boolean m_6375_(double pMouseX, double pMouseY, int pButton) {
      if (this.editBoxFocused != null && !this.editBoxFocused.m_5953_(pMouseX, pMouseY)) {
         this.editBoxFocused.m_94178_(false);
         this.editBoxFocused = null;
         this.switchStatesForGeneral(true);
         return true;
      } else if (editingStatPositions && pButton == 0) {
         hudX = (int)pMouseX;
         hudY = (int)pMouseY;
         return true;
      } else if (editingSkillPositions) {
         if (pButton == 0) {
            skillsX = (int)pMouseX;
            skillsY = (int)pMouseY;
         } else if (pButton == 1) {
            this.flippedSkills = !this.flippedSkills;
            this.cfg.leftSideSkills.set(this.flippedSkills);
         }

         return true;
      } else {
         return super.m_6375_(pMouseX, pMouseY, pButton);
      }
   }

   public boolean m_7933_(int pKeyCode, int pScanCode, int pModifiers) {
      if (pKeyCode == 256 || pKeyCode == 257 || pKeyCode == 259 || pKeyCode == 335) {
         if (editingStatPositions) {
            editingStatPositions = false;
            if (pKeyCode == 259) {
               hudX = this.tempX;
               hudY = this.tempY;
               return true;
            }

            this.cfg.hudX.set(hudX);
            this.cfg.hudY.set(hudY);
            return true;
         }

         if (editingSkillPositions) {
            editingSkillPositions = false;
            if (pKeyCode == 259) {
               skillsX = this.tempX;
               skillsY = this.tempY;
               return true;
            }

            this.cfg.skillsX.set(skillsX);
            this.cfg.skillsY.set(skillsY);
            return true;
         }
      }

      return super.m_7933_(pKeyCode, pScanCode, pModifiers);
   }

   private List<Pair<SettingsOption, Widget>> getSettings() {
      List<Pair<SettingsOption, Widget>> settings = new ArrayList();
      this.posY = this.guiTop + 20;
      SettingsButton tensuraScreen = this.commonButton(() -> {
         return Component.m_237115_(this.titleScreen ? "tensura.true" : "tensura.false");
      }, (onPress) -> {
         this.titleScreen = !this.titleScreen;
         this.cfg.titleScreen.set(this.titleScreen);
      }, (reset) -> {
         this.titleScreen = (Boolean)this.cfg.titleScreen.getDefault();
         this.cfg.titleScreen.set(this.titleScreen);
      });
      settings.add(Pair.of(new SettingsOption(Component.m_237115_("tensura.settings.titleScreen"), new MutableComponent[]{Component.m_237115_("tensura.settings.titleScreen.description0"), Component.m_237115_("tensura.settings.titleScreen.description1")}), tensuraScreen));
      SettingsButton hudVariant = this.commonButton(() -> {
         MutableComponent var10000;
         switch(this.hudVariant) {
         case 0:
            var10000 = Component.m_237115_("tensura.settings.hud_vanilla");
            break;
         case 1:
            var10000 = Component.m_237115_("tensura.settings.hud_tensura");
            break;
         case 2:
            var10000 = Component.m_237115_("tensura.settings.hud_combined_a");
            break;
         case 3:
            var10000 = Component.m_237115_("tensura.settings.hud_combined_b");
            break;
         default:
            var10000 = Component.m_237115_("argument.range.empty");
         }

         return var10000;
      }, (onPress) -> {
         if (this.hudVariant >= 3) {
            this.hudVariant = 0;
         } else {
            ++this.hudVariant;
         }

         this.cfg.hudVariant.set(this.hudVariant);
      }, (reset) -> {
         this.hudVariant = (Integer)this.cfg.hudVariant.getDefault();
         this.cfg.hudVariant.set(this.hudVariant);
      });
      settings.add(Pair.of(new SettingsOption(Component.m_237115_("tensura.settings.tensuraHud"), new MutableComponent[]{Component.m_237115_("tensura.settings.tensuraHud.description0"), Component.m_237115_("tensura.settings.tensuraHud.description1")}), hudVariant));
      SettingsButton arachnophobia = this.commonButton(() -> {
         return Component.m_237115_(this.arachnophobia ? "tensura.true" : "tensura.false");
      }, (onPress) -> {
         this.arachnophobia = !this.arachnophobia;
         this.cfg.arachnophobia.set(this.arachnophobia);
      }, (reset) -> {
         this.arachnophobia = (Boolean)this.cfg.arachnophobia.getDefault();
         this.cfg.arachnophobia.set(this.arachnophobia);
      });
      settings.add(Pair.of(new SettingsOption(Component.m_237115_("tensura.settings.arachnophobia"), new MutableComponent[]{Component.m_237115_("tensura.settings.arachnophobia.description0"), Component.m_237115_("tensura.settings.arachnophobia.description1")}), arachnophobia));
      SettingsButton statsSide = this.commonButton(() -> {
         return Component.m_237115_(this.leftSideStats ? "tensura.settings.left" : "tensura.settings.right");
      }, (onPress) -> {
         this.leftSideStats = !this.leftSideStats;
         this.cfg.leftSideStats.set(this.leftSideStats);
      }, (reset) -> {
         this.leftSideStats = (Boolean)this.cfg.leftSideStats.getDefault();
         this.cfg.leftSideStats.set(this.leftSideStats);
      });
      settings.add(Pair.of(new SettingsOption(Component.m_237115_("tensura.settings.leftSideStats"), new MutableComponent[]{Component.m_237115_("tensura.settings.leftSideStats.description0"), Component.m_237115_("tensura.settings.leftSideStats.description1")}), statsSide));
      SettingsButton skillsSide = this.commonButton(() -> {
         return Component.m_237115_(this.flippedSkills ? "tensura.settings.right" : "tensura.settings.left");
      }, (onPress) -> {
         this.flippedSkills = !this.flippedSkills;
         this.cfg.leftSideSkills.set(this.flippedSkills);
         skillsX = this.flippedSkills ? -2 : 0;
         skillsY = (Integer)this.cfg.skillsY.getDefault();
         this.cfg.skillsX.set(skillsX);
         this.cfg.skillsY.set(skillsY);
      }, (reset) -> {
         this.flippedSkills = (Boolean)this.cfg.leftSideSkills.getDefault();
         this.cfg.leftSideSkills.set(this.flippedSkills);
         skillsX = (Integer)this.cfg.skillsX.getDefault();
         skillsY = (Integer)this.cfg.skillsY.getDefault();
         this.cfg.skillsX.set(skillsX);
         this.cfg.skillsY.set(skillsY);
      });
      settings.add(Pair.of(new SettingsOption(Component.m_237115_("tensura.settings.flippedSkills"), new MutableComponent[]{Component.m_237115_("tensura.settings.flippedSkills.description0"), Component.m_237115_("tensura.settings.flippedSkills.description1"), Component.m_237115_("tensura.settings.flippedSkills.description2")}), skillsSide));
      SettingsButton analysisSide = this.commonButton(() -> {
         return Component.m_237115_(this.leftSideAnalysis ? "tensura.settings.left" : "tensura.settings.right");
      }, (onPress) -> {
         this.leftSideAnalysis = !this.leftSideAnalysis;
         this.cfg.leftSideAnalysis.set(this.leftSideAnalysis);
      }, (reset) -> {
         this.leftSideAnalysis = (Boolean)this.cfg.leftSideAnalysis.getDefault();
         this.cfg.leftSideAnalysis.set(this.leftSideAnalysis);
      });
      settings.add(Pair.of(new SettingsOption(Component.m_237115_("tensura.settings.leftSideAnalysis"), new MutableComponent[]{Component.m_237115_("tensura.settings.leftSideAnalysis.description0"), Component.m_237115_("tensura.settings.leftSideAnalysis.description1")}), analysisSide));
      SettingsEditBox hudSize = this.commonEditBox(this.hudSize, (string) -> {
         try {
            double value = Double.parseDouble(string);
            this.hudSize = value;
            this.cfg.size.set(value);
         } catch (NumberFormatException var4) {
         }

      });
      this.addResetForEditBox(hudSize, (reset) -> {
         this.hudSize = (Double)this.cfg.size.getDefault();
         this.cfg.size.set(this.hudSize);
         hudSize.m_94144_(String.valueOf(this.hudSize));
      });
      settings.add(Pair.of(new SettingsOption(Component.m_237115_("tensura.settings.hudSize"), new MutableComponent[]{Component.m_237115_("tensura.settings.hudSize.description0"), Component.m_237115_("tensura.settings.hudSize.description1"), Component.m_237115_("tensura.settings.hudSize.description2")}), hudSize));
      SettingsEditBox appraisal = this.commonEditBox((double)this.appraisalOpacity, (string) -> {
         try {
            int value = Integer.parseInt(string);
            this.appraisalOpacity = value;
            this.cfg.appraisalOpacity.set(value);
         } catch (NumberFormatException var3) {
         }

      });
      this.addResetForEditBox(appraisal, (reset) -> {
         this.appraisalOpacity = (Integer)this.cfg.appraisalOpacity.getDefault();
         this.cfg.appraisalOpacity.set(this.appraisalOpacity);
         appraisal.m_94144_(String.valueOf(this.appraisalOpacity));
      });
      settings.add(Pair.of(new SettingsOption(Component.m_237115_("tensura.settings.appraisalOpacity"), new MutableComponent[]{Component.m_237115_("tensura.settings.appraisalOpacity.description0"), Component.m_237115_("tensura.settings.appraisalOpacity.description1"), Component.m_237115_("tensura.settings.appraisalOpacity.description2")}), appraisal));
      SettingsButton statPos = this.commonButton(() -> {
         return Component.m_237115_("tensura.settings.edit");
      }, (onPress) -> {
         if (this.hudVariant == 3) {
            editingStatPositions = true;
            this.tempX = hudX;
            this.tempY = hudY;
         }

      }, (reset) -> {
         hudX = (Integer)this.cfg.hudX.getDefault();
         hudY = (Integer)this.cfg.hudY.getDefault();
         this.cfg.hudX.set(hudX);
         this.cfg.hudY.set(hudY);
      }, Component.m_237115_("tensura.settings.hud_position.desc1"));
      settings.add(Pair.of(new SettingsOption(Component.m_237115_("tensura.settings.hud_position"), new MutableComponent[]{Component.m_237115_("tensura.settings.edit_desc1"), Component.m_237115_("tensura.settings.edit_desc2")}), statPos));
      SettingsButton skillPos = this.commonButton(() -> {
         return Component.m_237115_("tensura.settings.edit");
      }, (onPress) -> {
         editingSkillPositions = true;
         this.tempX = skillsX;
         this.tempY = skillsY;
      }, (reset) -> {
         skillsX = (Integer)this.cfg.skillsX.getDefault();
         skillsY = (Integer)this.cfg.skillsY.getDefault();
         this.cfg.skillsX.set(skillsX);
         this.cfg.skillsY.set(skillsY);
      });
      settings.add(Pair.of(new SettingsOption(Component.m_237115_("tensura.settings.skill_position"), new MutableComponent[]{Component.m_237115_("tensura.settings.edit_desc1"), Component.m_237115_("tensura.settings.edit_desc2")}), skillPos));
      return settings;
   }

   private SettingsEditBox commonEditBox(double value, Consumer<String> charTyped) {
      SettingsEditBox editBox = new SettingsEditBox(this.f_96547_, this.guiRight - 190, this.posY(), this.guiCenterX, this.guiCenterY, 100, 20, Component.m_237119_());
      editBox.m_94144_(String.valueOf(value));
      editBox.m_94151_(charTyped);
      this.m_7787_(editBox);
      return editBox;
   }

   private void addResetForEditBox(SettingsEditBox editBox, OnPress reset) {
      Button button = new Button(editBox.f_93620_ + 110, editBox.f_93621_, 40, 20, Component.m_237115_("controls.reset").m_130940_(ChatFormatting.RED), reset, (button1, poseStack, x, y) -> {
      });
      editBox.resetButton = button;
      this.m_7787_(button);
   }

   private SettingsButton commonButton(Supplier<Component> text, OnPress onPress, OnPress reset) {
      SettingsButton button = new SettingsButton(this.guiRight - 190, this.posY(), 100, 20, text, onPress, reset, (pButton, pPoseStack, pMouseX, pMouseY) -> {
      });
      this.m_7787_(button);
      this.m_7787_(button.resetButton);
      return button;
   }

   private SettingsButton commonButton(Supplier<Component> text, OnPress onPress, OnPress reset, Component tooltip) {
      SettingsButton button = new SettingsButton(this.guiRight - 190, this.posY(), 100, 20, text, onPress, reset, (button1, poseStack1, x, y) -> {
         this.m_96602_(this.poseStack, tooltip, x, y);
      });
      this.m_7787_(button);
      this.m_7787_(button.resetButton);
      return button;
   }

   private SettingsButton simpleButton(int x, int y, int width, Component text, OnPress onPress) {
      SettingsButton button = new SettingsButton(x, y, width, 20, text, onPress, (pButton, pPoseStack, pMouseX, pMouseY) -> {
      });
      this.m_7787_(button);
      return button;
   }

   private SettingsButton simpleButton(int x, int y, Supplier<Component> text, OnPress onPress) {
      SettingsButton button = new SettingsButton(x, y, 100, 20, text, onPress, (pButton, pPoseStack, pMouseX, pMouseY) -> {
      });
      this.m_7787_(button);
      return button;
   }

   private void switchStatesForGeneral(boolean state) {
      if (this.nextPage != null) {
         this.nextPage.f_93623_ = state;
         this.nextPage.f_93624_ = state;
         this.previousPage.f_93623_ = state;
         this.previousPage.f_93624_ = state;
         this.resetAll.f_93623_ = state;
         this.resetAll.f_93624_ = state;
         this.returnToMenu.f_93623_ = state;
         this.returnToMenu.f_93624_ = state;
      }
   }

   private void switchStatesForButtons() {
      for(int i = 0; i < this.settings.size(); ++i) {
         Widget widget = (Widget)((Pair)this.settings.get(i)).getSecond();
         if (widget instanceof SettingsButton) {
            SettingsButton settingsButton = (SettingsButton)widget;
            settingsButton.switchStates(i >= this.startIndex && i <= this.endIndex);
         } else if (widget instanceof SettingsEditBox) {
            SettingsEditBox settingsEditBox = (SettingsEditBox)widget;
            settingsEditBox.switchStates(i >= this.startIndex && i <= this.endIndex);
         }
      }

   }

   private int posY() {
      if (this.posY >= this.guiTop + 20 + 30 * this.itemsPerPage) {
         this.posY = this.guiTop + 20;
      }

      this.posY += 30;
      return this.posY - 30;
   }

   private void resetEverything(boolean widgets) {
      this.titleScreen = (Boolean)this.cfg.titleScreen.get();
      this.hudVariant = (Integer)this.cfg.hudVariant.get();
      this.arachnophobia = (Boolean)this.cfg.arachnophobia.get();
      this.leftSideStats = (Boolean)this.cfg.leftSideStats.get();
      this.flippedSkills = (Boolean)this.cfg.leftSideSkills.get();
      this.leftSideAnalysis = (Boolean)this.cfg.leftSideAnalysis.get();
      this.hudSize = (Double)this.cfg.size.get();
      this.appraisalOpacity = (Integer)this.cfg.appraisalOpacity.get();
      hudX = (Integer)this.cfg.hudX.get();
      hudY = (Integer)this.cfg.hudY.get();
      skillsX = (Integer)this.cfg.skillsX.get();
      skillsY = (Integer)this.cfg.skillsY.get();
      this.tempX = 0;
      this.tempY = 0;
      this.editBoxFocused = null;
      editingStatPositions = false;
      editingSkillPositions = false;
      if (widgets) {
         this.f_169369_.clear();
         this.m_6702_().clear();
         this.m_142416_(this.nextPage);
         this.m_142416_(this.previousPage);
         this.m_142416_(this.resetAll);
         this.m_142416_(this.returnToMenu);
      }

      this.settings = this.getSettings();
      this.switchStatesForButtons();
      this.switchStatesForGeneral(true);
   }

   public static int getHudX() {
      return hudX;
   }

   public static int getHudY() {
      return hudY;
   }

   public static int getSkillsX() {
      return skillsX;
   }

   public static int getSkillsY() {
      return skillsY;
   }

   public static boolean isEditingStatPositions() {
      return editingStatPositions;
   }

   public static boolean isEditingSkillPositions() {
      return editingSkillPositions;
   }
}
