package com.github.manasmods.tensura.client.screen;

import com.github.manasmods.manascore.api.client.gui.FontRenderHelper;
import com.github.manasmods.manascore.api.client.gui.widget.ImagePredicateButton;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.client.TensuraGUIHelper;
import com.github.manasmods.tensura.client.keybind.TensuraKeybinds;
import com.github.manasmods.tensura.item.custom.ResetScrollItem;
import com.github.manasmods.tensura.menu.MainMenu;
import com.github.manasmods.tensura.network.TensuraNetwork;
import com.github.manasmods.tensura.network.play2server.GUISwitchPacket;
import com.github.manasmods.tensura.network.play2server.RequestAwakeningPacket;
import com.github.manasmods.tensura.network.play2server.SetSprintSpeedPacket;
import com.github.manasmods.tensura.race.Race;
import com.github.manasmods.tensura.race.RaceHelper;
import com.github.manasmods.tensura.registry.attribute.TensuraAttributeRegistry;
import com.github.manasmods.tensura.util.MathHelper;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.UnmodifiableIterator;
import com.mojang.blaze3d.platform.InputConstants;
import com.mojang.blaze3d.systems.RenderSystem;
import com.mojang.blaze3d.vertex.PoseStack;
import java.awt.Color;
import java.text.DecimalFormat;
import net.minecraft.ChatFormatting;
import net.minecraft.client.gui.screens.Screen;
import net.minecraft.client.gui.screens.inventory.AbstractContainerScreen;
import net.minecraft.client.gui.screens.inventory.InventoryScreen;
import net.minecraft.client.renderer.GameRenderer;
import net.minecraft.network.chat.Component;
import net.minecraft.network.protocol.game.ServerboundClientCommandPacket;
import net.minecraft.network.protocol.game.ServerboundClientCommandPacket.Action;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.util.Mth;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.player.Inventory;
import net.minecraft.world.entity.player.Player;

public class MainScreen extends AbstractContainerScreen<MainMenu> {
   private static final ResourceLocation BACKGROUND = new ResourceLocation("tensura", "textures/gui/main/main_gui.png");
   private static final ResourceLocation SETTINGS = new ResourceLocation("tensura", "textures/gui/settings_icon.png");
   private static final ResourceLocation TDL_AWAKENING = new ResourceLocation("tensura", "textures/gui/main/tdl_awakening.png");
   private static final ResourceLocation TH_AWAKENING = new ResourceLocation("tensura", "textures/gui/main/th_awakening.png");
   private final Player player;
   private String evolutionProgress = "0%";
   private final DecimalFormat roundDouble = new DecimalFormat("#");
   private int sliderOffset;
   private int speedValue;
   private boolean sliding;
   private boolean canAwakenTDL;
   private boolean canAwakenTH;

   public MainScreen(MainMenu pMenu, Inventory pPlayerInventory, Component pTitle) {
      super(pMenu, pPlayerInventory, pTitle.m_6881_().m_130940_(ChatFormatting.WHITE));
      this.f_97726_ = 256;
      this.f_97727_ = 163;
      this.player = pPlayerInventory.f_35978_;
   }

   protected void m_7856_() {
      super.m_7856_();
      this.f_96541_.m_91403_().m_104955_(new ServerboundClientCommandPacket(Action.REQUEST_STATS));
      ImmutableList<ImagePredicateButton> tabs = TensuraGUIHelper.addMenuTabs(this, 0, true);
      UnmodifiableIterator var2 = tabs.iterator();

      while(var2.hasNext()) {
         ImagePredicateButton button = (ImagePredicateButton)var2.next();
         this.m_142416_(button);
      }

      this.sliderOffset = this.getGuiTop() + 116;
      Race race = TensuraPlayerCapability.getRace(this.player);
      if (race == null) {
         this.speedValue = 62;
      } else {
         this.speedValue = MathHelper.mapDoubleToInt(race.getMovementSpeed(), race.getSprintSpeed(), 0, 62, TensuraPlayerCapability.getSprintSpeed(this.player));
      }

      this.sliding = false;
   }

   protected void m_7286_(PoseStack pPoseStack, float pPartialTick, int pMouseX, int pMouseY) {
      this.m_7333_(pPoseStack);
      RenderSystem.m_157427_(GameRenderer::m_172817_);
      RenderSystem.m_157429_(1.0F, 1.0F, 1.0F, 1.0F);
      RenderSystem.m_157456_(0, BACKGROUND);
      int x = (this.f_96543_ - this.f_97726_) / 2;
      int y = (this.f_96544_ - this.f_97727_) / 2;
      this.m_93228_(pPoseStack, x, y, 0, 0, this.f_97726_, this.f_97727_);
      TensuraGUIHelper.renderTabIcon(pPoseStack, this, 0, pMouseX, pMouseY);
      float sizeMultiplier = RaceHelper.getSizeMultiplier(this.player);
      sizeMultiplier = sizeMultiplier > 1.0F ? 1.0F / sizeMultiplier : 1.0F;
      InventoryScreen.m_98850_(this.getGuiLeft() + 33, this.getGuiTop() + 122, (int)(30.0F * sizeMultiplier), (float)(this.getGuiLeft() + 33 - pMouseX), (float)(this.getGuiTop() + 122 - 50 - pMouseY), this.player);
      RenderSystem.m_157456_(0, BACKGROUND);
      this.m_93228_(pPoseStack, this.getGuiLeft() + 54, this.sliderOffset - this.speedValue, 25, 164, 3, 10);
      RenderSystem.m_157456_(0, SETTINGS);
      m_93160_(pPoseStack, this.getGuiLeft() + 206, this.getGuiTop() + 7, 16, 16, 0.0F, 0.0F, 32, 32, 32, 32);
      this.canAwakenTDL = RaceHelper.canAwaken(this.player, false, ((MainMenu)this.f_97732_).getSoulRequirement());
      this.canAwakenTH = RaceHelper.canAwaken(this.player, true, ((MainMenu)this.f_97732_).getSoulRequirement());
      if (this.canAwakenTDL) {
         RenderSystem.m_157456_(0, TDL_AWAKENING);
         m_93160_(pPoseStack, this.getGuiLeft() - 50, this.canAwakenTH ? this.getGuiTop() + 52 : this.getGuiTop() + 71, 36, 36, 0.0F, 0.0F, 36, 36, 36, 36);
      }

      if (this.canAwakenTH) {
         RenderSystem.m_157456_(0, TH_AWAKENING);
         m_93160_(pPoseStack, this.getGuiLeft() - 50, this.canAwakenTDL ? this.getGuiTop() + 94 : this.getGuiTop() + 71, 36, 36, 0.0F, 0.0F, 36, 36, 36, 36);
      }

      int counter = TensuraPlayerCapability.getResetCounter(this.player);
      if (counter > 0) {
         ResetScrollItem.ResetCounterType type = ResetScrollItem.ResetCounterType.get(counter);
         RenderSystem.m_157456_(0, type.getTextureLocation());
         m_93160_(pPoseStack, this.getGuiLeft() + 26, this.getGuiTop() - 36, 32, 32, 0.0F, 0.0F, 32, 32, 32, 32);
      }

      this.renderInfo(pPoseStack, pMouseX, pMouseY);
   }

   protected void m_7027_(PoseStack pPoseStack, int pMouseX, int pMouseY) {
   }

   private void renderInfo(PoseStack stack, int pX, int pY) {
      TensuraPlayerCapability.getFrom(this.player).ifPresent((cap) -> {
         String name = TensuraEPCapability.getName(this.player);
         int nameColor = cap.isTrueDemonLord() ? ChatFormatting.DARK_PURPLE.m_126665_() : (cap.isTrueHero() ? ChatFormatting.GOLD.m_126665_() : Color.WHITE.getRGB());
         TensuraGUIHelper.renderScaledCenteredXText(this.f_96547_, stack, (Component)(name != null ? Component.m_237113_(name) : this.player.m_7755_()), this.getGuiLeft() + 6, this.getGuiTop() + 35, 72, 19, nameColor, false);
         Race race = cap.getRace();
         Component raceName = race != null && race.getName() != null ? race.getName() : Component.m_237115_("tensura.race.selection.skills.empty");
         Color color = TensuraEPCapability.isChaos(this.player) ? Color.RED : (TensuraEPCapability.isMajin(this.player) ? Color.YELLOW : Color.WHITE);
         FontRenderHelper.renderScaledTextInArea(stack, this.f_96547_, raceName, (float)(this.getGuiLeft() + 95), (float)(this.getGuiTop() + 36), 131.0F, 11.0F, color);
         FontRenderHelper.renderScaledTextInArea(stack, this.f_96547_, Component.m_237110_("tensura.main_menu.souls", new Object[]{(double)(cap.getSoulPoints() / 100) / 10.0D}), (float)(this.getGuiLeft() + 95), (float)(this.getGuiTop() + 52), 126.0F, 11.0F, Color.WHITE);
         FontRenderHelper.renderScaledTextInArea(stack, this.f_96547_, Component.m_237110_("tensura.main_menu.magicule", new Object[]{TensuraGUIHelper.shortenNumberComponent(cap.getMagicule()), TensuraGUIHelper.shortenNumberComponent(this.player.m_21133_((Attribute)TensuraAttributeRegistry.MAX_MAGICULE.get()))}), (float)(this.getGuiLeft() + 95), (float)(this.getGuiTop() + 78), 126.0F, 11.0F, Color.WHITE);
         if (pX > this.getGuiLeft() + 94 && pX < this.getGuiLeft() + 221 && pY > this.getGuiTop() + 78 && pY < this.getGuiTop() + 89 && !this.sliding) {
            this.m_96602_(stack, Component.m_237113_(this.currentVsMax(cap.getMagicule(), this.player.m_21133_((Attribute)TensuraAttributeRegistry.MAX_MAGICULE.get()))).m_130940_(ChatFormatting.AQUA), pX, pY);
         }

         FontRenderHelper.renderScaledTextInArea(stack, this.f_96547_, Component.m_237110_("tensura.main_menu.aura", new Object[]{TensuraGUIHelper.shortenNumberComponent(cap.getAura()), TensuraGUIHelper.shortenNumberComponent(this.player.m_21133_((Attribute)TensuraAttributeRegistry.MAX_AURA.get()))}), (float)(this.getGuiLeft() + 95), (float)(this.getGuiTop() + 99), 126.0F, 11.0F, Color.WHITE);
         if (pX > this.getGuiLeft() + 94 && pX < this.getGuiLeft() + 221 && pY > this.getGuiTop() + 99 && pY < this.getGuiTop() + 110 && !this.sliding) {
            this.m_96602_(stack, Component.m_237113_(this.currentVsMax(cap.getAura(), this.player.m_21133_((Attribute)TensuraAttributeRegistry.MAX_AURA.get()))).m_130940_(ChatFormatting.YELLOW), pX, pY);
         }

         this.renderProgressBar(stack);
      });
      TensuraEPCapability.getFrom(this.player).ifPresent((cap) -> {
         FontRenderHelper.renderScaledTextInArea(stack, this.f_96547_, Component.m_237110_("tensura.main_menu.existence_points", new Object[]{TensuraGUIHelper.shortenNumberComponent(cap.getEP())}), (float)(this.getGuiLeft() + 13), (float)(this.getGuiTop() + 144), 70.0F, 11.0F, Color.ORANGE);
         if (pX > this.getGuiLeft() + 8 && pX < this.getGuiLeft() + 82 && pY > this.getGuiTop() + 140 && pY < this.getGuiTop() + 155 && !this.sliding) {
            this.m_96602_(stack, Component.m_237113_(this.roundDouble.format(cap.getEP())).m_130940_(ChatFormatting.GOLD), pX, pY);
         }

      });
      int counter = TensuraPlayerCapability.getResetCounter(this.player);
      if (counter > 0) {
         ResetScrollItem.ResetCounterType type = ResetScrollItem.ResetCounterType.get(counter);
         TensuraGUIHelper.renderCenteredXText(this.f_96547_, stack, Component.m_237113_(String.valueOf(counter)), this.getGuiLeft() + 33, this.getGuiTop() - 16, 18, type.getTextColor(), false);
         if (pX > this.getGuiLeft() + 26 && pX < this.getGuiLeft() + 58 && pY > this.getGuiTop() - 36 && pY < this.getGuiTop() - 2 && !this.sliding) {
            this.m_96602_(stack, Component.m_237110_("tensura.main_menu.reset_counter", new Object[]{counter}).m_130940_(ChatFormatting.WHITE), pX, pY);
         }

      }
   }

   public void m_6305_(PoseStack pPoseStack, int pMouseX, int pMouseY, float pPartialTick) {
      super.m_6305_(pPoseStack, pMouseX, pMouseY, pPartialTick);
      this.m_7025_(pPoseStack, pMouseX, pMouseY);
   }

   protected void m_7025_(PoseStack pPoseStack, int pX, int pY) {
      String percent;
      if (!this.sliding && !TensuraGUIHelper.mouseOver(pX, pY, this.getGuiLeft() + 53, this.getGuiLeft() + 57, this.sliderOffset - this.speedValue - 1, this.sliderOffset + 10)) {
         if (pX > this.getGuiLeft() + 61 && pX < this.getGuiLeft() + 78 && pY > this.getGuiTop() + 51 && pY < this.getGuiTop() + 130) {
            percent = this.evolutionProgress;
            byte var5 = -1;
            switch(percent.hashCode()) {
            case 44801:
               if (percent.equals("-1%")) {
                  var5 = 2;
               }
               break;
            case 44832:
               if (percent.equals("-2%")) {
                  var5 = 1;
               }
               break;
            case 1507412:
               if (percent.equals("100%")) {
                  var5 = 0;
               }
            }

            switch(var5) {
            case 0:
               this.m_96602_(pPoseStack, Component.m_237115_("tensura.main_menu.evolution_progress.ready").m_130940_(ChatFormatting.GREEN), pX, pY);
               break;
            case 1:
               this.m_96602_(pPoseStack, Component.m_237115_("tensura.main_menu.evolution_progress.select").m_130940_(ChatFormatting.AQUA), pX, pY);
               break;
            case 2:
               this.m_96602_(pPoseStack, Component.m_237115_("tensura.main_menu.evolution_progress.none").m_130940_(ChatFormatting.RED), pX, pY);
               break;
            default:
               this.m_96602_(pPoseStack, Component.m_237110_("tensura.main_menu.evolution_progress", new Object[]{this.evolutionProgress}), pX, pY);
            }
         }

         if (pX > this.getGuiLeft() + 233 && pX < this.getGuiLeft() + 256 && pY > this.getGuiTop() + 30 && pY < this.getGuiTop() + 54) {
            this.m_96602_(pPoseStack, Component.m_237115_("tensura.main_menu"), pX, pY);
         }

         if (TensuraGUIHelper.mouseOver(pX, pY, this.getGuiLeft() + 205, this.getGuiLeft() + 222, this.getGuiTop() + 6, this.getGuiTop() + 23)) {
            this.m_96602_(pPoseStack, Component.m_237115_("tensura.settings"), pX, pY);
         }

         int posY = this.canAwakenTH ? this.getGuiTop() + 52 : this.getGuiTop() + 71;
         if (TensuraGUIHelper.mouseOver(pX, pY, this.getGuiLeft() - 51, this.getGuiLeft() - 14, posY - 1, posY + 36) && RaceHelper.canAwaken(this.player, false, ((MainMenu)this.f_97732_).getSoulRequirement())) {
            this.m_96602_(pPoseStack, Component.m_237115_("tensura.main_menu.tdl_awaken").m_130940_(ChatFormatting.DARK_PURPLE), pX, pY);
         }

         posY = this.canAwakenTDL ? this.getGuiTop() + 94 : this.getGuiTop() + 71;
         if (TensuraGUIHelper.mouseOver(pX, pY, this.getGuiLeft() - 51, this.getGuiLeft() - 14, posY - 1, posY + 36) && RaceHelper.canAwaken(this.player, true, ((MainMenu)this.f_97732_).getSoulRequirement())) {
            this.m_96602_(pPoseStack, Component.m_237115_("tensura.main_menu.th_awaken").m_130940_(ChatFormatting.GOLD), pX, pY);
         }

         super.m_7025_(pPoseStack, pX, pY);
      } else {
         percent = MathHelper.mapIntToInt(0, 62, 0, 100, this.speedValue) + "%";
         this.m_96602_(pPoseStack, Component.m_237110_("tensura.main_menu.speed", new Object[]{percent}), pX, pY);
      }
   }

   public boolean m_6375_(double pMouseX, double pMouseY, int pButton) {
      if (pMouseX > (double)(this.getGuiLeft() + 62) && pMouseX < (double)(this.getGuiLeft() + 79) && pMouseY > (double)(this.getGuiTop() + 51) && pMouseY < (double)(this.getGuiTop() + 130)) {
         Race race = TensuraPlayerCapability.getRace(this.player);
         if (race != null && !race.getNextEvolutions(this.player).isEmpty()) {
            TensuraGUIHelper.playSound(SoundEvents.f_12490_, 1.0F);
            TensuraNetwork.INSTANCE.sendToServer(new GUISwitchPacket(7));
            return true;
         }
      }

      if (TensuraGUIHelper.mouseOver(pMouseX, pMouseY, this.getGuiLeft() + 205, this.getGuiLeft() + 222, this.getGuiTop() + 6, this.getGuiTop() + 23)) {
         TensuraGUIHelper.playSound(SoundEvents.f_12490_, 1.0F);
         TensuraNetwork.INSTANCE.sendToServer(new GUISwitchPacket(8));
      }

      if (TensuraGUIHelper.mouseOver(pMouseX, pMouseY, this.getGuiLeft() + 53, this.getGuiLeft() + 57, this.sliderOffset - this.speedValue - 1, this.sliderOffset + 10) && pButton == 0) {
         this.sliding = true;
      }

      int posY = this.canAwakenTH ? this.getGuiTop() + 52 : this.getGuiTop() + 81;
      if (TensuraGUIHelper.mouseOver(pMouseX, pMouseY, this.getGuiLeft() - 51, this.getGuiLeft() - 14, posY - 1, posY + 36) && RaceHelper.canAwaken(this.player, false, ((MainMenu)this.f_97732_).getSoulRequirement())) {
         TensuraNetwork.INSTANCE.sendToServer(new RequestAwakeningPacket(false));
      }

      posY = this.canAwakenTDL ? this.getGuiTop() + 94 : this.getGuiTop() + 71;
      if (TensuraGUIHelper.mouseOver(pMouseX, pMouseY, this.getGuiLeft() - 51, this.getGuiLeft() - 14, posY - 1, posY + 36) && RaceHelper.canAwaken(this.player, true, ((MainMenu)this.f_97732_).getSoulRequirement())) {
         TensuraNetwork.INSTANCE.sendToServer(new RequestAwakeningPacket(true));
      }

      return super.m_6375_(pMouseX, pMouseY, pButton);
   }

   public boolean m_6348_(double pMouseX, double pMouseY, int pButton) {
      if (this.sliding) {
         this.sliding = false;
         Race race = TensuraPlayerCapability.getRace(this.player);
         if (race == null) {
            return super.m_6348_(pMouseX, pMouseY, pButton);
         }

         double newSpeed = MathHelper.mapIntToDouble(0, 62, race.getMovementSpeed(), race.getSprintSpeed(), this.speedValue);
         TensuraNetwork.INSTANCE.sendToServer(new SetSprintSpeedPacket(newSpeed));
      }

      return super.m_6348_(pMouseX, pMouseY, pButton);
   }

   public boolean m_7979_(double pMouseX, double pMouseY, int pButton, double pDragX, double pDragY) {
      if (this.sliding) {
         this.speedValue = Mth.m_14045_(this.speedValue - (int)pDragY, 0, 62);
      }

      return super.m_7979_(pMouseX, pMouseY, pButton, pDragX, pDragY);
   }

   private String currentVsMax(double current, double max) {
      String var10000 = this.roundDouble.format(current);
      return var10000 + "/" + this.roundDouble.format(max);
   }

   private void renderProgressBar(PoseStack poseStack) {
      Race trackedRace = TensuraPlayerCapability.getTrackedEvolution(this.player);
      Race race = TensuraPlayerCapability.getRace(this.player);
      if (race != null && race.getNextEvolutions(this.player).isEmpty()) {
         this.evolutionProgress = "-1%";
      } else if (trackedRace == null) {
         this.evolutionProgress = "-2%";
      } else {
         int evolutionPercentage = Mth.m_14045_((int)trackedRace.getEvolutionPercentage(this.player), 0, 100);
         int fill = (int)((float)(73 * evolutionPercentage) / 100.0F);
         int length = 73 - fill;
         int barXOffset = evolutionPercentage == 100 ? 13 : 1;
         int barYOffset = 164 + length;
         int pX = this.getGuiLeft() + 65;
         int pY = this.getGuiTop() + 54 + length;
         this.evolutionProgress = evolutionPercentage + "%";
         RenderSystem.m_157456_(0, BACKGROUND);
         this.m_93228_(poseStack, pX, pY, barXOffset, barYOffset, 11, fill);
      }
   }

   public boolean m_7933_(int pKeyCode, int pScanCode, int pModifiers) {
      if (this.f_96541_ != null) {
         if (TensuraKeybinds.MAIN_GUI.m_90832_(pKeyCode, pScanCode)) {
            this.f_96541_.m_91152_((Screen)null);
            this.f_96541_.f_91067_.m_91601_();
            return true;
         }

         if (this.f_96541_.f_91066_.f_92092_.isActiveAndMatches(InputConstants.m_84827_(pKeyCode, pScanCode))) {
            return true;
         }
      }

      return super.m_7933_(pKeyCode, pScanCode, pModifiers);
   }
}
