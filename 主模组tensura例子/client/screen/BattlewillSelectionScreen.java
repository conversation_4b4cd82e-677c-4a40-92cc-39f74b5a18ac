package com.github.manasmods.tensura.client.screen;

import com.github.manasmods.manascore.api.client.gui.FontRenderHelper;
import com.github.manasmods.manascore.api.client.gui.widget.ImagePredicateButton;
import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.TensuraSkill;
import com.github.manasmods.tensura.ability.battlewill.Battewill;
import com.github.manasmods.tensura.capability.skill.TensuraSkillCapability;
import com.github.manasmods.tensura.client.TensuraGUIHelper;
import com.github.manasmods.tensura.client.keybind.TensuraKeybinds;
import com.github.manasmods.tensura.menu.BattlewillSelectionMenu;
import com.github.manasmods.tensura.network.TensuraNetwork;
import com.github.manasmods.tensura.network.play2server.GUISwitchPacket;
import com.github.manasmods.tensura.network.play2server.skill.RequestAbilityPresetPacket;
import com.github.manasmods.tensura.network.play2server.skill.RequestPresetSlotChangePacket;
import com.github.manasmods.tensura.util.Cached;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.UnmodifiableIterator;
import com.mojang.blaze3d.platform.InputConstants;
import com.mojang.blaze3d.systems.RenderSystem;
import com.mojang.blaze3d.vertex.PoseStack;
import java.awt.Color;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Predicate;
import javax.annotation.Nullable;
import net.minecraft.ChatFormatting;
import net.minecraft.client.Minecraft;
import net.minecraft.client.gui.components.EditBox;
import net.minecraft.client.gui.components.Button.OnPress;
import net.minecraft.client.gui.components.Button.OnTooltip;
import net.minecraft.client.gui.screens.Screen;
import net.minecraft.client.gui.screens.inventory.AbstractContainerScreen;
import net.minecraft.client.renderer.GameRenderer;
import net.minecraft.client.resources.sounds.SimpleSoundInstance;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.MutableComponent;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.util.Mth;
import net.minecraft.world.entity.player.Inventory;
import net.minecraft.world.entity.player.Player;

public class BattlewillSelectionScreen extends AbstractContainerScreen<BattlewillSelectionMenu> {
   private static final ResourceLocation BACKGROUND = new ResourceLocation("tensura", "textures/gui/battlewill/battlewill_gui.png");
   private static final ResourceLocation CHECKBOX = new ResourceLocation("tensura", "textures/gui/checkbox.png");
   private static final ResourceLocation SKILL_BUTTON = new ResourceLocation("tensura", "textures/gui/skill_button.png");
   private static final ResourceLocation SCROLL_BAR = new ResourceLocation("tensura", "textures/gui/scroll_bar.png");
   private static final ResourceLocation COOLDOWN_CLOCK = new ResourceLocation("tensura", "textures/gui/skill_selection/cooldown_clock.png");
   private List<String> presetNames = new ArrayList();
   private Cached<List<ManasSkill>, String> filteredSkills;
   private EditBox searchField;
   private ManasSkill slot1;
   private ManasSkill slot2;
   private ManasSkill slot3;
   private ManasSkill selectedSkill = null;
   private int selectedPreset = 0;
   private final Player player;
   private boolean scrolling;
   private float scrollOffs;
   private int startIndex;
   private int slot;

   public BattlewillSelectionScreen(BattlewillSelectionMenu pMenu, Inventory pPlayerInventory, Component pTitle) {
      super(pMenu, pPlayerInventory, pTitle);
      this.f_97726_ = 256;
      this.f_97727_ = 163;
      this.player = pPlayerInventory.f_35978_;
   }

   protected void m_7856_() {
      super.m_7856_();
      this.scrollOffs = 0.0F;
      this.startIndex = 0;
      this.slot = 0;
      this.searchField = new EditBox(this.f_96547_, this.getGuiLeft() + 27, this.getGuiTop() + 31, 79, 9, Component.m_237119_());
      this.searchField.m_94182_(false);
      this.searchField.m_94151_((s) -> {
         if (!s.isEmpty()) {
            this.scrollOffs = 0.0F;
            this.startIndex = 0;
         }
      });
      this.m_142416_(this.searchField);
      this.filteredSkills = new Cached(() -> {
         List<ManasSkill> filteredSkillList = new ArrayList(SkillAPI.getSkillsFrom(this.player).getLearnedSkills().stream().map(ManasSkillInstance::getSkill).sorted(Comparator.comparing((skill) -> {
            return (ResourceLocation)Objects.requireNonNull(SkillAPI.getSkillRegistry().getKey(skill));
         })).toList());
         String filterValue = this.searchField.m_94155_();
         filteredSkillList.removeIf(this.filter(filterValue));
         return filteredSkillList;
      }, (info) -> {
         if (info.lastCallbackReference == null || !((String)info.lastCallbackReference).equals(this.searchField.m_94155_())) {
            info.lastCallbackReference = this.searchField.m_94155_();
            info.needsUpdate = true;
         }

         return info;
      });
      TensuraSkillCapability.getFrom(this.player).ifPresent((cap) -> {
         this.selectedPreset = cap.getActivePreset();
         this.presetNames = cap.getPresetNames();
      });

      ImagePredicateButton button;
      for(int i = 0; i < 9; ++i) {
         ResourceLocation var10006 = SkillScreen.PRESET_BUTTON;
         OnPress var10007 = (pButton) -> {
            if (this.selectedPreset == i) {
               TensuraNetwork.INSTANCE.sendToServer(new RequestAbilityPresetPacket(i, true));
            } else {
               this.selectedPreset = i;
               this.loadPreset();
            }

         };
         OnTooltip var10008 = (pButton, pPoseStack1, pMouseX1, pMouseY1) -> {
            this.m_96602_(pPoseStack1, Component.m_237113_((String)this.presetNames.get(i)), pMouseX1, pMouseY1);
         };
         BattlewillSelectionMenu var10009 = (BattlewillSelectionMenu)this.f_97732_;
         Objects.requireNonNull(var10009);
         button = new ImagePredicateButton(0, 0, 10, 11, var10006, var10007, var10008, var10009::check);
         button.f_93620_ = this.getGuiLeft() + 14 + i * 10;
         button.f_93621_ = this.getGuiTop() + 98;
         this.m_142416_(button);
      }

      ImmutableList<ImagePredicateButton> buttons = TensuraGUIHelper.addMenuTabs(this, 6, true);
      UnmodifiableIterator var2 = buttons.iterator();

      while(var2.hasNext()) {
         button = (ImagePredicateButton)var2.next();
         this.m_142416_(button);
      }

      this.loadPreset();
   }

   protected void m_7286_(PoseStack pPoseStack, float pPartialTick, int pX, int pY) {
      this.m_7333_(pPoseStack);
      RenderSystem.m_157427_(GameRenderer::m_172817_);
      RenderSystem.m_157429_(1.0F, 1.0F, 1.0F, 1.0F);
      RenderSystem.m_157456_(0, BACKGROUND);
      int x = (this.f_96543_ - this.f_97726_) / 2;
      int y = (this.f_96544_ - this.f_97727_) / 2;
      this.m_93228_(pPoseStack, x, y, 0, 0, this.f_97726_, this.f_97727_);
      TensuraGUIHelper.renderTabIcon(pPoseStack, this, 6, pX, pY);
      ManasSkill skill;
      int masteryBar;
      boolean hovering;
      if (this.selectedSkill != null) {
         ManasSkillInstance instance = this.getSkillInstance(this.selectedSkill);
         if (instance == null) {
            return;
         }

         skill = instance.getSkill();
         masteryBar = (int)((float)(106 * instance.getMastery()) / (float)skill.getMaxMastery());
         int barY = 164;
         if (instance.getMastery() < 0) {
            masteryBar = (int)((float)(106 * (instance.getMastery() + 100)) / 100.0F);
            barY += 9;
         }

         RenderSystem.m_157456_(0, BACKGROUND);
         this.m_93228_(pPoseStack, this.getGuiLeft() + 119, this.getGuiTop() + 146, 1, barY, masteryBar, 9);
         ResourceLocation location = skill.getSkillIcon();
         if (location != null) {
            RenderSystem.m_157456_(0, skill.getSkillIcon());
            m_93133_(pPoseStack, this.getGuiLeft() + 162, this.getGuiTop() + 32, 0.0F, 0.0F, 20, 20, 20, 20);
         }

         Component description = skill.getSkillDescription();
         FontRenderHelper.renderScaledTextInArea(pPoseStack, this.f_96547_, description, (float)(this.getGuiLeft() + 125), (float)(this.getGuiTop() + 67), 94.0F, 66.0F, Color.LIGHT_GRAY);
         if (instance.canBeToggled(this.player)) {
            int offsetX = 0;
            boolean hovering = pX >= this.f_97735_ + 190 && pY >= this.f_97736_ + 35 && pX < this.f_97735_ + 203 && pY < y + 48;
            if (hovering) {
               offsetX = 13;
            }

            int offsetY = instance.isToggled() ? 13 : 0;
            RenderSystem.m_157456_(0, CHECKBOX);
            m_93133_(pPoseStack, this.f_97735_ + 190, this.f_97736_ + 35, (float)offsetX, (float)offsetY, 13, 13, 26, 26);
         }

         hovering = pX > this.getGuiLeft() + 158 && pX < this.getGuiLeft() + 185 && pY > this.getGuiTop() + 29 && pY < this.getGuiTop() + 55;
         if (hovering && skill.getName() != null) {
            this.m_96602_(pPoseStack, skill.getName(), pX, pY);
         }

         if (instance.onCoolDown()) {
            RenderSystem.m_157456_(0, COOLDOWN_CLOCK);
            m_93133_(pPoseStack, this.getGuiLeft() + 142, this.getGuiTop() + 34, 0.0F, 0.0F, 12, 14, 12, 14);
         }

         if (pX > this.getGuiLeft() + 141 && pX < this.getGuiLeft() + 154 && pY > this.getGuiTop() + 33 && pY < this.getGuiTop() + 48) {
            this.m_96602_(pPoseStack, Component.m_237110_("tensura.skill.on_cooldown", new Object[]{instance.getCoolDown()}), pX, pY);
         }
      }

      int i;
      for(i = 0; i < 9; ++i) {
         if (i < 3) {
            skill = i == 0 ? this.slot1 : (i == 1 ? this.slot2 : this.slot3);
            MutableComponent name = this.skillName(skill);
            ManasSkillInstance instance = this.getSkillInstance(skill);
            if (instance != null && instance.getMastery() < 0) {
               name = name.m_130940_(ChatFormatting.GRAY);
            } else {
               name = name.m_130940_(ChatFormatting.DARK_AQUA);
            }

            int xName = this.getGuiLeft() + 10;
            int yName = this.getGuiTop() + 115 + i * 16;
            FontRenderHelper.renderScaledTextInArea(pPoseStack, this.f_96547_, TensuraGUIHelper.shortenTextComponent((Component)name, 15), (float)xName, (float)yName, 100.0F, 11.0F, Color.CYAN);
            hovering = pX > xName - 6 && pX < xName + 99 && pY > yName - 5 && pY < yName + 11;
            if (hovering) {
               this.m_96602_(pPoseStack, name, pX, pY);
            }
         }

         Color presetColor = this.selectedPreset == i ? Color.ORANGE : Color.LIGHT_GRAY;
         TensuraGUIHelper.renderScaledCenteredXText(this.f_96547_, pPoseStack, Component.m_237113_(String.valueOf(i + 1)), this.getGuiLeft() + 15 + i * 10, this.getGuiTop() + 100, 10, 11, presetColor, true);
      }

      i = (int)(26.0F * this.scrollOffs);
      RenderSystem.m_157456_(0, SCROLL_BAR);
      m_93133_(pPoseStack, this.f_97735_ + 98, this.f_97736_ + 48 + i, 0.0F, this.isScrollBarActive() ? 13.0F : 0.0F, 10, 13, 10, 26);
      List<ManasSkill> filteredSkills = (List)this.filteredSkills.getValue();
      masteryBar = Math.min(this.startIndex + 3, filteredSkills.size());
      this.renderButtons(pPoseStack, pX, pY, masteryBar, filteredSkills);
   }

   private void renderButtons(PoseStack pPoseStack, int pMouseX, int pMouseY, int pLastVisibleElementIndex, List<ManasSkill> list) {
      for(int i = this.startIndex; i < pLastVisibleElementIndex && i < list.size(); ++i) {
         int x = this.getGuiLeft() + 6;
         int y = this.getGuiTop() + 48 + (i - this.startIndex) * 13;
         int offset = 0;
         boolean hovering = pMouseX >= x && pMouseY >= y && pMouseX < x + 89 && pMouseY < y + 13;
         if (hovering) {
            offset = 13;
         }

         RenderSystem.m_157456_(0, SKILL_BUTTON);
         m_93133_(pPoseStack, x, y, 0.0F, (float)offset, 89, 13, 89, 26);
         ManasSkill manasSkill = (ManasSkill)list.get(i);
         MutableComponent name = this.skillName(manasSkill);
         ManasSkillInstance instance = this.getSkillInstance(manasSkill);
         if (instance != null && instance.getMastery() < 0) {
            name = name.m_130940_(ChatFormatting.GRAY);
         } else {
            name = name.m_130940_(ChatFormatting.DARK_AQUA);
         }

         TensuraGUIHelper.renderScaledShadowText(pPoseStack, this.f_96547_, TensuraGUIHelper.shortenTextComponent((Component)name, 14), (float)(this.getGuiLeft() + 9), (float)(this.getGuiTop() + 51 + (i - this.startIndex) * 13), 87.0F, 13.0F, Color.WHITE.getRGB(), 2.0F, 0.01F);
         if (hovering) {
            this.m_96602_(pPoseStack, name, pMouseX, pMouseY);
         }
      }

   }

   protected void m_7027_(PoseStack pPoseStack, int pMouseX, int pMouseY) {
   }

   public void m_6305_(PoseStack pPoseStack, int pMouseX, int pMouseY, float pPartialTick) {
      super.m_6305_(pPoseStack, pMouseX, pMouseY, pPartialTick);
      this.m_7025_(pPoseStack, pMouseX, pMouseY);
   }

   protected void m_7025_(PoseStack pPoseStack, int pX, int pY) {
      if (pX > this.getGuiLeft() + 5 && pX < this.getGuiLeft() + 23 && pY > this.getGuiTop() + 28 && pY < this.getGuiTop() + 38) {
         this.m_96602_(pPoseStack, Component.m_237115_("tooltip.tensura.return"), pX, pY);
      }

      if (this.selectedSkill != null) {
         ManasSkillInstance instance = this.getSkillInstance(this.selectedSkill);
         if (instance == null) {
            return;
         }

         if (pX > this.getGuiLeft() + 189 && pX < this.getGuiLeft() + 203 && pY > this.getGuiTop() + 35 && pY < this.getGuiTop() + 48) {
            Component component = instance.isToggled() ? Component.m_237115_("options.on") : Component.m_237115_("options.off");
            this.m_96602_(pPoseStack, instance.canBeToggled(this.player) && instance.canInteractSkill(this.player) ? component : Component.m_237115_("tensura.skill_selection_screen.untoggleable"), pX, pY);
         } else if (pX > this.getGuiLeft() + 115 & pX < this.getGuiLeft() + 228 && pY > this.getGuiTop() + 143 && pY < this.getGuiTop() + 158) {
            int mastery = instance.getMastery();
            MutableComponent component;
            if (mastery < 0) {
               int points = mastery + 100;
               component = Component.m_237110_("tensura.skill_selection_screen.skill_learning", new Object[]{points});
            } else {
               mastery = mastery * 100 / instance.getMaxMastery();
               component = Component.m_237110_("tensura.skill_selection_screen.skill_mastery", new Object[]{mastery + "%"});
            }

            this.m_96602_(pPoseStack, component, pX, pY);
         }
      }

      super.m_7025_(pPoseStack, pX, pY);
   }

   public boolean m_7933_(int pKeyCode, int pScanCode, int pModifiers) {
      if (this.searchField.m_7933_(pKeyCode, pScanCode, pModifiers)) {
         return true;
      } else if (this.searchField.m_93696_() && this.searchField.m_94213_() && pKeyCode != 256) {
         return true;
      } else {
         if (this.f_96541_ != null) {
            if (TensuraKeybinds.MAIN_GUI.m_90832_(pKeyCode, pScanCode)) {
               this.f_96541_.m_91152_((Screen)null);
               this.f_96541_.f_91067_.m_91601_();
               return true;
            }

            if (this.f_96541_.f_91066_.f_92092_.isActiveAndMatches(InputConstants.m_84827_(pKeyCode, pScanCode))) {
               return true;
            }
         }

         return super.m_7933_(pKeyCode, pScanCode, pModifiers);
      }
   }

   public boolean m_6375_(double pX, double pY, int pButton) {
      this.scrolling = false;
      List<ManasSkill> skills = (List)this.filteredSkills.getValue();
      int lastDisplayedIndex = Math.min(this.startIndex + 3, skills.size());

      for(int i = this.startIndex; i < lastDisplayedIndex; ++i) {
         int x = this.getGuiLeft() + 6;
         int y = this.getGuiTop() + 48 + (i - this.startIndex) * 13;
         if (skills.size() <= i) {
            break;
         }

         if (pX >= (double)x && pY >= (double)y && pX < (double)(x + 89) && pY < (double)(y + 13)) {
            Minecraft.m_91087_().m_91106_().m_120367_(SimpleSoundInstance.m_119752_(SoundEvents.f_12490_, 1.0F));
            this.selectedSkill = (ManasSkill)skills.get(i);
            return true;
         }
      }

      this.slot = this.mouseOverSkillSlot(pX, pY);
      ManasSkillInstance instance;
      if (this.slot != 0) {
         if (pButton == 0) {
            instance = this.getSkillInstance(this.selectedSkill);
            if (instance == null) {
               return false;
            }

            if (this.selectedSkill != null) {
               ManasSkill var13 = this.selectedSkill;
               if (var13 instanceof TensuraSkill) {
                  TensuraSkill skill = (TensuraSkill)var13;
                  if (skill.canBeSlotted(instance)) {
                     if (this.slot == 1) {
                        this.slot1 = this.selectedSkill;
                        this.selectedSkill = null;
                        Minecraft.m_91087_().m_91106_().m_120367_(SimpleSoundInstance.m_119752_(SoundEvents.f_11871_, 1.0F));
                        TensuraNetwork.INSTANCE.sendToServer(new RequestPresetSlotChangePacket(0, this.selectedPreset, SkillUtils.getSkillId(this.slot1)));
                        return true;
                     }

                     if (this.slot == 2) {
                        this.slot2 = this.selectedSkill;
                        this.selectedSkill = null;
                        Minecraft.m_91087_().m_91106_().m_120367_(SimpleSoundInstance.m_119752_(SoundEvents.f_11871_, 1.0F));
                        TensuraNetwork.INSTANCE.sendToServer(new RequestPresetSlotChangePacket(1, this.selectedPreset, SkillUtils.getSkillId(this.slot2)));
                        return true;
                     }

                     if (this.slot == 3) {
                        this.slot3 = this.selectedSkill;
                        this.selectedSkill = null;
                        Minecraft.m_91087_().m_91106_().m_120367_(SimpleSoundInstance.m_119752_(SoundEvents.f_11871_, 1.0F));
                        TensuraNetwork.INSTANCE.sendToServer(new RequestPresetSlotChangePacket(2, this.selectedPreset, SkillUtils.getSkillId(this.slot3)));
                        return true;
                     }
                  }
               }
            }
         } else if (pButton == 1) {
            ManasSkill var10001;
            switch(this.slot) {
            case 1:
               var10001 = this.slot1;
               break;
            case 2:
               var10001 = this.slot2;
               break;
            case 3:
               var10001 = this.slot3;
               break;
            default:
               var10001 = null;
            }

            this.selectedSkill = var10001;
            Minecraft.m_91087_().m_91106_().m_120367_(SimpleSoundInstance.m_119752_(SoundEvents.f_12490_, 1.0F));
         } else if (pButton == 2) {
            switch(this.slot) {
            case 1:
               this.slot1 = null;
               break;
            case 2:
               this.slot2 = null;
               break;
            case 3:
               this.slot3 = null;
            }

            TensuraNetwork.INSTANCE.sendToServer(new RequestPresetSlotChangePacket(this.slot - 1, this.selectedPreset));
            TensuraGUIHelper.playSound(SoundEvents.f_12016_, 1.0F);
         }
      }

      if (pX > (double)(this.getGuiLeft() + 190) && pX < (double)(this.getGuiLeft() + 203) && pY > (double)(this.getGuiTop() + 35) && pY < (double)(this.getGuiTop() + 48) && this.selectedSkill != null) {
         instance = this.getSkillInstance(this.selectedSkill);
         if (instance == null) {
            return false;
         } else if (!instance.canBeToggled(this.player)) {
            return false;
         } else if (!instance.canInteractSkill(this.player)) {
            return false;
         } else {
            Minecraft.m_91087_().m_91106_().m_120367_(SimpleSoundInstance.m_119752_(SoundEvents.f_11871_, 1.0F));
            SkillAPI.skillTogglePacket(SkillUtils.getSkillId(this.selectedSkill));
            return true;
         }
      } else if (pX > (double)(this.getGuiLeft() + 5) && pX < (double)(this.getGuiLeft() + 23) && pY > (double)(this.getGuiTop() + 28) && pY < (double)(this.getGuiTop() + 38)) {
         Minecraft.m_91087_().m_91106_().m_120367_(SimpleSoundInstance.m_119752_(SoundEvents.f_12490_, 1.0F));
         TensuraNetwork.INSTANCE.sendToServer(new GUISwitchPacket(0));
         return true;
      } else {
         if (pX >= (double)(this.getGuiLeft() + 98) && pX < (double)(this.getGuiLeft() + 109) && pY >= (double)(this.getGuiTop() + 48) && pY < (double)(this.getGuiTop() + 88)) {
            this.scrolling = true;
         }

         return super.m_6375_(pX, pY, pButton);
      }
   }

   private int mouseOverSkillSlot(double pX, double pY) {
      if (pX > (double)(this.getGuiLeft() + 5) && pX < (double)(this.getGuiLeft() + 109) && pY > (double)(this.getGuiTop() + 111) && pY < (double)(this.getGuiTop() + 126)) {
         return 1;
      } else if (pX > (double)(this.getGuiLeft() + 5) && pX < (double)(this.getGuiLeft() + 109) && pY > (double)(this.getGuiTop() + 127) && pY < (double)(this.getGuiTop() + 142)) {
         return 2;
      } else {
         return pX > (double)(this.getGuiLeft() + 5) && pX < (double)(this.getGuiLeft() + 109) && pY > (double)(this.getGuiTop() + 143) && pY < (double)(this.getGuiTop() + 158) ? 3 : 0;
      }
   }

   public boolean m_6050_(double pMouseX, double pMouseY, double pDelta) {
      if (this.isScrollBarActive()) {
         int i = this.getOffscreenRows();
         float f = (float)pDelta / (float)i;
         this.scrollOffs = Mth.m_14036_(this.scrollOffs - f, 0.0F, 1.0F);
         this.startIndex = Math.max(0, Math.min(this.startIndex - (int)pDelta, ((List)this.filteredSkills.getValue()).size() - 3));
      }

      return true;
   }

   public boolean m_7979_(double pMouseX, double pMouseY, int pButton, double pDragX, double pDragY) {
      if (this.scrolling && this.isScrollBarActive()) {
         int i = this.getGuiTop() + 48;
         int j = i + 34;
         this.scrollOffs = (float)((pMouseY - (double)i - 6.5D) / (double)((float)(j - i) - 13.0F));
         this.scrollOffs = Mth.m_14036_(this.scrollOffs, 0.0F, 1.0F);
         this.startIndex = (int)((double)(this.scrollOffs * (float)this.getOffscreenRows()) + 0.5D);
         return true;
      } else {
         return super.m_7979_(pMouseX, pMouseY, pButton, pDragX, pDragY);
      }
   }

   private boolean isScrollBarActive() {
      return ((List)this.filteredSkills.getValue()).size() > 3;
   }

   private int getOffscreenRows() {
      return ((List)this.filteredSkills.getValue()).size() - 3;
   }

   private MutableComponent skillName(@Nullable ManasSkill skill) {
      return skill != null && skill.getName() != null ? skill.getName() : Component.m_237115_("tensura.race.selection.skills.empty");
   }

   private void loadPreset() {
      TensuraSkillCapability.getFrom(this.player).ifPresent((cap) -> {
         this.slot1 = cap.getSkillInPresetSlot(this.selectedPreset, 0);
         this.slot2 = cap.getSkillInPresetSlot(this.selectedPreset, 1);
         this.slot3 = cap.getSkillInPresetSlot(this.selectedPreset, 2);
      });
   }

   @Nullable
   private ManasSkillInstance getSkillInstance(ManasSkill skill) {
      Optional<ManasSkillInstance> instance = SkillAPI.getSkillsFrom(this.player).getSkill(skill);
      return (ManasSkillInstance)instance.orElse((Object)null);
   }

   private Predicate<? super ManasSkill> filter(String filter) {
      Predicate<? super ManasSkill> predicate = (manasSkill) -> {
         if (!(manasSkill instanceof Battewill)) {
            return true;
         } else {
            Battewill battewill = (Battewill)manasSkill;
            ManasSkillInstance instance = this.getSkillInstance(manasSkill);
            if (instance == null) {
               return true;
            } else {
               String var5 = filter.toLowerCase();
               byte var6 = -1;
               switch(var5.hashCode()) {
               case -1714492862:
                  if (var5.equals("f:toggleable")) {
                     var6 = 8;
                  }
                  break;
               case -1714391181:
                  if (var5.equals("f:toggledoff")) {
                     var6 = 10;
                  }
                  break;
               case -1650989882:
                  if (var5.equals("f:offcooldown")) {
                     var6 = 12;
                  }
                  break;
               case -1622196529:
                  if (var5.equals("f:learned")) {
                     var6 = 5;
                  }
                  break;
               case -470944933:
                  if (var5.equals("f:toggledon")) {
                     var6 = 9;
                  }
                  break;
               case -38772971:
                  if (var5.equals("f:mastered")) {
                     var6 = 3;
                  }
                  break;
               case 3220:
                  if (var5.equals("f:")) {
                     var6 = 0;
                  }
                  break;
               case 185650330:
                  if (var5.equals("f:active")) {
                     var6 = 1;
                  }
                  break;
               case 571693117:
                  if (var5.equals("f:temporary")) {
                     var6 = 7;
                  }
                  break;
               case 947711918:
                  if (var5.equals("f:unmastered")) {
                     var6 = 4;
                  }
                  break;
               case 1251519410:
                  if (var5.equals("f:learning")) {
                     var6 = 6;
                  }
                  break;
               case 1296771550:
                  if (var5.equals("f:oncooldown")) {
                     var6 = 11;
                  }
                  break;
               case 1829950483:
                  if (var5.equals("f:passive")) {
                     var6 = 2;
                  }
               }

               boolean var10000;
               switch(var6) {
               case 0:
                  var10000 = false;
                  break;
               case 1:
                  var10000 = !battewill.canBeSlotted(instance);
                  break;
               case 2:
                  var10000 = battewill.canBeSlotted(instance);
                  break;
               case 3:
                  var10000 = !instance.isMastered(this.player);
                  break;
               case 4:
                  var10000 = instance.isMastered(this.player);
                  break;
               case 5:
                  var10000 = instance.getMastery() < 0 || !instance.isMastered(this.player);
                  break;
               case 6:
                  var10000 = instance.getMastery() >= 0;
                  break;
               case 7:
                  var10000 = !instance.isTemporarySkill();
                  break;
               case 8:
                  var10000 = !instance.canBeToggled(this.player);
                  break;
               case 9:
                  var10000 = !instance.isToggled();
                  break;
               case 10:
                  var10000 = !instance.canBeToggled(this.player) || instance.isToggled();
                  break;
               case 11:
                  var10000 = !instance.onCoolDown();
                  break;
               case 12:
                  var10000 = instance.onCoolDown();
                  break;
               default:
                  var10000 = filter.isEmpty() && filter.isBlank() ? false : !battewill.getName().getString().toLowerCase().contains(filter.toLowerCase());
               }

               return var10000;
            }
         }
      };
      return predicate;
   }
}
