package com.github.manasmods.tensura.handler;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.manascore.api.skills.capability.SkillStorage;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.magic.summon.SummonElementalMagic;
import com.github.manasmods.tensura.ability.magic.summon.SummonHoundDogMagic;
import com.github.manasmods.tensura.api.entity.subclass.IElementalSpirit;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.effect.InsanityEffect;
import com.github.manasmods.tensura.entity.HoundDogEntity;
import com.github.manasmods.tensura.entity.variant.HoundDogVariant;
import com.github.manasmods.tensura.race.Race;
import com.github.manasmods.tensura.race.merfolk.MerfolkRace;
import com.github.manasmods.tensura.registry.blocks.TensuraBlocks;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.items.TensuraArmorItems;
import com.github.manasmods.tensura.registry.items.TensuraToolItems;
import com.github.manasmods.tensura.registry.magic.SpiritualMagics;
import com.github.manasmods.tensura.registry.sound.TensuraSoundEvents;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import net.minecraft.ChatFormatting;
import net.minecraft.core.BlockPos;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.Style;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.tags.FluidTags;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.SimpleContainer;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.entity.EquipmentSlot;
import net.minecraft.world.entity.animal.Animal;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.FireworkRocketItem;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Items;
import net.minecraft.world.item.crafting.RecipeType;
import net.minecraft.world.item.crafting.SmeltingRecipe;
import net.minecraft.world.item.enchantment.EnchantmentHelper;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.block.entity.BlockEntity;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.block.state.properties.Property;
import net.minecraftforge.event.entity.living.AnimalTameEvent;
import net.minecraftforge.event.entity.player.PlayerEvent.BreakSpeed;
import net.minecraftforge.event.entity.player.PlayerEvent.NameFormat;
import net.minecraftforge.event.entity.player.PlayerInteractEvent.EntityInteract;
import net.minecraftforge.event.entity.player.PlayerInteractEvent.LeftClickBlock;
import net.minecraftforge.event.entity.player.PlayerInteractEvent.RightClickBlock;
import net.minecraftforge.event.entity.player.PlayerInteractEvent.RightClickItem;
import net.minecraftforge.event.level.BlockEvent.BreakEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod.EventBusSubscriber;
import net.minecraftforge.fml.common.Mod.EventBusSubscriber.Bus;

@EventBusSubscriber(
   modid = "tensura",
   bus = Bus.FORGE
)
public class PlayerInteractionHandler {
   @SubscribeEvent
   public static void getUsername(NameFormat e) {
      e.setDisplayname(TensuraEPCapability.getDisplayName(e.getEntity(), e.getDisplayname()));
   }

   @SubscribeEvent
   public static void onInteractBlock(LeftClickBlock e) {
      if (SkillUtils.noInteractiveMode(e.getEntity())) {
         e.setCanceled(true);
      }

   }

   @SubscribeEvent
   public static void onInteractItem(RightClickItem e) {
      if (SkillUtils.noInteractiveMode(e.getEntity())) {
         e.setCanceled(true);
      } else {
         ItemStack stack = e.getItemStack();
         Player player = e.getEntity();
         if (player.m_21255_() && stack.m_41720_() instanceof FireworkRocketItem) {
            ItemStack chestPlate = player.m_6844_(EquipmentSlot.CHEST);
            if (chestPlate.m_150930_((Item)TensuraArmorItems.BAT_GLIDER.get())) {
               e.setCanceled(true);
            } else {
               Race race = TensuraPlayerCapability.getRace(player);
               if (race != null && race.canFly() && !chestPlate.canElytraFly(player)) {
                  e.setCanceled(true);
               }
            }
         }
      }

   }

   @SubscribeEvent
   public static void onInteractEntity(EntityInteract e) {
      if (SkillUtils.noInteractiveMode(e.getEntity())) {
         e.setCanceled(true);
      }

   }

   @SubscribeEvent
   public static void onTaming(AnimalTameEvent e) {
      Animal animal = e.getAnimal();
      if (!animal.m_9236_().m_5776_()) {
         Player owner = e.getTamer();
         TensuraEPCapability.getFrom(animal).ifPresent((cap) -> {
            cap.setPermanentOwner(owner.m_20148_());
            TensuraEPCapability.sync(animal);
         });
         if (animal instanceof IElementalSpirit) {
            IElementalSpirit spirit = (IElementalSpirit)animal;
            SkillStorage storage = SkillAPI.getSkillsFrom(owner);
            ManasSkill skill;
            Optional optional;
            ManasSkillInstance instance;
            if (spirit.getSpiritLevel().getId() == 2) {
               skill = (ManasSkill)SpiritualMagics.SUMMON_MEDIUM_ELEMENTAL.get();
               optional = storage.getSkill(skill);
               if (optional.isEmpty()) {
                  instance = new ManasSkillInstance(skill);
                  SummonElementalMagic.addSpiritSummonLevel(instance, spirit);
                  if (storage.learnSkill(instance)) {
                     owner.m_5661_(Component.m_237110_("tensura.skill.acquire", new Object[]{skill.getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GOLD)), false);
                  }
               } else {
                  SummonElementalMagic.addSpiritSummonLevel((ManasSkillInstance)optional.get(), spirit);
                  storage.syncChanges();
               }
            } else if (spirit.getSpiritLevel().getId() == 3) {
               skill = (ManasSkill)SpiritualMagics.SUMMON_GREATER_ELEMENTAL.get();
               optional = storage.getSkill(skill);
               if (optional.isEmpty()) {
                  instance = new ManasSkillInstance(skill);
                  SummonElementalMagic.addSpiritSummonLevel(instance, spirit);
                  if (storage.learnSkill(instance)) {
                     owner.m_5661_(Component.m_237110_("tensura.skill.acquire", new Object[]{skill.getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GOLD)), false);
                  }
               } else {
                  SummonElementalMagic.addSpiritSummonLevel((ManasSkillInstance)optional.get(), spirit);
                  storage.syncChanges();
               }
            }
         } else if (animal instanceof HoundDogEntity) {
            HoundDogEntity dog = (HoundDogEntity)animal;
            if (dog.getVariant() == HoundDogVariant.EVOLVED && SkillAPI.getSkillsFrom(owner).learnSkill((ManasSkill)SpiritualMagics.SUMMON_HOUND_DOG.get())) {
               owner.m_5661_(Component.m_237110_("tensura.skill.acquire", new Object[]{((SummonHoundDogMagic)SpiritualMagics.SUMMON_HOUND_DOG.get()).getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GOLD)), false);
            }
         }

      }
   }

   @SubscribeEvent
   public static void onBlockBreak(BreakEvent e) {
      Player player = e.getPlayer();
      if (player.m_21023_((MobEffect)TensuraMobEffects.INSANITY.get()) && player.m_217043_().m_216332_(0, 30) == 3) {
         InsanityEffect.playInsanitySound((SoundEvent)TensuraSoundEvents.MC_CAVE19.get(), player, 0.5F);
      }

      if (!e.isCanceled()) {
         if (!player.m_150110_().f_35937_) {
            Level var3 = player.m_9236_();
            if (var3 instanceof ServerLevel) {
               ServerLevel level = (ServerLevel)var3;
               if (SkillUtils.canAutoSmelt(player)) {
                  BlockPos pos = e.getPos();
                  BlockEntity blockEntity = level.m_7702_(pos);
                  List<ItemStack> drops = Block.m_49874_(e.getState(), level, pos, blockEntity, player, player.m_21205_());
                  List<ItemStack> smeltedList = new ArrayList();
                  Iterator var7 = drops.iterator();

                  while(var7.hasNext()) {
                     ItemStack itemStack = (ItemStack)var7.next();
                     Optional<SmeltingRecipe> recipe = level.m_7465_().m_44015_(RecipeType.f_44108_, new SimpleContainer(new ItemStack[]{itemStack}), level);
                     if (!recipe.isEmpty()) {
                        ItemStack smelted = ((SmeltingRecipe)recipe.get()).m_8043_().m_41777_();
                        smelted.m_41764_(itemStack.m_41613_());
                        smeltedList.add(smelted);
                     }
                  }

                  if (!smeltedList.isEmpty()) {
                     level.m_46953_(pos, false, player);
                     e.getState().m_60734_().m_49805_(level, pos, e.getExpToDrop());
                     smeltedList.forEach((stack) -> {
                        Block.m_49840_(level, pos, stack);
                     });
                     e.setCanceled(true);
                     level.m_6263_((Player)null, (double)pos.m_123341_(), (double)pos.m_123342_(), (double)pos.m_123343_(), SoundEvents.f_11909_, SoundSource.PLAYERS, 0.5F, 1.0F);
                     level.m_8767_(ParticleTypes.f_123762_, (double)pos.m_123341_() + 0.5D, (double)pos.m_123342_() + 0.5D, (double)pos.m_123343_() + 0.5D, 5, 0.04D, 0.06D, 0.04D, 0.05D);
                  }
               }
            }

         }
      }
   }

   @SubscribeEvent
   public static void getBreakSpeed(BreakSpeed event) {
      Player player = event.getEntity();
      Race race = TensuraPlayerCapability.getRace(player);
      if (player.m_204029_(FluidTags.f_13131_) && !EnchantmentHelper.m_44934_(player)) {
         if (race instanceof MerfolkRace) {
            event.setNewSpeed(event.getOriginalSpeed() * 5.0F);
         } else if (player.m_21205_().m_150930_((Item)TensuraToolItems.SISSIE_TOOTH_PICKAXE.get())) {
            event.setNewSpeed(event.getOriginalSpeed() * 5.0F);
         }

      }
   }

   @SubscribeEvent
   public static void onRightClickBlockEvent(RightClickBlock event) {
      Player player = event.getEntity();
      if (SkillUtils.noInteractiveMode(player)) {
         event.setCanceled(true);
      } else {
         Level level = event.getLevel();
         BlockPos blockPos = event.getPos();
         BlockState blockState = level.m_8055_(blockPos);
         ItemStack itemStack = event.getItemStack();
         InteractionHand interactionHand = event.getHand();
         if ((itemStack.m_150930_(Items.f_42409_) || itemStack.m_150930_(Items.f_42613_)) && (blockState.m_60713_((Block)TensuraBlocks.UNLIT_TORCH.get()) || blockState.m_60713_((Block)TensuraBlocks.UNLIT_WALL_TORCH.get()) || blockState.m_60713_((Block)TensuraBlocks.UNLIT_LANTERN.get()))) {
            event.setCanceled(true);
            player.f_20911_ = false;
            lightUnlitObjects(blockState, blockPos, level, player, itemStack, interactionHand);
         }

      }
   }

   private static void lightUnlitObjects(BlockState blockState, BlockPos blockPos, Level level, Player player, ItemStack itemStack, InteractionHand interactionHand) {
      if (level instanceof ServerLevel) {
         ServerLevel serverLevel = (ServerLevel)level;
         int ID = blockState.m_60713_((Block)TensuraBlocks.UNLIT_TORCH.get()) ? 0 : (blockState.m_60713_((Block)TensuraBlocks.UNLIT_WALL_TORCH.get()) ? 1 : (blockState.m_60713_((Block)TensuraBlocks.UNLIT_LANTERN.get()) ? 2 : -1));
         BlockState var10000;
         switch(ID) {
         case 0:
            var10000 = copyBlockState(Blocks.f_50081_.m_49966_(), blockState);
            break;
         case 1:
            var10000 = copyBlockState(Blocks.f_50082_.m_49966_(), blockState);
            break;
         case 2:
            var10000 = copyBlockState(Blocks.f_50681_.m_49966_(), blockState);
            break;
         default:
            var10000 = null;
         }

         BlockState block = var10000;
         if (block != null) {
            serverLevel.m_46597_(blockPos, block);
            serverLevel.m_5594_((Player)null, player.m_20183_(), itemStack.m_150930_(Items.f_42409_) ? SoundEvents.f_11942_ : SoundEvents.f_11874_, SoundSource.PLAYERS, 1.0F, 1.0F);
            player.m_21011_(interactionHand, true);
            if (!player.m_150110_().f_35937_) {
               if (itemStack.m_150930_(Items.f_42409_)) {
                  itemStack.m_41622_(1, (Player)Objects.requireNonNull(player), (p) -> {
                     p.m_21190_(interactionHand);
                  });
               } else {
                  itemStack.m_41774_(1);
               }

            }
         }
      }
   }

   private static BlockState copyBlockState(BlockState modifyState, BlockState copyState) {
      Iterator var2 = copyState.m_61147_().iterator();

      while(var2.hasNext()) {
         Property<?> property = (Property)var2.next();
         if (modifyState.m_61138_(property)) {
            modifyState = copyProperty(modifyState, copyState, property);
         }
      }

      return modifyState;
   }

   private static <T extends Comparable<T>> BlockState copyProperty(BlockState original, BlockState copyFrom, Property<T> property) {
      return (BlockState)original.m_61124_(property, copyFrom.m_61143_(property));
   }
}
