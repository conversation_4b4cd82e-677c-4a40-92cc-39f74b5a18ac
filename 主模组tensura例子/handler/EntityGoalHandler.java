package com.github.manasmods.tensura.handler;

import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.api.entity.ai.AvoidFearedEntityGoal;
import com.github.manasmods.tensura.api.entity.ai.AvoidMagicEngineGoal;
import com.github.manasmods.tensura.api.entity.ai.FollowSwarmLeaderGoal;
import com.github.manasmods.tensura.api.entity.ai.PassiveMeleeAttackGoal;
import com.github.manasmods.tensura.api.entity.ai.TensuraOwnerHurtByTargetGoal;
import com.github.manasmods.tensura.api.entity.ai.TensuraOwnerHurtGoal;
import com.github.manasmods.tensura.config.SpawnRateConfig;
import com.github.manasmods.tensura.config.TensuraConfig;
import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.entity.GiantCodEntity;
import com.github.manasmods.tensura.entity.GiantSalmonEntity;
import com.github.manasmods.tensura.entity.human.HinataSakaguchiEntity;
import com.github.manasmods.tensura.registry.entity.TensuraVillagers;
import com.github.manasmods.tensura.registry.items.TensuraMaterialItems;
import com.github.manasmods.tensura.registry.items.TensuraMobDropItems;
import com.github.manasmods.tensura.registry.items.TensuraSmithingSchematicItems;
import it.unimi.dsi.fastutil.ints.Int2ObjectMap;
import java.util.Iterator;
import java.util.List;
import javax.annotation.Nullable;
import net.minecraft.core.BlockPos;
import net.minecraft.network.chat.Component;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.tags.TagKey;
import net.minecraft.util.RandomSource;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.Mob;
import net.minecraft.world.entity.PathfinderMob;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.goal.AvoidEntityGoal;
import net.minecraft.world.entity.ai.goal.WrappedGoal;
import net.minecraft.world.entity.ai.goal.target.TargetGoal;
import net.minecraft.world.entity.animal.Cod;
import net.minecraft.world.entity.animal.Salmon;
import net.minecraft.world.entity.boss.enderdragon.EnderDragon;
import net.minecraft.world.entity.npc.Villager;
import net.minecraft.world.entity.npc.VillagerProfession;
import net.minecraft.world.entity.npc.VillagerTrades.ItemListing;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Items;
import net.minecraft.world.item.MapItem;
import net.minecraft.world.item.trading.MerchantOffer;
import net.minecraft.world.level.GameRules;
import net.minecraft.world.level.ItemLike;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.levelgen.structure.Structure;
import net.minecraft.world.level.saveddata.maps.MapItemSavedData;
import net.minecraft.world.level.saveddata.maps.MapDecoration.Type;
import net.minecraftforge.event.entity.EntityJoinLevelEvent;
import net.minecraftforge.event.entity.living.LivingDeathEvent;
import net.minecraftforge.event.entity.living.LivingHurtEvent;
import net.minecraftforge.event.village.VillagerTradesEvent;
import net.minecraftforge.eventbus.api.EventPriority;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod.EventBusSubscriber;
import net.minecraftforge.fml.common.Mod.EventBusSubscriber.Bus;

@EventBusSubscriber(
   modid = "tensura",
   bus = Bus.FORGE
)
public class EntityGoalHandler {
   @SubscribeEvent
   public static void onJoinWorld(EntityJoinLevelEvent e) {
      if (!e.getLevel().m_5776_()) {
         Entity var3 = e.getEntity();
         if (var3 instanceof Salmon) {
            Salmon salmon = (Salmon)var3;
            salmon.f_21345_.m_25352_(6, new FollowSwarmLeaderGoal(salmon, GiantSalmonEntity.class));
         } else {
            var3 = e.getEntity();
            if (var3 instanceof Cod) {
               Cod cod = (Cod)var3;
               cod.f_21345_.m_25352_(6, new FollowSwarmLeaderGoal(cod, GiantCodEntity.class));
            }
         }

         Entity var7 = e.getEntity();
         if (var7 instanceof Mob) {
            Mob mob = (Mob)var7;
            if (mob instanceof PathfinderMob) {
               PathfinderMob entity = (PathfinderMob)mob;
               if (mob.m_21051_(Attributes.f_22281_) == null) {
                  boolean addMelee = true;
                  Iterator var4 = mob.f_21345_.m_148105_().iterator();

                  while(var4.hasNext()) {
                     WrappedGoal goal = (WrappedGoal)var4.next();
                     if (goal.m_26015_() instanceof TargetGoal) {
                        addMelee = false;
                     }
                  }

                  if (addMelee) {
                     float speed = entity instanceof Villager ? 0.75F : 1.5F;
                     mob.f_21345_.m_25352_(2, new PassiveMeleeAttackGoal(entity, (double)speed, false));
                  }
               }

               if (!mob.m_6095_().m_204039_(TensuraTags.EntityTypes.NO_FEAR)) {
                  mob.f_21345_.m_25352_(0, new AvoidFearedEntityGoal(entity, 30.0F, 1.5D, 2.0D));
               }

               if (mob.m_6095_().m_204039_(TensuraTags.EntityTypes.MONSTER)) {
                  mob.f_21345_.m_25352_(0, new AvoidMagicEngineGoal(entity, 27.0F, 1.0D, 1.5D));
                  mob.f_21345_.m_25352_(1, new AvoidEntityGoal(entity, HinataSakaguchiEntity.class, 40.0F, 1.0D, 3.0D));
               }
            }

            mob.f_21345_.m_25352_(1, new TensuraOwnerHurtByTargetGoal(mob));
            mob.f_21345_.m_25352_(1, new TensuraOwnerHurtGoal(mob));
         }

      }
   }

   @SubscribeEvent
   public static void onCodHurt(LivingHurtEvent e) {
      LivingEntity damageSource = e.getEntity();
      if (damageSource instanceof Cod) {
         Cod cod = (Cod)damageSource;
         if (!cod.f_19853_.m_5776_()) {
            Entity var3 = e.getSource().m_7639_();
            if (var3 instanceof LivingEntity) {
               damageSource = (LivingEntity)var3;
               if (damageSource.m_6084_()) {
                  if (damageSource instanceof Player) {
                     Player player = (Player)damageSource;
                     if (player.m_7500_() || player.m_5833_()) {
                        return;
                     }
                  }

                  List<GiantCodEntity> giantCods = cod.f_19853_.m_45976_(GiantCodEntity.class, cod.m_20191_().m_82400_((double)(Integer)SpawnRateConfig.INSTANCE.giantFishAwarenessRange.get()));
                  giantCods.forEach((giantCod) -> {
                     if (!SkillHelper.isSubordinate(damageSource, giantCod)) {
                        giantCod.m_6710_(damageSource);
                     }
                  });
               }
            }
         }
      }
   }

   @SubscribeEvent
   public static void onSalmonHurt(LivingHurtEvent e) {
      LivingEntity damageSource = e.getEntity();
      if (damageSource instanceof Salmon) {
         Salmon salmon = (Salmon)damageSource;
         if (!salmon.f_19853_.m_5776_()) {
            Entity var3 = e.getSource().m_7639_();
            if (var3 instanceof LivingEntity) {
               damageSource = (LivingEntity)var3;
               if (damageSource.m_6084_()) {
                  if (damageSource instanceof Player) {
                     Player player = (Player)damageSource;
                     if (player.m_7500_() || player.m_5833_()) {
                        return;
                     }
                  }

                  List<GiantSalmonEntity> giantSalmons = salmon.f_19853_.m_45976_(GiantSalmonEntity.class, salmon.m_20191_().m_82400_((double)(Integer)SpawnRateConfig.INSTANCE.giantFishAwarenessRange.get()));
                  giantSalmons.forEach((giantSalmon) -> {
                     if (!SkillHelper.isSubordinate(damageSource, giantSalmon)) {
                        giantSalmon.m_6710_(damageSource);
                     }
                  });
               }
            }
         }
      }
   }

   @SubscribeEvent(
      priority = EventPriority.LOWEST
   )
   public static void onDragonDeath(LivingDeathEvent e) {
      if (!e.isCanceled()) {
         LivingEntity var2 = e.getEntity();
         if (var2 instanceof EnderDragon) {
            EnderDragon dragon = (EnderDragon)var2;
            if (!dragon.f_19853_.m_5776_()) {
               if (dragon.m_9236_().m_46469_().m_46207_(GameRules.f_46135_)) {
                  dragon.m_19998_((ItemLike)TensuraMobDropItems.DRAGON_ESSENCE.get());
               }
            }
         }
      }
   }

   @SubscribeEvent
   public static void addVillagerTrades(VillagerTradesEvent event) {
      Int2ObjectMap trades;
      if (event.getType() == VillagerProfession.f_35588_) {
         trades = event.getTrades();
         EntityGoalHandler.TreasureMapForHighCrystals labyrinthMap = new EntityGoalHandler.TreasureMapForHighCrystals(40, 64, TensuraTags.Structures.ON_LABYRINTH_EXPLORER_MAPS, "tensura.map.labyrinth", Type.RED_X, 1, 5, (Double)TensuraConfig.INSTANCE.entitiesConfig.labyrinthMapChance.get());
         ((List)trades.get(5)).add(labyrinthMap);
         EntityGoalHandler.TreasureMapForHighCrystals hellGateMap = new EntityGoalHandler.TreasureMapForHighCrystals(40, 64, TensuraTags.Structures.ON_HELL_GATE_EXPLORER_MAPS, "tensura.map.hell_gate", Type.RED_X, 1, 5, (Double)TensuraConfig.INSTANCE.entitiesConfig.hellGateMapChance.get());
         ((List)trades.get(5)).add(hellGateMap);
      }

      if (event.getType() == TensuraVillagers.GEARSMITH.get()) {
         trades = event.getTrades();
         int emeraldsPrice = 7;
         addSingleTrade(1, trades, (ItemLike)TensuraMaterialItems.SILVER_INGOT.get(), Items.f_42616_, 4, 1, 32, 2, 0.9F);
         addDoubleTrade(1, trades, Items.f_42454_, Items.f_42616_, (ItemLike)TensuraSmithingSchematicItems.LEATHER_GEAR.get(), 1, emeraldsPrice, 1, 8, 2, 0.9F);
         addDoubleTrade(1, trades, Items.f_42416_, Items.f_42616_, (ItemLike)TensuraSmithingSchematicItems.IRON_GEAR.get(), 1, emeraldsPrice, 1, 8, 2, 0.9F);
         addDoubleTrade(1, trades, Items.f_42417_, Items.f_42616_, (ItemLike)TensuraSmithingSchematicItems.GOLD_GEAR.get(), 1, emeraldsPrice, 1, 8, 2, 0.9F);
         addDoubleTrade(1, trades, (ItemLike)TensuraMaterialItems.SILVER_INGOT.get(), Items.f_42616_, (ItemLike)TensuraSmithingSchematicItems.SILVER_GEAR.get(), 1, emeraldsPrice, 1, 8, 2, 0.9F);
         emeraldsPrice = 12;
         addSingleTrade(2, trades, (ItemLike)TensuraMobDropItems.LOW_QUALITY_MAGIC_CRYSTAL.get(), Items.f_42616_, 10, 1, 32, 3, 0.9F);
         addDoubleTrade(2, trades, (ItemLike)TensuraMobDropItems.MONSTER_LEATHER_A.get(), Items.f_42616_, (ItemLike)TensuraSmithingSchematicItems.MONSTER_LEATHER_GEAR.get(), 1, emeraldsPrice, 1, 32, 3, 0.9F);
         addDoubleTrade(2, trades, (ItemLike)TensuraMobDropItems.MONSTER_LEATHER_B.get(), Items.f_42616_, (ItemLike)TensuraSmithingSchematicItems.MONSTER_LEATHER_GEAR.get(), 1, emeraldsPrice, 1, 32, 3, 0.9F);
         addDoubleTrade(2, trades, (ItemLike)TensuraMobDropItems.MONSTER_LEATHER_C.get(), Items.f_42616_, (ItemLike)TensuraSmithingSchematicItems.MONSTER_LEATHER_GEAR.get(), 1, emeraldsPrice, 1, 32, 3, 0.9F);
         addDoubleTrade(2, trades, (ItemLike)TensuraMobDropItems.MONSTER_LEATHER_D.get(), Items.f_42616_, (ItemLike)TensuraSmithingSchematicItems.MONSTER_LEATHER_GEAR.get(), 1, emeraldsPrice, 1, 32, 3, 0.9F);
         addDoubleTrade(2, trades, (ItemLike)TensuraMobDropItems.MONSTER_LEATHER_SPECIAL_A.get(), Items.f_42616_, (ItemLike)TensuraSmithingSchematicItems.MONSTER_LEATHER_GEAR.get(), 1, emeraldsPrice, 1, 32, 3, 0.9F);
         addDoubleTrade(2, trades, (ItemLike)TensuraMobDropItems.KNIGHT_SPIDER_CARAPACE.get(), Items.f_42616_, (ItemLike)TensuraSmithingSchematicItems.KNIGHT_SPIDER_CARAPACE_GEAR.get(), 1, emeraldsPrice, 1, 32, 3, 0.9F);
         addDoubleTrade(2, trades, (ItemLike)TensuraMobDropItems.GIANT_ANT_CARAPACE.get(), Items.f_42616_, (ItemLike)TensuraSmithingSchematicItems.ANT_CARAPACE_GEAR.get(), 1, emeraldsPrice, 1, 32, 3, 0.9F);
         addDoubleTrade(2, trades, (ItemLike)TensuraMobDropItems.SERPENT_SCALE.get(), Items.f_42616_, (ItemLike)TensuraSmithingSchematicItems.SERPENT_SCALEMAIL_GEAR.get(), 1, emeraldsPrice, 1, 32, 3, 0.9F);
         emeraldsPrice = 25;
         addSingleTrade(3, trades, (ItemLike)TensuraMobDropItems.MEDIUM_QUALITY_MAGIC_CRYSTAL.get(), Items.f_42616_, 5, 1, 32, 4, 0.9F);
         addDoubleTrade(3, trades, Items.f_42415_, Items.f_42616_, (ItemLike)TensuraSmithingSchematicItems.DIAMOND_GEAR.get(), 1, emeraldsPrice, 1, 32, 4, 0.9F);
         addDoubleTrade(3, trades, (ItemLike)TensuraMaterialItems.LOW_MAGISTEEL_INGOT.get(), Items.f_42616_, (ItemLike)TensuraSmithingSchematicItems.LOW_MAGISTEEL_GEAR.get(), 1, emeraldsPrice, 1, 32, 4, 0.9F);
         addDoubleTrade(3, trades, (ItemLike)TensuraMaterialItems.HIGH_MAGISTEEL_INGOT.get(), Items.f_42616_, (ItemLike)TensuraSmithingSchematicItems.HIGH_MAGISTEEL_GEAR.get(), 1, emeraldsPrice, 1, 32, 4, 0.9F);
         addDoubleTrade(3, trades, (ItemLike)TensuraMobDropItems.ARMOURSAURUS_SHELL.get(), Items.f_42616_, (ItemLike)TensuraSmithingSchematicItems.ARMOURSAURUS_SCALEMAIL_GEAR.get(), 1, emeraldsPrice, 1, 32, 4, 0.9F);
         emeraldsPrice = 36;
         addSingleTrade(4, trades, (ItemLike)TensuraMobDropItems.HIGH_QUALITY_MAGIC_CRYSTAL.get(), Items.f_42616_, 2, 1, 32, 5, 0.9F);
         addDoubleTrade(4, trades, (ItemLike)TensuraMaterialItems.MITHRIL_INGOT.get(), Items.f_42616_, (ItemLike)TensuraSmithingSchematicItems.MITHRIL_GEAR.get(), 1, emeraldsPrice, 1, 32, 5, 0.9F);
         addDoubleTrade(4, trades, (ItemLike)TensuraMaterialItems.ORICHALCUM_INGOT.get(), Items.f_42616_, (ItemLike)TensuraSmithingSchematicItems.ORICHALCUM_GEAR.get(), 1, emeraldsPrice, 1, 32, 5, 0.9F);
         addDoubleTrade(4, trades, (ItemLike)TensuraMaterialItems.PURE_MAGISTEEL_INGOT.get(), Items.f_42616_, (ItemLike)TensuraSmithingSchematicItems.PURE_MAGISTEEL_GEAR.get(), 1, emeraldsPrice, 1, 32, 5, 0.9F);
         addDoubleTrade(4, trades, (ItemLike)TensuraMobDropItems.CHARYBDIS_SCALE.get(), Items.f_42616_, (ItemLike)TensuraSmithingSchematicItems.CHARYBDIS_SCALEMAIL_GEAR.get(), 1, emeraldsPrice, 1, 32, 5, 0.9F);
         emeraldsPrice = 49;
         addDoubleTrade(5, trades, (ItemLike)TensuraMaterialItems.ADAMANTITE_INGOT.get(), Items.f_42616_, (ItemLike)TensuraSmithingSchematicItems.ADAMANTITE_GEAR.get(), 1, emeraldsPrice, 1, 32, 6, 0.9F);
         addDoubleTrade(5, trades, (ItemLike)TensuraMaterialItems.HIHIIROKANE_INGOT.get(), Items.f_42616_, (ItemLike)TensuraSmithingSchematicItems.HIHIIROKANE_GEAR.get(), 1, emeraldsPrice, 1, 32, 6, 0.9F);
      }

   }

   private static void addSingleTrade(int villagerLevel, Int2ObjectMap<List<ItemListing>> trades, ItemLike what, ItemLike forWhat, int consumeCount, int receiveCount, int maxUses, int xp, float multiplier) {
      ((List)trades.get(villagerLevel)).add((trader, random) -> {
         return new MerchantOffer(new ItemStack(what, consumeCount), new ItemStack(forWhat, receiveCount), maxUses, xp, multiplier);
      });
   }

   private static void addDoubleTrade(int villagerLevel, Int2ObjectMap<List<ItemListing>> trades, ItemLike what1, ItemLike what2, ItemLike forWhat, int consumeCount1, int consumeCount2, int receiveCount, int maxUses, int xp, float multiplier) {
      ((List)trades.get(villagerLevel)).add((trader, random) -> {
         return new MerchantOffer(new ItemStack(what1, consumeCount1), new ItemStack(what2, consumeCount2), new ItemStack(forWhat, receiveCount), maxUses, xp, multiplier);
      });
   }

   static class TreasureMapForHighCrystals implements ItemListing {
      private final int costMin;
      private final int costMax;
      private final TagKey<Structure> destination;
      private final String displayName;
      private final Type destinationType;
      private final int maxUses;
      private final int villagerXp;
      private final double chance;

      public TreasureMapForHighCrystals(int pEmeraldCostMin, int pEmeraldCostMax, TagKey<Structure> pDestination, String pDisplayName, Type pDestinationType, int pMaxUses, int pVillagerXp, double chance) {
         this.costMin = pEmeraldCostMin;
         this.costMax = pEmeraldCostMax;
         this.destination = pDestination;
         this.displayName = pDisplayName;
         this.destinationType = pDestinationType;
         this.maxUses = pMaxUses;
         this.villagerXp = pVillagerXp;
         this.chance = chance;
      }

      @Nullable
      public MerchantOffer m_213663_(Entity pTrader, RandomSource pRandom) {
         Level var4 = pTrader.f_19853_;
         if (var4 instanceof ServerLevel) {
            ServerLevel level = (ServerLevel)var4;
            if ((double)pRandom.m_188501_() >= this.chance) {
               return null;
            } else {
               BlockPos pos = level.m_215011_(this.destination, pTrader.m_20183_(), 100, true);
               if (pos == null) {
                  return null;
               } else {
                  ItemStack map = MapItem.m_42886_(level, pos.m_123341_(), pos.m_123343_(), (byte)2, true, true);
                  MapItem.m_42850_(level, map);
                  MapItemSavedData.m_77925_(map, pos, "+", this.destinationType);
                  map.m_41714_(Component.m_237115_(this.displayName));
                  return new MerchantOffer(new ItemStack((ItemLike)TensuraMobDropItems.HIGH_QUALITY_MAGIC_CRYSTAL.get(), pRandom.m_216339_(this.costMin, this.costMax)), new ItemStack(Items.f_42522_), map, this.maxUses, this.villagerXp, 0.2F);
               }
            }
         } else {
            return null;
         }
      }
   }
}
