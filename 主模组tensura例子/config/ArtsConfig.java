package com.github.manasmods.tensura.config;

import java.util.Arrays;
import java.util.List;
import net.minecraftforge.common.ForgeConfigSpec.Builder;
import net.minecraftforge.common.ForgeConfigSpec.ConfigValue;

public class ArtsConfig {
   public final ConfigValue<List<? extends String>> battlewillManualList;
   public final ConfigValue<List<? extends String>> violentBreakEffect;

   public ArtsConfig(Builder builder) {
      builder.push("battlewillManualList");
      this.battlewillManualList = builder.comment("List of Battlewills that can be randomly obtained from using the Battlewill Manual").defineList("battlewills", Arrays.asList("tensura:aura_slash", "tensura:aura_sword", "tensura:earthshatter_kick", "tensura:ogre_sword_guillotine", "tensura:roaring_lion_punch", "tensura:dark_eight_palms", "tensura:elephant_stampede", "tensura:magic_bullet", "tensura:ogre_flame", "tensura:air_flight", "tensura:aura_shield", "tensura:battlewill", "tensura:diamond_path", "tensura:formhide", "tensura:instant_move", "tensura:violent_break"), (check) -> {
         return true;
      });
      builder.pop();
      builder.push("violentBreakEffect");
      this.violentBreakEffect = builder.comment("List of harmful effects that get removed upon activation").defineList("effects", Arrays.asList("minecraft:bad_omen", "minecraft:nausea", "minecraft:weakness", "minecraft:blindness", "minecraft:hunger", "minecraft:poison", "minecraft:darkness", "minecraft:mining_fatigue", "minecraft:levitation", "minecraft:slowness", "minecraft:unluck", "minecraft:wither", "tensura:burden", "tensura:chill", "tensura:fragility", "tensura:silence", "tensura:corrosion", "tensura:fatal_poison", "tensura:infection", "tensura:paralysis", "tensura:mind_control"), (check) -> {
         return true;
      });
      builder.pop();
   }
}
