package com.github.manasmods.tensura.config;

import net.minecraftforge.common.ForgeConfigSpec.Builder;
import net.minecraftforge.common.ForgeConfigSpec.DoubleValue;

public class AttributeConfig {
   public final DoubleValue maxArmor;
   public final DoubleValue maxAttack;
   public final DoubleValue maxHP;
   public final DoubleValue maxToughness;
   public final DoubleValue minimumSize;

   public AttributeConfig(Builder builder) {
      builder.push("attributeMax");
      this.maxHP = this.maxAttribute(builder, 1024, 30000, "maxHP");
      this.maxAttack = this.maxAttribute(builder, 2048, 10000, "maxAttack");
      this.maxArmor = this.maxAttribute(builder, 30, 1024, "maxArmor");
      this.maxToughness = this.maxAttribute(builder, 20, 1024, "maxToughness");
      builder.pop();
      builder.push("minimumSize");
      this.minimumSize = builder.comment("The minimum size a player can get through races and skills").defineInRange("minimumSize", 0.15D, 0.0D, 10000.0D);
      builder.pop();
   }

   private DoubleValue reducedPercentage(Builder builder, int defaultValue) {
      return builder.comment("The max percentage of damage getting reduced by armor points\nvanilla is 80\ndefault = " + defaultValue).defineInRange("reducedPercentage", (double)defaultValue, 0.0D, 100.0D);
   }

   private DoubleValue maxAttribute(Builder builder, int vanilla, int defaultValue, String string) {
      return builder.comment("The max value for this attribute\nvanilla is " + vanilla + "\ndefault = " + defaultValue).defineInRange(string, (double)defaultValue, 0.0D, 1.0E9D);
   }
}
