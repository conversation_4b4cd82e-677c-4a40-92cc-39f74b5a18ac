package com.github.manasmods.tensura.config;

import java.util.Arrays;
import java.util.List;
import net.minecraftforge.common.ForgeConfigSpec.Builder;
import net.minecraftforge.common.ForgeConfigSpec.ConfigValue;
import net.minecraftforge.common.ForgeConfigSpec.IntValue;

public class EnchantmentsConfig {
   public final IntValue unique;
   public final IntValue legendary;
   public final IntValue god;
   public final IntValue maxSlotting;
   public final IntValue maxResearcherBonus;
   public final ConfigValue<List<? extends String>> commonEnchantments;
   public final ConfigValue<List<? extends String>> uncommonEnchantments;
   public final ConfigValue<List<? extends String>> rareEnchantments;
   public final ConfigValue<List<? extends String>> veryRareEnchantments;
   public final ConfigValue<List<? extends String>> researcherBlacklist;
   public final ConfigValue<List<? extends String>> researcherExceedingMaxBlacklist;

   public EnchantmentsConfig(Builder builder) {
      builder.push("researcherEnchant");
      this.maxResearcherBonus = builder.comment("How many levels that Researcher and similar skills can go above the maximum level of an enchantment.").defineInRange("maxResearcherBonus", 2, -255, 255);
      this.researcherBlacklist = builder.comment("Lists of enchantments that Researcher and similar skills cannot learn or enchant.").defineList("blacklist", Arrays.asList("tensura:dead_end_rainbow", "tensura:holy_coat", "tensura:magic_interference", "tensura:tsukumogami"), (check) -> {
         return true;
      });
      this.researcherExceedingMaxBlacklist = builder.comment("Lists of enchantments that Researcher and similar skills cannot learn or enchant above the enchantment's maximum level.").defineList("maxLevelExceedingBlacklist", Arrays.asList("tensura:crushing", "tensura:sturdy", "tensura:swift", "tensura:magic_weapon", "tensura:holy_weapon", "tensura:slotting", "tensura:breathing_support", "tensura:elemental_boost", "tensura:elemental_resistance", "tensura:energy_steal", "tensura:barrier_piercing", "tensura:severance", "tensura:soul_eater"), (check) -> {
         return true;
      });
      builder.pop();
      builder.push("enchantmentEP");
      this.unique = builder.comment("The amount of EP needed to become a Unique Equipment").defineInRange("uniqueEP", 50000, 0, 1000000000);
      this.legendary = builder.comment("The amount of EP needed to become a Legend Equipment").defineInRange("legendaryEP", 250000, 0, 1000000000);
      this.god = builder.comment("The amount of EP needed to become a God Equipment").defineInRange("godEP", 1000000, 0, 1000000000);
      builder.pop();
      builder.push("maxSlotting");
      this.maxSlotting = builder.comment("The max level of Slotting a weapon can get for data pack").defineInRange("maxSlotting", 3, 0, 100);
      builder.pop();
      builder.push("commonEngrave");
      this.commonEnchantments = builder.comment("Common engraving enchantments").defineList("enchantments", Arrays.asList("tensura:crushing", "tensura:sturdy", "tensura:swift"), (check) -> {
         return true;
      });
      builder.pop();
      builder.push("uncommonEngrave");
      this.uncommonEnchantments = builder.comment("Uncommon engraving enchantments").defineList("enchantments", Arrays.asList("tensura:magic_weapon", "tensura:holy_weapon", "tensura:slotting", "tensura:breathing_support", "tensura:elemental_boost", "tensura:elemental_resistance"), (check) -> {
         return true;
      });
      builder.pop();
      builder.push("rareEngrave");
      this.rareEnchantments = builder.comment("Rare engraving enchantments").defineList("enchantments", Arrays.asList("tensura:energy_steal", "tensura:slotting", "tensura:barrier_piercing"), (check) -> {
         return true;
      });
      builder.pop();
      builder.push("veryRareEngrave");
      this.veryRareEnchantments = builder.comment("Very rare engraving enchantments").defineList("enchantments", Arrays.asList("tensura:severance", "tensura:soul_eater", "tensura:slotting", "tensura:elemental_boost", "tensura:elemental_resistance"), (check) -> {
         return true;
      });
      builder.pop();
   }
}
