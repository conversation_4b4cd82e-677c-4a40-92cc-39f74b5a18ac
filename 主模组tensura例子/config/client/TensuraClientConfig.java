package com.github.manasmods.tensura.config.client;

import net.minecraftforge.common.ForgeConfigSpec;
import net.minecraftforge.common.ForgeConfigSpec.Builder;
import org.apache.commons.lang3.tuple.Pair;

public class TensuraClientConfig {
   public static final TensuraClientConfig INSTANCE;
   public static final ForgeConfigSpec SPEC;
   public final DisplayConfig displayConfig;
   public final EffectsConfig effectsConfig;

   private TensuraClientConfig(Builder builder) {
      builder.push("displaySettings");
      this.displayConfig = new DisplayConfig(builder);
      builder.pop();
      builder.push("effectsConfig");
      this.effectsConfig = new EffectsConfig(builder);
      builder.pop();
   }

   static {
      Pair<TensuraClientConfig, ForgeConfigSpec> pair = (new Builder()).configure(TensuraClientConfig::new);
      INSTANCE = (TensuraClientConfig)pair.getKey();
      SPEC = (ForgeConfigSpec)pair.getValue();
   }
}
