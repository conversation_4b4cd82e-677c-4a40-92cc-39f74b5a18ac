package com.github.manasmods.tensura.config.client;

import java.util.Arrays;
import java.util.List;
import net.minecraftforge.common.ForgeConfigSpec.Builder;
import net.minecraftforge.common.ForgeConfigSpec.ConfigValue;

public class EffectsConfig {
   public final ConfigValue<List<? extends String>> sounds;

   public EffectsConfig(Builder builder) {
      builder.push("Insanity");
      this.sounds = builder.comment(new String[]{"List of sounds that can be heard", "default: tensura:voices3, tensura:voices4, tensura:whisper1, tensura:driplets1, minecraft:block.stone.step"}).defineList("sounds", Arrays.asList("tensura:voices3", "tensura:voices4", "tensura:whisper1", "tensura:driplets1", "minecraft:block.stone.step"), (check) -> {
         return true;
      });
      builder.pop();
   }
}
