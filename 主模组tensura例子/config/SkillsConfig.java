package com.github.manasmods.tensura.config;

import java.util.Arrays;
import java.util.List;
import net.minecraftforge.common.ForgeConfigSpec.BooleanValue;
import net.minecraftforge.common.ForgeConfigSpec.Builder;
import net.minecraftforge.common.ForgeConfigSpec.ConfigValue;
import net.minecraftforge.common.ForgeConfigSpec.DoubleValue;
import net.minecraftforge.common.ForgeConfigSpec.IntValue;

public class SkillsConfig {
   public final ConfigValue<List<? extends String>> reincarnationSkills;
   public final ConfigValue<List<? extends String>> secondSkills;
   public final ConfigValue<List<? extends String>> skillCreatorSkills;
   public final DoubleValue otherworlderDrops;
   public final DoubleValue additionalUniqueChance;
   public final DoubleValue hpForResistance;
   public final DoubleValue resistanceDamageMultiplier;
   public final DoubleValue hpForNullification;
   public final DoubleValue nullifcationDamageMultiplier;
   public final DoubleValue maximumEPSteal;
   public final BooleanValue counterPenaltyNonCharScroll;
   public final BooleanValue throwerLiquid;
   public final IntValue severanceRemoveSec;
   public final IntValue skillNumber;
   public final IntValue maxCounterBonus;
   public final IntValue bodyDespawnTick;
   public final IntValue bonusMasteryGain;
   public final IntValue bonusLearningGain;

   public SkillsConfig(Builder builder) {
      builder.push("reincarnationSkills");
      this.reincarnationSkills = builder.comment("List of Unique skills that can be randomly obtained through reincarnation").defineList("reincarnationSkills", Arrays.asList("tensura:absolute_severance", "tensura:berserk", "tensura:berserker", "tensura:bewilder", "tensura:chef", "tensura:chosen_one", "tensura:commander", "tensura:cook", "tensura:creator", "tensura:degenerate", "tensura:engorger", "tensura:envy", "tensura:falsifier", "tensura:fighter", "tensura:fusionist", "tensura:gourmand", "tensura:gourmet", "tensura:great_sage", "tensura:greed", "tensura:guardian", "tensura:healer", "tensura:infinity_prison", "tensura:lust", "tensura:martial_master", "tensura:mathematician", "tensura:merciless", "tensura:murderer", "tensura:musician", "tensura:observer", "tensura:oppressor", "tensura:predator", "tensura:pride", "tensura:reflector", "tensura:researcher", "tensura:royal_beast", "tensura:reaper", "tensura:reverser", "tensura:seer", "tensura:severer", "tensura:shadow_striker", "tensura:sloth", "tensura:sniper", "tensura:spearhead", "tensura:suppressor", "tensura:survivor", "tensura:traveler", "tensura:thrower", "tensura:tuner", "tensura:unyielding", "tensura:usurper", "tensura:villain", "tensura:wrath"), (check) -> {
         return true;
      });
      this.skillNumber = builder.comment("\nHow many skills from the list that a players can gain on Reincarnation").defineInRange("skillNumber", 1, 0, 1000);
      this.additionalUniqueChance = builder.comment("\nThe chance for a player to gain a skill from additional Unique rolls on Reincarnation\nOnly applies when the Skill Number on reincarnation is higher than 1").defineInRange("additionalUniqueChance", 100.0D, 0.0D, 100.0D);
      this.secondSkills = builder.comment("\nList of Unique skills that can be randomly obtained through reincarnation as a second Unique\nOnly applies when the Skill Number on reincarnation is 2").defineList("secondReincarnationSkills", Arrays.asList("tensura:chef", "tensura:commander", "tensura:degenerate", "tensura:falsifier", "tensura:gourmand", "tensura:great_sage", "tensura:guardian", "tensura:mathematician", "tensura:merciless", "tensura:murderer", "tensura:observer", "tensura:researcher", "tensura:royal_beast", "tensura:reverser", "tensura:seer", "tensura:suppressor", "tensura:survivor"), (check) -> {
         return true;
      });
      this.maxCounterBonus = builder.comment("\nThe maximum number of bonus Unique Skills that resetCounterBonusUnique gamerule can give to a player").defineInRange("maxCounterBonus", 100, 0, 10000);
      this.counterPenaltyNonCharScroll = builder.comment("\nApply the reset counter Penalty when a player uses a Race/Skill Reset Scroll even when they meet all of their reset requirements\nOnly applies when the resetIncompletePenalty gamerule is higher than 1").define("counterPenaltyNonCharScroll", false);
      builder.pop();
      builder.push("skillCreatorSkills");
      this.skillCreatorSkills = builder.comment("List of Unique Skills available in Skill Creator").defineList("skillCreatorSkills", Arrays.asList("tensura:anti_skill", "tensura:absolute_severance", "tensura:berserk", "tensura:berserker", "tensura:bewilder", "tensura:chef", "tensura:commander", "tensura:cook", "tensura:falsifier", "tensura:fighter", "tensura:fusionist", "tensura:gourmand", "tensura:guardian", "tensura:healer", "tensura:martial_master", "tensura:mathematician", "tensura:murderer", "tensura:musician", "tensura:observer", "tensura:oppressor", "tensura:reflector", "tensura:researcher", "tensura:royal_beast", "tensura:reaper", "tensura:reverser", "tensura:seer", "tensura:severer", "tensura:shadow_striker", "tensura:sniper", "tensura:spearhead", "tensura:suppressor", "tensura:survivor", "tensura:traveler", "tensura:thrower", "tensura:tuner", "tensura:usurper", "tensura:villain"), (check) -> {
         return true;
      });
      builder.pop();
      builder.push("otherworlderSkillsDrop");
      this.otherworlderDrops = builder.comment("The chance in percentage for otherworlders to drop their Unique skills to the attacker").defineInRange("dropChance", 0.0D, 0.0D, 100.0D);
      builder.pop();
      builder.push("resistanceValue");
      this.hpForResistance = builder.comment("How many times of current HP that incoming damage value needs to be higher to deal damage to the user with Resistances\n-1 = always applied regardless of HP").defineInRange("hpForResistance", 0.5D, -1.0D, 1000000.0D);
      this.resistanceDamageMultiplier = builder.comment("The multiplier that will be applied on incoming damage when the damage went through Resistances").defineInRange("resistanceDamageMultiplier", 0.5D, 0.0D, 1000000.0D);
      this.hpForNullification = builder.comment("How many times of current HP that incoming damage value needs to be higher to deal damage to the user with Nullifications\n-1 = always applied regardless of HP").defineInRange("hpForNullification", -1.0D, -1.0D, 1000000.0D);
      this.nullifcationDamageMultiplier = builder.comment("The multiplier that will be applied on incoming damage when the damage went through Resistances").defineInRange("nullificationDamageMultiplier", 0.0D, 0.0D, 1000000.0D);
      builder.pop();
      builder.push("masteryGain");
      this.bonusMasteryGain = builder.comment("Bonus points of mastery players get when using skills.").defineInRange("bonusMasteryGain", 0, 0, 1000000);
      this.bonusLearningGain = builder.comment("Bonus points of learning players get when using skills.").defineInRange("bonusLearningGain", 0, 0, 1000000);
      builder.pop();
      builder.push("miscSkillValue");
      this.throwerLiquid = builder.comment("Allow Thrower to place down Liquid on thrown").define("throwerLiquid", true);
      this.severanceRemoveSec = builder.comment("The number of seconds that Severance will remove itself after if not updated.\n0 = disable Severance\n-1 = last forever\ndefault = 300").defineInRange("severanceRemoveSec", 300, -1, 1000000);
      this.bodyDespawnTick = builder.comment("The number of seconds that Possession Bodies will despawn.\n0 = instant despawn\n-1 = doesn't despawn\ndefault = 300").defineInRange("bodyDespawnTick", 300, -1, 1000000);
      this.maximumEPSteal = builder.comment("The maximum amount of EP that an EP stealing ability can take").defineInRange("maximumEPSteal", 1000000.0D, 0.0D, 1.0E9D);
      builder.pop();
   }
}
