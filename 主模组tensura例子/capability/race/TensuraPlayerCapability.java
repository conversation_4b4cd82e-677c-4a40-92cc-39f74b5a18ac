package com.github.manasmods.tensura.capability.race;

import com.github.manasmods.manascore.api.attribute.AttributeModifierHelper;
import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.attribute.ManasCoreAttributes;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.config.TensuraConfig;
import com.github.manasmods.tensura.handler.CapabilityHandler;
import com.github.manasmods.tensura.menu.RaceSelectionMenu;
import com.github.manasmods.tensura.network.TensuraNetwork;
import com.github.manasmods.tensura.network.play2client.SyncPlayerCapabilityPacket;
import com.github.manasmods.tensura.race.Race;
import com.github.manasmods.tensura.race.RaceHelper;
import com.github.manasmods.tensura.registry.attribute.TensuraAttributeRegistry;
import com.github.manasmods.tensura.registry.race.TensuraRaces;
import com.github.manasmods.tensura.util.attribute.TensuraAttributeModifierIds;
import com.github.manasmods.tensura.world.TensuraGameRules;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Random;
import net.minecraft.core.BlockPos;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.nbt.ListTag;
import net.minecraft.network.FriendlyByteBuf;
import net.minecraft.network.chat.Component;
import net.minecraft.resources.ResourceKey;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.util.Mth;
import net.minecraft.world.SimpleMenuProvider;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.ai.attributes.AttributeModifier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.attributes.AttributeModifier.Operation;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import net.minecraftforge.common.ForgeMod;
import net.minecraftforge.common.capabilities.Capability;
import net.minecraftforge.common.capabilities.CapabilityManager;
import net.minecraftforge.common.capabilities.CapabilityToken;
import net.minecraftforge.common.util.LazyOptional;
import net.minecraftforge.event.AttachCapabilitiesEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod.EventBusSubscriber;
import net.minecraftforge.fml.common.Mod.EventBusSubscriber.Bus;
import net.minecraftforge.network.NetworkHooks;
import net.minecraftforge.network.PacketDistributor;
import net.minecraftforge.registries.IForgeRegistry;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.jetbrains.annotations.Nullable;

@EventBusSubscriber(
   modid = "tensura",
   bus = Bus.FORGE
)
public class TensuraPlayerCapability implements ITensuraPlayerCapability {
   private static final Logger log = LogManager.getLogger(TensuraPlayerCapability.class);
   public static final Capability<ITensuraPlayerCapability> CAPABILITY = CapabilityManager.get(new CapabilityToken<ITensuraPlayerCapability>() {
   });
   private static final ResourceLocation ID = new ResourceLocation("tensura", "player_cap");
   @Nullable
   private Race race = null;
   @Nullable
   private Race trackedEvolution = null;
   private double baseAura = 0.0D;
   private double baseMagicule = 0.0D;
   private double aura = 0.0D;
   private double magicule = 0.0D;
   private int sleepMode = 0;
   private int soulPoints = 0;
   private int resetCounter = 0;
   private boolean blessed = false;
   private boolean spiritualForm;
   private boolean demonLordSeed = false;
   private boolean trueDemonLord = false;
   private boolean heroEgg = false;
   private boolean trueHero = false;
   private double sprintSpeed = 0.13D;
   private List<ResourceLocation> intrinsicSkills = new ArrayList();

   @SubscribeEvent
   public static void attach(AttachCapabilitiesEvent<Entity> e) {
      if (e.getObject() instanceof Player) {
         e.addCapability(ID, new TensuraPlayerCapabilityProvider());
      }

   }

   public static LazyOptional<ITensuraPlayerCapability> getFrom(Player player) {
      return player.getCapability(CAPABILITY);
   }

   public static void sync(Player player) {
      if (player instanceof ServerPlayer) {
         ServerPlayer serverPlayer = (ServerPlayer)player;
         getFrom(serverPlayer).ifPresent((data) -> {
            TensuraNetwork.INSTANCE.send(PacketDistributor.TRACKING_ENTITY_AND_SELF.with(() -> {
               return serverPlayer;
            }), new SyncPlayerCapabilityPacket(data, serverPlayer.m_19879_()));
         });
      }

   }

   public void setBaseAura(double amount, LivingEntity entity) {
      int max = entity.m_9236_().m_46469_().m_46215_(TensuraGameRules.MAX_AP);
      if (amount > (double)max) {
         amount = (double)max;
      } else {
         double min = (double)TensuraGameRules.getMinEp(entity.m_9236_()) / 2.0D;
         if (amount < min) {
            amount = min;
         }
      }

      this.baseAura = amount;
      AttributeModifierHelper.setModifier(entity, (Attribute)TensuraAttributeRegistry.MAX_AURA.get(), new AttributeModifier(TensuraAttributeModifierIds.RACE_BASE_AURA_MODIFIER_ID, "tensura:race_base_aura", amount - entity.m_21172_((Attribute)TensuraAttributeRegistry.MAX_AURA.get()), Operation.ADDITION));
      TensuraEPCapability.updateEP(entity);
   }

   public void setBaseMagicule(double amount, LivingEntity entity) {
      this.setBaseMagicule(amount, entity, true);
   }

   public void setBaseMagicule(double amount, LivingEntity entity, boolean update) {
      int max = entity.m_9236_().m_46469_().m_46215_(TensuraGameRules.MAX_MP);
      if (amount > (double)max) {
         amount = (double)max;
      } else {
         double min = (double)TensuraGameRules.getMinEp(entity.m_9236_()) / 2.0D;
         if (amount < min) {
            amount = min;
         }
      }

      this.baseMagicule = amount;
      AttributeModifierHelper.setModifier(entity, (Attribute)TensuraAttributeRegistry.MAX_MAGICULE.get(), new AttributeModifier(TensuraAttributeModifierIds.RACE_BASE_MAGICULE_MODIFIER_ID, "tensura:race_base_magicule", amount - entity.m_21172_((Attribute)TensuraAttributeRegistry.MAX_MAGICULE.get()), Operation.ADDITION));
      TensuraEPCapability.updateEP(entity, update);
   }

   public void setSprintSpeed(double value, Player player) {
      if (this.race == null) {
         this.sprintSpeed = 0.13D;
      } else {
         this.sprintSpeed = Mth.m_14008_(value, this.race.getMovementSpeed(), this.race.getSprintSpeed());
      }

      double sprintMultiplier = this.race == null ? 1.2999999523162842D : this.sprintSpeed / this.race.getMovementSpeed();
      AttributeModifierHelper.setModifier(player, (Attribute)ManasCoreAttributes.SPRINTING_SPEED_MULTIPLIER.get(), new AttributeModifier(TensuraAttributeModifierIds.RACE_SPRINT_SPEED_MODIFIER_ID, "tensura:race_sprint_speed", sprintMultiplier / 1.2999999523162842D - player.m_21172_((Attribute)ManasCoreAttributes.SPRINTING_SPEED_MULTIPLIER.get()), Operation.ADDITION));
   }

   public void setRace(LivingEntity entity, Race race, boolean resetStat) {
      this.race = race;
      if (resetStat) {
         Random random = new Random();
         this.baseAura = (double)Math.round(race.getMinBaseAura() + (race.getMaxBaseAura() - race.getMinBaseAura()) * random.nextDouble());
         this.baseMagicule = (double)Math.round(race.getMinBaseMagicule() + (race.getMaxBaseMagicule() - race.getMinBaseMagicule()) * random.nextDouble());
         this.aura = this.baseAura;
         this.magicule = this.baseMagicule;
         TensuraEPCapability.getFrom(entity).ifPresent((cap) -> {
            cap.setNameable(false);
            cap.setMajin(race.isMajin());
            TensuraEPCapability.sync(entity);
         });
      }

      this.applyBaseAttributeModifiers(entity);
      this.sprintSpeed = race.getSprintSpeed();
      if (entity instanceof ServerPlayer) {
         ServerPlayer player = (ServerPlayer)entity;
         sync(player);
         TensuraEPCapability.updateEP(entity);
         this.updateRespawnPos(player, race);
      }

   }

   private void updateRespawnPos(ServerPlayer player, Race race) {
      ResourceKey<Level> dimension = race.getRespawnDimension();
      ResourceKey<Level> respawnDimension = player.m_8963_();
      if (dimension != respawnDimension) {
         if (dimension == Level.f_46428_) {
            player.m_9158_(dimension, (BlockPos)null, 0.0F, false, false);
         }
      }
   }

   public void setTrackedEvolution(Player player, Race race) {
      this.trackedEvolution = race;
   }

   public void addIntrinsicSkill(ManasSkill skill) {
      this.intrinsicSkills.add(SkillUtils.getSkillId(skill));
   }

   public void clearIntrinsicSkills() {
      this.intrinsicSkills = new ArrayList();
   }

   public CompoundTag serializeNBT() {
      CompoundTag tag = new CompoundTag();
      tag.m_128359_("race", (String)Optional.ofNullable(this.race).map((race1) -> {
         return ((IForgeRegistry)TensuraRaces.RACE_REGISTRY.get()).getKey(race1).toString();
      }).orElse(""));
      if (this.trackedEvolution == null) {
         tag.m_128473_("trackedEvolution");
      } else {
         tag.m_128359_("trackedEvolution", this.trackedEvolution.getRegistryName().toString());
      }

      tag.m_128405_("sleepMode", this.sleepMode);
      tag.m_128405_("soulPoints", this.soulPoints);
      tag.m_128405_("resetCounter", this.resetCounter);
      tag.m_128347_("baseAura", this.baseAura);
      tag.m_128347_("baseMagicule", this.baseMagicule);
      tag.m_128347_("aura", this.aura);
      tag.m_128347_("magicule", this.magicule);
      tag.m_128379_("spiritualForm", this.spiritualForm);
      tag.m_128379_("seed", this.demonLordSeed);
      tag.m_128379_("trueDemonLord", this.trueDemonLord);
      tag.m_128379_("blessed", this.blessed);
      tag.m_128379_("egg", this.heroEgg);
      tag.m_128379_("trueHero", this.trueHero);
      tag.m_128347_("movementSpeed", this.sprintSpeed);
      ListTag intrinsicList = new ListTag();

      for(int i = 0; i < this.intrinsicSkills.size(); ++i) {
         CompoundTag intrinsic = new CompoundTag();
         intrinsic.m_128359_("intrinsic" + i, ((ResourceLocation)this.intrinsicSkills.get(i)).toString());
         intrinsicList.add(intrinsic);
      }

      tag.m_128365_("intrinsicSkills", intrinsicList);
      return tag;
   }

   public void deserializeNBT(CompoundTag tag) {
      String raceValue = tag.m_128461_("race");
      ResourceLocation location;
      if (!raceValue.isEmpty()) {
         location = new ResourceLocation(raceValue);
         this.race = (Race)((IForgeRegistry)TensuraRaces.RACE_REGISTRY.get()).getValue(location);
      }

      if (tag.m_128425_("trackedEvolution", 8)) {
         location = new ResourceLocation(tag.m_128461_("trackedEvolution"));
         this.trackedEvolution = (Race)((IForgeRegistry)TensuraRaces.RACE_REGISTRY.get()).getValue(location);
      } else {
         this.trackedEvolution = null;
      }

      this.sleepMode = tag.m_128451_("sleepMode");
      this.soulPoints = tag.m_128451_("soulPoints");
      this.resetCounter = tag.m_128451_("resetCounter");
      this.baseAura = tag.m_128459_("baseAura");
      this.baseMagicule = tag.m_128459_("baseMagicule");
      this.aura = tag.m_128459_("aura");
      this.magicule = tag.m_128459_("magicule");
      this.spiritualForm = tag.m_128471_("spiritualForm");
      this.demonLordSeed = tag.m_128471_("seed");
      this.trueDemonLord = tag.m_128471_("trueDemonLord");
      this.blessed = tag.m_128471_("blessed");
      this.heroEgg = tag.m_128471_("egg");
      this.trueHero = tag.m_128471_("trueHero");
      this.sprintSpeed = tag.m_128459_("movementSpeed");
      this.intrinsicSkills.clear();
      ListTag intrinsicList = (ListTag)tag.m_128423_("intrinsicSkills");
      if (intrinsicList != null) {
         for(int i = 0; i < intrinsicList.size(); ++i) {
            CompoundTag spirit = (CompoundTag)intrinsicList.get(i);
            this.intrinsicSkills.add(ResourceLocation.m_135820_(spirit.m_128461_("intrinsic" + i)));
         }
      }

   }

   public void applyBaseAttributeModifiers(LivingEntity entity) {
      if (this.race != null) {
         double spiritualHP = (this.race.getBaseHealth() + this.race.getAdditionalSpiritualHealth()) * this.race.getSpiritualHealthMultiplier();
         AttributeModifierHelper.setModifier(entity, (Attribute)TensuraAttributeRegistry.MAX_SPIRITUAL_HEALTH.get(), new AttributeModifier(TensuraAttributeModifierIds.RACE_BASE_SPIRITUAL_HEALTH_MODIFIER_ID, "tensura:race_base_spiritual_health", spiritualHP - entity.m_21172_((Attribute)TensuraAttributeRegistry.MAX_SPIRITUAL_HEALTH.get()), Operation.ADDITION));
         AttributeModifierHelper.setModifier(entity, Attributes.f_22276_, new AttributeModifier(TensuraAttributeModifierIds.RACE_BASE_HEALTH_MODIFIER_ID, "tensura:race_base_health", this.race.getBaseHealth() - entity.m_21172_(Attributes.f_22276_), Operation.ADDITION));
         AttributeModifierHelper.setModifier(entity, Attributes.f_22281_, new AttributeModifier(TensuraAttributeModifierIds.RACE_ATTACK_DAMAGE_MODIFIER_ID, "tensura:race_attack_damage", this.race.getBaseAttackDamage() - entity.m_21172_(Attributes.f_22281_), Operation.ADDITION));
         AttributeModifierHelper.setModifier(entity, Attributes.f_22283_, new AttributeModifier(TensuraAttributeModifierIds.RACE_ATTACK_SPEED_MODIFIER_ID, "tensura:race_attack_speed", this.race.getBaseAttackSpeed() - entity.m_21172_(Attributes.f_22283_), Operation.ADDITION));
         AttributeModifierHelper.setModifier(entity, Attributes.f_22278_, new AttributeModifier(TensuraAttributeModifierIds.RACE_KNOCKBACK_RESISTANCE_MODIFIER_ID, "tensura:race_knockback_resistance", this.race.getKnockbackResistance() - entity.m_21172_(Attributes.f_22278_), Operation.ADDITION));
         double range = (double)(Math.max(RaceHelper.getRaceSize(this.race), 1.0F) * 3.0F);
         AttributeModifierHelper.setModifier(entity, (Attribute)ForgeMod.ATTACK_RANGE.get(), new AttributeModifier(TensuraAttributeModifierIds.RACE_ATTACK_REACH_MODIFIER_ID, "tensura:race_attack_reach_speed", range - entity.m_21172_((Attribute)ForgeMod.ATTACK_RANGE.get()), Operation.ADDITION));
         AttributeModifierHelper.setModifier(entity, (Attribute)ManasCoreAttributes.JUMP_POWER.get(), new AttributeModifier(TensuraAttributeModifierIds.RACE_JUMP_HEIGHT_MODIFIER_ID, "tensura:race_jump_power", this.race.getJumpHeight() - entity.m_21172_((Attribute)ManasCoreAttributes.JUMP_POWER.get()), Operation.ADDITION));
         AttributeModifierHelper.setModifier(entity, Attributes.f_22279_, new AttributeModifier(TensuraAttributeModifierIds.RACE_MOVEMENT_SPEED_MODIFIER_ID, "tensura:race_movement_speed", this.race.getMovementSpeed() - entity.m_21172_(Attributes.f_22279_), Operation.ADDITION));
         double sprintMultiplier = this.race.getSprintSpeed() / this.race.getMovementSpeed();
         AttributeModifierHelper.setModifier(entity, (Attribute)ManasCoreAttributes.SPRINTING_SPEED_MULTIPLIER.get(), new AttributeModifier(TensuraAttributeModifierIds.RACE_SPRINT_SPEED_MODIFIER_ID, "tensura:race_sprint_speed", sprintMultiplier / 1.2999999523162842D - entity.m_21172_((Attribute)ManasCoreAttributes.SPRINTING_SPEED_MULTIPLIER.get()), Operation.ADDITION));
         AttributeModifierHelper.setModifier(entity, (Attribute)TensuraAttributeRegistry.MAX_AURA.get(), new AttributeModifier(TensuraAttributeModifierIds.RACE_BASE_AURA_MODIFIER_ID, "tensura:race_base_aura", this.baseAura - entity.m_21172_((Attribute)TensuraAttributeRegistry.MAX_AURA.get()), Operation.ADDITION));
         AttributeModifierHelper.setModifier(entity, (Attribute)TensuraAttributeRegistry.MAX_MAGICULE.get(), new AttributeModifier(TensuraAttributeModifierIds.RACE_BASE_MAGICULE_MODIFIER_ID, "tensura:race_base_magicule", this.baseMagicule - entity.m_21172_((Attribute)TensuraAttributeRegistry.MAX_MAGICULE.get()), Operation.ADDITION));
      }
   }

   public static void checkForFirstLogin(Player entity) {
      if (entity instanceof ServerPlayer) {
         ServerPlayer player = (ServerPlayer)entity;
         getFrom(player).ifPresent((cap) -> {
            if (cap.getRace() == null) {
               if (player.m_9236_().m_46469_().m_46207_(TensuraGameRules.RIMURU_MODE)) {
                  RaceSelectionMenu.reincarnateAsRimuru(player);
               } else {
                  if (player.m_9236_().m_46469_().m_46207_(TensuraGameRules.SKILL_BEFORE_RACE)) {
                     cap.setRace(player, (Race)TensuraRaces.HUMAN.get(), true);
                     RaceSelectionMenu.grantUniqueSkill(player);
                  }

                  player.m_20331_(true);
                  List<ResourceLocation> races = loadRaces();
                  NetworkHooks.openScreen(player, new SimpleMenuProvider(RaceSelectionMenu::new, Component.m_237115_("tensura.race.selection")), (buf) -> {
                     buf.writeBoolean(false);
                     buf.m_236828_(races, FriendlyByteBuf::m_130085_);
                  });
               }

               RaceSelectionMenu.grantLearningResistance(player);
            }
         });
      }
   }

   public static List<ResourceLocation> loadRaces() {
      List<ResourceLocation> races = new ArrayList(((List)TensuraConfig.INSTANCE.racesConfig.startingRaces.get()).stream().map(ResourceLocation::new).toList());
      List<? extends String> randomRaces = (List)TensuraConfig.INSTANCE.racesConfig.randomRaces.get();
      if (randomRaces.isEmpty()) {
         return races;
      } else {
         Random random = new Random();
         String race = (String)randomRaces.get(random.nextInt(randomRaces.size()));
         if (!race.isEmpty() && !race.isBlank()) {
            races.add(new ResourceLocation(race));
         }

         return races;
      }
   }

   public static void resetMagiculeAura(Player player) {
      getFrom(player).ifPresent((cap) -> {
         if (cap.getRace() != null) {
            float penalty = (float)player.f_19853_.m_46469_().m_46215_(TensuraGameRules.EP_DEATH_PENALTY) / 200.0F;
            cap.setBaseAura(cap.getBaseAura() * (double)(1.0F - penalty), player);
            cap.setBaseMagicule(cap.getBaseMagicule() * (double)(1.0F - penalty), player);
            if (cap.getSoulPoints() > 0) {
               cap.setSoulPoints((int)((float)cap.getSoulPoints() * (1.0F - penalty)));
            }

            AttributeModifierHelper.setModifier(player, (Attribute)TensuraAttributeRegistry.MAX_AURA.get(), new AttributeModifier(TensuraAttributeModifierIds.RACE_BASE_AURA_MODIFIER_ID, "tensura:race_base_aura", cap.getBaseAura() - player.m_21172_((Attribute)TensuraAttributeRegistry.MAX_AURA.get()), Operation.ADDITION));
            cap.setAura(player.m_21133_((Attribute)TensuraAttributeRegistry.MAX_AURA.get()));
            AttributeModifierHelper.setModifier(player, (Attribute)TensuraAttributeRegistry.MAX_MAGICULE.get(), new AttributeModifier(TensuraAttributeModifierIds.RACE_BASE_MAGICULE_MODIFIER_ID, "tensura:race_base_magicule", cap.getBaseMagicule() - player.m_21172_((Attribute)TensuraAttributeRegistry.MAX_MAGICULE.get()), Operation.ADDITION));
            cap.setMagicule(player.m_21133_((Attribute)TensuraAttributeRegistry.MAX_MAGICULE.get()));
            sync(player);
         }
      });
   }

   @Nullable
   public static Race getRace(LivingEntity entity) {
      if (entity instanceof Player) {
         Player player = (Player)entity;
         ITensuraPlayerCapability capability = (ITensuraPlayerCapability)CapabilityHandler.getCapability(player, CAPABILITY);
         return capability == null ? null : capability.getRace();
      } else {
         return null;
      }
   }

   @Nullable
   public static Race getTrackedEvolution(LivingEntity entity) {
      if (entity instanceof Player) {
         Player player = (Player)entity;
         ITensuraPlayerCapability capability = (ITensuraPlayerCapability)CapabilityHandler.getCapability(player, CAPABILITY);
         return capability == null ? null : capability.getTrackedEvolution();
      } else {
         return null;
      }
   }

   public static double getBaseEP(Player player) {
      ITensuraPlayerCapability capability = (ITensuraPlayerCapability)CapabilityHandler.getCapability(player, CAPABILITY);
      return capability == null ? 0.0D : capability.getBaseMagicule() + capability.getBaseAura();
   }

   public static double getCurrentEP(Player player) {
      ITensuraPlayerCapability capability = (ITensuraPlayerCapability)CapabilityHandler.getCapability(player, CAPABILITY);
      return capability == null ? 0.0D : capability.getMagicule() + capability.getAura();
   }

   public static double getMagicule(Player player) {
      ITensuraPlayerCapability capability = (ITensuraPlayerCapability)CapabilityHandler.getCapability(player, CAPABILITY);
      return capability == null ? 0.0D : capability.getMagicule();
   }

   public static double getBaseMagicule(Player player) {
      ITensuraPlayerCapability capability = (ITensuraPlayerCapability)CapabilityHandler.getCapability(player, CAPABILITY);
      return capability == null ? 0.0D : capability.getBaseMagicule();
   }

   public static double getAura(Player player) {
      ITensuraPlayerCapability capability = (ITensuraPlayerCapability)CapabilityHandler.getCapability(player, CAPABILITY);
      return capability == null ? 0.0D : capability.getAura();
   }

   public static double getBaseAura(Player player) {
      ITensuraPlayerCapability capability = (ITensuraPlayerCapability)CapabilityHandler.getCapability(player, CAPABILITY);
      return capability == null ? 0.0D : capability.getBaseAura();
   }

   public static double getSprintSpeed(Player player) {
      ITensuraPlayerCapability capability = (ITensuraPlayerCapability)CapabilityHandler.getCapability(player, CAPABILITY);
      return capability == null ? 0.13D : capability.getSprintSpeed();
   }

   public static int getResetCounter(Player player) {
      ITensuraPlayerCapability capability = (ITensuraPlayerCapability)CapabilityHandler.getCapability(player, CAPABILITY);
      return capability == null ? 0 : capability.getResetCounter();
   }

   public static int getSoulPoints(Player player) {
      ITensuraPlayerCapability capability = (ITensuraPlayerCapability)CapabilityHandler.getCapability(player, CAPABILITY);
      return capability == null ? 0 : capability.getSoulPoints();
   }

   public static boolean isDemonLordSeed(Player player) {
      ITensuraPlayerCapability capability = (ITensuraPlayerCapability)CapabilityHandler.getCapability(player, CAPABILITY);
      return capability == null ? false : capability.isDemonLordSeed();
   }

   public static boolean isTrueDemonLord(Player player) {
      ITensuraPlayerCapability capability = (ITensuraPlayerCapability)CapabilityHandler.getCapability(player, CAPABILITY);
      return capability == null ? false : capability.isTrueDemonLord();
   }

   public static boolean isBlessed(Player player) {
      ITensuraPlayerCapability capability = (ITensuraPlayerCapability)CapabilityHandler.getCapability(player, CAPABILITY);
      return capability == null ? false : capability.isBlessed();
   }

   public static boolean isHeroEgg(Player player) {
      ITensuraPlayerCapability capability = (ITensuraPlayerCapability)CapabilityHandler.getCapability(player, CAPABILITY);
      return capability == null ? false : capability.isHeroEgg();
   }

   public static boolean isTrueHero(LivingEntity entity) {
      if (entity instanceof Player) {
         Player player = (Player)entity;
         ITensuraPlayerCapability capability = (ITensuraPlayerCapability)CapabilityHandler.getCapability(player, CAPABILITY);
         return capability == null ? false : capability.isTrueHero();
      } else {
         return false;
      }
   }

   public static boolean isSpiritualForm(Player player) {
      ITensuraPlayerCapability capability = (ITensuraPlayerCapability)CapabilityHandler.getCapability(player, CAPABILITY);
      return capability == null ? false : capability.isSpiritualForm();
   }

   public static List<ResourceLocation> getIntrinsicList(Player player) {
      ITensuraPlayerCapability capability = (ITensuraPlayerCapability)CapabilityHandler.getCapability(player, CAPABILITY);
      return (List)(capability == null ? new ArrayList() : capability.getIntrinsicSkills());
   }

   public static void setTrackedRace(LivingEntity entity, @Nullable Race race) {
      if (entity instanceof Player) {
         Player player = (Player)entity;
         ITensuraPlayerCapability capability = (ITensuraPlayerCapability)CapabilityHandler.getCapability(player, CAPABILITY);
         if (capability != null) {
            capability.setTrackedEvolution(player, race);
         }
      }
   }

   public static void setMagicule(Player player, double value) {
      getFrom(player).ifPresent((cap) -> {
         cap.setMagicule(value);
         sync(player);
      });
   }

   public static void setAura(Player player, double value) {
      getFrom(player).ifPresent((cap) -> {
         cap.setAura(value);
         sync(player);
      });
   }

   public static void setSprintSpeed(Player player, double value) {
      getFrom(player).ifPresent((cap) -> {
         if (cap.getRace() != null) {
            cap.setSprintSpeed(value, player);
            sync(player);
         }
      });
   }

   public static void decreaseMagicule(Player player, double value) {
      getFrom(player).ifPresent((cap) -> {
         cap.setMagicule(cap.getMagicule() - value);
      });
      sync(player);
   }

   public static void decreaseAura(Player player, double value) {
      getFrom(player).ifPresent((cap) -> {
         cap.setAura(cap.getAura() - value);
      });
      sync(player);
   }

   public static void increaseResetCounter(Player player, int i) {
      getFrom(player).ifPresent((cap) -> {
         cap.setResetCounter(Math.max(cap.getResetCounter() + i, 0));
         sync(player);
      });
   }

   public static void resetEverything(Player player) {
      getFrom(player).ifPresent((cap) -> {
         cap.setSoulPoints(0);
         cap.setDemonLordSeed(false);
         cap.setTrueDemonLord(false);
         cap.setBlessed(false);
         cap.setHeroEgg(false);
         cap.setTrueHero(false);
         cap.setSpiritualForm(false);
         cap.setSleepMode(0);
      });
      sync(player);
   }

   @Nullable
   public Race getRace() {
      return this.race;
   }

   @Nullable
   public Race getTrackedEvolution() {
      return this.trackedEvolution;
   }

   public double getBaseAura() {
      return this.baseAura;
   }

   public double getBaseMagicule() {
      return this.baseMagicule;
   }

   public double getAura() {
      return this.aura;
   }

   public double getMagicule() {
      return this.magicule;
   }

   public void setAura(double aura) {
      this.aura = aura;
   }

   public void setMagicule(double magicule) {
      this.magicule = magicule;
   }

   public int getSleepMode() {
      return this.sleepMode;
   }

   public int getSoulPoints() {
      return this.soulPoints;
   }

   public int getResetCounter() {
      return this.resetCounter;
   }

   public void setSleepMode(int sleepMode) {
      this.sleepMode = sleepMode;
   }

   public void setSoulPoints(int soulPoints) {
      this.soulPoints = soulPoints;
   }

   public void setResetCounter(int resetCounter) {
      this.resetCounter = resetCounter;
   }

   public boolean isBlessed() {
      return this.blessed;
   }

   public boolean isSpiritualForm() {
      return this.spiritualForm;
   }

   public void setBlessed(boolean blessed) {
      this.blessed = blessed;
   }

   public void setSpiritualForm(boolean spiritualForm) {
      this.spiritualForm = spiritualForm;
   }

   public boolean isDemonLordSeed() {
      return this.demonLordSeed;
   }

   public boolean isTrueDemonLord() {
      return this.trueDemonLord;
   }

   public void setDemonLordSeed(boolean demonLordSeed) {
      this.demonLordSeed = demonLordSeed;
   }

   public void setTrueDemonLord(boolean trueDemonLord) {
      this.trueDemonLord = trueDemonLord;
   }

   public boolean isHeroEgg() {
      return this.heroEgg;
   }

   public boolean isTrueHero() {
      return this.trueHero;
   }

   public void setHeroEgg(boolean heroEgg) {
      this.heroEgg = heroEgg;
   }

   public void setTrueHero(boolean trueHero) {
      this.trueHero = trueHero;
   }

   public double getSprintSpeed() {
      return this.sprintSpeed;
   }

   public void setSprintSpeed(double sprintSpeed) {
      this.sprintSpeed = sprintSpeed;
   }

   public List<ResourceLocation> getIntrinsicSkills() {
      return this.intrinsicSkills;
   }
}
