package com.github.manasmods.tensura.race.daemon;

import com.github.manasmods.tensura.ability.TensuraSkill;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.race.Race;
import com.github.manasmods.tensura.registry.race.TensuraRaces;
import com.github.manasmods.tensura.registry.skill.IntrinsicSkills;
import com.github.manasmods.tensura.util.JumpPowerHelper;
import com.mojang.datafixers.util.Pair;
import java.util.ArrayList;
import java.util.List;
import net.minecraft.ChatFormatting;
import net.minecraft.network.chat.Component;
import net.minecraft.world.entity.player.Player;
import org.jetbrains.annotations.Nullable;

public class DevilLordRace extends DaemonLordRace {
   public DevilLordRace() {
      super(Race.Difficulty.EASY);
   }

   public double getBaseHealth() {
      return 6666.0D;
   }

   public double getBaseAttackDamage() {
      return 5.0D;
   }

   public double getBaseAttackSpeed() {
      return 5.0D;
   }

   public double getKnockbackResistance() {
      return 1.0D;
   }

   public double getJumpHeight() {
      return JumpPowerHelper.defaultPlayer(3.0D);
   }

   public double getSprintSpeed() {
      return 1.4D;
   }

   public Pair<Double, Double> getBaseAuraRange() {
      return Pair.of(1000000.0D, 1000000.0D);
   }

   public Pair<Double, Double> getBaseMagiculeRange() {
      return Pair.of(1000000.0D, 1000000.0D);
   }

   @Nullable
   public Race getDefaultEvolution(Player player) {
      return null;
   }

   public double getSpiritualHealthMultiplier() {
      return 6.0D;
   }

   public List<Race> getNextEvolutions(Player player) {
      return new ArrayList();
   }

   public List<Race> getPreviousEvolutions(Player player) {
      List<Race> list = new ArrayList();
      list.add((Race)TensuraRaces.DAEMON_LORD.get());
      return list;
   }

   public double getEvolutionPercentage(Player player) {
      double percentage = 0.0D;
      if (TensuraEPCapability.getName(player) != null) {
         percentage += 25.0D;
      }

      if (!TensuraPlayerCapability.isSpiritualForm(player)) {
         percentage += 25.0D;
      }

      if (TensuraPlayerCapability.isTrueDemonLord(player) || TensuraPlayerCapability.isTrueHero(player)) {
         percentage += 50.0D;
      }

      return percentage;
   }

   public List<Component> getRequirementsForRendering(Player player) {
      List<Component> list = new ArrayList();
      list.add(Component.m_237115_("tensura.evolution_menu.name_requirement"));
      list.add(Component.m_237115_("tensura.evolution_menu.physical_body_requirement"));
      list.add(Component.m_237110_("tensura.evolution_menu.awaken_requirement", new Object[]{Component.m_237115_("tensura.attribute.true_demon_lord.name").m_130940_(ChatFormatting.DARK_PURPLE), Component.m_237115_("tensura.attribute.true_hero.name").m_130940_(ChatFormatting.GOLD)}));
      return list;
   }

   public boolean isDivine() {
      return true;
   }

   public List<TensuraSkill> getIntrinsicSkills(Player player) {
      List<TensuraSkill> list = super.getIntrinsicSkills(player);
      list.add((TensuraSkill)IntrinsicSkills.DIVINE_KI_RELEASE.get());
      return list;
   }
}
