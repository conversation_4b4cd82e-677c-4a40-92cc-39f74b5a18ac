package com.github.manasmods.tensura.race.lizardman;

import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.TensuraSkill;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.config.TensuraConfig;
import com.github.manasmods.tensura.race.Race;
import com.github.manasmods.tensura.registry.race.TensuraRaces;
import com.github.manasmods.tensura.registry.skill.IntrinsicSkills;
import com.github.manasmods.tensura.util.JumpPowerHelper;
import com.mojang.datafixers.util.Pair;
import java.util.ArrayList;
import java.util.List;
import net.minecraft.network.chat.Component;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.phys.Vec3;
import org.jetbrains.annotations.Nullable;

public class TrueDragonewtRace extends DragonewtRace {
   public double getBaseHealth() {
      return 1300.0D;
   }

   public double getBaseAttackDamage() {
      return 5.0D;
   }

   public double getBaseAttackSpeed() {
      return 4.6D;
   }

   public double getKnockbackResistance() {
      return 0.4D;
   }

   public double getJumpHeight() {
      return JumpPowerHelper.defaultPlayer(1.0D);
   }

   public double getSprintSpeed() {
      return 1.0D;
   }

   public Pair<Double, Double> getBaseAuraRange() {
      return Pair.of(600000.0D, 600000.0D);
   }

   public Pair<Double, Double> getBaseMagiculeRange() {
      return Pair.of(200000.0D, 200000.0D);
   }

   public double getAdditionalSpiritualHealth() {
      return 50.0D;
   }

   public double getSpiritualHealthMultiplier() {
      return 3.0D;
   }

   public List<TensuraSkill> getIntrinsicSkills(Player player) {
      List<TensuraSkill> list = super.getIntrinsicSkills(player);
      list.add((TensuraSkill)IntrinsicSkills.DRAGON_SKIN.get());
      list.add((TensuraSkill)IntrinsicSkills.FLAME_BREATH.get());
      list.add((TensuraSkill)IntrinsicSkills.THUNDER_BREATH.get());
      return list;
   }

   @Nullable
   public Race getDefaultEvolution(Player player) {
      return (Race)TensuraRaces.DIVINE_DRAGON.get();
   }

   @Nullable
   public Race getAwakeningEvolution(Player player) {
      return (Race)TensuraRaces.DIVINE_DRAGON.get();
   }

   public List<Race> getNextEvolutions(Player player) {
      List<Race> list = new ArrayList();
      list.add((Race)TensuraRaces.DIVINE_DRAGON.get());
      return list;
   }

   public List<Race> getPreviousEvolutions(Player player) {
      List<Race> list = new ArrayList();
      list.add((Race)TensuraRaces.DRAGONEWT.get());
      return list;
   }

   public double getEvolutionPercentage(Player player) {
      return TensuraPlayerCapability.getBaseEP(player) * 100.0D / (Double)TensuraConfig.INSTANCE.racesConfig.epToTrueDragonewt.get();
   }

   public List<Component> getRequirementsForRendering(Player player) {
      List<Component> list = new ArrayList();
      list.add(Component.m_237115_("tensura.evolution_menu.ep_requirement"));
      return list;
   }

   public boolean isSpiritual() {
      return true;
   }

   public void raceAbility(Player player) {
      if (player.m_20096_()) {
         Vec3 delta = player.m_20184_();
         double dy = delta.f_82480_ <= 0.0D ? 0.5D : delta.f_82480_ + 0.5D;
         player.m_20256_(new Vec3(delta.m_7096_(), dy, delta.m_7094_()));
      }

      SkillHelper.riptidePush(player, 0.5F);
      player.f_19864_ = true;
      player.f_19812_ = true;
      player.m_36320_();
   }
}
