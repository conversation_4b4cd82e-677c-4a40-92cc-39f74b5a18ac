package com.github.manasmods.tensura.race.ogre;

import com.github.manasmods.tensura.ability.TensuraSkill;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.config.TensuraConfig;
import com.github.manasmods.tensura.race.Race;
import com.github.manasmods.tensura.registry.race.TensuraRaces;
import com.github.manasmods.tensura.registry.skill.CommonSkills;
import com.github.manasmods.tensura.registry.skill.ExtraSkills;
import com.mojang.datafixers.util.Pair;
import java.util.ArrayList;
import java.util.List;
import net.minecraft.network.chat.Component;
import net.minecraft.world.entity.player.Player;
import org.jetbrains.annotations.Nullable;

public class EnlightenedOgreRace extends OgreRace {
   public EnlightenedOgreRace() {
      super(Race.Difficulty.EASY);
   }

   public double getBaseHealth() {
      return 120.0D;
   }

   public double getBaseAttackDamage() {
      return 5.0D;
   }

   public double getBaseAttackSpeed() {
      return 4.3D;
   }

   public double getSprintSpeed() {
      return 0.2D;
   }

   public Pair<Double, Double> getBaseAuraRange() {
      return Pair.of(100000.0D, 100000.0D);
   }

   public Pair<Double, Double> getBaseMagiculeRange() {
      return Pair.of(100000.0D, 100000.0D);
   }

   public double getAdditionalSpiritualHealth() {
      return 30.0D;
   }

   public double getSpiritualHealthMultiplier() {
      return 2.5D;
   }

   public List<TensuraSkill> getIntrinsicSkills(Player player) {
      List<TensuraSkill> list = super.getIntrinsicSkills(player);
      list.add((TensuraSkill)ExtraSkills.STEEL_STRENGTH.get());
      list.add((TensuraSkill)CommonSkills.SELF_REGENERATION.get());
      return list;
   }

   @Nullable
   public Race getDefaultEvolution(Player player) {
      return (Race)TensuraRaces.MYSTIC_ONI.get();
   }

   @Nullable
   public Race getHarvestFestivalEvolution(Player player) {
      return (Race)TensuraRaces.MYSTIC_ONI.get();
   }

   public List<Race> getNextEvolutions(Player player) {
      List<Race> list = new ArrayList();
      list.add((Race)TensuraRaces.MYSTIC_ONI.get());
      list.add((Race)TensuraRaces.WICKED_ONI.get());
      return list;
   }

   public List<Race> getPreviousEvolutions(Player player) {
      List<Race> list = new ArrayList();
      list.add((Race)TensuraRaces.OGRE.get());
      return list;
   }

   public double getEvolutionPercentage(Player player) {
      return TensuraPlayerCapability.getBaseEP(player) * 100.0D / (Double)TensuraConfig.INSTANCE.racesConfig.epToEnlightened.get();
   }

   public List<Component> getRequirementsForRendering(Player player) {
      List<Component> list = new ArrayList();
      list.add(Component.m_237115_("tensura.evolution_menu.ep_requirement"));
      return list;
   }

   public double getAuraEvolutionReward() {
      return 70000.0D;
   }

   public double getManaEvolutionReward() {
      return 30000.0D;
   }
}
