package com.github.manasmods.tensura.race.ogre;

import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.config.TensuraConfig;
import com.github.manasmods.tensura.race.Race;
import com.github.manasmods.tensura.registry.race.TensuraRaces;
import com.github.manasmods.tensura.util.JumpPowerHelper;
import com.mojang.datafixers.util.Pair;
import java.util.ArrayList;
import java.util.List;
import net.minecraft.network.chat.Component;
import net.minecraft.world.entity.player.Player;
import org.jetbrains.annotations.Nullable;

public class SpiritOniRace extends MysticOniRace {
   public double getBaseHealth() {
      return 1500.0D;
   }

   public double getBaseAttackDamage() {
      return 4.8D;
   }

   public double getBaseAttackSpeed() {
      return 7.0D;
   }

   public double getJumpHeight() {
      return JumpPowerHelper.defaultPlayer(2.0D);
   }

   public double getSprintSpeed() {
      return 1.0D;
   }

   public Pair<Double, Double> getBaseAuraRange() {
      return Pair.of(400000.0D, 400000.0D);
   }

   public Pair<Double, Double> getBaseMagiculeRange() {
      return Pair.of(400000.0D, 400000.0D);
   }

   public double getAdditionalSpiritualHealth() {
      return 50.0D;
   }

   @Nullable
   public Race getDefaultEvolution(Player player) {
      return (Race)TensuraRaces.DIVINE_ONI.get();
   }

   @Nullable
   public Race getAwakeningEvolution(Player player) {
      return (Race)TensuraRaces.DIVINE_ONI.get();
   }

   public List<Race> getNextEvolutions(Player player) {
      List<Race> list = new ArrayList();
      list.add((Race)TensuraRaces.DIVINE_ONI.get());
      return list;
   }

   public List<Race> getPreviousEvolutions(Player player) {
      List<Race> list = new ArrayList();
      list.add((Race)TensuraRaces.MYSTIC_ONI.get());
      return list;
   }

   public double getEvolutionPercentage(Player player) {
      return TensuraPlayerCapability.getBaseEP(player) * 100.0D / (Double)TensuraConfig.INSTANCE.racesConfig.epToSaint.get();
   }

   public List<Component> getRequirementsForRendering(Player player) {
      List<Component> list = new ArrayList();
      list.add(Component.m_237115_("tensura.evolution_menu.ep_requirement"));
      return list;
   }

   public double getAuraEvolutionReward() {
      return 300000.0D;
   }

   public double getManaEvolutionReward() {
      return 300000.0D;
   }

   public boolean isSpiritual() {
      return true;
   }
}
