package com.github.manasmods.tensura.race.human;

import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.config.TensuraConfig;
import com.github.manasmods.tensura.race.Race;
import com.github.manasmods.tensura.registry.race.TensuraRaces;
import com.github.manasmods.tensura.util.JumpPowerHelper;
import com.mojang.datafixers.util.Pair;
import java.util.ArrayList;
import java.util.List;
import net.minecraft.network.chat.Component;
import net.minecraft.world.entity.player.Player;
import org.jetbrains.annotations.Nullable;

public class EnlightenedHumanRace extends HumanRace {
   public EnlightenedHumanRace() {
      super(Race.Difficulty.EASY);
   }

   public double getBaseHealth() {
      return 100.0D;
   }

   public double getBaseAttackDamage() {
      return 2.0D;
   }

   public double getBaseAttackSpeed() {
      return 4.2D;
   }

   public double getJumpHeight() {
      return JumpPowerHelper.defaultPlayer(1.0D);
   }

   public double getSprintSpeed() {
      return 0.5D;
   }

   public double getAdditionalSpiritualHealth() {
      return 30.0D;
   }

   public double getSpiritualHealthMultiplier() {
      return 2.5D;
   }

   public Pair<Double, Double> getBaseAuraRange() {
      return Pair.of(140000.0D, 140000.0D);
   }

   public Pair<Double, Double> getBaseMagiculeRange() {
      return Pair.of(60000.0D, 60000.0D);
   }

   public Race getDefaultEvolution(Player player) {
      return (Race)TensuraRaces.HUMAN_SAINT.get();
   }

   @Nullable
   public Race getHarvestFestivalEvolution(Player player) {
      return null;
   }

   public List<Race> getNextEvolutions(Player player) {
      List<Race> list = new ArrayList();
      list.add((Race)TensuraRaces.HUMAN_SAINT.get());
      return list;
   }

   public List<Race> getPreviousEvolutions(Player player) {
      List<Race> list = new ArrayList();
      list.add((Race)TensuraRaces.HUMAN.get());
      return list;
   }

   public double getEvolutionPercentage(Player player) {
      return TensuraPlayerCapability.getBaseEP(player) * 100.0D / (Double)TensuraConfig.INSTANCE.racesConfig.epToEnlightened.get();
   }

   public List<Component> getRequirementsForRendering(Player player) {
      List<Component> list = new ArrayList();
      list.add(Component.m_237115_("tensura.evolution_menu.ep_requirement"));
      return list;
   }

   public double getAuraEvolutionReward() {
      return 70000.0D;
   }

   public double getManaEvolutionReward() {
      return 30000.0D;
   }
}
