package com.github.manasmods.tensura.race.wight;

import com.github.manasmods.tensura.ability.TensuraSkill;
import com.github.manasmods.tensura.race.Race;
import com.github.manasmods.tensura.race.RaceHelper;
import com.github.manasmods.tensura.race.vampire.VampireRace;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.race.TensuraRaces;
import com.github.manasmods.tensura.registry.skill.ResistanceSkills;
import com.github.manasmods.tensura.util.JumpPowerHelper;
import com.mojang.datafixers.util.Pair;
import java.util.ArrayList;
import java.util.List;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import org.jetbrains.annotations.Nullable;

public class WightRace extends Race {
   public WightRace() {
      super(Race.Difficulty.HARD);
   }

   public double getBaseHealth() {
      return 20.0D;
   }

   public float getPlayerSize() {
      return 2.0F;
   }

   public double getBaseAttackDamage() {
      return 1.0D;
   }

   public double getBaseAttackSpeed() {
      return 3.8D;
   }

   public double getKnockbackResistance() {
      return 0.0D;
   }

   public double getJumpHeight() {
      return JumpPowerHelper.defaultPlayer();
   }

   public double getMovementSpeed() {
      return 0.1D;
   }

   public Pair<Double, Double> getBaseAuraRange() {
      return Pair.of(100.0D, 500.0D);
   }

   public Pair<Double, Double> getBaseMagiculeRange() {
      return Pair.of(2000.0D, 3000.0D);
   }

   @Nullable
   public Race getDefaultEvolution(Player player) {
      return (Race)TensuraRaces.WIGHT_KING.get();
   }

   @Nullable
   public Race getAwakeningEvolution(Player player) {
      return (Race)TensuraRaces.SPIRIT_SKELETON.get();
   }

   @Nullable
   public Race getHarvestFestivalEvolution(Player player) {
      return (Race)TensuraRaces.WIGHT_KING.get();
   }

   public boolean isMajin() {
      return true;
   }

   public List<TensuraSkill> getIntrinsicSkills(Player player) {
      List<TensuraSkill> list = new ArrayList();
      list.add((TensuraSkill)ResistanceSkills.PHYSICAL_ATTACK_RESISTANCE.get());
      list.add((TensuraSkill)ResistanceSkills.PAIN_NULLIFICATION.get());
      return list;
   }

   public List<Race> getNextEvolutions(Player player) {
      List<Race> list = new ArrayList();
      list.add((Race)TensuraRaces.WIGHT_KING.get());
      list.add((Race)TensuraRaces.HUMAN.get());
      return list;
   }

   public boolean passivelyFriendlyWith(LivingEntity entity) {
      return RaceHelper.isUndead(entity);
   }

   public void raceTick(Player player) {
      if (VampireRace.isUnderSun(player)) {
         player.m_7292_(new MobEffectInstance(MobEffects.f_19613_, 40, 2, false, false, false));
         player.m_7292_(new MobEffectInstance(MobEffects.f_19597_, 40, 2, false, false, false));
         player.m_7292_(new MobEffectInstance(MobEffects.f_19599_, 40, 2, false, false, false));
         player.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.FRAGILITY.get(), 40, 2, false, false, false));
         if (VampireRace.shouldBurn(player)) {
            player.m_20254_(3);
         }

      }
   }
}
