package com.github.manasmods.tensura.race.wight;

import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.config.TensuraConfig;
import com.github.manasmods.tensura.race.Race;
import com.github.manasmods.tensura.race.vampire.VampireRace;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.race.TensuraRaces;
import com.mojang.datafixers.util.Pair;
import java.util.ArrayList;
import java.util.List;
import net.minecraft.network.chat.Component;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.world.entity.player.Player;
import org.jetbrains.annotations.Nullable;

public class WightKingRace extends WightRace {
   public double getBaseHealth() {
      return 200.0D;
   }

   public double getBaseAttackDamage() {
      return 2.0D;
   }

   public double getBaseAttackSpeed() {
      return 4.1D;
   }

   public double getSprintSpeed() {
      return 0.2D;
   }

   public Pair<Double, Double> getBaseAuraRange() {
      return Pair.of(100000.0D, 100000.0D);
   }

   public Pair<Double, Double> getBaseMagiculeRange() {
      return Pair.of(300000.0D, 300000.0D);
   }

   public double getAdditionalSpiritualHealth() {
      return 30.0D;
   }

   public double getSpiritualHealthMultiplier() {
      return 2.5D;
   }

   @Nullable
   public Race getDefaultEvolution(Player player) {
      return (Race)TensuraRaces.SPIRIT_SKELETON.get();
   }

   @Nullable
   public Race getHarvestFestivalEvolution(Player player) {
      return null;
   }

   public List<Race> getNextEvolutions(Player player) {
      List<Race> list = new ArrayList();
      list.add((Race)TensuraRaces.SPIRIT_SKELETON.get());
      return list;
   }

   public List<Race> getPreviousEvolutions(Player player) {
      List<Race> list = new ArrayList();
      list.add((Race)TensuraRaces.WIGHT.get());
      return list;
   }

   public double getEvolutionPercentage(Player player) {
      return TensuraPlayerCapability.getBaseEP(player) * 100.0D / (Double)TensuraConfig.INSTANCE.racesConfig.epToWightKing.get();
   }

   public List<Component> getRequirementsForRendering(Player player) {
      List<Component> list = new ArrayList();
      list.add(Component.m_237115_("tensura.evolution_menu.ep_requirement"));
      return list;
   }

   public void raceTick(Player player) {
      if (VampireRace.isUnderSun(player)) {
         player.m_7292_(new MobEffectInstance(MobEffects.f_19613_, 40, 1, false, false, false));
         player.m_7292_(new MobEffectInstance(MobEffects.f_19597_, 40, 1, false, false, false));
         player.m_7292_(new MobEffectInstance(MobEffects.f_19599_, 40, 1, false, false, false));
         player.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.FRAGILITY.get(), 40, 1, false, false, false));
      }

   }
}
