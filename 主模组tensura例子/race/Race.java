package com.github.manasmods.tensura.race;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.tensura.ability.TensuraSkill;
import com.github.manasmods.tensura.ability.magic.MagicElemental;
import com.github.manasmods.tensura.ability.magic.spiritual.SpiritualMagic;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.config.TensuraConfig;
import com.github.manasmods.tensura.effect.InsanityEffect;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.race.TensuraRaces;
import com.mojang.datafixers.util.Pair;
import java.awt.Color;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import javax.annotation.Nullable;
import net.minecraft.ChatFormatting;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.MutableComponent;
import net.minecraft.network.chat.contents.TranslatableContents;
import net.minecraft.resources.ResourceKey;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import net.minecraftforge.registries.IForgeRegistry;

public abstract class Race {
   private final Race.Difficulty difficulty;

   public Race(Race.Difficulty difficulty) {
      this.difficulty = difficulty;
   }

   public abstract double getBaseHealth();

   public abstract float getPlayerSize();

   public abstract double getBaseAttackDamage();

   public abstract double getBaseAttackSpeed();

   public abstract double getKnockbackResistance();

   public abstract double getJumpHeight();

   public abstract double getMovementSpeed();

   public double getSprintSpeed() {
      return this.getMovementSpeed() * 1.3D;
   }

   public abstract Pair<Double, Double> getBaseAuraRange();

   public abstract Pair<Double, Double> getBaseMagiculeRange();

   public double getMinBaseAura() {
      return (Double)this.getBaseAuraRange().getFirst();
   }

   public double getMaxBaseAura() {
      return (Double)this.getBaseAuraRange().getSecond();
   }

   public double getMinBaseMagicule() {
      return (Double)this.getBaseMagiculeRange().getFirst();
   }

   public double getMaxBaseMagicule() {
      return (Double)this.getBaseMagiculeRange().getSecond();
   }

   public double getAdditionalSpiritualHealth() {
      return 10.0D;
   }

   public double getSpiritualHealthMultiplier() {
      return 2.0D;
   }

   public List<TensuraSkill> getIntrinsicSkills(Player player) {
      return new ArrayList();
   }

   public boolean isIntrinsicSkill(Player player, ManasSkill skill) {
      return this.getIntrinsicSkills(player).contains(skill);
   }

   public List<Race> getNextEvolutions(Player player) {
      return new ArrayList();
   }

   public List<Race> getPreviousEvolutions(Player player) {
      return new ArrayList();
   }

   @Nullable
   public Race getDefaultEvolution(Player player) {
      return null;
   }

   @Nullable
   public Race getAwakeningEvolution(Player player) {
      return null;
   }

   @Nullable
   public Race getHarvestFestivalEvolution(Player player) {
      return null;
   }

   public double getEvolutionPercentage(Player player) {
      double minimalEP = this.getMinBaseMagicule() + this.getMinBaseAura();
      return TensuraPlayerCapability.getBaseEP(player) * 100.0D / minimalEP;
   }

   public void triggerEvolutionRewards(Player player) {
      TensuraPlayerCapability.getFrom(player).ifPresent((cap) -> {
         if (this.getManaEvolutionReward() > 0.0D) {
            cap.setBaseMagicule(cap.getBaseMagicule() + this.getManaEvolutionReward(), player);
         } else if (cap.getBaseMagicule() < this.getMinBaseMagicule()) {
            cap.setBaseMagicule(this.getMinBaseMagicule(), player);
         }

         if (this.getAuraEvolutionReward() > 0.0D) {
            cap.setBaseAura(cap.getBaseAura() + this.getAuraEvolutionReward(), player);
         } else if (cap.getBaseAura() < this.getMinBaseAura()) {
            cap.setBaseAura(this.getMinBaseAura(), player);
         }

      });
      TensuraPlayerCapability.sync(player);
   }

   public List<Component> getRequirementsForRendering(Player player) {
      List<Component> list = new ArrayList();
      list.add(Component.m_237115_("tensura.evolution_menu.ep_requirement"));
      return list;
   }

   public double getAuraEvolutionReward() {
      return 0.0D;
   }

   public double getManaEvolutionReward() {
      return 0.0D;
   }

   public boolean isMajin() {
      return false;
   }

   public boolean isSpiritual() {
      return false;
   }

   public boolean isDivine() {
      return false;
   }

   public ResourceKey<Level> getRespawnDimension() {
      return Level.f_46428_;
   }

   public boolean passivelyFriendlyWith(LivingEntity entity) {
      return false;
   }

   public boolean canFly() {
      return false;
   }

   public void raceTick(Player player) {
   }

   public void raceAbility(Player player) {
   }

   public boolean canActivateAbility(LivingEntity entity) {
      if (!this.isSpiritual() && entity instanceof Player) {
         Player player = (Player)entity;
         if (!player.m_7500_() && TensuraPlayerCapability.isSpiritualForm(player)) {
            return false;
         }
      }

      if (entity.m_21023_((MobEffect)TensuraMobEffects.MOVEMENT_INTERFERENCE.get())) {
         return false;
      } else if (entity.m_21023_((MobEffect)TensuraMobEffects.SLEEP_MODE.get())) {
         return false;
      } else if (entity.m_21023_((MobEffect)TensuraMobEffects.INFINITE_IMPRISONMENT.get())) {
         return false;
      } else if (InsanityEffect.havingNightmare(entity)) {
         return false;
      } else if (entity.m_21023_((MobEffect)TensuraMobEffects.REST.get())) {
         return false;
      } else {
         return !entity.m_21023_((MobEffect)TensuraMobEffects.SHADOW_STEP.get());
      }
   }

   public boolean hasGuaranteeElemental() {
      return false;
   }

   public double getElementalSpiritsChance(MagicElemental elemental, SpiritualMagic.SpiritLevel level) {
      double var10000;
      switch(level) {
      case LESSER:
         var10000 = (Double)TensuraConfig.INSTANCE.awakeningConfig.lesserSpiritPercentage.get();
         break;
      case MEDIUM:
         var10000 = (Double)TensuraConfig.INSTANCE.awakeningConfig.mediumSpiritPercentage.get();
         break;
      case GREATER:
         var10000 = (Double)TensuraConfig.INSTANCE.awakeningConfig.greaterSpiritPercentage.get();
         break;
      case LORD:
         var10000 = (Double)TensuraConfig.INSTANCE.awakeningConfig.lordSpiritPercentage.get();
         break;
      default:
         throw new IncompatibleClassChangeError();
      }

      return var10000;
   }

   @Nullable
   public ResourceLocation getRegistryName() {
      return ((IForgeRegistry)TensuraRaces.RACE_REGISTRY.get()).getKey(this);
   }

   @Nullable
   public MutableComponent getName() {
      ResourceLocation id = this.getRegistryName();
      return id == null ? null : Component.m_237115_(String.format("%s.race.%s", id.m_135827_(), id.m_135815_().replace('/', '.')));
   }

   public String getNameTranslationKey() {
      return ((TranslatableContents)this.getName().m_214077_()).m_237508_();
   }

   public boolean equals(Object o) {
      if (this == o) {
         return true;
      } else if (o != null && this.getClass() == o.getClass()) {
         Race race = (Race)o;
         return this.getRegistryName().equals(race.getRegistryName());
      } else {
         return false;
      }
   }

   public int hashCode() {
      return Objects.hash(new Object[]{this.difficulty, this.getBaseHealth()});
   }

   public Race.Difficulty getDifficulty() {
      return this.difficulty;
   }

   public static enum Difficulty {
      EASY(Component.m_237115_("tensura.race.difficulty.easy").m_130940_(ChatFormatting.GREEN)),
      INTERMEDIATE(Component.m_237115_("tensura.race.difficulty.intermediate").m_130938_((style) -> {
         return style.m_178520_(Color.ORANGE.getRGB());
      })),
      HARD(Component.m_237115_("tensura.race.difficulty.hard").m_130940_(ChatFormatting.RED)),
      EXTREME(Component.m_237115_("tensura.race.difficulty.extreme").m_130940_(ChatFormatting.DARK_RED));

      private final MutableComponent name;

      public MutableComponent asText() {
         return this.name;
      }

      private Difficulty(MutableComponent name) {
         this.name = name;
      }

      // $FF: synthetic method
      private static Race.Difficulty[] $values() {
         return new Race.Difficulty[]{EASY, INTERMEDIATE, HARD, EXTREME};
      }
   }
}
