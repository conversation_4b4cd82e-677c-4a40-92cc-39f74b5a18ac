package com.github.manasmods.tensura.race.vampire;

import com.github.manasmods.tensura.ability.TensuraSkill;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.config.TensuraConfig;
import com.github.manasmods.tensura.race.Race;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.race.TensuraRaces;
import com.github.manasmods.tensura.registry.skill.ExtraSkills;
import com.github.manasmods.tensura.registry.skill.IntrinsicSkills;
import com.github.manasmods.tensura.util.JumpPowerHelper;
import com.mojang.datafixers.util.Pair;
import java.util.ArrayList;
import java.util.List;
import net.minecraft.network.chat.Component;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.world.entity.player.Player;

public class DivineVampireRace extends VampireLordRace {
   public double getBaseHealth() {
      return 3500.0D;
   }

   public double getBaseAttackDamage() {
      return 8.0D;
   }

   public double getBaseAttackSpeed() {
      return 5.0D;
   }

   public double getJumpHeight() {
      return JumpPowerHelper.defaultPlayer(2.0D);
   }

   public double getSprintSpeed() {
      return 1.0D;
   }

   public Pair<Double, Double> getBaseAuraRange() {
      return Pair.of(1000000.0D, 1000000.0D);
   }

   public Pair<Double, Double> getBaseMagiculeRange() {
      return Pair.of(1000000.0D, 1000000.0D);
   }

   public double getEvolutionPercentage(Player player) {
      return TensuraPlayerCapability.getBaseEP(player) * 100.0D / (Double)TensuraConfig.INSTANCE.racesConfig.epToDivine.get();
   }

   public List<Component> getRequirementsForRendering(Player player) {
      List<Component> list = new ArrayList();
      list.add(Component.m_237115_("tensura.evolution_menu.ep_requirement"));
      return list;
   }

   public List<TensuraSkill> getIntrinsicSkills(Player player) {
      List<TensuraSkill> list = super.getIntrinsicSkills(player);
      list.add((TensuraSkill)ExtraSkills.INFINITE_REGENERATION.get());
      list.add((TensuraSkill)IntrinsicSkills.DIVINE_KI_RELEASE.get());
      return list;
   }

   public List<Race> getNextEvolutions(Player player) {
      return new ArrayList();
   }

   public List<Race> getPreviousEvolutions(Player player) {
      List<Race> list = new ArrayList();
      list.add((Race)TensuraRaces.VAMPIRE_LORD.get());
      return list;
   }

   public boolean isSpiritual() {
      return true;
   }

   public boolean isDivine() {
      return true;
   }

   public void raceTick(Player player) {
      if (player.f_19853_.m_46462_() && player.f_19853_.m_46941_() == 4) {
         player.m_7292_(new MobEffectInstance(MobEffects.f_19613_, 40, 0, false, false, false));
         player.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.FRAGILITY.get(), 40, 0, false, false, false));
      }

   }
}
