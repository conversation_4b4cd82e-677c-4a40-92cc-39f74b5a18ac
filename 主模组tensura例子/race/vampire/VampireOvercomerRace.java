package com.github.manasmods.tensura.race.vampire;

import com.github.manasmods.tensura.ability.TensuraSkill;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.config.TensuraConfig;
import com.github.manasmods.tensura.race.Race;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.race.TensuraRaces;
import com.github.manasmods.tensura.registry.skill.IntrinsicSkills;
import com.github.manasmods.tensura.util.JumpPowerHelper;
import com.mojang.datafixers.util.Pair;
import java.util.ArrayList;
import java.util.List;
import net.minecraft.network.chat.Component;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.world.entity.player.Player;
import org.jetbrains.annotations.Nullable;

public class VampireOvercomerRace extends VampireRace {
   public double getBaseHealth() {
      return 100.0D;
   }

   public double getBaseAttackDamage() {
      return 5.0D;
   }

   public double getBaseAttackSpeed() {
      return 4.2D;
   }

   public double getJumpHeight() {
      return JumpPowerHelper.defaultPlayer(1.0D);
   }

   public double getSprintSpeed() {
      return 0.3D;
   }

   public Pair<Double, Double> getBaseAuraRange() {
      return Pair.of(50000.0D, 70000.0D);
   }

   public Pair<Double, Double> getBaseMagiculeRange() {
      return Pair.of(100000.0D, 120000.0D);
   }

   public double getAdditionalSpiritualHealth() {
      return 30.0D;
   }

   public double getSpiritualHealthMultiplier() {
      return 2.5D;
   }

   public List<TensuraSkill> getIntrinsicSkills(Player player) {
      List<TensuraSkill> list = super.getIntrinsicSkills(player);
      list.add((TensuraSkill)IntrinsicSkills.BLOOD_MIST.get());
      return list;
   }

   @Nullable
   public Race getDefaultEvolution(Player player) {
      return (Race)TensuraRaces.VAMPIRE_LORD.get();
   }

   @Nullable
   public Race getHarvestFestivalEvolution(Player player) {
      return null;
   }

   public List<Race> getNextEvolutions(Player player) {
      List<Race> list = new ArrayList();
      list.add((Race)TensuraRaces.VAMPIRE_LORD.get());
      return list;
   }

   public List<Race> getPreviousEvolutions(Player player) {
      List<Race> list = new ArrayList();
      list.add((Race)TensuraRaces.VAMPIRE.get());
      return list;
   }

   public double getEvolutionPercentage(Player player) {
      return TensuraPlayerCapability.getBaseEP(player) * 100.0D / (Double)TensuraConfig.INSTANCE.racesConfig.epToOvercomer.get();
   }

   public List<Component> getRequirementsForRendering(Player player) {
      List<Component> list = new ArrayList();
      list.add(Component.m_237115_("tensura.evolution_menu.ep_requirement"));
      return list;
   }

   public void raceTick(Player player) {
      if (isUnderSun(player)) {
         player.m_7292_(new MobEffectInstance(MobEffects.f_19613_, 40, 2, false, false, false));
         player.m_7292_(new MobEffectInstance(MobEffects.f_19597_, 40, 1, false, false, false));
      } else if (player.f_19853_.m_46462_() && player.f_19853_.m_46941_() == 4) {
         player.m_7292_(new MobEffectInstance(MobEffects.f_19613_, 40, 2, false, false, false));
         player.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.FRAGILITY.get(), 40, 2, false, false, false));
      }

   }
}
