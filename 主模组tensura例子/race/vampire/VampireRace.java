package com.github.manasmods.tensura.race.vampire;

import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.TensuraSkill;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.config.TensuraConfig;
import com.github.manasmods.tensura.effect.template.Transformation;
import com.github.manasmods.tensura.race.Race;
import com.github.manasmods.tensura.race.RaceHelper;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.items.TensuraMobDropItems;
import com.github.manasmods.tensura.registry.race.TensuraRaces;
import com.github.manasmods.tensura.registry.skill.CommonSkills;
import com.github.manasmods.tensura.registry.skill.ExtraSkills;
import com.github.manasmods.tensura.registry.skill.IntrinsicSkills;
import com.github.manasmods.tensura.util.JumpPowerHelper;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import com.github.manasmods.tensura.world.TensuraGameRules;
import com.mojang.datafixers.util.Pair;
import java.util.ArrayList;
import java.util.List;
import net.minecraft.client.player.LocalPlayer;
import net.minecraft.core.BlockPos;
import net.minecraft.network.chat.Component;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.stats.Stats;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.world.entity.EquipmentSlot;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.Item;
import org.jetbrains.annotations.Nullable;

public class VampireRace extends GhoulRace implements Transformation {
   public VampireRace() {
      super(Race.Difficulty.EASY);
   }

   public double getBaseHealth() {
      return 30.0D;
   }

   public double getBaseAttackDamage() {
      return 4.0D;
   }

   public double getBaseAttackSpeed() {
      return 4.0D;
   }

   public double getKnockbackResistance() {
      return 0.2D;
   }

   public double getJumpHeight() {
      return JumpPowerHelper.defaultPlayer(0.25D);
   }

   public double getMovementSpeed() {
      return 0.1D;
   }

   public double getSprintSpeed() {
      return 0.2D;
   }

   public Pair<Double, Double> getBaseAuraRange() {
      return Pair.of(3000.0D, 5000.0D);
   }

   public Pair<Double, Double> getBaseMagiculeRange() {
      return Pair.of(5000.0D, 7000.0D);
   }

   public List<TensuraSkill> getIntrinsicSkills(Player player) {
      List<TensuraSkill> list = super.getIntrinsicSkills(player);
      list.add((TensuraSkill)ExtraSkills.STEEL_STRENGTH.get());
      list.add((TensuraSkill)ExtraSkills.SHADOW_MOTION.get());
      list.add((TensuraSkill)CommonSkills.COERCION.get());
      list.add((TensuraSkill)CommonSkills.PARALYSIS.get());
      list.add((TensuraSkill)CommonSkills.STRENGTH.get());
      list.add((TensuraSkill)CommonSkills.SELF_REGENERATION.get());
      list.add((TensuraSkill)IntrinsicSkills.CHARM.get());
      return list;
   }

   @Nullable
   public Race getDefaultEvolution(Player player) {
      return (Race)TensuraRaces.VAMPIRE_OVERCOMER.get();
   }

   @Nullable
   public Race getHarvestFestivalEvolution(Player player) {
      return (Race)TensuraRaces.VAMPIRE_OVERCOMER.get();
   }

   public List<Race> getNextEvolutions(Player player) {
      List<Race> list = new ArrayList();
      list.add((Race)TensuraRaces.VAMPIRE_OVERCOMER.get());
      return list;
   }

   public List<Race> getPreviousEvolutions(Player player) {
      List<Race> list = new ArrayList();
      list.add((Race)TensuraRaces.GHOUL.get());
      list.add((Race)TensuraRaces.HUMAN.get());
      return list;
   }

   public double getEvolutionPercentage(Player player) {
      double blood = 0.0D;
      if (player instanceof LocalPlayer) {
         LocalPlayer localPlayer = (LocalPlayer)player;
         blood = (double)localPlayer.m_108630_().m_13015_(Stats.f_12982_.m_12902_((Item)TensuraMobDropItems.ZANE_BLOOD.get()));
      } else if (player instanceof ServerPlayer) {
         ServerPlayer serverPlayer = (ServerPlayer)player;
         blood = (double)serverPlayer.m_8951_().m_13015_(Stats.f_12982_.m_12902_((Item)TensuraMobDropItems.ZANE_BLOOD.get()));
      }

      Race race = TensuraPlayerCapability.getRace(player);
      return race != null && !race.equals(TensuraRaces.HUMAN.get()) ? blood * 100.0D / (double)(Integer)TensuraConfig.INSTANCE.racesConfig.bloodForVampireAsGhoul.get() : blood * 100.0D / (double)(Integer)TensuraConfig.INSTANCE.racesConfig.bloodForVampireAsHuman.get();
   }

   public List<Component> getRequirementsForRendering(Player player) {
      List<Component> list = new ArrayList();
      list.add(Component.m_237110_("tensura.evolution_menu.consume_requirement", new Object[]{((Item)TensuraMobDropItems.ZANE_BLOOD.get()).m_7968_().m_41611_()}));
      return list;
   }

   public void raceAbility(Player player) {
      if (player.m_21023_((MobEffect)TensuraMobEffects.BATS_MODE.get())) {
         player.m_21195_((MobEffect)TensuraMobEffects.BATS_MODE.get());
         if (player.m_5833_() || player.m_7500_()) {
            return;
         }

         player.m_150110_().f_35936_ = false;
         player.m_150110_().f_35935_ = false;
         player.m_6885_();
      } else {
         LivingEntity target = SkillHelper.getTargetingEntity(player, 5.0D, false);
         if (target != null && RaceHelper.hasNoBlood(target) && (player.m_21223_() < player.m_21233_() || player.m_36324_().m_38721_() || player.m_7500_())) {
            if (target.m_6469_(TensuraDamageSources.bloodDrain(target), 2.0F)) {
               player.m_5634_(2.0F);
               player.m_36324_().m_38707_(2, 0.0F);
               player.m_9236_().m_6263_((Player)null, target.m_20185_(), target.m_20186_(), target.m_20189_(), SoundEvents.f_11911_, SoundSource.PLAYERS, 1.0F, 1.0F);
            }

            return;
         }

         if (player.f_19853_.m_46469_().m_46207_(TensuraGameRules.HARDCORE_RACE) && this.getClass() == VampireRace.class && isUnderSun(player)) {
            return;
         }

         if (this.failedToActivate(player, (MobEffect)TensuraMobEffects.BATS_MODE.get())) {
            return;
         }

         player.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.BATS_MODE.get(), 1728000, 0, false, false, false));
         if (player.m_5833_() || player.m_7500_()) {
            return;
         }

         player.m_150110_().f_35936_ = true;
         player.m_150110_().f_35935_ = true;
         player.m_6885_();
      }

   }

   public void raceTick(Player player) {
      if (isUnderSun(player)) {
         player.m_7292_(new MobEffectInstance(MobEffects.f_19613_, 40, 3, false, false, false));
         player.m_7292_(new MobEffectInstance(MobEffects.f_19597_, 40, 3, false, false, false));
         player.m_7292_(new MobEffectInstance(MobEffects.f_19599_, 40, 3, false, false, false));
         if (shouldBurn(player)) {
            player.m_20254_(2);
         }
      } else if (player.f_19853_.m_46462_() && player.f_19853_.m_46941_() == 4) {
         player.m_7292_(new MobEffectInstance(MobEffects.f_19613_, 40, 3, false, false, false));
         player.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.FRAGILITY.get(), 40, 3, false, false, false));
      }

   }

   public static boolean isUnderSun(LivingEntity entity) {
      if (!entity.m_6084_()) {
         return false;
      } else {
         if (entity instanceof Player) {
            Player player = (Player)entity;
            if (player.m_7500_()) {
               return false;
            }
         }

         if (!entity.m_5833_()) {
            if (!entity.f_19853_.m_46461_()) {
               return false;
            } else if (SkillUtils.noInteractiveMode(entity)) {
               return false;
            } else {
               float f = entity.m_213856_();
               boolean inWater = entity.isInFluidType((fluidType, height) -> {
                  return height > (double)entity.m_20206_();
               }) && (entity.m_20072_() || entity.f_146808_ || entity.f_146809_);
               boolean flag = inWater || entity.m_20285_();
               return f > 0.5F && !flag && entity.f_19853_.m_45527_(new BlockPos(entity.m_146892_()));
            }
         } else {
            return false;
         }
      }
   }

   public static boolean shouldBurn(Player player) {
      if (player.m_20072_()) {
         return false;
      } else {
         return player.f_19853_.m_46469_().m_46207_(TensuraGameRules.HARDCORE_RACE) ? true : player.m_6844_(EquipmentSlot.HEAD).m_41619_();
      }
   }
}
