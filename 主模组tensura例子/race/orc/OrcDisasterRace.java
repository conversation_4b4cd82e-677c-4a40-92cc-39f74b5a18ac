package com.github.manasmods.tensura.race.orc;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.TensuraSkill;
import com.github.manasmods.tensura.ability.skill.unique.StarvedSkill;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.config.TensuraConfig;
import com.github.manasmods.tensura.race.Race;
import com.github.manasmods.tensura.registry.race.TensuraRaces;
import com.github.manasmods.tensura.registry.skill.ResistanceSkills;
import com.github.manasmods.tensura.registry.skill.UniqueSkills;
import com.github.manasmods.tensura.world.TensuraGameRules;
import com.mojang.datafixers.util.Pair;
import java.util.ArrayList;
import java.util.List;
import net.minecraft.network.chat.Component;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.world.entity.player.Player;
import org.jetbrains.annotations.Nullable;

public class OrcDisasterRace extends OrcLordRace {
   public double getBaseHealth() {
      return 300.0D;
   }

   public double getBaseAttackDamage() {
      return 3.0D;
   }

   public double getBaseAttackSpeed() {
      return 4.4D;
   }

   public double getKnockbackResistance() {
      return 0.5D;
   }

   public double getSprintSpeed() {
      return 0.5D;
   }

   public Pair<Double, Double> getBaseAuraRange() {
      return Pair.of(100000.0D, 100000.0D);
   }

   public Pair<Double, Double> getBaseMagiculeRange() {
      return Pair.of(100000.0D, 100000.0D);
   }

   public double getAdditionalSpiritualHealth() {
      return 40.0D;
   }

   public double getSpiritualHealthMultiplier() {
      return 3.0D;
   }

   public List<TensuraSkill> getIntrinsicSkills(Player player) {
      List<TensuraSkill> list = super.getIntrinsicSkills(player);
      list.add((TensuraSkill)ResistanceSkills.MAGIC_RESISTANCE.get());
      list.add((TensuraSkill)ResistanceSkills.FLAME_ATTACK_RESISTANCE.get());
      return list;
   }

   @Nullable
   public Race getDefaultEvolution(Player player) {
      return (Race)TensuraRaces.SPIRIT_BOAR.get();
   }

   @Nullable
   public Race getHarvestFestivalEvolution(Player player) {
      return null;
   }

   public List<Race> getNextEvolutions(Player player) {
      List<Race> list = new ArrayList();
      list.add((Race)TensuraRaces.SPIRIT_BOAR.get());
      return list;
   }

   public List<Race> getPreviousEvolutions(Player player) {
      List<Race> list = new ArrayList();
      list.add((Race)TensuraRaces.ORC_LORD.get());
      return list;
   }

   public double getEvolutionPercentage(Player player) {
      double EP = TensuraPlayerCapability.getBaseEP(player);
      double percentage = Math.min(EP / (Double)TensuraConfig.INSTANCE.racesConfig.epToDisaster.get() * 50.0D, 50.0D);
      if (SkillUtils.isSkillMastered(player, (ManasSkill)UniqueSkills.STARVED.get())) {
         percentage += 50.0D;
      }

      return percentage;
   }

   public List<Component> getRequirementsForRendering(Player player) {
      List<Component> list = new ArrayList();
      list.add(Component.m_237115_("tensura.evolution_menu.ep_requirement"));
      list.add(Component.m_237110_("tensura.evolution_menu.mastery_requirement", new Object[]{((StarvedSkill)UniqueSkills.STARVED.get()).getName()}));
      return list;
   }

   public void raceTick(Player player) {
      if (player.f_19853_.m_46469_().m_46207_(TensuraGameRules.HARDCORE_RACE)) {
         player.m_7292_(new MobEffectInstance(MobEffects.f_19612_, 40, 4, false, false, false));
      }
   }
}
