package com.github.manasmods.tensura.loot;

import com.google.common.base.Suppliers;
import com.mojang.serialization.Codec;
import com.mojang.serialization.codecs.RecordCodecBuilder;
import it.unimi.dsi.fastutil.objects.ObjectArrayList;
import java.util.function.Supplier;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.level.storage.loot.LootContext;
import net.minecraft.world.level.storage.loot.predicates.LootItemCondition;
import net.minecraftforge.common.loot.IGlobalLootModifier;
import net.minecraftforge.common.loot.LootModifier;
import net.minecraftforge.registries.ForgeRegistries;
import org.jetbrains.annotations.NotNull;

public class AddItemModifier extends LootModifier {
   public static final Supplier<Codec<AddItemModifier>> CODEC = Suppliers.memoize(() -> {
      return RecordCodecBuilder.create((instance) -> {
         return codecStart(instance).and(ForgeRegistries.ITEMS.getCodec().fieldOf("item").forGetter((m) -> {
            return m.item;
         })).and(Codec.FLOAT.fieldOf("chance").forGetter((m) -> {
            return m.chance;
         })).and(Codec.INT.fieldOf("min").forGetter((m) -> {
            return m.min;
         })).and(Codec.INT.fieldOf("max").forGetter((m) -> {
            return m.max;
         })).apply(instance, AddItemModifier::new);
      });
   });
   private final Item item;
   private final float chance;
   private final int min;
   private final int max;

   public AddItemModifier(LootItemCondition[] conditionsIn, Item item, float chance, int min, int max) {
      super(conditionsIn);
      this.item = item;
      this.chance = chance;
      this.min = min;
      this.max = max;
   }

   @NotNull
   protected ObjectArrayList<ItemStack> doApply(ObjectArrayList<ItemStack> generatedLoot, LootContext context) {
      LootItemCondition[] var3 = this.conditions;
      int var4 = var3.length;

      for(int var5 = 0; var5 < var4; ++var5) {
         LootItemCondition condition = var3[var5];
         if (!condition.test(context)) {
            return generatedLoot;
         }
      }

      if (context.m_230907_().m_188501_() > this.chance) {
         return generatedLoot;
      } else {
         ItemStack stack = new ItemStack(this.item);
         stack.m_41764_(context.m_230907_().m_216339_(this.min, this.max + 1));
         generatedLoot.add(stack);
         return generatedLoot;
      }
   }

   public Codec<? extends IGlobalLootModifier> codec() {
      return (Codec)CODEC.get();
   }
}
