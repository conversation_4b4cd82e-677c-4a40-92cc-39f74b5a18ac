package com.github.manasmods.tensura.ability.battlewill.utility;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.battlewill.Battewill;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import net.minecraft.ChatFormatting;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.Style;
import net.minecraft.util.Mth;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;

public class AirFlightSkill extends Bat<PERSON>will {
   public double learningCost() {
      return 150.0D;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      return 15.0D;
   }

   public double auraCost(LivingEntity entity, ManasSkillInstance instance) {
      return 15.0D;
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      if (entity.m_21023_((MobEffect)TensuraMobEffects.MAGIC_INTERFERENCE.get())) {
         if (entity instanceof Player) {
            Player player = (Player)entity;
            player.m_5661_(Component.m_237115_("tensura.skill.magic_interference").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), true);
         }

      } else {
         entity.m_20242_(true);
      }
   }

   public void onRelease(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      if (entity.m_20096_() || entity.m_6144_()) {
         entity.m_20242_(false);
      }

   }

   public boolean onHeld(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      if (entity.m_21023_((MobEffect)TensuraMobEffects.MAGIC_INTERFERENCE.get())) {
         if (entity instanceof Player) {
            Player player = (Player)entity;
            player.m_5661_(Component.m_237115_("tensura.skill.magic_interference").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), true);
         }

         entity.m_20242_(false);
         return false;
      } else {
         if (heldTicks % 100 == 0 && heldTicks > 0) {
            this.addMasteryPoint(instance, entity);
         }

         pushForwardAtStaticSpeed(entity, instance.isMastered(entity) ? 0.4F : 0.2F);
         entity.m_183634_();
         if (heldTicks >= 10 && entity.m_20096_()) {
            entity.m_20242_(false);
         }

         return true;
      }
   }

   private static void pushForwardAtStaticSpeed(LivingEntity entity, float riptideLevel) {
      float f7 = entity.m_146908_();
      float f = entity.m_146909_();
      float f1 = -Mth.m_14031_(f7 * 0.017453292F) * Mth.m_14089_(f * 0.017453292F);
      float f2 = -Mth.m_14031_(f * 0.017453292F);
      float f3 = Mth.m_14089_(f7 * 0.017453292F) * Mth.m_14089_(f * 0.017453292F);
      float f4 = Mth.m_14116_(f1 * f1 + f2 * f2 + f3 * f3);
      float f5 = 3.0F * ((1.0F + riptideLevel) / 4.0F);
      f1 *= f5 / f4;
      f2 *= f5 / f4;
      f3 *= f5 / f4;
      entity.m_20334_((double)f1, (double)f2, (double)f3);
      entity.f_19812_ = true;
      entity.f_19864_ = true;
   }
}
