package com.github.manasmods.tensura.ability.magic.summon;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.magic.Magic;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.network.TensuraNetwork;
import com.github.manasmods.tensura.network.play2client.RequestFxSpawningPacket;
import java.util.Objects;
import javax.annotation.Nullable;
import net.minecraft.core.BlockPos;
import net.minecraft.core.particles.ParticleOptions;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.chat.MutableComponent;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.Mob;
import net.minecraft.world.entity.MobSpawnType;
import net.minecraft.world.entity.SpawnGroupData;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.ServerLevelAccessor;
import net.minecraft.world.level.ClipContext.Fluid;
import net.minecraft.world.phys.BlockHitResult;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.event.entity.living.LivingDeathEvent;
import net.minecraftforge.network.PacketDistributor;

public abstract class SummoningMagic<T extends Mob> extends Magic {
   public SummoningMagic() {
      super(Magic.MagicType.SUMMONING);
   }

   @Nullable
   public MutableComponent getColoredName() {
      MutableComponent name = super.getColoredName();
      return name == null ? null : name.m_130940_(this.getType().getChatFormatting());
   }

   public boolean canIgnoreCoolDown(ManasSkillInstance instance, LivingEntity entity) {
      return entity.m_6144_();
   }

   protected int getSuccessCooldown(ManasSkillInstance instance, LivingEntity entity) {
      return 0;
   }

   public void addHeldAttributeModifiers(ManasSkillInstance instance, LivingEntity entity) {
      if (!instance.onCoolDown()) {
         super.addHeldAttributeModifiers(instance, entity);
      }
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      if (instance.getMode() != 0) {
         if (entity.m_6144_()) {
            this.removeExistingSummon(instance, entity);
         } else {
            BlockHitResult result = SkillHelper.getPlayerPOVHitResult(entity.f_19853_, entity, Fluid.NONE, 10.0D);
            Vec3 pos = result.m_82450_();
            CompoundTag tag = instance.getOrCreateTag();
            tag.m_128473_("SummonUUID");
            tag.m_128347_("circleX", pos.f_82479_);
            tag.m_128347_("circleY", pos.f_82480_);
            tag.m_128347_("circleZ", pos.f_82481_);
            instance.markDirty();
         }
      }
   }

   public boolean onHeld(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      if (instance.getMode() == 0) {
         return false;
      } else if (entity.m_6144_()) {
         return false;
      } else if (heldTicks == 0 && this.alreadyCasting(entity)) {
         return false;
      } else {
         int castTime = this.castingTime(instance, entity);
         Level level = entity.m_9236_();
         CompoundTag tag = instance.getOrCreateTag();
         BlockPos pos = new BlockPos(tag.m_128459_("circleX"), tag.m_128459_("circleY"), tag.m_128459_("circleZ"));
         if (level.m_8055_(pos.m_7495_()).m_60767_().m_76333_() && level.m_8055_(pos.m_6625_(2)).m_60767_().m_76333_()) {
            if (heldTicks >= castTime) {
               if (heldTicks == castTime + 1) {
                  EntityType<? extends T> type = this.getSummonedType(instance);
                  if (type != null) {
                     T mob = (Mob)type.m_20615_(level);
                     if (mob != null) {
                        mob.m_21557_(true);
                        mob.f_19794_ = true;
                        mob.m_6518_((ServerLevelAccessor)level, level.m_6436_(pos), MobSpawnType.MOB_SUMMONED, (SpawnGroupData)null, (CompoundTag)null);
                        this.addAdditionalSummonData(instance, entity, mob);
                        mob.m_146884_(Vec3.m_82539_(pos).m_82520_(0.0D, -1.5D * (double)mob.m_20206_(), 0.0D));
                        level.m_7967_(mob);
                        tag.m_128362_("SummonUUID", mob.m_20148_());
                     }
                  }
               }

               int summoningTime = heldTicks - castTime;
               if (tag.m_128403_("SummonUUID")) {
                  Entity summonUUID = ((ServerLevel)level).m_8791_(tag.m_128342_("SummonUUID"));
                  if (summonUUID instanceof Mob) {
                     Mob mob = (Mob)summonUUID;
                     summonUUID.m_146884_(summonUUID.m_20182_().m_82520_(0.0D, (double)mob.m_20206_() * 1.5D / 40.0D, 0.0D));
                     TensuraParticleHelper.addServerParticlesAroundSelf(mob, this.getSummoningParticle(instance), 3.0D);
                     if (summoningTime == 40 && !SkillHelper.outOfMagicule(entity, instance)) {
                        this.addMasteryPoint(instance, entity);
                        summonUUID.f_19794_ = false;
                        mob.m_21557_(false);
                        mob.m_5496_(SoundEvents.f_11862_, 3.0F, 1.0F);
                        TensuraParticleHelper.addServerParticlesAroundSelf(mob, ParticleTypes.f_123747_, 2.0D);
                        TensuraParticleHelper.addServerParticlesAroundSelf(mob, ParticleTypes.f_123747_, 3.0D);
                        instance.setCoolDown(this.getSuccessCooldown(instance, entity));
                        this.removeHeldAttributeModifiers(instance, entity);
                     }
                  }
               }

               level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), this.getSummoningSound(instance), SoundSource.PLAYERS, 1.0F, 1.0F);
               TensuraNetwork.INSTANCE.send(PacketDistributor.TRACKING_ENTITY_AND_SELF.with(() -> {
                  return entity;
               }), new RequestFxSpawningPacket(this.getSummoningFxLocation(instance), pos, 0.0D, 0.0D, 0.0D, 0, true));
               return summoningTime <= 40;
            } else {
               if (entity instanceof Player) {
                  Player player = (Player)entity;
                  this.addCastingParticle(instance, player, heldTicks);
                  level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11887_, SoundSource.PLAYERS, 0.5F, 1.0F);
                  TensuraNetwork.INSTANCE.send(PacketDistributor.TRACKING_ENTITY_AND_SELF.with(() -> {
                     return entity;
                  }), new RequestFxSpawningPacket(this.getSummoningFxLocation(instance), pos, 0.0D, 0.0D, 0.0D, 0, true));
               }

               return true;
            }
         } else {
            return false;
         }
      }
   }

   public void onRelease(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      super.onRelease(instance, entity, heldTicks);
      if (instance.getMode() != 0) {
         if (!entity.m_6144_()) {
            CompoundTag tag = instance.getTag();
            if (tag != null && tag.m_128403_("SummonUUID")) {
               Entity summon = ((ServerLevel)entity.m_9236_()).m_8791_(tag.m_128342_("SummonUUID"));
               if (summon instanceof Mob) {
                  Mob mob = (Mob)summon;
                  if (mob.m_21525_()) {
                     summon.m_146870_();
                     mob.m_5496_(this.getFailSound(instance), 3.0F, 1.0F);
                     TensuraParticleHelper.addServerParticlesAroundSelf(mob, ParticleTypes.f_123747_);
                     TensuraParticleHelper.addServerParticlesAroundSelf(mob, ParticleTypes.f_123747_, 2.0D);
                     tag.m_128473_("SummonUUID");
                     instance.markDirty();
                  }
               }
            }

         }
      }
   }

   public void onSubordinateDeath(ManasSkillInstance instance, LivingEntity owner, LivingDeathEvent e) {
      CompoundTag tag = instance.getTag();
      if (tag != null) {
         if (tag.m_128403_("SummonUUID")) {
            LivingEntity entity = e.getEntity();
            if (Objects.equals(entity.m_20148_(), tag.m_128342_("SummonUUID"))) {
               tag.m_128473_("SummonUUID");
               instance.setCoolDown(0);
            }
         }
      }
   }

   @Nullable
   protected abstract EntityType<? extends T> getSummonedType(ManasSkillInstance var1);

   protected abstract void removeExistingSummon(ManasSkillInstance var1, LivingEntity var2);

   protected abstract void addAdditionalSummonData(ManasSkillInstance var1, LivingEntity var2, T var3);

   protected abstract ResourceLocation getSummoningFxLocation(ManasSkillInstance var1);

   protected abstract ParticleOptions getSummoningParticle(ManasSkillInstance var1);

   protected abstract SoundEvent getSummoningSound(ManasSkillInstance var1);

   protected abstract SoundEvent getFailSound(ManasSkillInstance var1);
}
