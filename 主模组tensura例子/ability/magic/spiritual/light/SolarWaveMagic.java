package com.github.manasmods.tensura.ability.magic.spiritual.light;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.magic.MagicElemental;
import com.github.manasmods.tensura.ability.magic.spiritual.SpiritualMagic;
import com.github.manasmods.tensura.entity.magic.projectile.SolarGrenadeProjectile;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;

public class SolarWaveMagic extends SpiritualMagic {
   public SolarWaveMagic() {
      super(MagicElemental.LIGHT, SpiritualMagic.SpiritLevel.MEDIUM);
   }

   public int defaultCast() {
      return 60;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      return 250.0D;
   }

   public void onRelease(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      super.onRelease(instance, entity, heldTicks);
      if (this.getHeldTicks(instance) >= this.castingTime(instance, entity)) {
         if (!SkillHelper.outOfMagicule(entity, instance)) {
            entity.m_21011_(InteractionHand.MAIN_HAND, true);
            this.addMasteryPoint(instance, entity);
            SolarGrenadeProjectile grenade = new SolarGrenadeProjectile(entity.m_9236_(), entity);
            grenade.setSpeed(1.0F);
            grenade.setDamage(30.0F);
            grenade.setEffectRange(4.0F);
            grenade.setSpiritAttack(true);
            MobEffectInstance blindness = new MobEffectInstance(MobEffects.f_19610_, 300, instance.isMastered(entity) ? 1 : 0, false, false, false);
            grenade.setMobEffect(blindness);
            grenade.setMpCost(this.magiculeCost(entity, instance));
            grenade.setSkill(instance);
            grenade.setPosAndShoot(entity);
            entity.m_9236_().m_7967_(grenade);
            entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11705_, SoundSource.PLAYERS, 1.0F, 1.0F);
         }
      }
   }
}
