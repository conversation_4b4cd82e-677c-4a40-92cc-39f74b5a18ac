package com.github.manasmods.tensura.ability.magic.spiritual.earth;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.magic.MagicElemental;
import com.github.manasmods.tensura.ability.magic.spiritual.SpiritualMagic;
import com.github.manasmods.tensura.entity.magic.spike.EarthSpikeEntity;
import java.util.Iterator;
import java.util.List;
import net.minecraft.core.BlockPos;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.ClipContext.Fluid;
import net.minecraft.world.phys.AABB;
import net.minecraft.world.phys.BlockHitResult;
import net.minecraft.world.phys.Vec3;

public class EarthSpikesMagic extends SpiritualMagic {
   public EarthSpikesMagic() {
      super(MagicElemental.EARTH, SpiritualMagic.SpiritLevel.MEDIUM);
   }

   public int defaultCast() {
      return 40;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      return 150.0D;
   }

   public void onRelease(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      super.onRelease(instance, entity, heldTicks);
      if (this.getHeldTicks(instance) >= this.castingTime(instance, entity)) {
         if (!SkillHelper.outOfMagicule(entity, instance)) {
            Level level = entity.m_9236_();
            Entity targetingEntity = SkillHelper.getTargetingEntity(entity, 20.0D, false, true);
            Vec3 pos;
            if (targetingEntity != null && targetingEntity.m_20096_()) {
               pos = targetingEntity.m_20182_();
            } else {
               BlockHitResult targetPos = SkillHelper.getPlayerPOVHitResult(entity.f_19853_, entity, Fluid.NONE, 20.0D);
               pos = targetPos.m_82450_();
               if (!level.m_8055_((new BlockPos(pos)).m_7495_()).m_60767_().m_76333_()) {
                  pos = null;
               }
            }

            if (pos != null) {
               instance.addMasteryPoint(entity);
               entity.m_21011_(InteractionHand.MAIN_HAND, true);
               entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12049_, SoundSource.PLAYERS, 1.0F, 1.0F);
               spawnSpikes(pos, entity, instance, 30.0F, this.magiculeCost(entity, instance));
               if (instance.isMastered(entity)) {
                  List<LivingEntity> list = entity.m_9236_().m_6443_(LivingEntity.class, (new AABB(new BlockPos(pos))).m_82400_(2.5D), (living) -> {
                     return !living.m_7306_(entity) && living.m_6084_() && !living.m_7307_(entity);
                  });
                  Iterator var8 = list.iterator();

                  while(var8.hasNext()) {
                     LivingEntity target = (LivingEntity)var8.next();
                     if (target.m_20096_() && target != targetingEntity) {
                        spawnSpikes(target.m_20182_(), entity, instance, 30.0F, this.magiculeCost(entity, instance));
                     }
                  }

               }
            }
         }
      }
   }

   public static void spawnSpikes(Vec3 pos, LivingEntity entity, ManasSkillInstance instance, float damage, double cost) {
      EarthSpikeEntity spike = new EarthSpikeEntity(entity.m_9236_(), entity);
      spike.m_146884_(pos);
      spike.setDamage(damage);
      spike.setExtendingTick(5);
      spike.setHeight(4.0F);
      spike.setMpCost(cost);
      spike.setSkill(instance);
      entity.m_9236_().m_7967_(spike);
   }
}
