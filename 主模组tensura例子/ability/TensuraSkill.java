package com.github.manasmods.tensura.ability;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.TickingSkill;
import com.github.manasmods.manascore.api.skills.event.UnlockSkillEvent;
import com.github.manasmods.manascore.capability.skill.event.TickEventListenerHandler;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.capability.skill.TensuraSkillCapability;
import com.github.manasmods.tensura.effect.InsanityEffect;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.util.TensuraAdvancementsHelper;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import com.github.manasmods.tensura.util.damage.TensuraDamageSource;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.UUID;
import javax.annotation.Nullable;
import net.minecraft.ChatFormatting;
import net.minecraft.Util;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.MutableComponent;
import net.minecraft.network.chat.Style;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.ai.attributes.AttributeInstance;
import net.minecraft.world.entity.ai.attributes.AttributeModifier;
import net.minecraft.world.entity.player.Player;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.event.entity.living.LivingDeathEvent;

public abstract class TensuraSkill extends ManasSkill {
   public TensuraSkillInstance createDefaultInstance() {
      return new TensuraSkillInstance(this);
   }

   @Nullable
   public MutableComponent getColoredName() {
      MutableComponent name = super.getName();
      return name == null ? null : name.m_130940_(ChatFormatting.WHITE);
   }

   public int modes() {
      return 1;
   }

   public int nextMode(LivingEntity entity, TensuraSkillInstance instance, boolean reverse) {
      return 0;
   }

   public Component getModeName(int mode) {
      return Component.m_237115_("tensura.skill.mode.default");
   }

   public boolean canBeSlotted(ManasSkillInstance instance) {
      return true;
   }

   public boolean isInSlot(LivingEntity living) {
      return TensuraSkillCapability.isSkillInSlot(living, this);
   }

   public boolean isHeld(LivingEntity entity) {
      Iterator var2 = TickEventListenerHandler.tickingSkills.get(entity.m_20148_()).iterator();

      TickingSkill tickingSkill;
      do {
         if (!var2.hasNext()) {
            return false;
         }

         tickingSkill = (TickingSkill)var2.next();
      } while(tickingSkill.getSkill() != this);

      return true;
   }

   public int getMaxHeldTime(ManasSkillInstance instance, LivingEntity living) {
      return !this.isInSlot(living) ? 20 : 72000;
   }

   protected boolean hasAttributeApplied(LivingEntity entity, Attribute attribute, String uuid) {
      AttributeInstance attributeInstance = entity.m_21051_(attribute);
      if (attributeInstance == null) {
         return false;
      } else {
         AttributeModifier modifier = attributeInstance.m_22111_(UUID.fromString(uuid));
         return modifier == null ? false : modifier.m_22214_().equals(Util.m_137492_("skill", this.getRegistryName()));
      }
   }

   public boolean meetEPRequirement(Player entity, double newEP) {
      return false;
   }

   public int getMasteryOnEPAcquirement(Player entity) {
      return -100;
   }

   public double getObtainingEpCost() {
      return 0.0D;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      return 0.0D;
   }

   public double auraCost(LivingEntity entity, ManasSkillInstance instance) {
      return 0.0D;
   }

   public TensuraDamageSource sourceWithMP(DamageSource source, LivingEntity entity, ManasSkillInstance instance) {
      return DamageSourceHelper.addSkillAndCost(source, this.magiculeCost(entity, instance), instance);
   }

   protected int defaultMasteryAdd(LivingEntity entity) {
      float chance = entity.m_217043_().m_188501_();
      if ((double)chance < 0.85D) {
         return 1;
      } else if ((double)chance < 0.95D) {
         return 2;
      } else {
         return (double)chance < 0.99D ? 3 : 4;
      }
   }

   public void addMasteryPoint(ManasSkillInstance instance, LivingEntity entity) {
      if (!instance.isTemporarySkill()) {
         int point = this.defaultMasteryAdd(entity);
         this.addMasteryPoint(instance, entity, point + SkillUtils.getBonusMasteryPoint(instance, entity, point));
      }
   }

   public void addMasteryPoint(ManasSkillInstance instance, LivingEntity entity, int point) {
      if (!this.isMastered(instance, entity) && (!instance.onCoolDown() || this.canIgnoreCoolDown(instance, entity))) {
         instance.setMastery(Math.min(instance.getMastery() + point, this.getMaxMastery()));
         if (instance.getMastery() > 0 && point > 0 && entity instanceof Player) {
            Player player = (Player)entity;
            player.m_5661_(Component.m_237110_("tensura.skill.mastery.point_added", new Object[]{this.getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GREEN)), true);
         }

         if (instance.isMastered(entity)) {
            if (entity instanceof ServerPlayer) {
               ServerPlayer player = (ServerPlayer)entity;
               TensuraAdvancementsHelper.grant(player, TensuraAdvancementsHelper.Advancements.MASTER_SKILL);
               ManasSkill var6 = instance.getSkill();
               if (var6 instanceof Skill) {
                  Skill skill = (Skill)var6;
                  if (skill.getType().equals(Skill.SkillType.UNIQUE)) {
                     TensuraAdvancementsHelper.grant(player, TensuraAdvancementsHelper.Advancements.MASTER_UNIQUE_SKILL);
                  }
               }

               player.m_5661_(Component.m_237110_("tensura.skill.mastery", new Object[]{instance.getSkill().getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GOLD)), false);
            }

            this.onSkillMastered(instance, entity);
         }

         instance.markDirty();
      }
   }

   public double learningCost() {
      return 0.0D;
   }

   public String modeLearningId(int mode) {
      return "None";
   }

   public void addLearnPoint(ManasSkillInstance instance, LivingEntity entity) {
      if (instance.getMastery() < 0) {
         if (SkillHelper.outOfMagicule(entity, this.learningCost())) {
            return;
         }

         if (entity instanceof Player) {
            Player player = (Player)entity;
            player.m_6330_(SoundEvents.f_11871_, SoundSource.PLAYERS, 1.0F, 1.0F);
         }

         instance.setCoolDown(10);
         int oldMastery = instance.getMastery();
         int newMastery = oldMastery + SkillUtils.getEarningLearnPoint(instance, entity, false);
         instance.setMastery(Math.min(newMastery, 0));
         instance.markDirty();
         if (oldMastery < 0 && newMastery >= 0) {
            UnlockSkillEvent event = new UnlockSkillEvent(instance, entity);
            if (MinecraftForge.EVENT_BUS.post(event)) {
               instance.setMastery(oldMastery);
               instance.markDirty();
               return;
            }

            instance.onLearnSkill(entity, event);
            if (entity instanceof ServerPlayer) {
               ServerPlayer player = (ServerPlayer)entity;
               player.m_5661_(Component.m_237110_("tensura.skill.acquire_learning", new Object[]{this.getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GOLD)), false);
               TensuraAdvancementsHelper.grant(player, TensuraAdvancementsHelper.Advancements.FAST_LEARNER);
            }
         } else if (entity instanceof Player) {
            Player player = (Player)entity;
            player.m_5661_(Component.m_237110_("tensura.skill.learn_points_added", new Object[]{this.getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GREEN)), true);
         }
      }

   }

   public boolean canLearnSkill(ManasSkillInstance instance, LivingEntity entity) {
      if (entity.m_5833_()) {
         return false;
      } else if (entity.m_21023_((MobEffect)TensuraMobEffects.SLEEP_MODE.get())) {
         return false;
      } else if (entity.m_21023_((MobEffect)TensuraMobEffects.REST.get())) {
         return false;
      } else if (entity.m_21023_((MobEffect)TensuraMobEffects.INFINITE_IMPRISONMENT.get())) {
         return false;
      } else {
         return !SkillUtils.noInteractiveMode(entity);
      }
   }

   protected boolean canActivateInRaceLimit(ManasSkillInstance instance) {
      return false;
   }

   public boolean canInteractSkill(ManasSkillInstance instance, LivingEntity entity) {
      if (!entity.m_5833_() && instance.getMastery() >= 0) {
         if (entity.m_21023_((MobEffect)TensuraMobEffects.SLEEP_MODE.get())) {
            return false;
         } else if (entity.m_21023_((MobEffect)TensuraMobEffects.REST.get())) {
            return false;
         } else if (InsanityEffect.havingNightmare(entity)) {
            return false;
         } else if (entity.m_21023_((MobEffect)TensuraMobEffects.INFINITE_IMPRISONMENT.get())) {
            return this.canActivateInRaceLimit(instance);
         } else if (entity.m_21023_((MobEffect)TensuraMobEffects.BATS_MODE.get())) {
            return this.canActivateInRaceLimit(instance);
         } else {
            return entity.m_21023_((MobEffect)TensuraMobEffects.SHADOW_STEP.get()) ? this.canActivateInRaceLimit(instance) : true;
         }
      } else {
         return false;
      }
   }

   public List<MobEffect> getImmuneEffects(ManasSkillInstance instance, LivingEntity entity) {
      return new ArrayList();
   }

   public void onNumberKeyPress(ManasSkillInstance instance, Player player, int keyNumber) {
   }

   public void onSubordinateDeath(ManasSkillInstance instance, LivingEntity owner, LivingDeathEvent e) {
   }
}
