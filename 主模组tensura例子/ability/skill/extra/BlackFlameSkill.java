package com.github.manasmods.tensura.ability.skill.extra;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.entity.magic.breath.BreathEntity;
import com.github.manasmods.tensura.entity.magic.skill.BlackFlameBallProjectile;
import com.github.manasmods.tensura.entity.magic.skill.HellFlareProjectile;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.particle.TensuraParticles;
import com.github.manasmods.tensura.registry.skill.CommonSkills;
import com.github.manasmods.tensura.registry.skill.ExtraSkills;
import com.github.manasmods.tensura.registry.skill.IntrinsicSkills;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import net.minecraft.ChatFormatting;
import net.minecraft.core.particles.ParticleOptions;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.MutableComponent;
import net.minecraft.network.chat.Style;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.entity.projectile.AbstractArrow;
import net.minecraft.world.level.Level;
import net.minecraftforge.event.entity.living.LivingHurtEvent;

public class BlackFlameSkill extends Skill {
   public BlackFlameSkill() {
      super(Skill.SkillType.EXTRA);
   }

   public boolean meetEPRequirement(Player entity, double newEP) {
      if (!SkillUtils.hasSkill(entity, (ManasSkill)ExtraSkills.BLACK_LIGHTNING.get())) {
         return false;
      } else {
         return !SkillUtils.hasSkill(entity, (ManasSkill)ExtraSkills.MOLECULAR_MANIPULATION.get()) ? false : SkillUtils.isSkillMastered(entity, (ManasSkill)IntrinsicSkills.FLAME_TRANSFORM.get());
      }
   }

   public double learningCost() {
      return 1000.0D;
   }

   public boolean canBeToggled(ManasSkillInstance instance, LivingEntity living) {
      return true;
   }

   public int modes() {
      return 4;
   }

   public int nextMode(LivingEntity entity, TensuraSkillInstance instance, boolean reverse) {
      int var10000;
      if (reverse) {
         switch(instance.getMode()) {
         case 1:
            var10000 = instance.getOrCreateTag().m_128451_("HellFlare") >= 200 ? 4 : (this.canEquipHellFlare(entity) ? 3 : 2);
            break;
         case 2:
            var10000 = 1;
            break;
         case 3:
            var10000 = 2;
            break;
         case 4:
            var10000 = this.canEquipHellFlare(entity) ? 3 : 2;
            break;
         default:
            var10000 = 0;
         }

         return var10000;
      } else {
         switch(instance.getMode()) {
         case 1:
            var10000 = 2;
            break;
         case 2:
            var10000 = this.canEquipHellFlare(entity) ? 3 : 1;
            break;
         case 3:
            var10000 = instance.getOrCreateTag().m_128451_("HellFlare") >= 200 ? 4 : 1;
            break;
         default:
            var10000 = 1;
         }

         return var10000;
      }
   }

   public Component getModeName(int mode) {
      MutableComponent var10000;
      switch(mode) {
      case 1:
         var10000 = Component.m_237115_("tensura.skill.mode.black_flame.breath");
         break;
      case 2:
         var10000 = Component.m_237115_("tensura.skill.mode.black_flame.ball");
         break;
      case 3:
         var10000 = Component.m_237115_("tensura.skill.mode.black_flame.hell_flare");
         break;
      case 4:
         var10000 = Component.m_237115_("tensura.skill.mode.black_flame.limited_hell_flare");
         break;
      default:
         var10000 = Component.m_237119_();
      }

      return var10000;
   }

   private boolean canEquipHellFlare(LivingEntity entity) {
      if (!SkillUtils.hasSkill(entity, (ManasSkill)CommonSkills.RANGED_BARRIER.get())) {
         return false;
      } else {
         return SkillUtils.hasSkill(entity, (ManasSkill)ExtraSkills.FLAME_MANIPULATION.get()) || SkillUtils.hasSkill(entity, (ManasSkill)ExtraSkills.FLAME_DOMINATION.get());
      }
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      double var10000;
      switch(instance.getMode()) {
      case 1:
         var10000 = 50.0D;
         break;
      case 2:
         var10000 = 100.0D;
         break;
      default:
         var10000 = 0.0D;
      }

      return var10000;
   }

   public String modeLearningId(int mode) {
      return mode == 3 ? "HellFlare" : "None";
   }

   public boolean canIgnoreCoolDown(ManasSkillInstance instance, LivingEntity entity) {
      if (instance.getMastery() < 0) {
         return false;
      } else {
         CompoundTag tag = instance.getOrCreateTag();
         return instance.getMode() != 3 && tag.m_128451_("HellFlare") <= 100;
      }
   }

   public void onTouchEntity(ManasSkillInstance instance, LivingEntity attacker, LivingHurtEvent e) {
      if (instance.isToggled()) {
         if (!SkillHelper.outOfMagicule(attacker, 5.0D)) {
            if (DamageSourceHelper.isPhysicalAttack(e.getSource()) || e.getSource().m_7639_() instanceof AbstractArrow) {
               LivingEntity target = e.getEntity();
               int level = this.isMastered(instance, attacker) ? 1 : 0;
               if (attacker instanceof Player) {
                  Player player = (Player)attacker;
                  SkillHelper.addEffectWithSource(target, player, (MobEffect)TensuraMobEffects.BLACK_BURN.get(), 200, level);
               } else {
                  target.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.BLACK_BURN.get(), 200, level, false, false, false));
               }

               attacker.m_9236_().m_6263_((Player)null, attacker.m_20185_(), attacker.m_20186_(), attacker.m_20189_(), SoundEvents.f_11909_, SoundSource.PLAYERS, 1.0F, 1.0F);
               TensuraParticleHelper.addServerParticlesAroundSelf(target, (ParticleOptions)TensuraParticles.BLACK_FIRE.get(), 1.0D);
            }
         }
      }
   }

   public boolean onHeld(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      if (instance.getMode() != 1) {
         return false;
      } else if (heldTicks % 20 == 0 && SkillHelper.outOfMagicule(entity, instance)) {
         return false;
      } else {
         if (heldTicks % 100 == 0 && heldTicks > 0) {
            this.addMasteryPoint(instance, entity);
         }

         float damage = instance.isMastered(entity) ? 20.0F : 10.0F;
         BreathEntity.spawnBreathEntity((EntityType)TensuraEntityTypes.BLACK_FLAME_BREATH.get(), entity, instance, damage, this.magiculeCost(entity, instance));
         entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11705_, SoundSource.PLAYERS, 1.0F, 1.0F);
         return true;
      }
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      Level level = entity.m_9236_();
      CompoundTag tag = instance.getOrCreateTag();
      switch(instance.getMode()) {
      case 1:
         instance.getOrCreateTag().m_128405_("BreathEntity", 0);
         instance.markDirty();
         break;
      case 2:
         if (SkillHelper.outOfMagicule(entity, instance)) {
            return;
         }

         this.addMasteryPoint(instance, entity);
         BlackFlameBallProjectile ball = new BlackFlameBallProjectile(entity.m_9236_(), entity);
         ball.setDamage(50.0F);
         ball.setSpeed(1.5F);
         ball.setExplosionRadius(1.0F);
         ball.setMpCost(this.magiculeCost(entity, instance));
         ball.setSkill(instance);
         ball.setPosAndShoot(entity);
         level.m_7967_(ball);
         level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11705_, SoundSource.PLAYERS, 1.0F, 1.0F);
         break;
      case 3:
         if (SkillHelper.outOfMagicule(entity, instance)) {
            return;
         }

         int learnPoint = tag.m_128451_("HellFlare");
         if (learnPoint < 100) {
            tag.m_128405_("HellFlare", learnPoint + SkillUtils.getEarningLearnPoint(instance, entity, true));
            if (entity instanceof Player) {
               Player player = (Player)entity;
               if (tag.m_128451_("HellFlare") >= 100) {
                  player.m_5661_(Component.m_237110_("tensura.skill.acquire_learning", new Object[]{this.getModeName(3)}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GOLD)), false);
               } else {
                  instance.setCoolDown(10);
                  SkillUtils.learningFailPenalty(entity);
                  player.m_5661_(Component.m_237110_("tensura.skill.learn_points_added", new Object[]{this.getModeName(3)}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GREEN)), true);
               }

               player.m_6330_(SoundEvents.f_11871_, SoundSource.PLAYERS, 1.0F, 1.0F);
            }

            instance.markDirty();
            return;
         }

         instance.setCoolDown(1);
         int masteryPoint = tag.m_128451_("HellFlare");
         if (masteryPoint < 200) {
            tag.m_128405_("HellFlare", masteryPoint + SkillUtils.getBonusMasteryPoint(instance, entity, 1));
            if (tag.m_128451_("HellFlare") >= 200 && entity instanceof Player) {
               Player player = (Player)entity;
               player.m_5661_(Component.m_237110_("tensura.skill.mastery", new Object[]{this.getModeName(4)}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GOLD)), false);
            }

            instance.markDirty();
         }

         HellFlareProjectile projectile = new HellFlareProjectile(entity.m_9236_(), entity);
         projectile.setDamage(750.0F);
         projectile.setSpeed(1.5F);
         projectile.setAreaLife(60);
         projectile.setAreaRadius(15.0F);
         projectile.setSkill(instance);
         projectile.setMpCost(this.magiculeCost(entity, instance));
         projectile.setPosAndShoot(entity);
         entity.m_9236_().m_7967_(projectile);
         entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11705_, SoundSource.PLAYERS, 1.0F, 1.0F);
         break;
      case 4:
         if (SkillHelper.outOfMagicule(entity, instance)) {
            return;
         }

         instance.setCoolDown(2);
         HellFlareProjectile projectile = new HellFlareProjectile(entity.m_9236_(), entity);
         projectile.setDamage(2500.0F);
         projectile.setSpeed(1.5F);
         projectile.setAreaLife(60);
         projectile.setAreaRadius(2.5F);
         projectile.setSkill(instance);
         projectile.setMpCost(this.magiculeCost(entity, instance));
         projectile.setPosAndShoot(entity);
         entity.m_9236_().m_7967_(projectile);
         entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11705_, SoundSource.PLAYERS, 1.0F, 1.0F);
      }

   }
}
