package com.github.manasmods.tensura.ability.skill.extra;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.config.TensuraConfig;
import com.github.manasmods.tensura.network.TensuraNetwork;
import com.github.manasmods.tensura.network.play2client.RequestFxSpawningPacket;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import java.util.Iterator;
import java.util.List;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.ai.attributes.AttributeModifier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.attributes.AttributeModifier.Operation;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.entity.EntityTypeTest;
import net.minecraftforge.network.PacketDistributor;

public class MortalFearSkill extends Skill {
   private static final String HAKI = "52863491-edf5-4226-8fcc-c4e42a519af1";

   public MortalFearSkill() {
      super(Skill.SkillType.EXTRA);
      this.addHeldAttributeModifier(Attributes.f_22279_, "52863491-edf5-4226-8fcc-c4e42a519af1", -0.949999988079071D, Operation.MULTIPLY_TOTAL);
   }

   public double learningCost() {
      return 500.0D;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      return 50.0D;
   }

   public double getAttributeModifierAmplifier(ManasSkillInstance instance, LivingEntity entity, AttributeModifier modifier) {
      return modifier.m_22218_() * (instance.isMastered(entity) ? 0.9473684210526315D : 1.0D);
   }

   public boolean onHeld(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      Level var5 = entity.m_9236_();
      if (var5 instanceof ServerLevel) {
         ServerLevel serverLevel = (ServerLevel)var5;
         if (heldTicks % 100 == 0 && heldTicks > 0) {
            this.addMasteryPoint(instance, entity);
         }

         double scale = instance.getTag() == null ? 0.0D : instance.getTag().m_128459_("scale");
         double multiplier = scale == 0.0D ? 1.0D : Math.min(scale, 1.0D);
         double ownerEP = TensuraEPCapability.getEP(entity) * multiplier;
         entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11862_, SoundSource.NEUTRAL, 1.0F, 1.0F);
         List<? extends LivingEntity> list = serverLevel.m_143280_(EntityTypeTest.m_156916_(LivingEntity.class), (living) -> {
            return SkillHelper.isSubordinate(entity, living);
         });
         if (!list.isEmpty()) {
            if (heldTicks % 20 == 0 && SkillHelper.outOfMagicule(entity, this.magiculeCost(entity, instance) * (double)list.size())) {
               return false;
            }

            if (heldTicks % 5 == 0) {
               TensuraNetwork.INSTANCE.send(PacketDistributor.TRACKING_ENTITY_AND_SELF.with(() -> {
                  return entity;
               }), new RequestFxSpawningPacket(new ResourceLocation("tensura:mortal_fear"), entity.m_19879_(), 0.0D, 1.0D, 0.0D, true));
            }

            this.mortalFear(list, entity, heldTicks, ownerEP, 5.0D, instance.isMastered(entity));
         } else {
            if (heldTicks % 20 == 0 && SkillHelper.outOfMagicule(entity, instance)) {
               return false;
            }

            this.mortalFear(List.of(entity), entity, heldTicks, ownerEP, 10.0D, instance.isMastered(entity));
            if (heldTicks % 5 == 0) {
               TensuraNetwork.INSTANCE.send(PacketDistributor.TRACKING_ENTITY_AND_SELF.with(() -> {
                  return entity;
               }), new RequestFxSpawningPacket(new ResourceLocation("tensura:sub_mortal_fear"), entity.m_19879_(), 0.0D, 1.0D, 0.0D, true));
            }
         }

         return true;
      } else {
         return false;
      }
   }

   public void onRelease(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      if (this.hasAttributeApplied(entity, Attributes.f_22279_, "52863491-edf5-4226-8fcc-c4e42a519af1")) {
         instance.setCoolDown(instance.isMastered(entity) ? 3 : 5);
      }
   }

   private void mortalFear(List<? extends LivingEntity> list, LivingEntity entity, int heldTicks, double EP, double radius, boolean mastered) {
      Iterator var9 = list.iterator();

      label51:
      while(true) {
         List targetList;
         do {
            if (!var9.hasNext()) {
               return;
            }

            LivingEntity subordinate = (LivingEntity)var9.next();
            if (subordinate != entity && heldTicks % 5 == 0) {
               TensuraNetwork.INSTANCE.send(PacketDistributor.TRACKING_ENTITY_AND_SELF.with(() -> {
                  return subordinate;
               }), new RequestFxSpawningPacket(new ResourceLocation("tensura:sub_mortal_fear"), subordinate.m_19879_(), 0.0D, 1.0D, 0.0D, true));
            }

            if (subordinate != entity) {
               subordinate.m_7292_(new MobEffectInstance(MobEffects.f_19600_, 1200, mastered ? 7 : 3, false, true));
            }

            targetList = entity.m_9236_().m_6443_(LivingEntity.class, subordinate.m_20191_().m_82400_(radius), (entityData) -> {
               return entityData.m_6084_() && !entityData.m_7307_(entity) && !entityData.m_7306_(entity);
            });
         } while(targetList.isEmpty());

         Iterator var12 = targetList.iterator();

         while(true) {
            LivingEntity target;
            Player player;
            do {
               if (!var12.hasNext()) {
                  continue label51;
               }

               target = (LivingEntity)var12.next();
               if (!(target instanceof Player)) {
                  break;
               }

               player = (Player)target;
            } while(player.m_150110_().f_35934_);

            double targetEP = TensuraEPCapability.getEP(target);
            double difference = EP / targetEP;
            if (!(difference <= 2.0D)) {
               int fearLevel = (int)(difference * 0.25D - 0.5D);
               fearLevel = Math.min(fearLevel, (Integer)TensuraConfig.INSTANCE.mobEffectConfig.maxFear.get());
               SkillHelper.checkThenAddEffectSource(target, entity, (MobEffect)TensuraMobEffects.FEAR.get(), 200, fearLevel);
               HakiSkill.hakiPush(target, entity, fearLevel);
            }
         }
      }
   }

   public void onScroll(ManasSkillInstance instance, LivingEntity entity, double delta) {
      HakiSkill.changeEPUsed(instance, entity, delta);
   }
}
