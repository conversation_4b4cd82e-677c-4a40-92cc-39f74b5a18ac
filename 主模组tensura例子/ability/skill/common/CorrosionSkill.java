package com.github.manasmods.tensura.ability.skill.common;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.event.SkillGriefEvent;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.particle.TensuraParticles;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import com.github.manasmods.tensura.world.TensuraGameRules;
import com.google.common.collect.BiMap;
import java.util.Optional;
import net.minecraft.core.BlockPos;
import net.minecraft.core.particles.SimpleParticleType;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.stats.Stats;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.food.FoodProperties;
import net.minecraft.world.item.HoneycombItem;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Items;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.level.block.WeatheringCopper;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.phys.BlockHitResult;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.event.entity.living.LivingHurtEvent;

public class CorrosionSkill extends Skill {
   public CorrosionSkill() {
      super(Skill.SkillType.COMMON);
   }

   public boolean canBeToggled(ManasSkillInstance instance, LivingEntity entity) {
      return instance.isMastered(entity);
   }

   public boolean meetEPRequirement(Player entity, double newEP) {
      if (entity instanceof ServerPlayer) {
         ServerPlayer player = (ServerPlayer)entity;
         if (player.m_8951_().m_13015_(Stats.f_12986_.m_12902_((EntityType)TensuraEntityTypes.TEMPEST_SERPENT.get())) >= 100) {
            return true;
         } else if (player.m_8951_().m_13015_(Stats.f_12986_.m_12902_((EntityType)TensuraEntityTypes.ORC_LORD.get())) > 0) {
            return true;
         } else if (player.m_8951_().m_13015_(Stats.f_12986_.m_12902_((EntityType)TensuraEntityTypes.ORC_DISASTER.get())) > 0) {
            return true;
         } else {
            return player.m_8951_().m_13015_(Stats.f_12982_.m_12902_(Items.f_42583_)) >= 100;
         }
      } else {
         return false;
      }
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      ItemStack itemStack = entity.m_21205_();
      if (!itemStack.m_150930_(Items.f_42583_)) {
         FoodProperties foodProperties = itemStack.getFoodProperties(entity);
         if (foodProperties != null && foodProperties.m_38746_()) {
            itemStack.m_41774_(1);
            this.addMasteryPoint(instance, entity);
            if (entity instanceof Player) {
               Player player = (Player)entity;
               ItemStack stack = new ItemStack(Items.f_42583_);
               if (!player.m_36356_(stack)) {
                  player.m_36176_(stack, false);
               }
            }

            entity.m_21011_(InteractionHand.MAIN_HAND, true);
            entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12031_, SoundSource.PLAYERS, 1.0F, 1.0F);
         }

      }
   }

   public void onRightClickBlock(ManasSkillInstance instance, LivingEntity entity, BlockHitResult hitResult) {
      if (this.isInSlot(entity)) {
         if (TensuraGameRules.canSkillGrief(entity.m_9236_())) {
            if (entity.m_21205_().m_41619_() && entity.m_21206_().m_41619_()) {
               if (!entity.f_20911_) {
                  Level level = entity.m_9236_();
                  this.addMasteryPoint(instance, entity);
                  BlockPos pos = hitResult.m_82425_();
                  BlockState state = level.m_8055_(pos);
                  Block var8 = state.m_60734_();
                  if (var8 instanceof WeatheringCopper) {
                     WeatheringCopper copper = (WeatheringCopper)var8;
                     copper.m_142123_(state).ifPresent((blockState) -> {
                        SkillGriefEvent.Pre preGrief = new SkillGriefEvent.Pre(entity, instance, pos);
                        if (!MinecraftForge.EVENT_BUS.post(preGrief)) {
                           level.m_46597_(pos, blockState);
                           MinecraftForge.EVENT_BUS.post(new SkillGriefEvent.Post(entity, instance, pos));
                           this.successCorrosion(entity, level, pos);
                        }
                     });
                  } else {
                     BlockState waxOff = (BlockState)Optional.ofNullable((Block)((BiMap)HoneycombItem.f_150864_.get()).get(state.m_60734_())).map((block) -> {
                        return block.m_152465_(state);
                     }).orElse((Object)null);
                     if (waxOff != null) {
                        SkillGriefEvent.Pre preGrief = new SkillGriefEvent.Pre(entity, instance, pos);
                        if (MinecraftForge.EVENT_BUS.post(preGrief)) {
                           return;
                        }

                        level.m_46597_(pos, waxOff);
                        MinecraftForge.EVENT_BUS.post(new SkillGriefEvent.Post(entity, instance, pos));
                        this.successCorrosion(entity, level, pos);
                     }
                  }

               }
            }
         }
      }
   }

   public void onTouchEntity(ManasSkillInstance instance, LivingEntity attacker, LivingHurtEvent e) {
      if (this.isInSlot(attacker) || instance.isToggled()) {
         if (e.getSource().m_7640_() == attacker) {
            if (DamageSourceHelper.isPhysicalAttack(e.getSource())) {
               LivingEntity target = e.getEntity();
               MobEffect corrosion = instance.isMastered(attacker) ? (MobEffect)TensuraMobEffects.CORROSION.get() : MobEffects.f_19615_;
               SkillHelper.checkThenAddEffectSource(target, attacker, corrosion, 200, 0);
               attacker.m_9236_().m_6263_((Player)null, attacker.m_20185_(), attacker.m_20186_(), attacker.m_20189_(), SoundEvents.f_12031_, SoundSource.PLAYERS, 1.0F, 1.0F);
               ((ServerLevel)attacker.m_9236_()).m_8767_((SimpleParticleType)TensuraParticles.ACID_BUBBLE.get(), target.m_20182_().f_82479_, target.m_20182_().f_82480_ + (double)target.m_20206_() / 2.0D, target.m_20182_().f_82481_, 20, 0.08D, 0.08D, 0.08D, 0.15D);
               CompoundTag tag = instance.getOrCreateTag();
               int time = tag.m_128451_("activatedTimes");
               if (time % 10 == 0) {
                  this.addMasteryPoint(instance, attacker);
               }

               tag.m_128405_("activatedTimes", time + 1);
            }
         }
      }
   }

   protected void successCorrosion(LivingEntity entity, Level level, BlockPos pos) {
      entity.m_21011_(InteractionHand.MAIN_HAND, true);
      entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12031_, SoundSource.PLAYERS, 1.0F, 1.0F);
      ((ServerLevel)level).m_8767_((SimpleParticleType)TensuraParticles.ACID_BUBBLE.get(), (double)pos.m_123341_() + 0.5D, (double)pos.m_123342_() + 0.5D, (double)pos.m_123343_() + 0.5D, 10, 0.08D, 0.08D, 0.08D, 0.1D);
   }
}
