package com.github.manasmods.tensura.ability.skill.resist;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import javax.annotation.Nullable;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.LivingEntity;
import net.minecraftforge.event.entity.living.LivingAttackEvent;

public class PhysicalAttackNullification extends ResistSkill {
   public PhysicalAttackNullification() {
      super(ResistSkill.ResistType.NULLIFICATION);
   }

   public boolean isDamageResisted(DamageSource damageSource, ManasSkillInstance instance) {
      return DamageSourceHelper.isPhysicalAttack(damageSource);
   }

   public boolean isResistanceBypass(DamageSource damageSource) {
      return PhysicalAttackResistance.canBypassPhysical(damageSource);
   }

   public void onBeingDamaged(ManasSkillInstance instance, LivingAttackEvent event) {
      if (!event.isCanceled()) {
         if (instance.isToggled()) {
            if (!(this.getResistanceDamageMultiplier(true) > 0.0D)) {
               if (!(this.getHpMultiplierForResistance(true) >= 0.0D)) {
                  if (!this.isNullificationBypass(event.getSource())) {
                     if (this.isDamageResisted(event.getSource(), instance) || ogreBerserkerResist(event.getSource(), event.getEntity())) {
                        if (this.isResistanceBypass(event.getSource())) {
                           return;
                        }

                        event.setCanceled(true);
                     }

                  }
               }
            }
         }
      }
   }

   public static boolean ogreBerserkerResist(@Nullable DamageSource source, LivingEntity target) {
      if (source != null && source.m_19378_()) {
         return false;
      } else {
         MobEffectInstance instance = target.m_21124_((MobEffect)TensuraMobEffects.OGRE_BERSERKER.get());
         if (instance == null) {
            return false;
         } else {
            return instance.m_19564_() >= 1;
         }
      }
   }
}
