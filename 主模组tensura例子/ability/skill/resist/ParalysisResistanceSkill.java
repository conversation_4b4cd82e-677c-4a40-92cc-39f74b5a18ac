package com.github.manasmods.tensura.ability.skill.resist;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.skill.ResistanceSkills;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Nullable;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.entity.LivingEntity;
import net.minecraftforge.event.entity.living.LivingDamageEvent;
import org.jetbrains.annotations.NotNull;

public class ParalysisResistanceSkill extends ResistSkill {
   public void onTakenDamage(ManasSkillInstance instance, LivingDamageEvent event) {
      if (!event.isCanceled()) {
         if (instance.getMastery() < 0) {
            if (event.getEntity().m_21023_((MobEffect)TensuraMobEffects.PARALYSIS.get())) {
               if ((double)event.getAmount() > this.learningCost()) {
                  this.addLearnPoint(instance, event.getEntity());
               }

            }
         }
      }
   }

   public int pointRequirement() {
      return 350;
   }

   @NotNull
   public List<MobEffect> getImmuneEffects(ManasSkillInstance instance, LivingEntity entity) {
      return (List)(!instance.isToggled() ? new ArrayList() : List.of((MobEffect)TensuraMobEffects.PARALYSIS.get()));
   }

   @Nullable
   protected ManasSkill getNullificationForm() {
      return (ManasSkill)ResistanceSkills.PARALYSIS_NULLIFICATION.get();
   }
}
