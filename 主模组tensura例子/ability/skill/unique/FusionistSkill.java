package com.github.manasmods.tensura.ability.skill.unique;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.entity.magic.misc.FusionistLandmineEntity;
import com.github.manasmods.tensura.entity.magic.skill.FusionistProjectile;
import com.github.manasmods.tensura.event.SkillGriefEvent;
import com.github.manasmods.tensura.world.TensuraGameRules;
import java.util.Iterator;
import java.util.List;
import net.minecraft.ChatFormatting;
import net.minecraft.core.BlockPos;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.MutableComponent;
import net.minecraft.network.chat.Style;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.BlockItem;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.ClipContext.Fluid;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.phys.AABB;
import net.minecraft.world.phys.BlockHitResult;
import net.minecraftforge.common.MinecraftForge;

public class FusionistSkill extends Skill {
   public FusionistSkill() {
      super(Skill.SkillType.UNIQUE);
   }

   public double getObtainingEpCost() {
      return 20000.0D;
   }

   public int modes() {
      return 3;
   }

   public int nextMode(LivingEntity entity, TensuraSkillInstance instance, boolean reverse) {
      if (reverse) {
         return instance.getMode() == 1 ? 3 : instance.getMode() - 1;
      } else {
         return instance.getMode() == 3 ? 1 : instance.getMode() + 1;
      }
   }

   public Component getModeName(int mode) {
      MutableComponent var10000;
      switch(mode) {
      case 1:
         var10000 = Component.m_237115_("tensura.skill.mode.fusionist.disassemble");
         break;
      case 2:
         var10000 = Component.m_237115_("tensura.skill.mode.fusionist.fuse");
         break;
      case 3:
         var10000 = Component.m_237115_("tensura.skill.mode.fusionist.projectile");
         break;
      default:
         var10000 = Component.m_237119_();
      }

      return var10000;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      double var10000;
      switch(instance.getMode()) {
      case 2:
         var10000 = 1000.0D;
         break;
      case 3:
         var10000 = 200.0D;
         break;
      default:
         var10000 = 0.0D;
      }

      return var10000;
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      Level level = entity.m_9236_();
      CompoundTag tag = instance.getOrCreateTag();
      Player player;
      Player player;
      Player player;
      switch(instance.getMode()) {
      case 1:
         if (entity.m_6144_()) {
            ItemStack stack = entity.m_21205_();
            Item var20 = stack.m_41720_();
            if (var20 instanceof BlockItem) {
               BlockItem item = (BlockItem)var20;
               if (!item.m_40614_().m_49966_().m_204336_(TensuraTags.Blocks.SKILL_UNBREAKABLE)) {
                  stack.m_41774_(1);
                  entity.m_21011_(InteractionHand.MAIN_HAND, true);
                  tag.m_128405_("matters", tag.m_128451_("matters") + 1);
                  if (entity instanceof Player) {
                     Player player = (Player)entity;
                     player.m_5661_(Component.m_237110_("tensura.skill.fusionist.matter_amount", new Object[]{tag.m_128451_("matters")}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), true);
                  }

                  level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11871_, SoundSource.PLAYERS, 1.0F, 1.0F);
                  instance.markDirty();
                  return;
               }
            }
         }

         BlockHitResult result = SkillHelper.getPlayerPOVHitResult(level, entity, Fluid.NONE, 5.0D);
         BlockPos pos = result.m_82425_();
         BlockState state = level.m_8055_(pos);
         if (state.m_60795_()) {
            return;
         }

         if (state.m_204336_(TensuraTags.Blocks.SKILL_UNBREAKABLE)) {
            return;
         }

         if (!TensuraGameRules.canSkillGrief(level)) {
            return;
         }

         if (!state.canEntityDestroy(level, pos, entity)) {
            return;
         }

         SkillGriefEvent.Pre preGrief = new SkillGriefEvent.Pre(entity, instance, pos);
         if (MinecraftForge.EVENT_BUS.post(preGrief)) {
            return;
         }

         level.m_46953_(pos, false, entity);
         entity.m_21011_(InteractionHand.MAIN_HAND, true);
         tag.m_128405_("matters", tag.m_128451_("matters") + 1);
         if (entity instanceof Player) {
            player = (Player)entity;
            player.m_5661_(Component.m_237110_("tensura.skill.fusionist.matter_amount", new Object[]{tag.m_128451_("matters")}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), true);
         }

         level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11871_, SoundSource.PLAYERS, 1.0F, 1.0F);
         instance.markDirty();
         MinecraftForge.EVENT_BUS.post(new SkillGriefEvent.Post(entity, instance, pos));
         break;
      case 2:
         FusionistLandmineEntity landmine;
         if (tag.m_128441_("mineY")) {
            AABB aabb = (new AABB(new BlockPos(tag.m_128459_("mineX"), tag.m_128459_("mineY"), tag.m_128459_("mineZ")))).m_82400_(0.5D);
            List<FusionistLandmineEntity> list = level.m_6443_(FusionistLandmineEntity.class, aabb, (entityData) -> {
               return entityData.getOwner() == entity;
            });
            if (!list.isEmpty()) {
               Iterator var19 = list.iterator();

               while(var19.hasNext()) {
                  landmine = (FusionistLandmineEntity)var19.next();
                  double y = TensuraGameRules.canSkillGrief(level) ? landmine.m_20186_() : landmine.m_20186_() + 1.0D;
                  landmine.trigger(landmine.m_20185_(), y, landmine.m_20189_());
               }

               tag.m_128473_("mineX");
               tag.m_128473_("mineY");
               tag.m_128473_("mineZ");
               this.addMasteryPoint(instance, entity);
               entity.m_21011_(InteractionHand.MAIN_HAND, true);
               level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11837_, SoundSource.PLAYERS, 1.0F, 1.0F);
               return;
            }
         }

         label128: {
            if (tag.m_128451_("matters") < 5) {
               if (!(entity instanceof Player)) {
                  break label128;
               }

               player = (Player)entity;
               if (!player.m_7500_()) {
                  break label128;
               }
            }

            BlockHitResult result = SkillHelper.getPlayerPOVHitResult(level, entity, Fluid.NONE, 5.0D);
            BlockPos pos = result.m_82425_();
            if (level.m_8055_(pos).m_60795_()) {
               return;
            }

            if (SkillHelper.outOfMagicule(entity, instance)) {
               return;
            }

            label106: {
               landmine = new FusionistLandmineEntity(level, (double)pos.m_123341_() + 0.5D, (double)pos.m_123342_(), (double)pos.m_123343_() + 0.5D, entity);
               landmine.setRadius(40);
               level.m_7967_(landmine);
               entity.m_21011_(InteractionHand.MAIN_HAND, true);
               tag.m_128347_("mineX", landmine.m_20185_());
               tag.m_128347_("mineY", landmine.m_20186_());
               tag.m_128347_("mineZ", landmine.m_20189_());
               if (entity instanceof Player) {
                  player = (Player)entity;
                  if (player.m_7500_()) {
                     break label106;
                  }
               }

               tag.m_128405_("matters", tag.m_128451_("matters") - 5);
            }

            instance.markDirty();
            level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11837_, SoundSource.PLAYERS, 1.0F, 1.0F);
            break;
         }

         if (entity instanceof Player) {
            player = (Player)entity;
            player.m_5661_(Component.m_237115_("tensura.skill.fusionist.out_of_matter").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), true);
         }
         break;
      case 3:
         label136: {
            if (tag.m_128451_("matters") < 1) {
               if (!(entity instanceof Player)) {
                  break label136;
               }

               player = (Player)entity;
               if (!player.m_7500_()) {
                  break label136;
               }
            }

            if (SkillHelper.outOfMagicule(entity, instance)) {
               return;
            }

            label92: {
               entity.m_21011_(InteractionHand.MAIN_HAND, true);
               FusionistProjectile sphere = new FusionistProjectile(level, entity);
               sphere.setSpeed(1.5F);
               sphere.setExplosionRadius(6.0F);
               sphere.setMpCost(this.magiculeCost(entity, instance));
               sphere.setSkill(instance);
               sphere.setPosAndShoot(entity);
               level.m_7967_(sphere);
               if (entity instanceof Player) {
                  Player player = (Player)entity;
                  if (player.m_7500_()) {
                     break label92;
                  }
               }

               tag.m_128405_("matters", tag.m_128451_("matters") - 1);
            }

            this.addMasteryPoint(instance, entity);
            level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11687_, SoundSource.PLAYERS, 1.0F, 1.0F);
            return;
         }

         if (entity instanceof Player) {
            player = (Player)entity;
            player.m_5661_(Component.m_237115_("tensura.skill.fusionist.out_of_matter").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), true);
         }
      }

   }
}
