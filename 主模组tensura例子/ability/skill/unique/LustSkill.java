package com.github.manasmods.tensura.ability.skill.unique;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.network.TensuraNetwork;
import com.github.manasmods.tensura.network.play2client.RequestFxSpawningPacket;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.particle.TensuraParticles;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import java.util.Iterator;
import java.util.List;
import net.minecraft.ChatFormatting;
import net.minecraft.core.particles.ParticleOptions;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.MutableComponent;
import net.minecraft.network.chat.Style;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.animal.Animal;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import net.minecraftforge.network.PacketDistributor;

public class LustSkill extends Skill {
   public LustSkill() {
      super(Skill.SkillType.UNIQUE);
   }

   public double getObtainingEpCost() {
      return 100000.0D;
   }

   public int getMaxMastery() {
      return 1500;
   }

   public double learningCost() {
      return 5000.0D;
   }

   public int modes() {
      return 5;
   }

   public int nextMode(LivingEntity entity, TensuraSkillInstance instance, boolean reverse) {
      int var10000;
      if (reverse) {
         switch(instance.getMode()) {
         case 1:
            var10000 = this.isMastered(instance, entity) ? 5 : 4;
            break;
         case 2:
            var10000 = 1;
            break;
         case 3:
            var10000 = 2;
            break;
         case 4:
            var10000 = 3;
            break;
         case 5:
            var10000 = 4;
            break;
         default:
            var10000 = 0;
         }

         return var10000;
      } else {
         switch(instance.getMode()) {
         case 1:
            var10000 = 2;
            break;
         case 2:
            var10000 = 3;
            break;
         case 3:
            var10000 = 4;
            break;
         case 4:
            var10000 = this.isMastered(instance, entity) ? 5 : 1;
            break;
         default:
            var10000 = 1;
         }

         return var10000;
      }
   }

   public Component getModeName(int mode) {
      MutableComponent var10000;
      switch(mode) {
      case 1:
         var10000 = Component.m_237115_("tensura.skill.mode.lust.drain");
         break;
      case 2:
         var10000 = Component.m_237115_("tensura.skill.mode.lust.invigorate");
         break;
      case 3:
         var10000 = Component.m_237115_("tensura.skill.mode.lust.rebirth");
         break;
      case 4:
         var10000 = Component.m_237115_("tensura.skill.mode.lust.embracing_drain");
         break;
      case 5:
         var10000 = Component.m_237115_("tensura.skill.mode.lust.death_blessing");
         break;
      default:
         var10000 = Component.m_237119_();
      }

      return var10000;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      double var10000;
      switch(instance.getMode()) {
      case 1:
         var10000 = 100.0D;
         break;
      case 2:
      default:
         var10000 = 0.0D;
         break;
      case 3:
         var10000 = 3000.0D;
         break;
      case 4:
         var10000 = 500.0D;
         break;
      case 5:
         var10000 = 7500.0D;
      }

      return var10000;
   }

   public String modeLearningId(int mode) {
      String var10000;
      switch(mode) {
      case 4:
         var10000 = "EmbracingDrain";
         break;
      case 5:
         var10000 = "DeathBlessing";
         break;
      default:
         var10000 = "None";
      }

      return var10000;
   }

   public boolean canIgnoreCoolDown(ManasSkillInstance instance, LivingEntity entity) {
      if (instance.getMastery() < 0) {
         return false;
      } else {
         return instance.getMode() != 4 && instance.getMode() != 5;
      }
   }

   public boolean onHeld(ManasSkillInstance skillInstance, LivingEntity entity, int heldTicks) {
      if (skillInstance.getMode() != 5) {
         return false;
      } else if (skillInstance.getOrCreateTag().m_128451_("DeathBlessing") < 100) {
         return false;
      } else if (heldTicks % 20 == 0 && SkillHelper.outOfMagicule(entity, skillInstance)) {
         return false;
      } else {
         if (heldTicks % 200 == 0 && heldTicks > 0) {
            this.addMasteryPoint(skillInstance, entity);
         }

         entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_215771_, SoundSource.PLAYERS, 1.0F, 1.0F);
         if (heldTicks % 10 == 0) {
            TensuraNetwork.INSTANCE.send(PacketDistributor.TRACKING_ENTITY_AND_SELF.with(() -> {
               return entity;
            }), new RequestFxSpawningPacket(new ResourceLocation("tensura:death_blessing"), entity.m_19879_(), 0.0D, 1.0D, 0.0D, true));
         }

         List<LivingEntity> list = entity.m_9236_().m_6443_(LivingEntity.class, entity.m_20191_().m_82400_(7.0D), (living) -> {
            return !living.m_7306_(entity) && living.m_6084_();
         });
         if (!list.isEmpty() && heldTicks > 0 && heldTicks % 100 == 0) {
            double ownerEP = TensuraEPCapability.getEP(entity);
            Iterator var8 = list.iterator();

            while(true) {
               LivingEntity target;
               Player player;
               do {
                  if (!var8.hasNext()) {
                     return true;
                  }

                  target = (LivingEntity)var8.next();
                  if (!(target instanceof Player)) {
                     break;
                  }

                  player = (Player)target;
               } while(player.m_150110_().f_35934_);

               double targetEP = TensuraEPCapability.getEP(target);
               double difference = targetEP / ownerEP;
               if (difference <= 0.5D) {
                  if (target.m_6469_(this.sourceWithMP(TensuraDamageSources.deathBless(entity), entity, skillInstance), target.m_21233_() * 10.0F)) {
                     SkillHelper.gainMP(entity, targetEP * 0.75D, false);
                     SkillHelper.gainAP(entity, targetEP * 0.25D, false);
                  }
               } else if (target.m_6469_(this.sourceWithMP(TensuraDamageSources.deathBless(entity), entity, skillInstance), target.m_21233_() / 2.0F)) {
                  double epGain = difference <= 0.75D ? targetEP * 0.25D : targetEP * 0.05D;
                  if (target.m_6084_()) {
                     this.reduceStat(target, epGain, entity);
                  }

                  SkillHelper.gainMP(entity, epGain * 0.75D, false);
                  SkillHelper.gainAP(entity, epGain * 0.25D, false);
               }
            }
         } else if (entity instanceof Player) {
            Player player = (Player)entity;
            int second = 5 + heldTicks / 100 * 5;
            player.m_5661_(Component.m_237110_("tensura.skill.time_held.max", new Object[]{heldTicks / 20, second}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GOLD)), true);
         }

         return true;
      }
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      if (instance instanceof TensuraSkillInstance) {
         TensuraSkillInstance skillInstance = (TensuraSkillInstance)instance;
         Level level = entity.m_9236_();
         CompoundTag tag;
         int learnPoint;
         Player player;
         Player player;
         switch(skillInstance.getMode()) {
         case 1:
            if (SkillHelper.outOfMagicule(entity, skillInstance)) {
               return;
            }

            this.addMasteryPoint(skillInstance, entity);
            entity.m_21011_(InteractionHand.MAIN_HAND, true);
            LivingEntity target = SkillHelper.getTargetingEntity(entity, 5.0D, false);
            if (target != null) {
               if (target instanceof Player) {
                  Player player = (Player)target;
                  if (player.m_150110_().f_35934_) {
                     return;
                  }
               }

               if (SkillHelper.drainEnergy(target, entity, 200.0D, false)) {
                  if (this.isMastered(instance, entity)) {
                     SkillHelper.drainEnergy(target, entity, 0.005D, true);
                  }

                  instance.setCoolDown(1);
                  level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12053_, SoundSource.PLAYERS, 1.0F, 1.0F);
                  TensuraParticleHelper.addServerParticlesAroundSelf(target, (ParticleOptions)TensuraParticles.YELLOW_LIGHTNING_SPARK.get());
               }
            } else if (!entity.m_21023_((MobEffect)TensuraMobEffects.LUST_DRAIN.get())) {
               instance.setCoolDown(1);
               entity.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.LUST_DRAIN.get(), 200, this.isMastered(instance, entity) ? 1 : 0, false, false, false));
               level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12053_, SoundSource.PLAYERS, 1.0F, 1.0F);
            }
            break;
         case 2:
            LivingEntity target = SkillHelper.getTargetingEntity(entity, 5.0D, false);
            double lackedMagicule;
            boolean success;
            int cost;
            float lackedHealth;
            if (target != null && entity.m_6144_()) {
               if (target instanceof Animal) {
                  Animal animal = (Animal)target;
                  if (entity instanceof Player) {
                     player = (Player)entity;
                     if (animal.m_21223_() == animal.m_21233_() && animal.m_146764_() >= 0) {
                        animal.m_146762_(0);
                        animal.m_27595_(player);
                     }
                  }
               }

               success = SkillHelper.removePredicateEffect(target, (effect) -> {
                  return true;
               }, this.magiculeCost(entity, instance));
               cost = instance.isMastered(entity) ? 40 : 80;
               lackedHealth = target.m_21233_() - target.m_21223_();
               lackedMagicule = SkillHelper.outOfMagiculeStillConsume(entity, (double)((int)(lackedHealth * (float)cost)));
               if (lackedMagicule > 0.0D) {
                  lackedHealth = (float)((double)lackedHealth - lackedMagicule / (double)cost);
               }

               target.m_5634_(lackedHealth);
               success = success || lackedHealth > 0.0F;
               if (success) {
                  TensuraParticleHelper.addServerParticlesAroundSelf(target, ParticleTypes.f_123749_, 1.0D);
               }
            } else {
               success = SkillHelper.removePredicateEffect(entity, (effect) -> {
                  return true;
               }, this.magiculeCost(entity, instance));
               cost = instance.isMastered(entity) ? 40 : 80;
               lackedHealth = entity.m_21233_() - entity.m_21223_();
               lackedMagicule = SkillHelper.outOfMagiculeStillConsume(entity, (double)((int)(lackedHealth * (float)cost)));
               if (lackedMagicule > 0.0D) {
                  lackedHealth = (float)((double)lackedHealth - lackedMagicule / (double)cost);
               }

               entity.m_5634_(lackedHealth);
               success = success || lackedHealth > 0.0F;
               if (success) {
                  TensuraParticleHelper.addServerParticlesAroundSelf(entity, ParticleTypes.f_123749_, 1.0D);
               }
            }

            if (success) {
               this.addMasteryPoint(skillInstance, entity);
               instance.setCoolDown(instance.isMastered(entity) ? 5 : 3);
               entity.m_21011_(InteractionHand.MAIN_HAND, true);
               level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12275_, SoundSource.PLAYERS, 1.0F, 1.0F);
            }
            break;
         case 3:
            if (SkillHelper.outOfMagicule(entity, skillInstance)) {
               return;
            }

            SkillHelper.comingSoonMessage(entity, (Component)Component.m_237110_("tooltip.tensura.coming_soon_feature", new Object[]{this.getModeName(3)}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GOLD)));
            break;
         case 4:
            if (SkillHelper.outOfMagicule(entity, skillInstance)) {
               return;
            }

            tag = skillInstance.getOrCreateTag();
            learnPoint = tag.m_128451_("EmbracingDrain");
            if (learnPoint < 100) {
               tag.m_128405_("EmbracingDrain", learnPoint + SkillUtils.getEarningLearnPoint(instance, entity, true));
               if (entity instanceof Player) {
                  player = (Player)entity;
                  if (tag.m_128451_("EmbracingDrain") >= 100) {
                     player.m_5661_(Component.m_237110_("tensura.skill.acquire_learning", new Object[]{this.getModeName(4)}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GOLD)), false);
                  } else {
                     instance.setCoolDown(10);
                     SkillUtils.learningFailPenalty(entity);
                     player.m_5661_(Component.m_237110_("tensura.skill.learn_points_added", new Object[]{this.getModeName(4)}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GREEN)), true);
                  }

                  player.m_6330_(SoundEvents.f_11871_, SoundSource.PLAYERS, 1.0F, 1.0F);
               }

               skillInstance.markDirty();
               return;
            }

            LivingEntity target = SkillHelper.getTargetingEntity(entity, 3.0D, false);
            if (target == null) {
               return;
            }

            if (target instanceof Player) {
               player = (Player)target;
               if (player.m_150110_().f_35934_) {
                  return;
               }
            }

            if (SkillHelper.outOfMagicule(entity, skillInstance)) {
               return;
            }

            this.addMasteryPoint(skillInstance, entity);
            int embrace = instance.isMastered(entity) ? 1 : 0;
            SkillHelper.checkThenAddEffectSource(target, entity, (MobEffect)TensuraMobEffects.LUST_EMBRACEMENT.get(), 100, embrace);
            SkillHelper.checkThenAddEffectSource(entity, entity, (MobEffect)TensuraMobEffects.LUST_EMBRACEMENT.get(), 100, embrace);
            level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12053_, SoundSource.PLAYERS, 1.0F, 1.0F);
            break;
         case 5:
            tag = skillInstance.getOrCreateTag();
            learnPoint = tag.m_128451_("DeathBlessing");
            if (learnPoint < 100) {
               if (SkillHelper.outOfMagicule(entity, skillInstance)) {
                  return;
               }

               tag.m_128405_("DeathBlessing", learnPoint + SkillUtils.getEarningLearnPoint(instance, entity, true));
               if (entity instanceof Player) {
                  player = (Player)entity;
                  if (tag.m_128451_("DeathBlessing") >= 100) {
                     player.m_5661_(Component.m_237110_("tensura.skill.acquire_learning", new Object[]{this.getModeName(5)}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GOLD)), false);
                  } else {
                     instance.setCoolDown(10);
                     SkillUtils.learningFailPenalty(entity);
                     player.m_5661_(Component.m_237110_("tensura.skill.learn_points_added", new Object[]{this.getModeName(5)}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GREEN)), true);
                  }

                  player.m_6330_(SoundEvents.f_11871_, SoundSource.PLAYERS, 1.0F, 1.0F);
               }

               skillInstance.markDirty();
            }
         }

      }
   }

   private void reduceStat(LivingEntity target, double amount, LivingEntity attacker) {
      if (target instanceof Player) {
         Player playerTarget = (Player)target;
         TensuraPlayerCapability.getFrom(playerTarget).ifPresent((cap) -> {
            cap.setMagicule(cap.getMagicule() - amount * 0.75D);
            cap.setAura(cap.getAura() - amount * 0.25D);
         });
         TensuraPlayerCapability.sync(playerTarget);
      } else {
         SkillHelper.reduceEP(target, attacker, amount, false);
      }

   }
}
