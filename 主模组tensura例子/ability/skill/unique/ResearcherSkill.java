package com.github.manasmods.tensura.ability.skill.unique;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.manascore.api.skills.capability.SkillStorage;
import com.github.manasmods.tensura.ability.ISpatialStorage;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.config.TensuraConfig;
import com.github.manasmods.tensura.menu.ResearcherEnchantmentMenu;
import com.github.manasmods.tensura.menu.ResearcherSpatialStorageMenu;
import com.github.manasmods.tensura.menu.container.SpatialStorageContainer;
import com.github.manasmods.tensura.network.TensuraNetwork;
import com.github.manasmods.tensura.network.play2client.ClientboundSpatialStorageOpenPacket;
import com.github.manasmods.tensura.registry.skill.UniqueSkills;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Map.Entry;
import net.minecraft.ChatFormatting;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.nbt.ListTag;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.Style;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.SimpleMenuProvider;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.inventory.ContainerLevelAccess;
import net.minecraft.world.inventory.InventoryMenu;
import net.minecraft.world.item.enchantment.Enchantment;
import net.minecraft.world.item.enchantment.EnchantmentHelper;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.event.entity.player.PlayerContainerEvent.Open;
import net.minecraftforge.network.NetworkHooks;
import net.minecraftforge.network.PacketDistributor;
import org.jetbrains.annotations.NotNull;

public class ResearcherSkill extends Skill implements ISpatialStorage {
   public ResearcherSkill() {
      super(Skill.SkillType.UNIQUE);
   }

   public double getObtainingEpCost() {
      return 30000.0D;
   }

   public double learningCost() {
      return 10000.0D;
   }

   public void onSkillMastered(ManasSkillInstance instance, LivingEntity entity) {
      TensuraSkillInstance skill = new TensuraSkillInstance((ManasSkill)UniqueSkills.GODLY_CRAFTSMAN.get());
      if (SkillUtils.learnSkill(entity, (ManasSkillInstance)skill)) {
         addEnchantments(entity, getAllEnchantments(entity, this), skill.getSkill());
         if (entity instanceof Player) {
            Player player = (Player)entity;
            player.m_5661_(Component.m_237110_("tensura.skill.acquire", new Object[]{skill.getSkill().getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.DARK_GREEN)), false);
         }
      }

   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      ManasSkillInstance craftsman = this.getGodlyCraftsman(entity);
      if (entity instanceof ServerPlayer) {
         ServerPlayer player = (ServerPlayer)entity;
         if (player.m_6047_() && !(player.f_36096_ instanceof ResearcherEnchantmentMenu)) {
            player.f_36096_.m_6877_(player);
            player.m_6330_(SoundEvents.f_11889_, SoundSource.PLAYERS, 1.0F, 1.0F);
            ManasSkill skill = craftsman != null ? craftsman.getSkill() : this;
            NetworkHooks.openScreen(player, new SimpleMenuProvider((pContainerId, pPlayerInventory, pPlayer) -> {
               return new ResearcherEnchantmentMenu(pContainerId, pPlayerInventory, ContainerLevelAccess.m_39289_(player.f_19853_, player.m_20183_()), skill);
            }, Component.m_237119_()), (buf) -> {
               buf.m_130085_(SkillUtils.getSkillId(skill));
            });
            return;
         }
      }

      if (craftsman != null) {
         addSelectedEnchantments(entity, Map.of(), true, craftsman.getSkill());
         this.moveItemsToSpatialStorage(instance, craftsman, entity, true);
      } else {
         addSelectedEnchantments(entity, Map.of(), true, this);
         this.openSpatialStorage(entity, instance);
      }
   }

   @NotNull
   public SpatialStorageContainer getSpatialStorage(ManasSkillInstance instance) {
      SpatialStorageContainer container = new SpatialStorageContainer(64, 128);
      container.m_7797_(instance.getOrCreateTag().m_128437_("SpatialStorage", 10));
      return container;
   }

   public void openSpatialStorage(LivingEntity entity, ManasSkillInstance instance) {
      if (entity instanceof ServerPlayer) {
         ServerPlayer player = (ServerPlayer)entity;
         player.m_9217_();
         if (player.f_36096_ instanceof InventoryMenu) {
            player.m_6330_(SoundEvents.f_11889_, SoundSource.PLAYERS, 1.0F, 1.0F);
         } else {
            player.m_6330_(SoundEvents.f_12088_, SoundSource.PLAYERS, 1.0F, 1.0F);
            player.f_36096_.m_6877_(player);
         }

         ManasSkill skill = instance.getSkill();
         SpatialStorageContainer container = this.getSpatialStorage(instance);
         TensuraNetwork.INSTANCE.send(PacketDistributor.PLAYER.with(() -> {
            return player;
         }), new ClientboundSpatialStorageOpenPacket(player.m_19879_(), player.f_8940_, container.m_6643_(), container.m_6893_(), SkillUtils.getSkillId(skill), (byte)3));
         player.f_36096_ = new ResearcherSpatialStorageMenu(player.f_8940_, player.m_150109_(), player, container, skill);
         player.m_143399_(player.f_36096_);
         MinecraftForge.EVENT_BUS.post(new Open(player, player.f_36096_));
      }

   }

   private ManasSkillInstance getGodlyCraftsman(LivingEntity entity) {
      SkillStorage storage = SkillAPI.getSkillsFrom(entity);
      Optional<ManasSkillInstance> craftsmanOptional = storage.getSkill((ManasSkill)UniqueSkills.GODLY_CRAFTSMAN.get());
      return (ManasSkillInstance)craftsmanOptional.orElse((Object)null);
   }

   public static Map<Enchantment, Integer> getAllEnchantments(LivingEntity entity, ManasSkill skill) {
      String tagID = "StoredEnchantments";
      Map<Enchantment, Integer> map = new HashMap();
      Optional<ManasSkillInstance> instance = SkillAPI.getSkillsFrom(entity).getSkill(skill);
      if (instance.isEmpty()) {
         return map;
      } else {
         ManasSkillInstance researcher = (ManasSkillInstance)instance.get();
         CompoundTag tag = researcher.getOrCreateTag();
         return (Map)(!tag.m_128441_(tagID) ? map : EnchantmentHelper.m_44882_(tag.m_128437_(tagID, 10)));
      }
   }

   public static boolean addEnchantments(LivingEntity entity, Map<Enchantment, Integer> map, ManasSkill skill) {
      String tagID = "StoredEnchantments";
      Optional<ManasSkillInstance> instance = SkillAPI.getSkillsFrom(entity).getSkill(skill);
      if (instance.isEmpty()) {
         return false;
      } else {
         ManasSkillInstance researcher = (ManasSkillInstance)instance.get();
         CompoundTag tag = researcher.getOrCreateTag();
         ListTag enchantments;
         if (!tag.m_128441_(tagID)) {
            enchantments = new ListTag();
         } else {
            enchantments = tag.m_128437_(tagID, 10);
         }

         boolean success = false;
         Iterator var9 = map.entrySet().iterator();

         while(true) {
            boolean flag;
            ResourceLocation id;
            int level;
            do {
               Entry entry;
               Enchantment enchantment;
               do {
                  if (!var9.hasNext()) {
                     if (success) {
                        tag.m_128365_(tagID, enchantments);
                        researcher.markDirty();
                        SkillAPI.getSkillsFrom(entity).syncChanges();
                     }

                     return success;
                  }

                  entry = (Entry)var9.next();
                  flag = true;
                  enchantment = (Enchantment)entry.getKey();
                  id = EnchantmentHelper.m_182432_(enchantment);
               } while(((List)TensuraConfig.INSTANCE.enchantmentsConfig.researcherBlacklist.get()).contains(enchantment.toString()));

               level = (Integer)entry.getValue();
               if (entity instanceof Player) {
                  Player player = (Player)entity;
                  level = Math.min(level, ResearcherEnchantmentMenu.getMaxEnchantLevel(enchantment, player));
               }
            } while(level <= 0);

            for(int i = 0; i < enchantments.size(); ++i) {
               CompoundTag compoundtag = enchantments.m_128728_(i);
               ResourceLocation location = EnchantmentHelper.m_182446_(compoundtag);
               if (location != null && location.equals(id)) {
                  if (EnchantmentHelper.m_182438_(compoundtag) < level) {
                     EnchantmentHelper.m_182440_(compoundtag, level);
                     success = true;
                  }

                  flag = false;
                  break;
               }
            }

            if (flag) {
               enchantments.add(EnchantmentHelper.m_182443_(id, level));
               enchantments = sortEnchantments(enchantments);
               success = true;
            }
         }
      }
   }

   private static ListTag sortEnchantments(ListTag listTag) {
      List<CompoundTag> locations = new ArrayList();

      for(int i = 0; i < listTag.size(); ++i) {
         locations.add(listTag.m_128728_(i));
      }

      locations.sort(Comparator.comparing((compoundTag) -> {
         return compoundTag.m_128461_("id");
      }));
      ListTag sortedListTag = new ListTag();
      sortedListTag.addAll(locations);
      return sortedListTag;
   }

   public static Map<Enchantment, Integer> getSelectedEnchantments(LivingEntity entity, ManasSkill skill) {
      String tagID = "SelectedEnchantments";
      Map<Enchantment, Integer> map = new HashMap();
      Optional<ManasSkillInstance> instance = SkillAPI.getSkillsFrom(entity).getSkill(skill);
      if (instance.isEmpty()) {
         return map;
      } else {
         ManasSkillInstance researcher = (ManasSkillInstance)instance.get();
         CompoundTag tag = researcher.getOrCreateTag();
         return (Map)(!tag.m_128441_(tagID) ? map : EnchantmentHelper.m_44882_(tag.m_128437_(tagID, 10)));
      }
   }

   public static Map<Enchantment, Integer> getNewEnchantments(LivingEntity entity, ManasSkill skill) {
      String tagID = "NewEnchantments";
      Map<Enchantment, Integer> map = new HashMap();
      Optional<ManasSkillInstance> instance = SkillAPI.getSkillsFrom(entity).getSkill(skill);
      if (instance.isEmpty()) {
         return map;
      } else {
         ManasSkillInstance researcher = (ManasSkillInstance)instance.get();
         CompoundTag tag = researcher.getOrCreateTag();
         return (Map)(!tag.m_128441_(tagID) ? map : EnchantmentHelper.m_44882_(tag.m_128437_(tagID, 10)));
      }
   }

   public static void setSelectedEnchantment(LivingEntity entity, Enchantment enchantment, int level, ManasSkill skill) {
      String tagID = "SelectedEnchantments";
      Optional<ManasSkillInstance> instance = SkillAPI.getSkillsFrom(entity).getSkill(skill);
      if (!instance.isEmpty()) {
         ManasSkillInstance researcher = (ManasSkillInstance)instance.get();
         CompoundTag tag = researcher.getOrCreateTag();
         String newID = "NewEnchantments";
         ListTag newEnchantments;
         if (!tag.m_128441_(tagID)) {
            newEnchantments = new ListTag();
         } else {
            newEnchantments = tag.m_128437_(tagID, 10);
         }

         ListTag enchantments;
         if (!tag.m_128441_(tagID)) {
            enchantments = new ListTag();
         } else {
            enchantments = tag.m_128437_(tagID, 10);
         }

         boolean flag = true;
         ResourceLocation id = EnchantmentHelper.m_182432_(enchantment);

         for(int i = 0; i < enchantments.size(); ++i) {
            CompoundTag compoundtag = enchantments.m_128728_(i);
            ResourceLocation location = EnchantmentHelper.m_182446_(compoundtag);
            if (location != null && location.equals(id)) {
               if (level == 0) {
                  enchantments.remove(compoundtag);
                  newEnchantments.remove(compoundtag);
               } else {
                  EnchantmentHelper.m_182440_(compoundtag, level);
                  newEnchantments.add(compoundtag);
               }

               flag = false;
               break;
            }
         }

         if (flag) {
            CompoundTag enchant = EnchantmentHelper.m_182443_(id, level);
            enchantments.add(enchant);
            newEnchantments.add(enchant);
         }

         tag.m_128365_(newID, newEnchantments);
         tag.m_128365_(tagID, enchantments);
         researcher.markDirty();
         SkillAPI.getSkillsFrom(entity).syncChanges();
      }
   }

   public static void addSelectedEnchantments(LivingEntity entity, Map<Enchantment, Integer> map, boolean clear, ManasSkill skill) {
      String tagID = "SelectedEnchantments";
      Optional<ManasSkillInstance> instance = SkillAPI.getSkillsFrom(entity).getSkill(skill);
      if (!instance.isEmpty()) {
         ManasSkillInstance researcher = (ManasSkillInstance)instance.get();
         CompoundTag tag = researcher.getOrCreateTag();
         if (clear && tag.m_128441_(tagID)) {
            tag.m_128473_(tagID);
            tag.m_128473_("NewEnchantments");
         }

         ListTag enchantments;
         if (!tag.m_128441_(tagID)) {
            enchantments = new ListTag();
         } else {
            enchantments = tag.m_128437_(tagID, 10);
         }

         Iterator var9 = map.entrySet().iterator();

         while(var9.hasNext()) {
            Entry<Enchantment, Integer> entry = (Entry)var9.next();
            boolean flag = true;
            ResourceLocation id = EnchantmentHelper.m_182432_((Enchantment)entry.getKey());

            for(int i = 0; i < enchantments.size(); ++i) {
               CompoundTag compoundtag = enchantments.m_128728_(i);
               ResourceLocation location = EnchantmentHelper.m_182446_(compoundtag);
               if (location != null && location.equals(id)) {
                  EnchantmentHelper.m_182440_(compoundtag, (Integer)entry.getValue());
                  flag = false;
                  break;
               }
            }

            if (flag) {
               enchantments.add(EnchantmentHelper.m_182443_(id, (Integer)entry.getValue()));
            }
         }

         tag.m_128365_(tagID, enchantments);
         researcher.markDirty();
         SkillAPI.getSkillsFrom(entity).syncChanges();
      }
   }
}
