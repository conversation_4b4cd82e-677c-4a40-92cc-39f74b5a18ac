package com.github.manasmods.tensura.ability.skill.unique;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.ability.skill.resist.AbnormalConditionNullification;
import com.github.manasmods.tensura.capability.skill.TensuraSkillCapability;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.skill.UniqueSkills;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import com.github.manasmods.tensura.util.damage.TensuraDamageSource;
import java.util.ArrayList;
import java.util.List;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraftforge.event.entity.living.LivingDamageEvent;
import org.jetbrains.annotations.NotNull;

public class SurvivorSkill extends Skill {
   public SurvivorSkill() {
      super(Skill.SkillType.UNIQUE);
   }

   public double getObtainingEpCost() {
      return 30000.0D;
   }

   public boolean canBeToggled(ManasSkillInstance instance, LivingEntity living) {
      return true;
   }

   public boolean canBeSlotted(ManasSkillInstance instance) {
      return instance.getMastery() < 0;
   }

   public boolean canTick(ManasSkillInstance instance, LivingEntity entity) {
      return instance.isToggled();
   }

   public void onTakenDamage(ManasSkillInstance instance, LivingDamageEvent e) {
      if (instance.isToggled()) {
         DamageSource source = e.getSource();
         if (!this.isNullificationBypass(source)) {
            if (DamageSourceHelper.isPoison(source)) {
               e.setAmount(0.0F);
            }

            LivingEntity entity = e.getEntity();
            if (DamageSourceHelper.isNaturalEffects(source) && !this.isResistanceBypass(source)) {
               if (e.getAmount() > entity.m_21223_() / 2.0F) {
                  e.setAmount(e.getAmount() / 2.0F);
               } else {
                  e.setAmount(0.0F);
               }
            }

         }
      }
   }

   public void onTick(ManasSkillInstance instance, LivingEntity entity) {
      if (entity instanceof Player) {
         CompoundTag tag = instance.getOrCreateTag();
         int time = tag.m_128451_("activatedTimes");
         if (time % 6 == 0) {
            this.addMasteryPoint(instance, entity);
         }

         tag.m_128405_("activatedTimes", time + 1);
         entity.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.INSTANT_REGENERATION.get(), 240, 0, false, false, false));
      }
   }

   public void onToggleOn(ManasSkillInstance instance, LivingEntity entity) {
      SkillHelper.removePredicateEffect(entity, (effect) -> {
         return effect == TensuraMobEffects.CHILL.get() || AbnormalConditionNullification.getAbnormalEffects().contains(effect);
      });
   }

   public void onToggleOff(ManasSkillInstance instance, LivingEntity entity) {
      MobEffectInstance effectInstance = entity.m_21124_((MobEffect)TensuraMobEffects.INSTANT_REGENERATION.get());
      if (effectInstance != null && effectInstance.m_19564_() < 1) {
         entity.m_21195_((MobEffect)TensuraMobEffects.INSTANT_REGENERATION.get());
      }

   }

   @NotNull
   public List<MobEffect> getImmuneEffects(ManasSkillInstance instance, LivingEntity entity) {
      List<MobEffect> list = new ArrayList();
      if (!instance.isToggled()) {
         return list;
      } else {
         list.addAll(AbnormalConditionNullification.getAbnormalEffects());
         list.add((MobEffect)TensuraMobEffects.CHILL.get());
         return list;
      }
   }

   public boolean isNullificationBypass(DamageSource damageSource) {
      Entity var3 = damageSource.m_7639_();
      if (var3 instanceof LivingEntity) {
         LivingEntity living = (LivingEntity)var3;
         if (TensuraSkillCapability.isSkillInSlot(living, (ManasSkill)UniqueSkills.ANTI_SKILL.get())) {
            return true;
         } else {
            boolean var10000;
            if (damageSource instanceof TensuraDamageSource) {
               TensuraDamageSource source = (TensuraDamageSource)damageSource;
               if (source.getIgnoreResistance() >= 2.0F) {
                  var10000 = true;
                  return var10000;
               }
            }

            var10000 = false;
            return var10000;
         }
      } else {
         return false;
      }
   }

   public boolean isResistanceBypass(DamageSource damageSource) {
      Entity var3 = damageSource.m_7639_();
      if (var3 instanceof LivingEntity) {
         LivingEntity living = (LivingEntity)var3;
         if (damageSource instanceof TensuraDamageSource) {
            TensuraDamageSource source = (TensuraDamageSource)damageSource;
            if (source.getIgnoreResistance() == 1.0F) {
               return true;
            }
         }

         return SkillUtils.reducingResistances(living);
      } else {
         return false;
      }
   }
}
