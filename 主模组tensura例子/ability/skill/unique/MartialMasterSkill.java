package com.github.manasmods.tensura.ability.skill.unique;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.event.UnlockSkillEvent;
import com.github.manasmods.manascore.attribute.ManasCoreAttributes;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.battlewill.Battewill;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.ability.skill.extra.ThoughtAccelerationSkill;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.registry.blocks.TensuraBlocks;
import com.github.manasmods.tensura.registry.skill.ExtraSkills;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import com.github.manasmods.tensura.util.damage.TensuraDamageSource;
import com.github.manasmods.tensura.world.TensuraGameRules;
import java.util.Iterator;
import java.util.List;
import java.util.UUID;
import net.minecraft.ChatFormatting;
import net.minecraft.core.BlockPos;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.Style;
import net.minecraft.network.protocol.game.ClientboundAnimatePacket;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.util.Mth;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.ClipContext.Fluid;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.phys.AABB;
import net.minecraft.world.phys.BlockHitResult;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.common.ForgeMod;
import net.minecraftforge.event.entity.ProjectileImpactEvent;
import net.minecraftforge.event.entity.living.LivingAttackEvent;
import net.minecraftforge.event.entity.living.LivingHurtEvent;

public class MartialMasterSkill extends Skill {
   protected static final UUID ACCELERATION = UUID.fromString("872fee6e-e8c7-4fda-b060-aad307ae4a4c");

   public MartialMasterSkill() {
      super(Skill.SkillType.UNIQUE);
   }

   public boolean canBeToggled(ManasSkillInstance instance, LivingEntity living) {
      return instance.getMastery() >= 0;
   }

   public double auraCost(LivingEntity entity, ManasSkillInstance instance) {
      return 100.0D;
   }

   public void onLearnSkill(ManasSkillInstance instance, LivingEntity entity, UnlockSkillEvent event) {
      if (instance.getMastery() >= 0 && !instance.isTemporarySkill()) {
         Skill eye = (Skill)ExtraSkills.HEAVENLY_EYE.get();
         Player player;
         if (entity instanceof Player) {
            player = (Player)entity;
            if (!player.m_7500_()) {
               double cost = eye.getObtainingEpCost() * (double)player.f_19853_.m_46469_().m_46215_(TensuraGameRules.MP_SKILL_COST) / 100.0D;
               TensuraPlayerCapability.getFrom(player).ifPresent((cap) -> {
                  cap.setBaseMagicule(cap.getBaseMagicule() + cost, player, false);
                  TensuraPlayerCapability.sync(player);
               });
            }
         }

         if (SkillUtils.learnSkill(entity, (ManasSkill)eye) && entity instanceof Player) {
            player = (Player)entity;
            player.m_5661_(Component.m_237110_("tensura.skill.acquire", new Object[]{eye.getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GOLD)), false);
         }

      }
   }

   public void onBeingDamaged(ManasSkillInstance instance, LivingAttackEvent event) {
      if (!event.isCanceled()) {
         LivingEntity entity = event.getEntity();
         if (this.isInSlot(entity)) {
            DamageSource damageSource = event.getSource();
            if (!damageSource.m_19378_() && !damageSource.m_19387_()) {
               if (damageSource.m_7640_() != null && damageSource.m_7640_() == damageSource.m_7639_()) {
                  if (!(entity.m_217043_().m_188501_() > 0.25F)) {
                     entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12318_, SoundSource.PLAYERS, 2.0F, 1.0F);
                     event.setCanceled(true);
                     if (SkillUtils.canNegateDodge(entity, damageSource)) {
                        event.setCanceled(false);
                     }

                  }
               }
            }
         }
      }
   }

   public void onProjectileHit(ManasSkillInstance instance, LivingEntity entity, ProjectileImpactEvent event) {
      if (this.isInSlot(entity)) {
         if (!SkillUtils.isProjectileAlwaysHit(event.getProjectile())) {
            entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12318_, SoundSource.PLAYERS, 2.0F, 1.0F);
            event.setCanceled(true);
         }
      }
   }

   public void onDamageEntity(ManasSkillInstance instance, LivingEntity attacker, LivingHurtEvent e) {
      if (instance.isToggled()) {
         DamageSource source = e.getSource();
         if (source instanceof TensuraDamageSource) {
            TensuraDamageSource damageSource = (TensuraDamageSource)source;
            if (damageSource.getSkill() != null && damageSource.getSkill().getSkill() instanceof Battewill) {
               e.setAmount(e.getAmount() * 3.0F);
               return;
            }
         }

         if (DamageSourceHelper.isPhysicalAttack(source) && source.m_7640_() == source.m_7639_()) {
            e.setAmount((float)((double)e.getAmount() + attacker.m_21133_(Attributes.f_22281_) * 2.0D));
         }

      }
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      if (entity.m_20096_() || entity.m_20072_()) {
         if (!SkillHelper.outOfAura(entity, instance)) {
            this.addMasteryPoint(instance, entity);
            Level level = entity.m_9236_();
            int range = instance.isMastered(entity) ? 20 : 15;
            BlockHitResult result = SkillHelper.getPlayerPOVHitResult(level, entity, Fluid.NONE, (double)range);
            BlockPos resultPos = result.m_82425_().m_121945_(result.m_82434_());
            Vec3 vec3 = SkillHelper.getFloorPos(resultPos);
            if (!level.m_8055_(resultPos).m_60767_().m_76336_()) {
               vec3 = SkillHelper.getFloorPos(resultPos.m_7494_());
            }

            if (level.m_8055_(resultPos).m_60713_((Block)TensuraBlocks.LABYRINTH_BARRIER_BLOCK.get())) {
               level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12317_, SoundSource.PLAYERS, 1.0F, 1.0F);
            } else if (!entity.m_9236_().m_6857_().m_61937_(new BlockPos(vec3.m_7096_(), vec3.m_7098_(), vec3.m_7094_()))) {
               if (entity instanceof Player) {
                  Player player = (Player)entity;
                  player.m_5661_(Component.m_237115_("tensura.skill.teleport.out_border").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), false);
               }

            } else {
               Vec3 source = entity.m_20182_().m_82520_(0.0D, (double)(entity.m_20206_() / 2.0F), 0.0D);
               Vec3 offSetToTarget = vec3.m_82546_(source);

               for(int particleIndex = 1; particleIndex < Mth.m_14107_(offSetToTarget.m_82553_()); ++particleIndex) {
                  Vec3 particlePos = source.m_82549_(offSetToTarget.m_82541_().m_82490_((double)particleIndex));
                  ((ServerLevel)level).m_8767_(ParticleTypes.f_123796_, particlePos.f_82479_, particlePos.f_82480_, particlePos.f_82481_, 1, 0.0D, 0.0D, 0.0D, 0.0D);
                  TensuraParticleHelper.addServerParticlesAroundPos(entity.m_217043_(), level, particlePos, ParticleTypes.f_123766_, 3.0D);
                  TensuraParticleHelper.addServerParticlesAroundPos(entity.m_217043_(), level, particlePos, ParticleTypes.f_123766_, 2.0D);
                  AABB aabb = (new AABB(new BlockPos(particlePos))).m_82400_(Math.max(entity.m_21133_((Attribute)ForgeMod.ATTACK_RANGE.get()), 2.0D));
                  List<LivingEntity> livingEntityList = level.m_6443_(LivingEntity.class, aabb, (targetx) -> {
                     return !targetx.m_7306_(entity) && !targetx.m_7307_(entity);
                  });
                  if (!livingEntityList.isEmpty()) {
                     float bonus = instance.isMastered(entity) ? 75.0F : 0.0F;
                     float amount = (float)(entity.m_21133_(Attributes.f_22281_) * entity.m_21133_((Attribute)ManasCoreAttributes.CRIT_MULTIPLIER.get()));
                     Iterator var16 = livingEntityList.iterator();

                     while(var16.hasNext()) {
                        LivingEntity target = (LivingEntity)var16.next();
                        if (target.m_6469_(this.sourceWithMP(DamageSource.m_19370_(entity), entity, instance), amount + bonus)) {
                           ItemStack stack = entity.m_21205_();
                           stack.m_41720_().m_7579_(stack, target, entity);
                           entity.m_9236_().m_6263_((Player)null, target.m_20185_(), target.m_20186_(), target.m_20189_(), SoundEvents.f_12313_, entity.m_5720_(), 1.0F, 1.0F);
                           if (level instanceof ServerLevel) {
                              ServerLevel serverLevel = (ServerLevel)level;
                              serverLevel.m_7726_().m_8394_(entity, new ClientboundAnimatePacket(entity, 4));
                           }
                        }
                     }
                  }
               }

               entity.m_183634_();
               entity.m_19877_();
               entity.m_20219_(vec3);
               entity.f_19812_ = true;
               entity.m_21011_(InteractionHand.MAIN_HAND, true);
               level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12317_, SoundSource.PLAYERS, 1.0F, 1.0F);
            }
         }
      }
   }

   public void onToggleOn(ManasSkillInstance instance, LivingEntity entity) {
      ThoughtAccelerationSkill.onToggle(instance, entity, ACCELERATION, true);
   }

   public void onToggleOff(ManasSkillInstance instance, LivingEntity entity) {
      ThoughtAccelerationSkill.onToggle(instance, entity, ACCELERATION, false);
   }
}
