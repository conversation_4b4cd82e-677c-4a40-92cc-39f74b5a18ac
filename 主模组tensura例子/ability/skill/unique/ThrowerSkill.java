package com.github.manasmods.tensura.ability.skill.unique;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.entity.magic.misc.ThrownItemProjectile;
import com.github.manasmods.tensura.entity.projectile.KunaiProjectile;
import com.github.manasmods.tensura.entity.projectile.SevererBladeProjectile;
import com.github.manasmods.tensura.entity.projectile.SpearProjectile;
import com.github.manasmods.tensura.entity.projectile.ThrownHolyWater;
import com.github.manasmods.tensura.entity.projectile.WebBulletProjectile;
import com.github.manasmods.tensura.item.custom.HolyWaterItem;
import com.github.manasmods.tensura.item.custom.KunaiItem;
import com.github.manasmods.tensura.item.custom.WebCartridgeItem;
import com.github.manasmods.tensura.item.templates.custom.SimpleSpearItem;
import com.github.manasmods.tensura.registry.items.TensuraToolItems;
import com.github.manasmods.tensura.registry.skill.ExtraSkills;
import com.github.manasmods.tensura.registry.skill.UniqueSkills;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import com.mojang.math.Vector3f;
import javax.annotation.Nullable;
import net.minecraft.core.BlockPos;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.entity.projectile.AbstractArrow;
import net.minecraft.world.entity.projectile.FireworkRocketEntity;
import net.minecraft.world.entity.projectile.Projectile;
import net.minecraft.world.entity.projectile.Snowball;
import net.minecraft.world.entity.projectile.ThrowableItemProjectile;
import net.minecraft.world.entity.projectile.ThrownEgg;
import net.minecraft.world.entity.projectile.ThrownEnderpearl;
import net.minecraft.world.entity.projectile.ThrownExperienceBottle;
import net.minecraft.world.entity.projectile.ThrownPotion;
import net.minecraft.world.entity.projectile.ThrownTrident;
import net.minecraft.world.entity.projectile.AbstractArrow.Pickup;
import net.minecraft.world.item.ArrowItem;
import net.minecraft.world.item.EggItem;
import net.minecraft.world.item.EnderpearlItem;
import net.minecraft.world.item.ExperienceBottleItem;
import net.minecraft.world.item.FireworkRocketItem;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.SnowballItem;
import net.minecraft.world.item.ThrowablePotionItem;
import net.minecraft.world.item.TridentItem;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.ClipContext.Fluid;
import net.minecraft.world.phys.Vec3;

public class ThrowerSkill extends Skill {
   public ThrowerSkill() {
      super(Skill.SkillType.UNIQUE);
   }

   public double getObtainingEpCost() {
      return 15000.0D;
   }

   public double learningCost() {
      return 40.0D;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      return 40.0D;
   }

   public boolean canBeToggled(ManasSkillInstance instance, LivingEntity living) {
      return true;
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      if (!SkillHelper.outOfMagicule(entity, instance)) {
         instance.addMasteryPoint(entity);
         Level level = entity.m_9236_();
         ItemStack mainHandStack = entity.m_21205_();
         LivingEntity target = SkillHelper.getTargetingEntity(entity, 4.0D, false);
         if (target != null) {
            double scale = 3.0D;
            if (SkillUtils.isSkillToggled(entity, (ManasSkill)ExtraSkills.GRAVITY_DOMINATION.get())) {
               scale += 2.0D;
            } else if (SkillUtils.isSkillToggled(entity, (ManasSkill)ExtraSkills.GRAVITY_MANIPULATION.get())) {
               ++scale;
            }

            entity.m_21011_(InteractionHand.MAIN_HAND, true);
            TensuraParticleHelper.addServerParticlesAroundSelf(target, ParticleTypes.f_123796_, 1.0D);
            entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12317_, SoundSource.PLAYERS, 1.0F, 1.0F);
            Vec3 vec3 = (new Vec3(target.m_20185_() - entity.m_20185_(), target.m_20186_() - entity.m_20186_() + 0.5D, target.m_20189_() - entity.m_20189_())).m_82490_(1.0D / (double)target.m_20270_(entity));
            target.m_20256_(vec3.m_82541_().m_82490_(scale));
            target.f_19812_ = true;
            target.f_19864_ = true;
         } else {
            Projectile projectile = getProjectile(level, entity, mainHandStack, instance);
            float speed = instance.isToggled() ? 1.0F : 3.0F;
            Vector3f vector3f = new Vector3f(entity.m_20252_(speed));
            if (projectile instanceof AbstractArrow) {
               label38: {
                  AbstractArrow arrow = (AbstractArrow)projectile;
                  if (entity instanceof Player) {
                     Player player = (Player)entity;
                     if (player.m_150110_().f_35937_) {
                        arrow.f_36705_ = Pickup.CREATIVE_ONLY;
                        break label38;
                     }
                  }

                  arrow.f_36705_ = Pickup.ALLOWED;
               }
            } else if (projectile instanceof ThrowableItemProjectile) {
               ThrowableItemProjectile itemProjectile = (ThrowableItemProjectile)projectile;
               itemProjectile.m_37446_(mainHandStack);
            }

            projectile.m_6686_((double)vector3f.m_122239_(), (double)vector3f.m_122260_(), (double)vector3f.m_122269_(), 2.0F, 0.0F);
            level.m_7967_(projectile);
            entity.m_21011_(entity.m_7655_(), true);
            if (entity instanceof Player) {
               Player player = (Player)entity;
               if (!player.m_150110_().f_35937_) {
                  mainHandStack.m_41774_(1);
               }
            }

         }
      }
   }

   public static Projectile getProjectile(Level level, LivingEntity entity, ItemStack stack, @Nullable ManasSkillInstance instance) {
      Item var5 = stack.m_41720_();
      if (var5 instanceof ArrowItem) {
         ArrowItem arrowItem = (ArrowItem)var5;
         AbstractArrow arrow = arrowItem.m_6394_(level, stack, entity);
         double baseDamage = arrow.m_36789_() + (double)(instance != null && instance.isMastered(entity) ? 50 : 30);
         if (SkillUtils.isSkillToggled(entity, (ManasSkill)ExtraSkills.GRAVITY_DOMINATION.get())) {
            baseDamage *= 3.0D;
         } else if (SkillUtils.isSkillToggled(entity, (ManasSkill)ExtraSkills.GRAVITY_MANIPULATION.get())) {
            baseDamage *= 2.0D;
         }

         arrow.m_36781_(baseDamage);
         return arrow;
      } else if (stack.m_41720_() instanceof ExperienceBottleItem) {
         return new ThrownExperienceBottle(level, entity);
      } else if (stack.m_41720_() instanceof EggItem) {
         return new ThrownEgg(level, entity);
      } else if (stack.m_41720_() instanceof EnderpearlItem) {
         return new ThrownEnderpearl(level, entity);
      } else if (stack.m_41720_() instanceof SnowballItem) {
         return new Snowball(level, entity);
      } else if (stack.m_41720_() instanceof FireworkRocketItem) {
         return new FireworkRocketEntity(level, stack, entity, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), true);
      } else if (stack.m_41720_() instanceof ThrowablePotionItem) {
         return new ThrownPotion(level, entity);
      } else if (stack.m_41720_() instanceof HolyWaterItem) {
         return new ThrownHolyWater(level, entity);
      } else if (stack.m_41720_() instanceof WebCartridgeItem) {
         return new WebBulletProjectile(level, entity, true, stack, stack);
      } else {
         float baseDamage;
         if (stack.m_41619_()) {
            baseDamage = instance != null && instance.isMastered(entity) ? 50.0F : 30.0F;
         } else {
            baseDamage = instance != null && instance.isMastered(entity) ? 100.0F : 50.0F;
         }

         int multiplier = 1;
         if (SkillUtils.isSkillToggled(entity, (ManasSkill)ExtraSkills.GRAVITY_DOMINATION.get())) {
            multiplier = 3;
         } else if (SkillUtils.isSkillToggled(entity, (ManasSkill)ExtraSkills.GRAVITY_MANIPULATION.get())) {
            multiplier = 2;
         }

         baseDamage *= (float)multiplier;
         if (stack.m_41720_().equals(TensuraToolItems.SEVERER_BLADE.get())) {
            SevererBladeProjectile blade = new SevererBladeProjectile(level, entity, true, stack);
            blade.setBaseDamage(baseDamage);
            return blade;
         } else if (stack.m_41720_() instanceof TridentItem) {
            ThrownTrident trident = new ThrownTrident(level, entity, stack);
            trident.m_36781_((double)baseDamage);
            return trident;
         } else if (stack.m_41720_() instanceof SimpleSpearItem) {
            SpearProjectile spear = new SpearProjectile(level, entity, stack, true);
            spear.m_36781_((double)baseDamage);
            if (instance != null && instance.isMastered(entity)) {
               spear.setLoyaltyLevel(Math.max(multiplier, spear.getLoyaltyLevel()));
            }

            return spear;
         } else if (stack.m_41720_() instanceof KunaiItem) {
            ItemStack kunai = stack.m_41777_();
            kunai.m_41764_(1);
            KunaiProjectile kunaiProjectile = new KunaiProjectile(level, entity, kunai, true);
            kunaiProjectile.m_36781_((double)baseDamage);
            if (instance != null && instance.isMastered(entity)) {
               kunaiProjectile.setLoyaltyLevel(Math.max(multiplier, kunaiProjectile.getLoyaltyLevel()));
            }

            return kunaiProjectile;
         } else {
            ThrownItemProjectile projectile = new ThrownItemProjectile(level, entity, stack, true, baseDamage);
            projectile.getSourceItem().m_41764_(1);
            projectile.setWeaponDamage(DamageSourceHelper.getMainWeaponDamage(entity, (Entity)null));
            if (instance != null && instance.isMastered(entity)) {
               projectile.setLoyaltyLevel(multiplier);
            }

            return projectile;
         }
      }
   }

   @Nullable
   public static LivingEntity getHomingEntity(LivingEntity player) {
      return SkillHelper.getTargetingEntity(player, 50.0D, false);
   }

   @Nullable
   public static BlockPos getHomingPos(LivingEntity player) {
      if (!SkillUtils.isSkillToggled(player, (ManasSkill)UniqueSkills.THROWER.get())) {
         return null;
      } else {
         LivingEntity target = getHomingEntity(player);
         return target != null ? target.m_20183_() : SkillHelper.getPlayerPOVHitResult(player.m_9236_(), player, Fluid.NONE, 50.0D).m_82425_();
      }
   }
}
