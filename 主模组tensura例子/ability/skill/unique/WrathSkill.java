package com.github.manasmods.tensura.ability.skill.unique;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.network.TensuraNetwork;
import com.github.manasmods.tensura.network.play2client.RequestFxSpawningPacket;
import com.github.manasmods.tensura.registry.attribute.TensuraAttributeRegistry;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.skill.ResistanceSkills;
import java.util.Iterator;
import java.util.List;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.MutableComponent;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import net.minecraftforge.event.entity.player.PlayerEvent.PlayerRespawnEvent;
import net.minecraftforge.network.PacketDistributor;

public class WrathSkill extends Skill {
   public WrathSkill() {
      super(Skill.SkillType.UNIQUE);
   }

   public double getObtainingEpCost() {
      return 100000.0D;
   }

   public int getMaxMastery() {
      return 1500;
   }

   public boolean canBeToggled(ManasSkillInstance instance, LivingEntity living) {
      return instance.isMastered(living);
   }

   public int modes() {
      return 2;
   }

   public int nextMode(LivingEntity entity, TensuraSkillInstance instance, boolean reverse) {
      return instance.getMode() == 1 ? 2 : 1;
   }

   public Component getModeName(int mode) {
      MutableComponent var10000;
      switch(mode) {
      case 1:
         var10000 = Component.m_237115_("tensura.skill.mode.wrath.breader");
         break;
      case 2:
         var10000 = Component.m_237115_("tensura.skill.mode.wrath.enrage");
         break;
      default:
         var10000 = Component.m_237119_();
      }

      return var10000;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      return instance.getMode() == 2 ? 100.0D : 0.0D;
   }

   public boolean canTick(ManasSkillInstance instance, LivingEntity entity) {
      return !instance.isMastered(entity) ? false : instance.isToggled();
   }

   public void onTick(ManasSkillInstance instance, LivingEntity entity) {
      if (entity instanceof Player) {
         Player player = (Player)entity;
         this.breederReactor(player);
         TensuraNetwork.INSTANCE.send(PacketDistributor.TRACKING_ENTITY_AND_SELF.with(() -> {
            return entity;
         }), new RequestFxSpawningPacket(new ResourceLocation("tensura:wrath_boost"), entity.m_19879_(), 0.0D, 1.0D, 0.0D, true));
      }
   }

   public boolean onHeld(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      switch(instance.getMode()) {
      case 1:
         if (instance.isToggled() && instance.isMastered(entity)) {
            return false;
         } else {
            if (entity instanceof Player) {
               Player player = (Player)entity;
               if (heldTicks % 100 == 0 && heldTicks > 0) {
                  this.breederReactor(player);
               }

               if (heldTicks % 100 == 0 && heldTicks > 0) {
                  this.addMasteryPoint(instance, entity);
               }

               if (heldTicks % 10 == 0) {
                  TensuraNetwork.INSTANCE.send(PacketDistributor.TRACKING_ENTITY_AND_SELF.with(() -> {
                     return entity;
                  }), new RequestFxSpawningPacket(new ResourceLocation("tensura:wrath_boost"), entity.m_19879_(), 0.0D, 1.0D, 0.0D, true));
               }

               player.m_6330_(SoundEvents.f_215771_, SoundSource.PLAYERS, 2.0F, 1.0F);
               return true;
            }

            return false;
         }
      case 2:
         if (heldTicks % 20 == 0 && SkillHelper.outOfMagicule(entity, instance)) {
            return false;
         } else {
            Level level = entity.m_9236_();
            level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_215777_, SoundSource.PLAYERS, 10.0F, 1.0F);
            TensuraParticleHelper.addServerParticlesAroundSelf(entity, ParticleTypes.f_123792_, 2.0D);
            List<LivingEntity> list = level.m_6443_(LivingEntity.class, entity.m_20191_().m_82400_(7.0D), (living) -> {
               return !living.m_7306_(entity) && living.m_6084_() && !living.m_7307_(entity);
            });
            if (!list.isEmpty()) {
               if (heldTicks % 100 == 0 && heldTicks > 0) {
                  this.addMasteryPoint(instance, entity);
               }

               Iterator var6 = list.iterator();

               while(true) {
                  LivingEntity target;
                  do {
                     do {
                        if (!var6.hasNext()) {
                           return true;
                        }

                        target = (LivingEntity)var6.next();
                     } while(SkillUtils.isSkillToggled(target, (ManasSkill)ResistanceSkills.SPIRITUAL_ATTACK_NULLIFICATION.get()));
                  } while(SkillUtils.isSkillToggled(target, (ManasSkill)ResistanceSkills.SPIRITUAL_ATTACK_RESISTANCE.get()) && entity.m_217043_().m_188503_(3) != 1);

                  int effectLevel = 0;
                  MobEffectInstance rampage = target.m_21124_((MobEffect)TensuraMobEffects.RAMPAGE.get());
                  int duration;
                  if (rampage != null && heldTicks > 0) {
                     duration = rampage.m_19557_() + 2;
                     effectLevel = (duration - 400) / 200;
                  } else {
                     duration = 400;
                  }

                  target.m_147207_(new MobEffectInstance((MobEffect)TensuraMobEffects.RAMPAGE.get(), duration, effectLevel, false, false, false), entity);
                  if (heldTicks % 10 == 0) {
                     TensuraParticleHelper.addServerParticlesAroundSelf(target, ParticleTypes.f_123792_, 2.0D);
                  }
               }
            }

            return true;
         }
      default:
         return false;
      }
   }

   private void breederReactor(Player player) {
      TensuraPlayerCapability.getFrom(player).ifPresent((cap) -> {
         double maxMP = player.m_21133_((Attribute)TensuraAttributeRegistry.MAX_MAGICULE.get());
         double mpGain = maxMP * 0.02D;
         cap.setMagicule(cap.getMagicule() + mpGain);
         double rampageChance = cap.getMagicule() > maxMP ? 0.05D : 0.01D;
         if ((double)player.m_217043_().m_188501_() < rampageChance) {
            MobEffectInstance effectInstance = player.m_21124_((MobEffect)TensuraMobEffects.RAMPAGE.get());
            int level = 0;
            if (effectInstance != null) {
               level = effectInstance.m_19564_() + 1;
            }

            SkillHelper.addEffectWithSource(player, player, (MobEffect)TensuraMobEffects.RAMPAGE.get(), 600, level, false, false, false, true);
         }

      });
      TensuraPlayerCapability.sync(player);
   }

   public void onRespawn(ManasSkillInstance instance, PlayerRespawnEvent event) {
      if (!event.isEndConquered()) {
         if (instance.isToggled()) {
            instance.setToggled(false);
         }

      }
   }
}
