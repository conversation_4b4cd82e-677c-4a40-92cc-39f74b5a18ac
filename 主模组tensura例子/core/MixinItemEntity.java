package com.github.manasmods.tensura.core;

import com.github.manasmods.tensura.registry.blocks.TensuraBlocks;
import net.minecraft.core.BlockPos;
import net.minecraft.world.entity.item.ItemEntity;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Items;
import net.minecraft.world.level.ItemLike;
import net.minecraft.world.level.Level;
import net.minecraft.world.phys.Vec3;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;

@Mixin({ItemEntity.class})
public abstract class MixinItemEntity {
   @Inject(
      method = {"tick"},
      at = {@At("HEAD")}
   )
   private void replaceTorchWithUnlitTorch(CallbackInfo ci) {
      ItemEntity entity = (ItemEntity)this;
      if (entity.m_20069_()) {
         if (entity.m_32055_().m_150930_(Items.f_42000_)) {
            BlockPos blockPos = entity.m_20183_();
            Vec3 vec3 = entity.m_20184_();
            Level level = entity.m_9236_();
            ItemStack torch = entity.m_32055_();
            if (!level.m_5776_()) {
               entity.m_146870_();
               ItemStack unlitTorch = new ItemStack((ItemLike)TensuraBlocks.Items.UNLIT_TORCH.get());
               unlitTorch.m_41764_(torch.m_41613_());
               ItemEntity unlitEntity = new ItemEntity(level, (double)blockPos.m_123341_(), (double)blockPos.m_123342_(), (double)blockPos.m_123343_(), unlitTorch);
               level.m_7967_(unlitEntity);
               unlitEntity.m_20256_(vec3);
            }
         }
      }
   }
}
