package com.github.manasmods.tensura.core.client;

import com.github.manasmods.tensura.network.play2server.skill.RequestFalsifierItemPacket;
import net.minecraft.client.renderer.entity.layers.CustomHeadLayer;
import net.minecraft.world.entity.EquipmentSlot;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.item.ItemStack;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Redirect;

@Mixin({CustomHeadLayer.class})
public class MixinCustomHeadLayer {
   @Redirect(
      method = {"render*"},
      at = @At(
   value = "INVOKE",
   target = "Lnet/minecraft/world/entity/LivingEntity;getItemBySlot(Lnet/minecraft/world/entity/EquipmentSlot;)Lnet/minecraft/world/item/ItemStack;"
)
   )
   public ItemStack renderMainArmWithItem(LivingEntity entity, EquipmentSlot slot) {
      return RequestFalsifierItemPacket.getFalsifierItem(entity, entity.m_6844_(slot), slot);
   }
}
