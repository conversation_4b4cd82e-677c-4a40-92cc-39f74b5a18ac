package com.github.manasmods.tensura.core.client;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.tensura.ability.SkillClientUtils;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.capability.effects.TensuraEffectsCapability;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.skill.ResistanceSkills;
import com.mojang.blaze3d.vertex.PoseStack;
import net.minecraft.client.Camera;
import net.minecraft.client.Minecraft;
import net.minecraft.client.OptionInstance;
import net.minecraft.client.renderer.GameRenderer;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.Unique;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfoReturnable;

@Mixin(
   value = {GameRenderer.class},
   priority = 800
)
public class MixinGameRenderer {
   @Unique
   private Double tensuraMod$defaultSensitivity;
   @Unique
   private Minecraft tensuraMod$minecraft = Minecraft.m_91087_();

   @Inject(
      method = {"bobHurt"},
      at = {@At("HEAD")},
      cancellable = true
   )
   private void getOverlayCoords(PoseStack pMatrixStack, float pPartialTicks, CallbackInfo ci) {
      Entity var5 = this.tensuraMod$minecraft.m_91288_();
      if (var5 instanceof LivingEntity) {
         LivingEntity entity = (LivingEntity)var5;
         if (entity.m_21224_()) {
            return;
         }

         if ((SkillUtils.hasPainNull(entity) || SkillUtils.isSkillToggled(entity, (ManasSkill)ResistanceSkills.PAIN_RESISTANCE.get())) && !TensuraEffectsCapability.hasSyncedEffect(entity, (MobEffect)TensuraMobEffects.LUST_EMBRACEMENT.get())) {
            ci.cancel();
         }
      }

   }

   @Inject(
      at = {@At(
   value = "RETURN",
   ordinal = 1
)},
      method = {"getFov(Lnet/minecraft/client/Camera;FZ)D"},
      cancellable = true
   )
   private void onGetFov(Camera camera, float tickDelta, boolean changingFov, CallbackInfoReturnable<Double> cir) {
      Entity var6 = camera.m_90592_();
      if (var6 instanceof LivingEntity) {
         LivingEntity living = (LivingEntity)var6;
         cir.setReturnValue(this.tensuraMod$zoom((Double)cir.getReturnValue(), living));
      }

   }

   @Unique
   public double tensuraMod$zoom(double fov, LivingEntity living) {
      OptionInstance<Double> mouseSensitivity = this.tensuraMod$minecraft.f_91066_.m_231964_();
      double zoom = SkillClientUtils.zoomValue(living);
      if (zoom == 0.0D) {
         if (this.tensuraMod$defaultSensitivity != null) {
            mouseSensitivity.m_231514_(this.tensuraMod$defaultSensitivity);
            this.tensuraMod$defaultSensitivity = null;
         }

         return fov;
      } else {
         if (this.tensuraMod$defaultSensitivity == null) {
            this.tensuraMod$defaultSensitivity = (Double)mouseSensitivity.m_231551_();
         }

         mouseSensitivity.m_231514_(this.tensuraMod$defaultSensitivity * (1.0D / zoom));
         return fov / zoom;
      }
   }
}
