package com.github.manasmods.tensura.core.client;

import com.github.manasmods.tensura.ability.SkillClientUtils;
import net.minecraft.client.Minecraft;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.player.Player;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfoReturnable;

@Mixin({Minecraft.class})
public abstract class MixinMinecraft {
   @Inject(
      method = {"shouldEntityAppearGlowing"},
      at = {@At("RETURN")},
      cancellable = true
   )
   public void setGlowing(Entity pEntity, CallbackInfoReturnable<Boolean> cir) {
      if (!(Boolean)cir.getReturnValue()) {
         Player player = ((Minecraft)this).f_91074_;
         if (player != null && pEntity != player && SkillClientUtils.getGlowColor(player, pEntity) != 0) {
            cir.setReturnValue(true);
         }

      }
   }
}
