package com.github.manasmods.tensura.core.client;

import com.github.manasmods.tensura.ability.SkillClientUtils;
import com.mojang.blaze3d.vertex.PoseStack;
import net.minecraft.client.Minecraft;
import net.minecraft.client.renderer.LevelRenderer;
import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.client.renderer.OutlineBufferSource;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.player.Player;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.ModifyArg;

@Mixin(
   value = {LevelRenderer.class},
   priority = 800
)
public abstract class MixinLevelRenderer {
   @ModifyArg(
      method = {"renderLevel"},
      at = @At(
   value = "INVOKE",
   target = "Lnet/minecraft/client/renderer/LevelRenderer;renderEntity(Lnet/minecraft/world/entity/Entity;DDDFLcom/mojang/blaze3d/vertex/PoseStack;Lnet/minecraft/client/renderer/MultiBufferSource;)V"
),
      index = 6
   )
   public MultiBufferSource setGlowing(Entity pEntity, double pCamX, double pCamY, double pCamZ, float pPartialTick, PoseStack pPoseStack, MultiBufferSource pBufferSource) {
      Player player = Minecraft.m_91087_().f_91074_;
      if (player != null) {
         OutlineBufferSource outlinebuffersource = ((LevelRenderer)this).f_109464_.m_110109_();
         int i = SkillClientUtils.getGlowColor(player, pEntity);
         if (i != 0) {
            int k = i >> 16 & 255;
            int l = i >> 8 & 255;
            int i1 = i & 255;
            outlinebuffersource.m_109929_(k, l, i1, 0);
            return outlinebuffersource;
         }
      }

      return pBufferSource;
   }
}
