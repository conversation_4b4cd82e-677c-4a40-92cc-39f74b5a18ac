package com.github.manasmods.tensura.core;

import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import net.minecraft.util.Mth;
import net.minecraft.util.RandomSource;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.projectile.Projectile;
import net.minecraft.world.phys.Vec3;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.Unique;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;

@Mixin({Projectile.class})
public abstract class MixinProjectile {
   @Inject(
      method = {"shootFromRotation"},
      at = {@At("HEAD")},
      cancellable = true
   )
   public void shootFromRotation(Entity pShooter, float pX, float pY, float pZ, float pVelocity, float pInaccuracy, CallbackInfo ci) {
      Projectile projectile = (Projectile)this;
      if (pShooter instanceof LivingEntity) {
         LivingEntity living = (LivingEntity)pShooter;
         if (!living.m_6144_() && SkillUtils.hasWarpShot(living)) {
            LivingEntity pEntity = (LivingEntity)SkillHelper.getTargetingEntity(LivingEntity.class, living, 50.0D, 0.1D, false, false);
            if (pEntity != null && pEntity.m_6084_() && !SkillHelper.outOfMagicule(living, 30.0D)) {
               projectile.m_146884_(pEntity.m_146892_().m_82546_(pEntity.m_20252_(1.0F).m_82490_(1.5D)));
               Vec3 towardEntity = (new Vec3(pEntity.m_20185_() - projectile.m_20185_(), pEntity.m_20188_() - projectile.m_20186_(), pEntity.m_20189_() - projectile.m_20189_())).m_82490_(0.10000000149011612D);
               this.tensuraMod$shoot(towardEntity.m_7096_(), towardEntity.m_7098_(), towardEntity.m_7094_(), pVelocity, 0.0F);
               ci.cancel();
            }
         }
      }

   }

   @Inject(
      method = {"shoot"},
      at = {@At("HEAD")},
      cancellable = true
   )
   public void shoot(double pX, double pY, double pZ, float pVelocity, float pInaccuracy, CallbackInfo ci) {
      Projectile projectile = (Projectile)this;
      Entity var12 = projectile.m_37282_();
      if (var12 instanceof LivingEntity) {
         LivingEntity living = (LivingEntity)var12;
         if (!living.m_6144_() && SkillUtils.hasWarpShot(living)) {
            LivingEntity pEntity = (LivingEntity)SkillHelper.getTargetingEntity(LivingEntity.class, living, 50.0D, 0.1D, false, false);
            if (pEntity != null && pEntity.m_6084_() && !SkillHelper.outOfMagicule(living, 30.0D)) {
               projectile.m_146884_(pEntity.m_146892_().m_82546_(pEntity.m_20252_(1.0F).m_82490_(1.5D)));
               Vec3 towardEntity = (new Vec3(pEntity.m_20185_() - projectile.m_20185_(), pEntity.m_20188_() - projectile.m_20186_(), pEntity.m_20189_() - projectile.m_20189_())).m_82490_(0.10000000149011612D);
               this.tensuraMod$shoot(towardEntity.m_7096_(), towardEntity.m_7098_(), towardEntity.m_7094_(), pVelocity, 0.0F);
               ci.cancel();
            }
         }
      }

   }

   @Unique
   public void tensuraMod$shoot(double pX, double pY, double pZ, float pVelocity, float pInaccuracy) {
      Projectile projectile = (Projectile)this;
      RandomSource random = RandomSource.m_216327_();
      Vec3 vec3 = (new Vec3(pX, pY, pZ)).m_82541_().m_82520_(random.m_216328_(0.0D, 0.0172275D * (double)pInaccuracy), random.m_216328_(0.0D, 0.0172275D * (double)pInaccuracy), random.m_216328_(0.0D, 0.0172275D * (double)pInaccuracy)).m_82490_((double)pVelocity);
      projectile.m_20256_(vec3);
      double d0 = vec3.m_165924_();
      projectile.m_146922_((float)(Mth.m_14136_(vec3.f_82479_, vec3.f_82481_) * 57.2957763671875D));
      projectile.m_146926_((float)(Mth.m_14136_(vec3.f_82480_, d0) * 57.2957763671875D));
      projectile.f_19859_ = projectile.m_146908_();
      projectile.f_19860_ = projectile.m_146909_();
   }
}
