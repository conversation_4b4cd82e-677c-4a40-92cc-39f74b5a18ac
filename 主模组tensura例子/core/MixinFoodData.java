package com.github.manasmods.tensura.core;

import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.race.Race;
import com.github.manasmods.tensura.race.vampire.VampireRace;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.food.FoodData;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Redirect;

@Mixin(
   value = {FoodData.class},
   priority = 2000
)
public abstract class MixinFoodData {
   @Redirect(
      method = {"tick(Lnet/minecraft/world/entity/player/Player;)V"},
      at = @At(
   value = "INVOKE",
   target = "Lnet/minecraft/world/entity/player/Player;heal(F)V"
)
   )
   public void healTick(Player player, float heal) {
      Race race = TensuraPlayerCapability.getRace(player);
      if (!(race instanceof VampireRace)) {
         player.m_5634_(heal);
      }

   }
}
