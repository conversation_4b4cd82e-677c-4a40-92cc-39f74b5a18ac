package com.github.manasmods.tensura.block;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.block.entity.CharybdisCoreBlockEntity;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.config.TensuraConfig;
import com.github.manasmods.tensura.entity.projectile.PrimedCharybdisCoreEntity;
import com.github.manasmods.tensura.registry.particle.TensuraParticles;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.function.ToIntFunction;
import javax.annotation.Nullable;
import net.minecraft.ChatFormatting;
import net.minecraft.core.BlockPos;
import net.minecraft.core.Direction;
import net.minecraft.core.particles.ParticleOptions;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.Style;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.InteractionResult;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.BlockItem;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.TooltipFlag;
import net.minecraft.world.item.context.BlockPlaceContext;
import net.minecraft.world.level.BlockGetter;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.BaseEntityBlock;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.level.block.Mirror;
import net.minecraft.world.level.block.RenderShape;
import net.minecraft.world.level.block.Rotation;
import net.minecraft.world.level.block.SoundType;
import net.minecraft.world.level.block.entity.BlockEntity;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.block.state.BlockBehaviour.Properties;
import net.minecraft.world.level.block.state.StateDefinition.Builder;
import net.minecraft.world.level.block.state.properties.BlockStateProperties;
import net.minecraft.world.level.block.state.properties.DirectionProperty;
import net.minecraft.world.level.block.state.properties.EnumProperty;
import net.minecraft.world.level.block.state.properties.Property;
import net.minecraft.world.level.block.state.properties.SculkSensorPhase;
import net.minecraft.world.level.gameevent.GameEventListener;
import net.minecraft.world.level.material.Material;
import net.minecraft.world.level.material.MaterialColor;
import net.minecraft.world.level.material.PushReaction;
import net.minecraft.world.phys.BlockHitResult;
import net.minecraft.world.phys.Vec3;
import net.minecraft.world.phys.shapes.CollisionContext;
import net.minecraft.world.phys.shapes.VoxelShape;

public class CharybdisCoreBlock extends BaseEntityBlock {
   public static final DirectionProperty FACING;
   public static final EnumProperty<SculkSensorPhase> MODE;

   public CharybdisCoreBlock() {
      super(Properties.m_60944_(Material.f_76286_, MaterialColor.f_76364_).m_60978_(0.5F).m_60953_(getLightEmission()).m_60918_(SoundType.f_56713_).m_60955_());
      this.m_49959_((BlockState)((BlockState)((BlockState)this.f_49792_.m_61090_()).m_61124_(FACING, Direction.UP)).m_61124_(MODE, SculkSensorPhase.INACTIVE));
   }

   public void m_5871_(ItemStack pStack, @Nullable BlockGetter pLevel, List<Component> pTooltip, TooltipFlag pFlag) {
      super.m_5871_(pStack, pLevel, pTooltip, pFlag);
      CompoundTag stateTag = pStack.m_41737_("BlockStateTag");
      if (stateTag != null) {
         if (stateTag.m_128441_("sculk_sensor_phase")) {
            if (stateTag.m_128461_("sculk_sensor_phase").equals("inactive")) {
               pTooltip.add(Component.m_237115_("tooltip.tensura.charybdis_core.inactive").m_130940_(ChatFormatting.RED));
               CompoundTag compoundtag = BlockItem.m_186336_(pStack);
               if (compoundtag == null) {
                  return;
               }

               double EP = compoundtag.m_128459_("EP");
               if (EP > 0.0D) {
                  pTooltip.add(Component.m_237115_("tensura.attribute.existence_points.shortened_name").m_130946_(": " + EP).m_130940_(ChatFormatting.GOLD));
               }
            } else if (stateTag.m_128461_("sculk_sensor_phase").equals("active")) {
               pTooltip.add(Component.m_237115_("tooltip.tensura.charybdis_core.active").m_130940_(ChatFormatting.GOLD));
            } else if (stateTag.m_128461_("sculk_sensor_phase").equals("cooldown")) {
               pTooltip.add(Component.m_237115_("tooltip.tensura.charybdis_core.inert").m_130940_(ChatFormatting.DARK_GREEN));
            }

         }
      }
   }

   public RenderShape m_7514_(BlockState pState) {
      return RenderShape.MODEL;
   }

   public static ToIntFunction<BlockState> getLightEmission() {
      return (state) -> {
         byte var10000;
         switch((SculkSensorPhase)state.m_61143_(MODE)) {
         case COOLDOWN:
            var10000 = 8;
            break;
         case INACTIVE:
            var10000 = 3;
            break;
         default:
            var10000 = 12;
         }

         return var10000;
      };
   }

   public PushReaction m_5537_(BlockState pState) {
      return PushReaction.BLOCK;
   }

   protected void m_7926_(Builder<Block, BlockState> pBuilder) {
      pBuilder.m_61104_(new Property[]{FACING}).m_61104_(new Property[]{MODE});
   }

   public BlockState m_6843_(BlockState pState, Rotation pRot) {
      return (BlockState)pState.m_61124_(FACING, pRot.m_55954_((Direction)pState.m_61143_(FACING)));
   }

   public BlockState m_6943_(BlockState pState, Mirror pMirror) {
      return pState.m_60717_(pMirror.m_54846_((Direction)pState.m_61143_(FACING)));
   }

   public BlockState m_5573_(BlockPlaceContext pContext) {
      Direction direction = pContext.m_43719_();
      return (BlockState)super.m_5573_(pContext).m_61124_(FACING, direction);
   }

   public VoxelShape m_5940_(BlockState pState, BlockGetter pLevel, BlockPos pPos, CollisionContext pContext) {
      VoxelShape var10000;
      switch((Direction)pState.m_61143_(FACING)) {
      case NORTH:
         var10000 = Block.m_49796_(3.0D, 3.5D, 7.2D, 13.0D, 12.5D, 16.2D);
         break;
      case SOUTH:
         var10000 = Block.m_49796_(3.0D, 3.5D, 0.0D, 13.0D, 12.5D, 8.8D);
         break;
      case WEST:
         var10000 = Block.m_49796_(7.2D, 3.5D, 3.0D, 16.2D, 12.5D, 13.0D);
         break;
      case EAST:
         var10000 = Block.m_49796_(0.2D, 3.5D, 3.0D, 8.8D, 12.5D, 13.0D);
         break;
      case DOWN:
         var10000 = Block.m_49796_(3.0D, 7.0D, 3.0D, 12.0D, 16.0D, 13.0D);
         break;
      default:
         var10000 = Block.m_49796_(3.0D, 0.0D, 3.0D, 12.0D, 9.0D, 13.0D);
      }

      return var10000;
   }

   @Nullable
   public BlockEntity m_142194_(BlockPos pPos, BlockState pState) {
      return new CharybdisCoreBlockEntity(pPos, pState);
   }

   @Nullable
   public <T extends BlockEntity> GameEventListener m_214009_(ServerLevel pLevel, T pBlockEntity) {
      CharybdisCoreBlockEntity var10000;
      if (pBlockEntity instanceof CharybdisCoreBlockEntity) {
         CharybdisCoreBlockEntity blockEntity = (CharybdisCoreBlockEntity)pBlockEntity;
         var10000 = blockEntity;
      } else {
         var10000 = null;
      }

      return var10000;
   }

   public InteractionResult m_6227_(BlockState pState, Level pLevel, BlockPos pPos, Player pPlayer, InteractionHand pHand, BlockHitResult pHit) {
      if (pLevel.m_5776_()) {
         return InteractionResult.PASS;
      } else if (pPlayer.m_6047_() && pPlayer.m_21120_(pHand).m_41619_() && pLevel instanceof ServerLevel) {
         ServerLevel level = (ServerLevel)pLevel;
         List<ItemStack> drops = Block.m_49869_(pState, level, pPos, pLevel.m_7702_(pPos));
         if (drops.size() == 1) {
            pPlayer.m_21008_(pHand, (ItemStack)drops.get(0));
         } else {
            Iterator var14 = drops.iterator();

            while(var14.hasNext()) {
               ItemStack stack = (ItemStack)var14.next();
               pPlayer.m_36356_(stack);
            }
         }

         pLevel.m_46961_(pPos, false);
         return InteractionResult.m_19078_(pLevel.f_46443_);
      } else {
         switch((SculkSensorPhase)pState.m_61143_(MODE)) {
         case ACTIVE:
            PrimedCharybdisCoreEntity core = new PrimedCharybdisCoreEntity(pLevel, (double)pPos.m_123341_() + 0.5D, (double)pPos.m_123342_(), (double)pPos.m_123343_() + 0.5D);
            pLevel.m_7967_(core);
            TensuraParticleHelper.addServerParticlesAroundSelf(core, ParticleTypes.f_235898_);
            TensuraParticleHelper.addServerParticlesAroundSelf(core, (ParticleOptions)TensuraParticles.SOUL.get());
            pLevel.m_6263_((Player)null, core.m_20185_(), core.m_20186_(), core.m_20189_(), SoundEvents.f_12563_, SoundSource.BLOCKS, 1.0F, 1.0F);
            pLevel.m_7471_(pPos, false);
            return InteractionResult.m_19078_(pLevel.f_46443_);
         case COOLDOWN:
            List<ManasSkill> list = ((List)TensuraConfig.INSTANCE.blocksConfig.inertCoreSkills.get()).stream().map((skillx) -> {
               return (ManasSkill)SkillAPI.getSkillRegistry().getValue(new ResourceLocation(skillx));
            }).filter(Objects::nonNull).toList();
            if (!list.isEmpty()) {
               Iterator var8 = list.iterator();

               while(var8.hasNext()) {
                  ManasSkill skill = (ManasSkill)var8.next();
                  if (SkillUtils.learnSkill(pPlayer, (ManasSkill)skill)) {
                     pPlayer.m_5661_(Component.m_237110_("tensura.skill.acquire", new Object[]{skill.getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GOLD)), false);
                  }
               }
            }

            pLevel.m_46961_(pPos, false);
            pLevel.m_6263_((Player)null, (double)pPos.m_123341_(), (double)pPos.m_123342_(), (double)pPos.m_123343_(), SoundEvents.f_11887_, SoundSource.BLOCKS, 1.0F, 1.0F);
            TensuraParticleHelper.addServerParticlesAroundPos(pLevel.m_213780_(), pLevel, Vec3.m_82512_(pPos), ParticleTypes.f_235898_, 1.0D);
            TensuraParticleHelper.addServerParticlesAroundPos(pLevel.m_213780_(), pLevel, Vec3.m_82512_(pPos), ParticleTypes.f_123746_, 1.0D);
            TensuraParticleHelper.addServerParticlesAroundPos(pLevel.m_213780_(), pLevel, Vec3.m_82512_(pPos), (ParticleOptions)TensuraParticles.SOUL.get(), 1.0D);
            return InteractionResult.m_19078_(pLevel.f_46443_);
         default:
            return InteractionResult.PASS;
         }
      }
   }

   static {
      FACING = BlockStateProperties.f_61372_;
      MODE = BlockStateProperties.f_155999_;
   }
}
