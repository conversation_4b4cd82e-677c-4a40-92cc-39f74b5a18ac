package com.github.manasmods.tensura.block;

import com.github.manasmods.tensura.registry.dimensions.HellTeleporter;
import com.github.manasmods.tensura.registry.dimensions.TensuraDimensions;
import net.minecraft.ChatFormatting;
import net.minecraft.core.BlockPos;
import net.minecraft.core.Direction;
import net.minecraft.network.chat.Component;
import net.minecraft.resources.ResourceKey;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.Mob;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.context.BlockPlaceContext;
import net.minecraft.world.level.BlockGetter;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.LevelReader;
import net.minecraft.world.level.block.SoundType;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.material.Fluid;
import net.minecraft.world.level.material.MaterialColor;
import net.minecraft.world.level.material.Material.Builder;
import net.minecraft.world.level.pathfinder.BlockPathTypes;
import net.minecraft.world.phys.shapes.BooleanOp;
import net.minecraft.world.phys.shapes.Shapes;
import org.jetbrains.annotations.Nullable;

public class HellPortal extends SimpleBlock {
   public HellPortal() {
      super((new Builder(MaterialColor.f_76398_)).m_76354_().m_76353_().m_76359_(), (properties) -> {
         return properties.m_60971_((blockState, blockGetter, blockPos) -> {
            return false;
         }).m_222994_().m_60955_().m_60910_().m_60918_(SoundType.f_56744_).m_60913_(-1.0F, 3600000.0F);
      });
   }

   public boolean m_180643_(BlockState pState, BlockGetter pLevel, BlockPos pPos) {
      return false;
   }

   public boolean m_5946_(BlockState pState, Fluid pFluid) {
      return false;
   }

   public boolean m_6864_(BlockState pState, BlockPlaceContext pUseContext) {
      return false;
   }

   public boolean m_7898_(BlockState pState, LevelReader pLevel, BlockPos pPos) {
      return true;
   }

   @Nullable
   public BlockPathTypes getBlockPathType(BlockState state, BlockGetter level, BlockPos pos, @Nullable Mob mob) {
      return BlockPathTypes.BLOCKED;
   }

   public void m_7892_(BlockState pState, Level pLevel, BlockPos pPos, Entity pEntity) {
      if (pLevel instanceof ServerLevel) {
         ServerLevel serverLevel = (ServerLevel)pLevel;
         if (!pEntity.m_20159_() && !pEntity.m_20092_() && !pEntity.m_20160_() && pEntity.m_6072_() && Shapes.m_83157_(Shapes.m_83064_(pEntity.m_20191_().m_82386_((double)(-pPos.m_123341_()), (double)(-pPos.m_123342_()), (double)(-pPos.m_123343_()))), pState.m_60808_(pLevel, pPos), BooleanOp.f_82689_)) {
            ResourceKey<Level> key = pLevel.m_46472_() == TensuraDimensions.HELL ? Level.f_46428_ : TensuraDimensions.HELL;
            if (key == pLevel.m_46472_()) {
               return;
            }

            ServerLevel level = serverLevel.m_7654_().m_129880_(key);
            if (level == null) {
               this.failedTeleportation(pEntity, "Hell");
               return;
            }

            pEntity.changeDimension(level, new HellTeleporter(false));
            pEntity.f_19839_ = 300;
         }
      }

      super.m_7892_(pState, pLevel, pPos, pEntity);
   }

   private void failedTeleportation(Entity entity, String argument) {
      if (entity instanceof Player) {
         Player player = (Player)entity;
         player.m_5661_(Component.m_237110_("tensura.dimension.teleportation_fail", new Object[]{argument}).m_130940_(ChatFormatting.RED), false);
      }

      entity.f_19839_ = 100;
   }

   public boolean m_6104_(BlockState state, BlockState stateFrom, Direction direction) {
      return stateFrom.m_60713_(this) ? true : super.m_6104_(state, stateFrom, direction);
   }

   public boolean m_7420_(BlockState pState, BlockGetter pLevel, BlockPos pPos) {
      return true;
   }
}
