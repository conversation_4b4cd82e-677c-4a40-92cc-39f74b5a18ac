package com.github.manasmods.tensura.block.entity;

import com.github.manasmods.tensura.registry.blocks.TensuraBlockEntities;
import net.minecraft.core.BlockPos;
import net.minecraft.world.level.block.entity.BlockEntity;
import net.minecraft.world.level.block.entity.BlockEntityType;
import net.minecraft.world.level.block.state.BlockState;
import software.bernie.geckolib3.core.IAnimatable;
import software.bernie.geckolib3.core.manager.AnimationData;
import software.bernie.geckolib3.core.manager.AnimationFactory;
import software.bernie.geckolib3.util.GeckoLibUtil;

public class OrcDisasterHeadBlockEntity extends BlockEntity implements IAnimatable {
   private final AnimationFactory factory = GeckoLibUtil.createFactory(this, true);

   public OrcDisasterHeadBlockEntity(BlockPos pPos, BlockState pBlockState) {
      super((BlockEntityType)TensuraBlockEntities.ORC_DISASTER_HEAD.get(), pPos, pBlockState);
   }

   public void registerControllers(AnimationData data) {
   }

   public AnimationFactory getFactory() {
      return this.factory;
   }
}
