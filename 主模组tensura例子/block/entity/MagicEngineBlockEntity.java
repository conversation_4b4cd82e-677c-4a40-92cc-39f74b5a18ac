package com.github.manasmods.tensura.block.entity;

import com.github.manasmods.tensura.block.MagicEngineBlock;
import com.github.manasmods.tensura.entity.magic.barrier.MagicEngineBarrierEntity;
import com.github.manasmods.tensura.registry.blocks.TensuraBlockEntities;
import java.util.UUID;
import javax.annotation.Nullable;
import net.minecraft.core.BlockPos;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.protocol.game.ClientboundBlockEntityDataPacket;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.entity.BlockEntity;
import net.minecraft.world.level.block.entity.BlockEntityType;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.phys.Vec3;

public class MagicEngineBlockEntity extends BlockEntity {
   public int spin;
   private UUID barrier;

   public MagicEngineBlockEntity(BlockPos pPos, BlockState pBlockState) {
      super((BlockEntityType)TensuraBlockEntities.MAGIC_ENGINE.get(), pPos, pBlockState);
   }

   protected void m_183515_(CompoundTag pTag) {
      super.m_183515_(pTag);
      if (this.barrier != null) {
         pTag.m_128362_("Barrier", this.barrier);
      } else {
         pTag.m_128473_("Barrier");
      }

   }

   public void m_142466_(CompoundTag pTag) {
      super.m_142466_(pTag);
      if (pTag.m_128403_("Barrier")) {
         this.barrier = pTag.m_128342_("Barrier");
      } else {
         this.barrier = null;
      }

   }

   @Nullable
   public UUID getBarrierUUID() {
      return this.barrier;
   }

   public void setBarrierUUID(@Nullable UUID uuid) {
      this.barrier = uuid;
   }

   public ClientboundBlockEntityDataPacket getUpdatePacket() {
      return ClientboundBlockEntityDataPacket.m_195640_(this);
   }

   public CompoundTag m_5995_() {
      CompoundTag tag = super.m_5995_();
      this.m_183515_(tag);
      return tag;
   }

   public static void tick(Level level, BlockPos pos, BlockState state, MagicEngineBlockEntity pEntity) {
      if (!level.m_5776_()) {
         ServerLevel serverLevel = (ServerLevel)level;
         UUID uuid = pEntity.getBarrierUUID();
         Entity entity;
         if ((Boolean)state.m_61143_(MagicEngineBlock.ENABLED)) {
            if (uuid != null) {
               entity = serverLevel.m_8791_(uuid);
               if (entity instanceof MagicEngineBarrierEntity) {
                  MagicEngineBarrierEntity barrier = (MagicEngineBarrierEntity)entity;
                  if (entity.m_20238_(Vec3.m_82512_(pos)) >= 1.0D) {
                     entity.m_146870_();
                     pEntity.setBarrierUUID((UUID)null);
                  } else {
                     barrier.increaseLife(1);
                  }
               } else {
                  pEntity.setBarrierUUID((UUID)null);
               }

            } else {
               MagicEngineBarrierEntity barrier = new MagicEngineBarrierEntity(level);
               barrier.setLife(20);
               barrier.m_146884_(Vec3.m_82512_(pos));
               level.m_7967_(barrier);
               pEntity.setBarrierUUID(barrier.m_20148_());
            }
         } else if (uuid != null) {
            entity = serverLevel.m_8791_(uuid);
            if (entity instanceof MagicEngineBarrierEntity) {
               entity.m_146870_();
            }

            pEntity.setBarrierUUID((UUID)null);
         }
      }
   }
}
