package com.github.manasmods.tensura.block.entity;

import com.github.manasmods.tensura.registry.blocks.TensuraBlockEntities;
import net.minecraft.core.BlockPos;
import net.minecraft.world.level.block.entity.BlockEntityType;
import net.minecraft.world.level.block.entity.SignBlockEntity;
import net.minecraft.world.level.block.state.BlockState;

public class TensuraSignBlockEntity extends SignBlockEntity {
   public TensuraSignBlockEntity(BlockPos pWorldPosition, BlockState pBlockState) {
      super(pWorldPosition, pBlockState);
   }

   public BlockEntityType<?> m_58903_() {
      return (BlockEntityType)TensuraBlockEntities.SIGN.get();
   }
}
