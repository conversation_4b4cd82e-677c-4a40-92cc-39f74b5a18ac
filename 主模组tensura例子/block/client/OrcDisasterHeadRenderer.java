package com.github.manasmods.tensura.block.client;

import com.github.manasmods.tensura.block.entity.OrcDisasterHeadBlockEntity;
import com.github.manasmods.tensura.registry.blocks.TensuraBlocks;
import com.mojang.blaze3d.vertex.PoseStack;
import com.mojang.math.Vector3f;
import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.client.renderer.blockentity.BlockEntityRendererProvider.Context;
import net.minecraft.core.BlockPos;
import net.minecraft.core.Direction;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.entity.BlockEntity;
import net.minecraft.world.level.block.state.properties.BlockStateProperties;
import software.bernie.geckolib3.renderers.geo.GeoBlockRenderer;

public class OrcDisasterHeadRenderer extends GeoBlockRenderer<OrcDisasterHeadBlockEntity> {
   private Integer rotation = 0;
   private Level level;
   private BlockPos blockPos;

   public OrcDisasterHeadRenderer(Context rendererProvider) {
      super(rendererProvider, new OrcDisasterHeadModel());
   }

   public void m_6922_(BlockEntity tile, float partialTicks, PoseStack poseStack, MultiBufferSource bufferSource, int packedLight, int packedOverlay) {
      this.rotation = (Integer)tile.m_58900_().m_61143_(BlockStateProperties.f_61390_);
      this.level = tile.m_58904_();
      this.blockPos = tile.m_58899_();
      super.m_6922_(tile, partialTicks, poseStack, bufferSource, packedLight, packedOverlay);
   }

   protected void rotateBlock(Direction facing, PoseStack poseStack) {
      if (this.level != null && this.level.m_8055_(this.blockPos).m_60734_() == TensuraBlocks.ORC_DISASTER_HEAD.get()) {
         if (facing != Direction.UP && facing != Direction.DOWN) {
            super.rotateBlock(facing, poseStack);
            poseStack.m_85837_(0.0D, 0.2D, 0.2D);
         } else {
            poseStack.m_85845_(Vector3f.f_122225_.m_122240_((float)this.rotation * 22.5F * -1.0F));
            poseStack.m_85837_(0.0D, -0.01D, 0.0D);
         }
      }
   }
}
