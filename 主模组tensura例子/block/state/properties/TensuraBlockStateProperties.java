package com.github.manasmods.tensura.block.state.properties;

import net.minecraft.world.level.block.state.properties.EnumProperty;

public class TensuraBlockStateProperties {
   public static final EnumProperty<SmithingBenchPart> SMITHING_BENCH_PART = EnumProperty.m_61587_("part", SmithingBenchPart.class);
   public static final EnumProperty<LoomPart> LOOM_PART = EnumProperty.m_61587_("part", LoomPart.class);
   public static final EnumProperty<KilnPart> KILN_PART = EnumProperty.m_61587_("part", KilnPart.class);
   public static final EnumProperty<CookingPotPart> COOKING_POT_PART = EnumProperty.m_61587_("part", CookingPotPart.class);
}
