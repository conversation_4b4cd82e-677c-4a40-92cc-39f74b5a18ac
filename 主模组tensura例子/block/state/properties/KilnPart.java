package com.github.manasmods.tensura.block.state.properties;

import net.minecraft.util.StringRepresentable;
import org.jetbrains.annotations.NotNull;

public enum KilnPart implements StringRepresentable {
   BASE("base"),
   TOP("top");

   private final String name;

   @NotNull
   public String m_7912_() {
      return this.name;
   }

   public String toString() {
      return this.name;
   }

   private KilnPart(String name) {
      this.name = name;
   }

   public String getName() {
      return this.name;
   }

   // $FF: synthetic method
   private static KilnPart[] $values() {
      return new KilnPart[]{BASE, TOP};
   }
}
