package com.github.manasmods.tensura.block.state.properties;

import net.minecraft.util.StringRepresentable;
import org.jetbrains.annotations.NotNull;

public enum CookingPotPart implements StringRepresentable {
   BOTTOM("bottom"),
   TOP("top");

   private final String name;

   @NotNull
   public String m_7912_() {
      return this.name;
   }

   public String toString() {
      return this.name;
   }

   private CookingPotPart(String name) {
      this.name = name;
   }

   public String getName() {
      return this.name;
   }

   // $FF: synthetic method
   private static CookingPotPart[] $values() {
      return new CookingPotPart[]{BOTTOM, TOP};
   }
}
