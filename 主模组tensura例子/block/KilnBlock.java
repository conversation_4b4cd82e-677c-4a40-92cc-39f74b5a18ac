package com.github.manasmods.tensura.block;

import com.github.manasmods.tensura.block.entity.KilnBlockEntity;
import com.github.manasmods.tensura.block.state.properties.KilnPart;
import com.github.manasmods.tensura.block.state.properties.TensuraBlockStateProperties;
import com.github.manasmods.tensura.registry.blocks.TensuraBlockEntities;
import java.util.function.ToIntFunction;
import net.minecraft.core.BlockPos;
import net.minecraft.core.Direction;
import net.minecraft.core.Direction.Axis;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.util.RandomSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.InteractionResult;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.context.BlockPlaceContext;
import net.minecraft.world.level.BlockGetter;
import net.minecraft.world.level.Explosion;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.LevelAccessor;
import net.minecraft.world.level.block.BaseEntityBlock;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.block.Mirror;
import net.minecraft.world.level.block.RenderShape;
import net.minecraft.world.level.block.Rotation;
import net.minecraft.world.level.block.SimpleWaterloggedBlock;
import net.minecraft.world.level.block.SoundType;
import net.minecraft.world.level.block.entity.BlockEntity;
import net.minecraft.world.level.block.entity.BlockEntityTicker;
import net.minecraft.world.level.block.entity.BlockEntityType;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.block.state.BlockBehaviour.Properties;
import net.minecraft.world.level.block.state.StateDefinition.Builder;
import net.minecraft.world.level.block.state.properties.BlockStateProperties;
import net.minecraft.world.level.block.state.properties.BooleanProperty;
import net.minecraft.world.level.block.state.properties.DirectionProperty;
import net.minecraft.world.level.block.state.properties.EnumProperty;
import net.minecraft.world.level.block.state.properties.Property;
import net.minecraft.world.level.material.FluidState;
import net.minecraft.world.level.material.Fluids;
import net.minecraft.world.level.material.Material;
import net.minecraft.world.level.material.MaterialColor;
import net.minecraft.world.phys.BlockHitResult;
import net.minecraft.world.phys.shapes.CollisionContext;
import net.minecraft.world.phys.shapes.Shapes;
import net.minecraft.world.phys.shapes.VoxelShape;
import net.minecraftforge.network.NetworkHooks;
import org.jetbrains.annotations.Nullable;

public class KilnBlock extends BaseEntityBlock implements SimpleWaterloggedBlock {
   public static final DirectionProperty FACING;
   public static final BooleanProperty LIT;
   public static final BooleanProperty WATERLOGGED;
   public static final EnumProperty<KilnPart> PART;
   private static final VoxelShape TOP_SHAPE;
   private static final VoxelShape BASE_SHAPE;

   public KilnBlock() {
      super(Properties.m_60944_(Material.f_76279_, MaterialColor.f_164534_).m_60913_(50.0F, 1200.0F).m_60918_(SoundType.f_56742_).m_60955_().m_60953_(litBlockEmission(13)).m_60999_());
      this.m_49959_((BlockState)((BlockState)((BlockState)((BlockState)((BlockState)this.m_49965_().m_61090_()).m_61124_(FACING, Direction.NORTH)).m_61124_(PART, KilnPart.BASE)).m_61124_(LIT, Boolean.FALSE)).m_61124_(WATERLOGGED, Boolean.FALSE));
   }

   public void m_6402_(Level pLevel, BlockPos pPos, BlockState pState, @Nullable LivingEntity pPlacer, ItemStack pStack) {
      super.m_6402_(pLevel, pPos, pState, pPlacer, pStack);
      if (!pLevel.m_5776_()) {
         BlockPos blockpos = this.getOtherPartPosition(pPos, (KilnPart)pState.m_61143_(PART));
         pLevel.m_7731_(blockpos, (BlockState)((BlockState)pState.m_61124_(PART, KilnPart.TOP)).m_61124_(WATERLOGGED, this.isWaterAtPosition(pLevel, blockpos)), 3);
         pLevel.m_6289_(pPos, Blocks.f_50016_);
         pState.m_60701_(pLevel, pPos, 3);
      }
   }

   private BlockPos getOtherPartPosition(BlockPos sourcePos, KilnPart part) {
      return part == KilnPart.BASE ? sourcePos.m_7494_() : sourcePos.m_7495_();
   }

   protected void m_7926_(Builder<Block, BlockState> pBuilder) {
      pBuilder.m_61104_(new Property[]{FACING, LIT, PART, WATERLOGGED});
   }

   public void m_5707_(Level pLevel, BlockPos pPos, BlockState pState, Player pPlayer) {
      super.m_5707_(pLevel, pPos, pState, pPlayer);
      if (!pLevel.m_5776_()) {
         BlockPos blockpos = this.getOtherPartPosition(pPos, (KilnPart)pState.m_61143_(PART));
         pLevel.m_46597_(blockpos, Blocks.f_50016_.m_49966_());
      }

   }

   public void m_7592_(Level pLevel, BlockPos pPos, Explosion pExplosion) {
      if (!pLevel.m_5776_()) {
         BlockState pState = pLevel.m_8055_(pPos);
         BlockPos blockpos = this.getOtherPartPosition(pPos, (KilnPart)pState.m_61143_(PART));
         pLevel.m_46597_(blockpos, Blocks.f_50016_.m_49966_());
      }
   }

   @Nullable
   public BlockState m_5573_(BlockPlaceContext pContext) {
      BlockPos blockpos = pContext.m_8083_();
      Level level = pContext.m_43725_();
      return blockpos.m_123342_() <= level.m_151558_() - 1 && level.m_8055_(blockpos.m_7494_()).m_60629_(pContext) ? (BlockState)((BlockState)this.m_49966_().m_61124_(FACING, pContext.m_8125_().m_122424_())).m_61124_(WATERLOGGED, this.isWaterAtPosition(level, blockpos)) : null;
   }

   public BlockState m_7417_(BlockState pState, Direction pDirection, BlockState pNeighborState, LevelAccessor pLevel, BlockPos pCurrentPos, BlockPos pNeighborPos) {
      if ((Boolean)pState.m_61143_(WATERLOGGED)) {
         pLevel.m_186469_(pCurrentPos, Fluids.f_76193_, Fluids.f_76193_.m_6718_(pLevel));
      }

      return super.m_7417_(pState, pDirection, pNeighborState, pLevel, pCurrentPos, pNeighborPos);
   }

   private boolean isWaterAtPosition(Level level, BlockPos blockPos) {
      return level.m_6425_(blockPos).m_192917_(Fluids.f_76193_);
   }

   public VoxelShape m_5940_(BlockState pState, BlockGetter pLevek, BlockPos pPos, CollisionContext pContext) {
      KilnPart part = (KilnPart)pState.m_61143_(PART);
      return part == KilnPart.TOP ? TOP_SHAPE : BASE_SHAPE;
   }

   public BlockState m_6843_(BlockState pState, Rotation pRotation) {
      return (BlockState)pState.m_61124_(FACING, pRotation.m_55954_((Direction)pState.m_61143_(FACING)));
   }

   public BlockState m_6943_(BlockState pState, Mirror pMirror) {
      return pState.m_60717_(pMirror.m_54846_((Direction)pState.m_61143_(FACING)));
   }

   public void m_214162_(BlockState pState, Level pLevel, BlockPos pPos, RandomSource pRandom) {
      if ((Boolean)pState.m_61143_(LIT) && ((KilnPart)pState.m_61143_(PART)).equals(KilnPart.BASE)) {
         double d0 = (double)pPos.m_123341_() + 0.5D;
         double d1 = (double)pPos.m_123342_();
         double d2 = (double)pPos.m_123343_() + 0.5D;
         if (pRandom.m_188500_() < 0.1D) {
            pLevel.m_7785_(d0, d1, d2, SoundEvents.f_11715_, SoundSource.BLOCKS, 1.0F, 1.0F, false);
         }

         Direction direction = (Direction)pState.m_61143_(FACING);
         Axis direction$axis = direction.m_122434_();
         double d3 = pRandom.m_188500_() * 0.6D - 0.3D;
         double d4 = direction$axis == Axis.X ? (double)direction.m_122429_() * 0.52D : d3;
         double d5 = pRandom.m_188500_() * 6.0D / 16.0D;
         double d6 = direction$axis == Axis.Z ? (double)direction.m_122431_() * 0.52D : d3;
         pLevel.m_7106_(ParticleTypes.f_123762_, d0 + d4, d1 + d5, d2 + d6, 0.0D, 0.0D, 0.0D);
         pLevel.m_7106_(ParticleTypes.f_123744_, d0 + d4, d1 + d5, d2 + d6, 0.0D, 0.0D, 0.0D);
      }

   }

   public static ToIntFunction<BlockState> litBlockEmission(int pLightValue) {
      return (state) -> {
         return (Boolean)state.m_61143_(BlockStateProperties.f_61443_) ? pLightValue : 0;
      };
   }

   public FluidState m_5888_(BlockState pState) {
      return (Boolean)pState.m_61143_(WATERLOGGED) ? Fluids.f_76193_.m_76068_(false) : super.m_5888_(pState);
   }

   public RenderShape m_7514_(BlockState pState) {
      return RenderShape.MODEL;
   }

   public void m_6810_(BlockState pState, Level pLevel, BlockPos pPos, BlockState pNewState, boolean pIsMoving) {
      if (pState.m_60734_() != pNewState.m_60734_()) {
         BlockEntity blockEntity = pLevel.m_7702_(pPos);
         if (blockEntity instanceof KilnBlockEntity) {
            KilnBlockEntity kilnblockEntity = (KilnBlockEntity)blockEntity;
            kilnblockEntity.drops();
         }
      }

      super.m_6810_(pState, pLevel, pPos, pNewState, pIsMoving);
   }

   public InteractionResult m_6227_(BlockState pState, Level pLevel, BlockPos pPos, Player pPlayer, InteractionHand pHand, BlockHitResult pHit) {
      return pLevel.m_5776_() ? InteractionResult.m_19078_(true) : this.openMenu((ServerPlayer)pPlayer, pLevel, ((KilnPart)pState.m_61143_(PART)).equals(KilnPart.BASE) ? pPos : pPos.m_7495_());
   }

   private InteractionResult openMenu(ServerPlayer player, Level level, BlockPos pos) {
      BlockEntity entity = level.m_7702_(pos);
      if (entity instanceof KilnBlockEntity) {
         KilnBlockEntity kilnblockEntity = (KilnBlockEntity)entity;
         NetworkHooks.openScreen(player, kilnblockEntity, pos);
         return InteractionResult.m_19078_(false);
      } else {
         throw new IllegalStateException(String.format("Container provider for Kiln block is missing!\n Position: %s; World: %s", pos, level.m_46472_()));
      }
   }

   @Nullable
   public BlockEntity m_142194_(BlockPos pos, BlockState state) {
      return state.m_61143_(PART) == KilnPart.BASE ? new KilnBlockEntity(pos, state) : null;
   }

   @Nullable
   public <T extends BlockEntity> BlockEntityTicker<T> m_142354_(Level level, BlockState state, BlockEntityType<T> type) {
      return state.m_61143_(PART) == KilnPart.BASE ? m_152132_(type, (BlockEntityType)TensuraBlockEntities.KILN.get(), KilnBlockEntity::tick) : null;
   }

   static {
      FACING = BlockStateProperties.f_61374_;
      LIT = BlockStateProperties.f_61443_;
      WATERLOGGED = BlockStateProperties.f_61362_;
      PART = TensuraBlockStateProperties.KILN_PART;
      TOP_SHAPE = Shapes.m_83124_(m_49796_(3.0D, 0.0D, 3.0D, 13.0D, 2.0D, 13.0D), new VoxelShape[]{m_49796_(4.0D, 2.0D, 4.0D, 12.0D, 12.0D, 12.0D), m_49796_(3.0D, 12.0D, 3.0D, 13.0D, 13.0D, 13.0D), m_49796_(2.0D, 13.0D, 2.0D, 14.0D, 15.0D, 14.0D), m_49796_(3.0D, 15.0D, 3.0D, 13.0D, 16.0D, 13.0D)});
      BASE_SHAPE = Shapes.m_83124_(m_49796_(0.0D, 0.0D, 0.0D, 16.0D, 12.0D, 16.0D), new VoxelShape[]{m_49796_(1.0D, 12.0D, 1.0D, 15.0D, 14.0D, 15.0D), m_49796_(2.0D, 14.0D, 2.0D, 14.0D, 16.0D, 14.0D)});
   }
}
