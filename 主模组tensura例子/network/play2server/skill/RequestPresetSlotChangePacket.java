package com.github.manasmods.tensura.network.play2server.skill;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.tensura.capability.skill.TensuraSkillCapability;
import java.util.Optional;
import java.util.function.Supplier;
import net.minecraft.network.FriendlyByteBuf;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.server.level.ServerPlayer;
import net.minecraftforge.network.NetworkEvent.Context;

public class RequestPresetSlotChangePacket {
   private final int preset;
   private final int slot;
   private final ResourceLocation skill;

   public RequestPresetSlotChangePacket(FriendlyByteBuf buf) {
      this.preset = buf.readInt();
      this.slot = buf.readInt();
      this.skill = buf.m_130281_();
   }

   public RequestPresetSlotChangePacket(int slot, int preset, ResourceLocation skill) {
      this.preset = preset;
      this.slot = slot;
      this.skill = skill;
   }

   public RequestPresetSlotChangePacket(int slot, int preset) {
      this.preset = preset;
      this.slot = slot;
      this.skill = new ResourceLocation("tensura", "none");
   }

   public void toBytes(FriendlyByteBuf buf) {
      buf.writeInt(this.preset);
      buf.writeInt(this.slot);
      if (this.skill != null) {
         buf.m_130085_(this.skill);
      }

   }

   public void handle(Supplier<Context> ctx) {
      ((Context)ctx.get()).enqueueWork(() -> {
         ServerPlayer serverPlayer = ((Context)ctx.get()).getSender();
         if (serverPlayer != null) {
            this.setSkillInSlot(serverPlayer);
         }

      });
      ((Context)ctx.get()).setPacketHandled(true);
   }

   private void setSkillInSlot(ServerPlayer serverPlayer) {
      TensuraSkillCapability.getFrom(serverPlayer).ifPresent((cap) -> {
         if (this.skill.m_135815_().equals("none")) {
            cap.setInstanceInPresetSlot((ManasSkillInstance)null, this.preset, this.slot);
         } else {
            ManasSkill manasSkill = (ManasSkill)SkillAPI.getSkillRegistry().getValue(this.skill);
            if (manasSkill != null) {
               Optional<ManasSkillInstance> optional = SkillAPI.getSkillsFrom(serverPlayer).getSkill(manasSkill);
               if (!optional.isEmpty()) {
                  ManasSkillInstance instance = (ManasSkillInstance)optional.get();
                  cap.setInstanceInPresetSlot(instance, this.preset, this.slot);
               }
            }
         }
      });
      TensuraSkillCapability.sync(serverPlayer);
   }
}
