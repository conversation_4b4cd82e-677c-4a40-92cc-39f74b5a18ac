package com.github.manasmods.tensura.network.play2server;

import com.github.manasmods.tensura.menu.KilnMenu;
import java.util.function.Consumer;
import java.util.function.Supplier;
import net.minecraft.network.FriendlyByteBuf;
import net.minecraft.world.inventory.AbstractContainerMenu;
import net.minecraftforge.network.NetworkEvent.Context;

public class RequestKilnActionPacket {
   private final RequestKilnActionPacket.Action action;

   public RequestKilnActionPacket(FriendlyByteBuf buf) {
      this.action = (RequestKilnActionPacket.Action)buf.m_130066_(RequestKilnActionPacket.Action.class);
   }

   public void toBytes(FriendlyByteBuf buf) {
      buf.m_130068_(this.action);
   }

   public void handle(Supplier<Context> ctx) {
      ((Context)ctx.get()).enqueueWork(() -> {
         this.action.contextConsumer.accept((Context)ctx.get());
      });
      ((Context)ctx.get()).setPacketHandled(true);
   }

   public RequestKilnActionPacket(RequestKilnActionPacket.Action action) {
      this.action = action;
   }

   public static enum Action {
      MIXING_LEFT((context) -> {
         AbstractContainerMenu patt976$temp = context.getSender().f_36096_;
         if (patt976$temp instanceof KilnMenu) {
            KilnMenu menu = (KilnMenu)patt976$temp;
            if (!menu.blockEntity.hasPrevMixingRecipe()) {
               return;
            }

            menu.blockEntity.mixingPrevRecipe();
         }

      }),
      MIXING_RIGHT((context) -> {
         AbstractContainerMenu patt1242$temp = context.getSender().f_36096_;
         if (patt1242$temp instanceof KilnMenu) {
            KilnMenu menu = (KilnMenu)patt1242$temp;
            if (!menu.blockEntity.hasNextMixingRecipe()) {
               return;
            }

            menu.blockEntity.mixingNextRecipe();
         }

      });

      private final Consumer<Context> contextConsumer;

      private Action(Consumer<Context> contextConsumer) {
         this.contextConsumer = contextConsumer;
      }

      // $FF: synthetic method
      private static RequestKilnActionPacket.Action[] $values() {
         return new RequestKilnActionPacket.Action[]{MIXING_LEFT, MIXING_RIGHT};
      }
   }
}
