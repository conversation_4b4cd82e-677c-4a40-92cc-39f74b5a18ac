package com.github.manasmods.tensura.network.play2client;

import java.util.function.Supplier;
import net.minecraft.network.FriendlyByteBuf;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.fml.DistExecutor;
import net.minecraftforge.network.NetworkEvent.Context;

public class ClientboundNPCScreenOpenPacket {
   protected final int containerId;
   protected final int size;
   protected final int entityId;
   protected final double EP;

   public ClientboundNPCScreenOpenPacket(int containerId, int size, int entityId, double EP) {
      this.containerId = containerId;
      this.size = size;
      this.entityId = entityId;
      this.EP = EP;
   }

   public ClientboundNPCScreenOpenPacket(FriendlyByteBuf buffer) {
      this.containerId = buffer.readByte();
      this.size = buffer.m_130242_();
      this.entityId = buffer.readInt();
      this.EP = buffer.readDouble();
   }

   public void toBytes(FriendlyByteBuf buffer) {
      buffer.writeByte(this.containerId);
      buffer.m_130130_(this.size);
      buffer.writeInt(this.entityId);
      buffer.writeDouble(this.EP);
   }

   public static void handle(ClientboundNPCScreenOpenPacket message, Supplier<Context> context) {
      ((Context)context.get()).enqueueWork(() -> {
         DistExecutor.unsafeRunWhenOn(Dist.CLIENT, () -> {
            return () -> {
               ClientAccess.handleClientboundNPCScreenOpenPacket(message, context);
            };
         });
      });
      ((Context)context.get()).setPacketHandled(true);
   }
}
