package com.github.manasmods.tensura.network.play2client;

import com.github.manasmods.tensura.data.pack.Slotting.SlottingElementCombination;
import com.mojang.serialization.Codec;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Supplier;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.nbt.NbtOps;
import net.minecraft.network.FriendlyByteBuf;
import net.minecraft.resources.ResourceLocation;
import net.minecraftforge.network.NetworkEvent.Context;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class SyncElementCombinationPacket {
   private static final Logger log = LogManager.getLogger(SyncElementCombinationPacket.class);
   private static final Codec<Map<ResourceLocation, SlottingElementCombination>> MAPPER;
   public static Map<ResourceLocation, SlottingElementCombination> SYNCED_DATA;
   private final Map<ResourceLocation, SlottingElementCombination> map;

   public SyncElementCombinationPacket(FriendlyByteBuf buf) {
      this.map = (Map)MAPPER.parse(NbtOps.f_128958_, buf.m_130260_()).result().orElse(new HashMap());
   }

   public void toBytes(FriendlyByteBuf buf) {
      buf.m_130079_((CompoundTag)MAPPER.encodeStart(NbtOps.f_128958_, this.map).result().orElse(new CompoundTag()));
   }

   public void handle(Supplier<Context> ctx) {
      ((Context)ctx.get()).enqueueWork(this::handleOnMainThread);
      ((Context)ctx.get()).setPacketHandled(true);
   }

   private void handleOnMainThread() {
      SYNCED_DATA = this.map;
      log.debug("Got {} SlottingElementCombinations from Server", this.map.size());
   }

   public SyncElementCombinationPacket(Map<ResourceLocation, SlottingElementCombination> map) {
      this.map = map;
   }

   static {
      MAPPER = Codec.unboundedMap(ResourceLocation.f_135803_, SlottingElementCombination.CODEC);
      SYNCED_DATA = new HashMap();
   }
}
