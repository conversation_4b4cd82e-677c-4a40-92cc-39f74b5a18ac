package com.github.manasmods.tensura.network.play2client;

import java.util.function.Supplier;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.FriendlyByteBuf;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.fml.DistExecutor;
import net.minecraftforge.network.NetworkEvent.Context;

public class SyncSmithingCapabilityPacket {
   private final CompoundTag data;
   private final int entityId;

   public SyncSmithingCapabilityPacket(FriendlyByteBuf buf) {
      this.data = buf.m_130261_();
      this.entityId = buf.readInt();
   }

   public void toBytes(FriendlyByteBuf buf) {
      buf.m_130079_(this.data);
      buf.writeInt(this.entityId);
   }

   public void handle(Supplier<Context> ctx) {
      ((Context)ctx.get()).enqueueWork(() -> {
         DistExecutor.unsafeRunWhenOn(Dist.CLIENT, () -> {
            return () -> {
               ClientAccess.updateSmithingCapability(this.entityId, this.data);
            };
         });
      });
      ((Context)ctx.get()).setPacketHandled(true);
   }

   public SyncSmithingCapabilityPacket(CompoundTag data, int entityId) {
      this.data = data;
      this.entityId = entityId;
   }
}
