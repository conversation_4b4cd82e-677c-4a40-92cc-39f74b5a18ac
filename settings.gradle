pluginManagement {
    repositories {
        gradlePluginPortal()
        maven {
            name = 'MinecraftForge'
            url = 'https://maven.aliyun.com/repository/public'
        }
        maven { url = 'https://maven.parchmentmc.org' }
        maven { url = 'https://maven.aliyun.com/repository/gradle-plugin' }
        maven { url = 'https://maven.aliyun.com/repository/central' }
        maven { url = 'https://maven.aliyun.com/repository/jcenter' }
    }
}

plugins {
    id 'org.gradle.toolchains.foojay-resolver-convention' version '0.7.0'
}

rootProject.name = 'tensuraaddonexample'
